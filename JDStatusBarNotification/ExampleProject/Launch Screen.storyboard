<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="21179.7" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" launchScreen="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES" initialViewController="OoA-lO-nbV">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21169.4"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--Navigation Controller-->
        <scene sceneID="xCh-It-96P">
            <objects>
                <navigationController id="OoA-lO-nbV" sceneMemberID="viewController">
                    <navigationBar key="navigationBar" contentMode="scaleToFill" id="pkm-Aa-BSb">
                        <rect key="frame" x="0.0" y="44" width="414" height="44"/>
                        <autoresizingMask key="autoresizingMask"/>
                    </navigationBar>
                    <connections>
                        <segue destination="6q5-sv-wno" kind="relationship" relationship="rootViewController" id="TFY-C6-14F"/>
                    </connections>
                </navigationController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="3xM-fq-zSB" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-665" y="338"/>
        </scene>
        <!--Table View Controller-->
        <scene sceneID="O4U-Wt-M6a">
            <objects>
                <tableViewController id="6q5-sv-wno" userLabel="Table View Controller" sceneMemberID="viewController">
                    <tableView key="view" clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="static" style="insetGrouped" separatorStyle="default" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="18" estimatedSectionHeaderHeight="-1" sectionFooterHeight="18" estimatedSectionFooterHeight="-1" id="55L-QV-yLO">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <color key="backgroundColor" systemColor="secondarySystemBackgroundColor"/>
                        <sections>
                            <tableViewSection id="yiH-hd-uut" userLabel="Custom Style">
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="default" indentationWidth="10" id="wcj-Ly-FA3">
                                        <rect key="frame" x="20" y="18" width="374" height="43.5"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="wcj-Ly-FA3" id="juc-hk-XW7">
                                            <rect key="frame" x="0.0" y="0.0" width="374" height="43.5"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="Default Style" id="cBW-VO-j7B">
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="default" indentationWidth="10" id="x02-Jz-0GP">
                                        <rect key="frame" x="20" y="117.5" width="374" height="43.5"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="x02-Jz-0GP" id="izk-mN-dIL">
                                            <rect key="frame" x="0.0" y="0.0" width="374" height="43.5"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="default" indentationWidth="10" id="Skx-xa-D9Z">
                                        <rect key="frame" x="20" y="161" width="374" height="43.5"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="Skx-xa-D9Z" id="vb3-mr-haJ">
                                            <rect key="frame" x="0.0" y="0.0" width="374" height="43.5"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="Settings" id="m4z-mQ-7Gj">
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="default" indentationWidth="10" id="Wau-Ku-juB">
                                        <rect key="frame" x="20" y="260.5" width="374" height="43.5"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="Wau-Ku-juB" id="plE-fO-A79">
                                            <rect key="frame" x="0.0" y="0.0" width="374" height="43.5"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="default" indentationWidth="10" id="5Qf-zZ-b8i">
                                        <rect key="frame" x="20" y="304" width="374" height="43.5"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="5Qf-zZ-b8i" id="MyG-LH-UHa">
                                            <rect key="frame" x="0.0" y="0.0" width="374" height="43.5"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="default" indentationWidth="10" id="0G2-DW-cRP">
                                        <rect key="frame" x="20" y="347.5" width="374" height="43.5"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="0G2-DW-cRP" id="4Mr-BC-XDt">
                                            <rect key="frame" x="0.0" y="0.0" width="374" height="43.5"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="default" indentationWidth="10" id="tvn-Vg-21B">
                                        <rect key="frame" x="20" y="391" width="374" height="43.5"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="tvn-Vg-21B" id="hcB-Xl-ZH1">
                                            <rect key="frame" x="0.0" y="0.0" width="374" height="43.5"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="Included Styles" id="vOh-AF-i0a">
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="default" indentationWidth="10" id="q34-0T-qnR">
                                        <rect key="frame" x="20" y="490.5" width="374" height="43.5"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="q34-0T-qnR" id="crz-gU-1WZ">
                                            <rect key="frame" x="0.0" y="0.0" width="374" height="43.5"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="default" indentationWidth="10" id="6jH-xQ-ntW">
                                        <rect key="frame" x="20" y="534" width="374" height="43.5"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="6jH-xQ-ntW" id="zaO-jt-jPO">
                                            <rect key="frame" x="0.0" y="0.0" width="374" height="43.5"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="default" indentationWidth="10" id="9D3-oe-B7X">
                                        <rect key="frame" x="20" y="577.5" width="374" height="43.5"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="9D3-oe-B7X" id="0EZ-bS-pRz">
                                            <rect key="frame" x="0.0" y="0.0" width="374" height="43.5"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="default" indentationWidth="10" id="LbC-C1-Tsh">
                                        <rect key="frame" x="20" y="621" width="374" height="43.5"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="LbC-C1-Tsh" id="8UQ-u1-Xsn">
                                            <rect key="frame" x="0.0" y="0.0" width="374" height="43.5"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="default" indentationWidth="10" id="pWg-l6-ZAg">
                                        <rect key="frame" x="20" y="664.5" width="374" height="43.5"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="pWg-l6-ZAg" id="imn-4N-FW7">
                                            <rect key="frame" x="0.0" y="0.0" width="374" height="43.5"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="default" indentationWidth="10" id="6be-qV-njI">
                                        <rect key="frame" x="20" y="708" width="374" height="43.5"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="6be-qV-njI" id="KWR-Ia-gLg">
                                            <rect key="frame" x="0.0" y="0.0" width="374" height="43.5"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="default" indentationWidth="10" id="MVK-PU-psi">
                                        <rect key="frame" x="20" y="751.5" width="374" height="43.5"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="MVK-PU-psi" id="iYc-op-oHi">
                                            <rect key="frame" x="0.0" y="0.0" width="374" height="43.5"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="Custom Style Examples" id="PL0-o2-v50">
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="default" indentationWidth="10" id="rlt-37-xrl">
                                        <rect key="frame" x="20" y="851" width="374" height="43.5"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="rlt-37-xrl" id="9pp-vy-VIs">
                                            <rect key="frame" x="0.0" y="0.0" width="374" height="43.5"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="default" indentationWidth="10" id="HgZ-tm-0JN">
                                        <rect key="frame" x="20" y="894.5" width="374" height="43.5"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="HgZ-tm-0JN" id="4pX-EI-2So">
                                            <rect key="frame" x="0.0" y="0.0" width="374" height="43.5"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="default" indentationWidth="10" id="aSm-Il-yZu">
                                        <rect key="frame" x="20" y="938" width="374" height="43.5"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="aSm-Il-yZu" id="cgf-7y-YYZ">
                                            <rect key="frame" x="0.0" y="0.0" width="374" height="43.5"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="default" indentationWidth="10" id="p8k-el-xT4">
                                        <rect key="frame" x="20" y="981.5" width="374" height="43.5"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="p8k-el-xT4" id="M8U-4y-ipS">
                                            <rect key="frame" x="0.0" y="0.0" width="374" height="43.5"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="default" indentationWidth="10" id="Cae-1o-pvJ">
                                        <rect key="frame" x="20" y="1025" width="374" height="43.5"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="Cae-1o-pvJ" id="Iq2-Uq-dEU">
                                            <rect key="frame" x="0.0" y="0.0" width="374" height="43.5"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                        </sections>
                        <connections>
                            <outlet property="dataSource" destination="6q5-sv-wno" id="8Zt-Qu-ojc"/>
                            <outlet property="delegate" destination="6q5-sv-wno" id="x8a-HS-Ucz"/>
                        </connections>
                    </tableView>
                    <navigationItem key="navigationItem" title="Example" id="Yyo-5C-sX9"/>
                </tableViewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="8j0-jh-f4h" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="14" y="337"/>
        </scene>
    </scenes>
    <resources>
        <systemColor name="secondarySystemBackgroundColor">
            <color red="0.94901960784313721" green="0.94901960784313721" blue="0.96862745098039216" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
    </resources>
</document>
