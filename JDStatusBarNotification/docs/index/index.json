{"interfaceLanguages": {"swift": [{"children": [{"title": "<PERSON><PERSON>s", "type": "groupMarker"}, {"path": "/documentation/jdstatusbarnotification/gettingstarted", "title": "Getting Started", "type": "article"}, {"title": "Notification Presenter", "type": "groupMarker"}, {"children": [{"title": "Retrieve the presenter", "type": "groupMarker"}, {"path": "/documentation/jdstatusbarnotification/notificationpresenter/shared", "title": "static var shared: NotificationPresenter", "type": "property"}, {"title": "Present a notification", "type": "groupMarker"}, {"path": "/documentation/jdstatusbarnotification/notificationpresenter/completion", "title": "NotificationPresenter.Completion", "type": "typealias"}, {"path": "/documentation/jdstatusbarnotification/notificationpresenter/present(_:subtitle:stylename:duration:completion:)", "title": "func present(String, subtitle: String?, styleName: String?, duration: Double?, completion: Completion?) -> UIView", "type": "method"}, {"path": "/documentation/jdstatusbarnotification/notificationpresenter/present(_:subtitle:includedstyle:duration:completion:)", "title": "func present(String, subtitle: String?, includedStyle: IncludedStatusBarNotificationStyle, duration: Double?, completion: Completion?) -> UIView", "type": "method"}, {"children": [{"title": "Default Style", "type": "groupMarker"}, {"path": "/documentation/jdstatusbarnotification/includedstatusbarnotificationstyle/defaultstyle", "title": "case defaultStyle", "type": "case"}, {"path": "/documentation/jdstatusbarnotification/includedstatusbarnotificationstyle/light", "title": "case light", "type": "case"}, {"path": "/documentation/jdstatusbarnotification/includedstatusbarnotificationstyle/dark", "title": "case dark", "type": "case"}, {"title": "Other Included Styles", "type": "groupMarker"}, {"path": "/documentation/jdstatusbarnotification/includedstatusbarnotificationstyle/success", "title": "case success", "type": "case"}, {"path": "/documentation/jdstatusbarnotification/includedstatusbarnotificationstyle/warning", "title": "case warning", "type": "case"}, {"path": "/documentation/jdstatusbarnotification/includedstatusbarnotificationstyle/error", "title": "case error", "type": "case"}, {"path": "/documentation/jdstatusbarnotification/includedstatusbarnotificationstyle/matrix", "title": "case matrix", "type": "case"}, {"title": "Initializers", "type": "groupMarker"}, {"path": "/documentation/jdstatusbarnotification/includedstatusbarnotificationstyle/init(rawvalue:)", "title": "init?(rawValue: Int)", "type": "init"}, {"title": "Default Implementations", "type": "groupMarker"}, {"children": [{"title": "Operators", "type": "groupMarker"}, {"path": "/documentation/jdstatusbarnotification/includedstatusbarnotificationstyle/!=(_:_:)", "title": "static func != (Self, Self) -> <PERSON><PERSON>", "type": "op"}], "path": "/documentation/jdstatusbarnotification/includedstatusbarnotificationstyle/equatable-implementations", "title": "Equatable Implementations", "type": "symbol"}, {"children": [{"title": "Instance Properties", "type": "groupMarker"}, {"path": "/documentation/jdstatusbarnotification/includedstatusbarnotificationstyle/hashvalue", "title": "var hashValue: Int", "type": "property"}, {"title": "Instance Methods", "type": "groupMarker"}, {"path": "/documentation/jdstatusbarnotification/includedstatusbarnotificationstyle/hash(into:)", "title": "func hash(into: inout <PERSON><PERSON>)", "type": "method"}], "path": "/documentation/jdstatusbarnotification/includedstatusbarnotificationstyle/rawrepresentable-implementations", "title": "RawRepresentable Implementations", "type": "symbol"}], "path": "/documentation/jdstatusbarnotification/includedstatusbarnotificationstyle", "title": "IncludedStatusBarNotificationStyle", "type": "enum"}, {"title": "Present a notification (using a custom view)", "type": "groupMarker"}, {"path": "/documentation/jdstatusbarnotification/notificationpresenter/presentcustomview(_:sizingcontroller:stylename:completion:)", "title": "func presentCustomView(UIView, sizingController: NotificationPresenterCustomViewSizingController?, styleName: String?, completion: Completion?) -> UIView", "type": "method"}, {"path": "/documentation/jdstatusbarnotification/notificationpresenter/presentswiftview(stylename:viewbuilder:completion:)", "title": "func presentSwiftView(styleName: String?, viewBuilder: () -> some View, completion: Completion?) -> UIView", "type": "method"}, {"children": [{"title": "Instance Methods", "type": "groupMarker"}, {"path": "/documentation/jdstatusbarnotification/notificationpresentercustomviewsizingcontroller/sizethatfits(in:)", "title": "func sizeThatFits(in: CGSize) -> CGSize", "type": "method"}], "path": "/documentation/jdstatusbarnotification/notificationpresentercustomviewsizingcontroller", "title": "NotificationPresenterCustomViewSizingController", "type": "protocol"}, {"title": "Dismiss a notification", "type": "groupMarker"}, {"path": "/documentation/jdstatusbarnotification/notificationpresenter/dismiss(animated:after:completion:)", "title": "func dismiss(animated: <PERSON><PERSON>, after: Double?, completion: Completion?)", "type": "method"}, {"title": "Customize the style (Appearance & Behavior)", "type": "groupMarker"}, {"path": "/documentation/jdstatusbarnotification/notificationpresenter/preparestyleclosure", "title": "NotificationPresenter.PrepareStyleClosure", "type": "typealias"}, {"path": "/documentation/jdstatusbarnotification/notificationpresenter/updatedefaultstyle(_:)", "title": "func updateDefaultStyle(PrepareStyleClosure)", "type": "method"}, {"path": "/documentation/jdstatusbarnotification/notificationpresenter/addstyle(named:usingstyle:prepare:)", "title": "func addStyle(named: String, usingStyle: IncludedStatusBarNotificationStyle, prepare: PrepareStyleClosure) -> String", "type": "method"}, {"title": "Display supplementary views", "type": "groupMarker"}, {"path": "/documentation/jdstatusbarnotification/notificationpresenter/displayprogressbar(at:)", "title": "func displayProgressBar(at: Double)", "type": "method"}, {"path": "/documentation/jdstatusbarnotification/notificationpresenter/animateprogressbar(to:duration:completion:)", "title": "func animateProgressBar(to: Double, duration: Double, completion: Completion?)", "type": "method"}, {"path": "/documentation/jdstatusbarnotification/notificationpresenter/displayactivityindicator(_:)", "title": "func displayActivityIndicator(Bool)", "type": "method"}, {"path": "/documentation/jdstatusbarnotification/notificationpresenter/displayleftview(_:)", "title": "func displayLeftView(UIView?)", "type": "method"}, {"title": "Additional Presenter APIs", "type": "groupMarker"}, {"path": "/documentation/jdstatusbarnotification/notificationpresenter/isvisible", "title": "var isVisible: Bool", "type": "property"}, {"path": "/documentation/jdstatusbarnotification/notificationpresenter/updatetitle(_:)", "title": "func updateTitle(String)", "type": "method"}, {"path": "/documentation/jdstatusbarnotification/notificationpresenter/updatesubtitle(_:)", "title": "func updateSubtitle(String?)", "type": "method"}, {"path": "/documentation/jdstatusbarnotification/notificationpresenter/setwindowscene(_:)", "title": "func setWindowScene(UIWindowScene?)", "type": "method"}, {"title": "Legacy API support (for objc only)", "type": "groupMarker"}, {"path": "/documentation/jdstatusbarnotification/notificationpresenter/zlp(t:)", "title": "func zlp(t: String) -> UIView", "type": "method"}, {"path": "/documentation/jdstatusbarnotification/notificationpresenter/zlp(t:c:)", "title": "func zlp(t: String, c: Completion?) -> UIView", "type": "method"}, {"path": "/documentation/jdstatusbarnotification/notificationpresenter/zlp(t:d:)", "title": "func zlp(t: String, d: Double) -> UIView", "type": "method"}, {"path": "/documentation/jdstatusbarnotification/notificationpresenter/zlp(t:st:c:)", "title": "func zlp(t: String, st: String, c: Completion?) -> UIView", "type": "method"}, {"path": "/documentation/jdstatusbarnotification/notificationpresenter/zlp(t:s:)", "title": "func zlp(t: String, s: IncludedStatusBarNotificationStyle) -> UIView", "type": "method"}, {"path": "/documentation/jdstatusbarnotification/notificationpresenter/zlp(t:s:c:)", "title": "func zlp(t: String, s: IncludedStatusBarNotificationStyle, c: Completion?) -> UIView", "type": "method"}, {"path": "/documentation/jdstatusbarnotification/notificationpresenter/zlp(t:d:s:)", "title": "func zlp(t: String, d: Double, s: IncludedStatusBarNotificationStyle) -> UIView", "type": "method"}, {"path": "/documentation/jdstatusbarnotification/notificationpresenter/zlp(t:st:s:c:)", "title": "func zlp(t: String, st: String?, s: IncludedStatusBarNotificationStyle, c: Completion?) -> UIView", "type": "method"}, {"path": "/documentation/jdstatusbarnotification/notificationpresenter/zlp(t:cu:)", "title": "func zlp(t: String, cu: String) -> UIView", "type": "method"}, {"path": "/documentation/jdstatusbarnotification/notificationpresenter/zlp(t:cu:c:)", "title": "func zlp(t: String, cu: String?, c: Completion?) -> UIView", "type": "method"}, {"path": "/documentation/jdstatusbarnotification/notificationpresenter/zlp(t:d:cu:)", "title": "func zlp(t: String, d: Double, cu: String) -> UIView", "type": "method"}, {"path": "/documentation/jdstatusbarnotification/notificationpresenter/zlp(t:st:cu:c:)", "title": "func zlp(t: String, st: String?, cu: String?, c: Completion?) -> UIView", "type": "method"}, {"path": "/documentation/jdstatusbarnotification/notificationpresenter/zlp(cv:s:c:)", "title": "func zlp(cv: <PERSON><PERSON><PERSON><PERSON><PERSON>, s: String?, c: Completion?) -> UIView", "type": "method"}, {"path": "/documentation/jdstatusbarnotification/notificationpresenter/zld()", "title": "func zld()", "type": "method"}, {"path": "/documentation/jdstatusbarnotification/notificationpresenter/zld(a:)", "title": "func zld(a: <PERSON><PERSON>)", "type": "method"}, {"path": "/documentation/jdstatusbarnotification/notificationpresenter/zld(c:)", "title": "func zld(c: <PERSON><PERSON><PERSON>?)", "type": "method"}, {"path": "/documentation/jdstatusbarnotification/notificationpresenter/zld(d:)", "title": "func zld(d: Double)", "type": "method"}, {"path": "/documentation/jdstatusbarnotification/notificationpresenter/zld(d:c:)", "title": "func zld(d: Double, c: Completion?)", "type": "method"}, {"path": "/documentation/jdstatusbarnotification/notificationpresenter/zld(a:d:c:)", "title": "func zld(a: <PERSON><PERSON>, d: Double, c: Completion?)", "type": "method"}, {"path": "/documentation/jdstatusbarnotification/notificationpresenter/zlas(n:p:)", "title": "func zlas(n: String, p: PrepareStyleClosure) -> String", "type": "method"}], "path": "/documentation/jdstatusbarnotification/notificationpresenter", "title": "NotificationPresenter", "type": "class"}, {"title": "Notification Style", "type": "groupMarker"}, {"children": [{"title": "Notification Bar Behavior", "type": "groupMarker"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationstyle/animationtype", "title": "var animationType: StatusBarNotificationAnimationType", "type": "property"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationstyle/candismissduringuserinteraction", "title": "var canDismissDuringUserInteraction: Bool", "type": "property"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationstyle/canswipetodismiss", "title": "var canSwipeToDismiss: Bool", "type": "property"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationstyle/cantaptohold", "title": "var canTapToHold: Bool", "type": "property"}, {"title": "Styling the text", "type": "groupMarker"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationstyle/textstyle", "title": "var textStyle: StatusBarNotificationTextStyle", "type": "property"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationstyle/subtitlestyle", "title": "var subtitleStyle: StatusBarNotificationTextStyle", "type": "property"}, {"title": "Styling the background", "type": "groupMarker"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationstyle/backgroundstyle", "title": "var backgroundStyle: StatusBarNotificationBackgroundStyle", "type": "property"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationstyle/systemstatusbarstyle", "title": "var systemStatusBarStyle: StatusBarNotificationSystemBarStyle", "type": "property"}, {"title": "Styling supplementary views", "type": "groupMarker"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationstyle/progressbarstyle", "title": "var progressBarStyle: StatusBarNotificationProgressBarStyle", "type": "property"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationstyle/leftviewstyle", "title": "var leftViewStyle: StatusBarNotificationLeftViewStyle", "type": "property"}, {"title": "Style Enumerations", "type": "groupMarker"}, {"children": [{"title": "Enumeration Cases", "type": "groupMarker"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationanimationtype/bounce", "title": "case bounce", "type": "case"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationanimationtype/fade", "title": "case fade", "type": "case"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationanimationtype/move", "title": "case move", "type": "case"}, {"title": "Initializers", "type": "groupMarker"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationanimationtype/init(rawvalue:)", "title": "init?(rawValue: Int)", "type": "init"}, {"title": "Default Implementations", "type": "groupMarker"}, {"children": [{"title": "Operators", "type": "groupMarker"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationanimationtype/!=(_:_:)", "title": "static func != (Self, Self) -> <PERSON><PERSON>", "type": "op"}], "path": "/documentation/jdstatusbarnotification/statusbarnotificationanimationtype/equatable-implementations", "title": "Equatable Implementations", "type": "symbol"}, {"children": [{"title": "Instance Properties", "type": "groupMarker"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationanimationtype/hashvalue", "title": "var hashValue: Int", "type": "property"}, {"title": "Instance Methods", "type": "groupMarker"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationanimationtype/hash(into:)", "title": "func hash(into: inout <PERSON><PERSON>)", "type": "method"}], "path": "/documentation/jdstatusbarnotification/statusbarnotificationanimationtype/rawrepresentable-implementations", "title": "RawRepresentable Implementations", "type": "symbol"}], "path": "/documentation/jdstatusbarnotification/statusbarnotificationanimationtype", "title": "StatusBarNotificationAnimationType", "type": "enum"}, {"children": [{"title": "Enumeration Cases", "type": "groupMarker"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundtype/fullwidth", "title": "case fullWidth", "type": "case"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundtype/pill", "title": "case pill", "type": "case"}, {"title": "Initializers", "type": "groupMarker"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundtype/init(rawvalue:)", "title": "init?(rawValue: Int)", "type": "init"}, {"title": "Default Implementations", "type": "groupMarker"}, {"children": [{"title": "Operators", "type": "groupMarker"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundtype/!=(_:_:)", "title": "static func != (Self, Self) -> <PERSON><PERSON>", "type": "op"}], "path": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundtype/equatable-implementations", "title": "Equatable Implementations", "type": "symbol"}, {"children": [{"title": "Instance Properties", "type": "groupMarker"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundtype/hashvalue", "title": "var hashValue: Int", "type": "property"}, {"title": "Instance Methods", "type": "groupMarker"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundtype/hash(into:)", "title": "func hash(into: inout <PERSON><PERSON>)", "type": "method"}], "path": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundtype/rawrepresentable-implementations", "title": "RawRepresentable Implementations", "type": "symbol"}], "path": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundtype", "title": "StatusBarNotificationBackgroundType", "type": "enum"}, {"children": [{"title": "Enumeration Cases", "type": "groupMarker"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationsystembarstyle/darkcontent", "title": "case darkContent", "type": "case"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationsystembarstyle/defaultstyle", "title": "case defaultStyle", "type": "case"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationsystembarstyle/lightcontent", "title": "case lightContent", "type": "case"}, {"title": "Initializers", "type": "groupMarker"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationsystembarstyle/init(rawvalue:)", "title": "init?(rawValue: Int)", "type": "init"}, {"title": "Default Implementations", "type": "groupMarker"}, {"children": [{"title": "Operators", "type": "groupMarker"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationsystembarstyle/!=(_:_:)", "title": "static func != (Self, Self) -> <PERSON><PERSON>", "type": "op"}], "path": "/documentation/jdstatusbarnotification/statusbarnotificationsystembarstyle/equatable-implementations", "title": "Equatable Implementations", "type": "symbol"}, {"children": [{"title": "Instance Properties", "type": "groupMarker"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationsystembarstyle/hashvalue", "title": "var hashValue: Int", "type": "property"}, {"title": "Instance Methods", "type": "groupMarker"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationsystembarstyle/hash(into:)", "title": "func hash(into: inout <PERSON><PERSON>)", "type": "method"}], "path": "/documentation/jdstatusbarnotification/statusbarnotificationsystembarstyle/rawrepresentable-implementations", "title": "RawRepresentable Implementations", "type": "symbol"}], "path": "/documentation/jdstatusbarnotification/statusbarnotificationsystembarstyle", "title": "StatusBarNotificationSystemBarStyle", "type": "enum"}, {"children": [{"title": "Enumeration Cases", "type": "groupMarker"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewalignment/centerwithtext", "title": "case centerWithText", "type": "case"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewalignment/left", "title": "case left", "type": "case"}, {"title": "Initializers", "type": "groupMarker"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewalignment/init(rawvalue:)", "title": "init?(rawValue: Int)", "type": "init"}, {"title": "Default Implementations", "type": "groupMarker"}, {"children": [{"title": "Operators", "type": "groupMarker"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewalignment/!=(_:_:)", "title": "static func != (Self, Self) -> <PERSON><PERSON>", "type": "op"}], "path": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewalignment/equatable-implementations", "title": "Equatable Implementations", "type": "symbol"}, {"children": [{"title": "Instance Properties", "type": "groupMarker"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewalignment/hashvalue", "title": "var hashValue: Int", "type": "property"}, {"title": "Instance Methods", "type": "groupMarker"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewalignment/hash(into:)", "title": "func hash(into: inout <PERSON><PERSON>)", "type": "method"}], "path": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewalignment/rawrepresentable-implementations", "title": "RawRepresentable Implementations", "type": "symbol"}], "path": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewalignment", "title": "StatusBarNotificationLeftViewAlignment", "type": "enum"}, {"children": [{"title": "Enumeration Cases", "type": "groupMarker"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarposition/bottom", "title": "case bottom", "type": "case"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarposition/center", "title": "case center", "type": "case"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarposition/top", "title": "case top", "type": "case"}, {"title": "Initializers", "type": "groupMarker"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarposition/init(rawvalue:)", "title": "init?(rawValue: Int)", "type": "init"}, {"title": "Default Implementations", "type": "groupMarker"}, {"children": [{"title": "Operators", "type": "groupMarker"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarposition/!=(_:_:)", "title": "static func != (Self, Self) -> <PERSON><PERSON>", "type": "op"}], "path": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarposition/equatable-implementations", "title": "Equatable Implementations", "type": "symbol"}, {"children": [{"title": "Instance Properties", "type": "groupMarker"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarposition/hashvalue", "title": "var hashValue: Int", "type": "property"}, {"title": "Instance Methods", "type": "groupMarker"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarposition/hash(into:)", "title": "func hash(into: inout <PERSON><PERSON>)", "type": "method"}], "path": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarposition/rawrepresentable-implementations", "title": "RawRepresentable Implementations", "type": "symbol"}], "path": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarposition", "title": "StatusBarNotificationProgressBarPosition", "type": "enum"}], "path": "/documentation/jdstatusbarnotification/statusbarnotificationstyle", "title": "StatusBarNotificationStyle", "type": "class"}, {"children": [{"title": "Initializers", "type": "groupMarker"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationtextstyle/init()", "title": "init()", "type": "init"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationtextstyle/init(textcolor:font:)", "title": "init(textColor: UIColor?, font: UIFont?)", "type": "init"}, {"title": "Instance Properties", "type": "groupMarker"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationtextstyle/font", "title": "var font: UIFont?", "type": "property"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationtextstyle/shadowcolor", "title": "var shadowColor: UIColor?", "type": "property"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationtextstyle/shadowoffset", "title": "var shadowOffset: CGPoint", "type": "property"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationtextstyle/textcolor", "title": "var textColor: UIColor?", "type": "property"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationtextstyle/textoffsety", "title": "var textOffsetY: Double", "type": "property"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationtextstyle/textshadowcolor", "title": "var textShadowColor: UIColor?", "type": "property"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationtextstyle/textshadowoffset", "title": "var textShadowOffset: CGSize", "type": "property"}], "path": "/documentation/jdstatusbarnotification/statusbarnotificationtextstyle", "title": "StatusBarNotificationTextStyle", "type": "class"}, {"children": [{"title": "Instance Properties", "type": "groupMarker"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundstyle/backgroundcolor", "title": "var backgroundColor: UIColor?", "type": "property"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundstyle/backgroundtype", "title": "var backgroundType: StatusBarNotificationBackgroundType", "type": "property"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundstyle/pillstyle", "title": "var pillStyle: StatusBarNotificationPillStyle", "type": "property"}], "path": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundstyle", "title": "StatusBarNotificationBackgroundStyle", "type": "class"}, {"children": [{"title": "Instance Properties", "type": "groupMarker"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationpillstyle/bordercolor", "title": "var borderColor: UIColor?", "type": "property"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationpillstyle/borderwidth", "title": "var borderWidth: Double", "type": "property"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationpillstyle/height", "title": "var height: Double", "type": "property"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationpillstyle/minimumwidth", "title": "var minimumWidth: Double", "type": "property"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationpillstyle/shadowcolor", "title": "var shadowColor: UIColor?", "type": "property"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationpillstyle/shadowoffset", "title": "var shadowOffset: CGSize", "type": "property"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationpillstyle/shadowoffsetxy", "title": "var shadowOffsetXY: CGPoint", "type": "property"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationpillstyle/shadowradius", "title": "var shadowRadius: Double", "type": "property"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationpillstyle/topspacing", "title": "var topSpacing: Double", "type": "property"}], "path": "/documentation/jdstatusbarnotification/statusbarnotificationpillstyle", "title": "StatusBarNotificationPillStyle", "type": "class"}, {"children": [{"title": "Instance Properties", "type": "groupMarker"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarstyle/barcolor", "title": "var barColor: UIColor?", "type": "property"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarstyle/barheight", "title": "var barHeight: Double", "type": "property"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarstyle/cornerradius", "title": "var cornerRadius: Double", "type": "property"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarstyle/horizontalinsets", "title": "var horizontalInsets: Double", "type": "property"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarstyle/offsety", "title": "var offsetY: Double", "type": "property"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarstyle/position", "title": "var position: StatusBarNotificationProgressBarPosition", "type": "property"}], "path": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarstyle", "title": "StatusBarNotificationProgressBarStyle", "type": "class"}, {"children": [{"title": "Instance Properties", "type": "groupMarker"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewstyle/alignment", "title": "var alignment: StatusBarNotificationLeftViewAlignment", "type": "property"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewstyle/offset", "title": "var offset: CGPoint", "type": "property"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewstyle/offsetx", "title": "var offsetX: Double", "type": "property"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewstyle/spacing", "title": "var spacing: Double", "type": "property"}, {"path": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewstyle/tintcolor", "title": "var tintColor: UIColor?", "type": "property"}], "path": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewstyle", "title": "StatusBarNotificationLeftViewStyle", "type": "class"}, {"title": "Protocols", "type": "groupMarker"}, {"children": [{"title": "Instance Properties", "type": "groupMarker"}, {"path": "/documentation/jdstatusbarnotification/stylableview/style", "title": "var style: StatusBarNotificationStyle", "type": "property"}], "path": "/documentation/jdstatusbarnotification/stylableview", "title": "St<PERSON>bleView", "type": "protocol"}], "path": "/documentation/jdstatusbarnotification", "title": "JDStatusBarNotification", "type": "module"}]}, "schemaVersion": {"major": 0, "minor": 1, "patch": 1}}