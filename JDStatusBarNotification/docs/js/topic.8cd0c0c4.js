/*!
 * This source file is part of the Swift.org open source project
 * 
 * Copyright (c) 2021 Apple Inc. and the Swift project authors
 * Licensed under Apache License v2.0 with Runtime Library Exception
 * 
 * See https://swift.org/LICENSE.txt for license information
 * See https://swift.org/CONTRIBUTORS.txt for Swift project authors
 */
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["topic"],{"0466":function(e,t,n){},"0503":function(e,t,n){"use strict";n("e38e")},"0c11":function(e,t,n){},"0c14":function(e,t,n){},1006:function(e,t,n){"use strict";n("a95e")},"1dd5":function(e,t,n){"use strict";n("7b17")},"257c":function(e,t,n){"use strict";n("7748")},"2a05":function(e,t,n){},"2f9d":function(e,t,n){"use strict";n("525c")},3213:function(e,t,n){"use strict";n.r(t);var s=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[e.topicData?n(e.componentFor(e.topicData),e._b({key:e.topicKey,tag:"component",attrs:{hierarchy:e.hierarchy}},"component",e.propsFor(e.topicData),!1)):e._e()],1)},i=[],r=n("25a9"),o=n("a97e");const{BreakpointName:a}=o["a"].constants;var c,l,u={state:{linkableSections:[],breakpoint:a.large,references:{}},addLinkableSection(e){const t={...e,visibility:0};t.sectionNumber=this.state.linkableSections.length,this.state.linkableSections.push(t)},reset(){this.state.linkableSections=[],this.state.breakpoint=a.large,this.state.references={}},updateLinkableSection(e){this.state.linkableSections=this.state.linkableSections.map(t=>e.anchor===t.anchor?{...t,visibility:e.visibility}:t)},updateBreakpoint(e){this.state.breakpoint=e},setReferences(e){this.state.references=e}},d=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"article"},[e.isTargetIDE?e._e():n("NavigationBar",{attrs:{chapters:e.hierarchy.modules,technology:e.metadata.category,topic:e.heroTitle||"",rootReference:e.hierarchy.reference,identifierUrl:e.identifierUrl}}),n("main",{attrs:{id:"main",role:"main",tabindex:"0"}},[e._t("above-hero"),e._l(e.sections,(function(t,s){return n(e.componentFor(t),e._b({key:s,tag:"component"},"component",e.propsFor(t),!1))}))],2),n("PortalTarget",{attrs:{name:"modal-destination",multiple:""}})],1)},p=[],h=n("2b88"),m=n("e425"),f=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("NavBase",{attrs:{id:"nav","aria-label":e.technology,hasSolidBackground:""}},[n("template",{slot:"default"},[n("ReferenceUrlProvider",{attrs:{reference:e.rootReference},scopedSlots:e._u([{key:"default",fn:function(t){var s=t.urlWithParams;return n("NavTitleContainer",{attrs:{to:s}},[n("template",{slot:"default"},[e._v(e._s(e.technology))]),n("template",{slot:"subhead"},[e._v(e._s(e.$tc("tutorials.title",2)))])],2)}}])})],1),n("template",{slot:"after-title"},[n("div",{staticClass:"separator"})]),n("template",{slot:"tray"},[n("div",{staticClass:"mobile-dropdown-container"},[n("MobileDropdown",{attrs:{options:e.chapters,sections:e.optionsForSections,currentOption:e.currentSection?e.currentSection.title:""},on:{"select-section":e.onSelectSection}})],1),n("div",{staticClass:"dropdown-container"},[n("PrimaryDropdown",{staticClass:"primary-dropdown",attrs:{options:e.chapters,currentOption:e.topic}}),n("ChevronIcon",{staticClass:"icon-inline"}),e.currentSection?n("SecondaryDropdown",{staticClass:"secondary-dropdown",attrs:{options:e.optionsForSections,currentOption:e.currentSection.title,sectionTracker:e.sectionIndicatorText},on:{"select-section":e.onSelectSection}}):e._e()],1),e._t("tray",null,{siblings:e.chapters.length+e.optionsForSections.length})],2)],2)},v=[],b=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("SVGIcon",{staticClass:"chevron-icon",attrs:{viewBox:"0 0 14 14",themeId:"chevron"}},[n("path",{attrs:{d:"M3.22 1.184l0.325-0.38 7.235 6.201-7.235 6.19-0.325-0.38 6.792-5.811-6.792-5.82z"}})])},g=[],y=n("be08"),C={name:"ChevronIcon",components:{SVGIcon:y["a"]}},w=C,_=n("2877"),k=Object(_["a"])(w,b,g,!1,null,null,null),S=k.exports,x=n("d26a"),T=n("2f34"),I={name:"ReferenceUrlProvider",mixins:[T["a"]],props:{reference:{type:String,required:!0}},computed:{resolvedReference:({references:e,reference:t})=>e[t]||{},url:({resolvedReference:e})=>e.url,title:({resolvedReference:e})=>e.title},render(){return this.$scopedSlots.default({url:this.url,urlWithParams:Object(x["b"])(this.url,this.$route.query),title:this.title,reference:this.resolvedReference})}},A=I,$=Object(_["a"])(A,c,l,!1,null,null,null),O=$.exports,N=n("8a61"),P=n("cbcf"),q=n("653a"),D=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("NavMenuItems",{staticClass:"mobile-dropdown"},e._l(e.options,(function(t){return n("ReferenceUrlProvider",{key:t.reference,attrs:{reference:t.reference},scopedSlots:e._u([{key:"default",fn:function(s){var i=s.title;return n("NavMenuItemBase",{staticClass:"chapter-list",attrs:{role:"group"}},[n("p",{staticClass:"chapter-name"},[e._v(e._s(i))]),n("ul",{staticClass:"tutorial-list"},e._l(t.projects,(function(t){return n("ReferenceUrlProvider",{key:t.reference,attrs:{reference:t.reference},scopedSlots:e._u([{key:"default",fn:function(t){var s=t.url,i=t.urlWithParams,r=t.title;return n("li",{staticClass:"tutorial-list-item"},[n("router-link",{staticClass:"option tutorial",attrs:{to:i,value:r}},[e._v(" "+e._s(r)+" ")]),s===e.$route.path?n("ul",{staticClass:"section-list",attrs:{role:"listbox"}},e._l(e.sections,(function(t){return n("li",{key:t.title},[n("router-link",{class:e.classesFor(t),attrs:{to:{path:t.path,query:e.$route.query},value:t.title},nativeOn:{click:function(n){return e.onClick(t)}}},[e._v(" "+e._s(t.title)+" ")])],1)})),0):e._e()],1)}}],null,!0)})})),1)])}}],null,!0)})})),1)},j=[],R=n("863d"),M=n("9b30"),B={name:"MobileDropdown",components:{NavMenuItems:M["a"],NavMenuItemBase:R["a"],ReferenceUrlProvider:O},props:{options:{type:Array,required:!0},currentOption:{type:String,required:!0},sections:{type:Array,required:!1,default:()=>[]}},methods:{classesFor(e){return["option","section",{active:this.currentOption===e.title},this.depthClass(e)]},depthClass(e){const{depth:t=0}=e;return"depth"+t},onClick(e){this.$emit("select-section",e.path)}}},L=B,E=(n("e688"),Object(_["a"])(L,D,j,!1,null,"154acfbd",null)),F=E.exports,V=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("DropdownCustom",{staticClass:"tutorial-dropdown",attrs:{value:e.currentOption,"aria-label":e.$t("tutorials.nav.current",{thing:e.$t("sections.title")}),isSmall:""},scopedSlots:e._u([{key:"default",fn:function(t){var s=t.closeAndFocusToggler,i=t.contentClasses,r=t.navigateOverOptions,o=t.OptionClass,a=t.ActiveOptionClass;return[n("ul",{staticClass:"options",class:i,attrs:{role:"listbox",tabindex:"0"}},e._l(e.options,(function(t){return n("router-link",{key:t.title,attrs:{to:{path:t.path,query:e.$route.query},custom:""},scopedSlots:e._u([{key:"default",fn:function(i){var c,l=i.navigate;return[n("li",{class:[o,(c={},c[a]=e.currentOption===t.title,c)],attrs:{role:"option",value:t.title,"aria-selected":e.currentOption===t.title,"aria-current":e.ariaCurrent(t.title),tabindex:-1},on:{click:function(n){return e.setActive(t,l,s,n)},keydown:[function(n){return!n.type.indexOf("key")&&e._k(n.keyCode,"enter",13,n.key,"Enter")?null:e.setActive(t,l,s,n)},function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"esc",27,t.key,["Esc","Escape"])?null:s.apply(null,arguments)},function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"tab",9,t.key,"Tab")?null:s.apply(null,arguments)},function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"down",40,t.key,["Down","ArrowDown"])?null:(t.preventDefault(),r(t,1))},function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"up",38,t.key,["Up","ArrowUp"])?null:(t.preventDefault(),r(t,-1))}]}},[e._v(" "+e._s(t.title)+" ")])]}}],null,!0)})})),1)]}}])},[n("template",{slot:"toggle-post-content"},[n("span",{staticClass:"section-tracker"},[e._v(e._s(e.sectionTracker))])])],2)},U=[],H=function(){var e,t=this,n=t.$createElement,s=t._self._c||n;return s("BaseDropdown",{staticClass:"dropdown-custom",class:(e={},e[t.OpenedClass]=t.isOpen,e["dropdown-small"]=t.isSmall,e),attrs:{value:t.value},scopedSlots:t._u([{key:"dropdown",fn:function(e){var n=e.dropdownClasses;return[s("span",{staticClass:"visuallyhidden",attrs:{id:"DropdownLabel_"+t._uid}},[t._v(t._s(t.ariaLabel))]),s("button",{ref:"dropdownToggle",staticClass:"form-dropdown-toggle",class:n,attrs:{role:"button",id:"DropdownToggle_"+t._uid,"aria-labelledby":"DropdownLabel_"+t._uid+" DropdownToggle_"+t._uid,"aria-expanded":t.isOpen?"true":"false","aria-haspopup":"true"},on:{click:t.toggleDropdown,keydown:[function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:(e.preventDefault(),t.openDropdown.apply(null,arguments))},function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"esc",27,e.key,["Esc","Escape"])?null:t.closeAndFocusToggler.apply(null,arguments)},function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"down",40,e.key,["Down","ArrowDown"])?null:(e.preventDefault(),t.openDropdown.apply(null,arguments))},function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"up",38,e.key,["Up","ArrowUp"])?null:(e.preventDefault(),t.openDropdown.apply(null,arguments))}]}},[s("span",{staticClass:"form-dropdown-title"},[t._v(t._s(t.value))]),t._t("toggle-post-content")],2)]}}],null,!0)},[s("template",{slot:"eyebrow"},[t._t("eyebrow")],2),s("template",{slot:"after"},[t._t("default",null,null,{value:t.value,isOpen:t.isOpen,contentClasses:["form-dropdown-content",{"is-open":t.isOpen}],closeDropdown:t.closeDropdown,onChangeAction:t.onChangeAction,closeAndFocusToggler:t.closeAndFocusToggler,navigateOverOptions:t.navigateOverOptions,OptionClass:t.OptionClass,ActiveOptionClass:t.ActiveOptionClass})],2)],2)},z=[],G=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"form-element"},[e._t("dropdown",(function(){return[n("select",e._b({directives:[{name:"model",rawName:"v-model",value:e.modelValue,expression:"modelValue"}],class:e.dropdownClasses,on:{change:function(t){var n=Array.prototype.filter.call(t.target.options,(function(e){return e.selected})).map((function(e){var t="_value"in e?e._value:e.value;return t}));e.modelValue=t.target.multiple?n:n[0]}}},"select",e.$attrs,!1),[e._t("default")],2)]}),{dropdownClasses:e.dropdownClasses,value:e.value}),n("InlineChevronDownIcon",{staticClass:"form-icon",attrs:{"aria-hidden":"true"}}),e.$slots.eyebrow?n("span",{staticClass:"form-label",attrs:{"aria-hidden":"true"}},[e._t("eyebrow")],2):e._e(),e._t("after")],2)},W=[],Q=n("7948"),K={name:"BaseDropdown",inheritAttrs:!1,props:{value:{type:String,default:""}},components:{InlineChevronDownIcon:Q["a"]},computed:{modelValue:{get:({value:e})=>e,set(e){this.$emit("input",e)}},dropdownClasses({value:e}){return["form-dropdown",{"form-dropdown-selectnone":""===e,"no-eyebrow":!this.$slots.eyebrow}]}}},X=K,J=(n("ed71"),Object(_["a"])(X,G,W,!1,null,"998803d8",null)),Y=J.exports;const Z="is-open",ee="option",te="option-active";var ne={name:"DropdownCustom",components:{BaseDropdown:Y},constants:{OpenedClass:Z,OptionClass:ee,ActiveOptionClass:te},props:{value:{type:String,default:""},ariaLabel:{type:String,default:""},isSmall:{type:Boolean,default:!1}},data(){return{isOpen:!1,OpenedClass:Z,OptionClass:ee,ActiveOptionClass:te}},mounted(){document.addEventListener("click",this.closeOnLoseFocus)},beforeDestroy(){document.removeEventListener("click",this.closeOnLoseFocus)},methods:{onChangeAction(e){this.$emit("input",e)},toggleDropdown(){this.isOpen?this.closeDropdown():this.openDropdown()},async closeAndFocusToggler(){this.closeDropdown(),await this.$nextTick(),this.$refs.dropdownToggle.focus({preventScroll:!0})},closeDropdown(){this.isOpen=!1,this.$emit("close")},openDropdown(){this.isOpen=!0,this.$emit("open"),this.focusActiveLink()},closeOnLoseFocus(e){!this.$el.contains(e.target)&&this.isOpen&&this.closeDropdown()},navigateOverOptions({target:e},t){const n=this.$el.querySelectorAll("."+ee),s=Array.from(n),i=s.indexOf(e),r=s[i+t];r&&r.focus({preventScroll:!0})},async focusActiveLink(){const e=this.$el.querySelector("."+te);e&&(await this.$nextTick(),e.focus({preventScroll:!0}))}}},se=ne,ie=(n("e84c"),Object(_["a"])(se,H,z,!1,null,"12dd746a",null)),re=ie.exports,oe={name:"SecondaryDropdown",components:{DropdownCustom:re},props:{options:{type:Array,required:!0},currentOption:{type:String,required:!0},sectionTracker:{type:String,required:!1}},methods:{ariaCurrent(e){return this.currentOption===e&&"section"},setActive(e,t,n,s){t(s),this.$emit("select-section",e.path),n()}}},ae=oe,ce=(n("7f7b"),Object(_["a"])(ae,V,U,!1,null,"554d62b6",null)),le=ce.exports,ue=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("DropdownCustom",{staticClass:"tutorial-dropdown",attrs:{value:e.currentOption,"aria-label":e.$t("tutorials.nav.current",{thing:e.$tc("tutorials.title",1)}),isSmall:""},scopedSlots:e._u([{key:"default",fn:function(t){var s=t.closeAndFocusToggler,i=t.contentClasses,r=t.closeDropdown,o=t.navigateOverOptions,a=t.OptionClass,c=t.ActiveOptionClass;return[n("ul",{staticClass:"options",class:i,attrs:{tabindex:"0"}},e._l(e.options,(function(t){return n("ReferenceUrlProvider",{key:t.reference,attrs:{reference:t.reference},scopedSlots:e._u([{key:"default",fn:function(i){var l=i.title;return n("li",{staticClass:"chapter-list",attrs:{role:"group"}},[n("p",{staticClass:"chapter-name"},[e._v(e._s(l))]),n("ul",{attrs:{role:"listbox"}},e._l(t.projects,(function(t){return n("ReferenceUrlProvider",{key:t.reference,attrs:{reference:t.reference},scopedSlots:e._u([{key:"default",fn:function(t){var i=t.urlWithParams,l=t.title;return[n("router-link",{attrs:{to:i,custom:""},scopedSlots:e._u([{key:"default",fn:function(t){var i,u=t.navigate,d=t.isActive;return[n("li",{class:(i={},i[a]=!0,i[c]=d,i),attrs:{role:"option",value:l,"aria-selected":d,"aria-current":!!d&&"tutorial",tabindex:-1},on:{click:function(t){return e.setActive(u,r,t)},keydown:[function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.setActive(u,r,t)},function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"esc",27,t.key,["Esc","Escape"])?null:s.apply(null,arguments)},function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"tab",9,t.key,"Tab")?null:s.apply(null,arguments)},function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"down",40,t.key,["Down","ArrowDown"])?null:(t.preventDefault(),o(t,1))},function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"up",38,t.key,["Up","ArrowUp"])?null:(t.preventDefault(),o(t,-1))}]}},[e._v(" "+e._s(l)+" ")])]}}],null,!0)})]}}],null,!0)})})),1)])}}],null,!0)})})),1)]}}])})},de=[],pe={name:"PrimaryDropdown",components:{DropdownCustom:re,ReferenceUrlProvider:O},props:{options:{type:Array,required:!0},currentOption:{type:String,required:!0}},methods:{setActive(e,t,n){e(n),t()}}},he=pe,me=(n("9938"),Object(_["a"])(he,ue,de,!1,null,"4bddb6e6",null)),fe=me.exports;const ve={title:"Introduction",url:"#introduction",reference:"introduction",sectionNumber:0,depth:0};var be={name:"NavigationBar",components:{NavTitleContainer:q["a"],NavBase:P["a"],ReferenceUrlProvider:O,PrimaryDropdown:fe,SecondaryDropdown:le,MobileDropdown:F,ChevronIcon:S},mixins:[N["a"],T["a"]],props:{chapters:{type:Array,required:!0},technology:{type:String,required:!0},topic:{type:String,required:!0},rootReference:{type:String,required:!0},identifierUrl:{type:String,required:!0}},data(){return{currentSection:ve,tutorialState:this.store.state}},watch:{pageSectionWithHighestVisibility(e){e&&(this.currentSection=e)}},computed:{currentProject(){return this.chapters.reduce((e,{projects:t})=>e.concat(t),[]).find(e=>e.reference===this.identifierUrl)},pageSections(){if(!this.currentProject)return[];const e=[ve].concat(this.currentProject.sections);return this.tutorialState.linkableSections.map((t,n)=>{const s=e[n],i=this.references[s.reference],{url:r,title:o}=i||s;return{...t,title:o,path:r}})},optionsForSections(){return this.pageSections.map(({depth:e,path:t,title:n})=>({depth:e,path:t,title:n}))},pageSectionWithHighestVisibility(){return[...this.pageSections].sort((e,t)=>t.visibility-e.visibility).find(e=>e.visibility>0)},sectionIndicatorText(){const e=this.tutorialState.linkableSections.length-1,{sectionNumber:t}=this.currentSection||{};if(0!==t)return this.$t("tutorials.section-of",{number:t,total:e})}},methods:{onSelectSection(e){const t=e.split("#")[1];this.handleFocusAndScroll(t)}}},ge=be,ye=(n("ea88"),Object(_["a"])(ge,f,v,!1,null,"717bc942",null)),Ce=ye.exports,we=n("bf08"),_e=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"body"},[n("BodyContent",{attrs:{content:e.content}})],1)},ke=[],Se=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("article",{staticClass:"body-content"},e._l(e.content,(function(t,s){return n(e.componentFor(t),e._b({key:s,tag:"component",staticClass:"layout"},"component",e.propsFor(t),!1))})),1)},xe=[],Te=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"columns",class:e.classes},[e._l(e.columns,(function(t,s){return[n("Asset",{key:t.media,attrs:{identifier:t.media,videoAutoplays:!1}}),t.content?n("ContentNode",{key:s,attrs:{content:t.content}}):e._e()]}))],2)},Ie=[],Ae=n("80e4"),$e=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("BaseContentNode",{attrs:{content:e.articleContent}})},Oe=[],Ne=n("5677"),Pe={name:"ContentNode",components:{BaseContentNode:Ne["default"]},props:Ne["default"].props,computed:{articleContent(){return this.map(e=>{switch(e.type){case Ne["default"].BlockType.codeListing:return{...e,showLineNumbers:!0};case Ne["default"].BlockType.heading:{const{anchor:t,...n}=e;return n}default:return e}})}},methods:Ne["default"].methods,BlockType:Ne["default"].BlockType,InlineType:Ne["default"].InlineType},qe=Pe,De=(n("cb8d"),Object(_["a"])(qe,$e,Oe,!1,null,"3cfe1c35",null)),je=De.exports,Re={name:"Columns",components:{Asset:Ae["a"],ContentNode:je},props:{columns:{type:Array,required:!0}},computed:{classes(){return{"cols-2":2===this.columns.length,"cols-3":3===this.columns.length}}}},Me=Re,Be=(n("e9b0"),Object(_["a"])(Me,Te,Ie,!1,null,"30edf911",null)),Le=Be.exports,Ee=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"content-and-media",class:e.classes},[n("ContentNode",{attrs:{content:e.content}}),n("Asset",{attrs:{identifier:e.media}})],1)},Fe=[];const Ve={leading:"leading",trailing:"trailing"};var Ue={name:"ContentAndMedia",components:{Asset:Ae["a"],ContentNode:je},props:{content:je.props.content,media:Ae["a"].props.identifier,mediaPosition:{type:String,default:()=>Ve.trailing,validator:e=>Object.prototype.hasOwnProperty.call(Ve,e)}},computed:{classes(){return{"media-leading":this.mediaPosition===Ve.leading,"media-trailing":this.mediaPosition===Ve.trailing}}},MediaPosition:Ve},He=Ue,ze=(n("1006"),Object(_["a"])(He,Ee,Fe,!1,null,"3fa44f9e",null)),Ge=ze.exports,We=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"full-width"},e._l(e.groups,(function(t,s){return n(e.componentFor(t),e._b({key:s,tag:"component",staticClass:"group"},"component",e.propsFor(t),!1),[n("ContentNode",{attrs:{content:t.content}})],1)})),1)},Qe=[],Ke=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n(e.tag,{tag:"component",attrs:{id:e.anchor}},[e._t("default")],2)},Xe=[],Je=n("72e7"),Ye={name:"LinkableElement",mixins:[Je["a"]],inject:{navigationBarHeight:{default(){}},store:{default(){return{addLinkableSection(){},updateLinkableSection(){}}}}},props:{anchor:{type:String,required:!0},depth:{type:Number,default:()=>0},tag:{type:String,default:()=>"div"},title:{type:String,required:!0}},computed:{intersectionRootMargin(){const e=this.navigationBarHeight?`-${this.navigationBarHeight}px`:"0%";return e+" 0% -50% 0%"}},created(){this.store.addLinkableSection({anchor:this.anchor,depth:this.depth,title:this.title,visibility:0})},methods:{onIntersect(e){const t=Math.min(1,e.intersectionRatio);this.store.updateLinkableSection({anchor:this.anchor,depth:this.depth,title:this.title,visibility:t})}}},Ze=Ye,et=Object(_["a"])(Ze,Ke,Xe,!1,null,null,null),tt=et.exports;const{BlockType:nt}=je;var st={name:"FullWidth",components:{ContentNode:je,LinkableElement:tt},props:je.props,computed:{groups:({content:e})=>e.reduce((e,t)=>0===e.length||t.type===nt.heading?[...e,{heading:t.type===nt.heading?t:null,content:[t]}]:[...e.slice(0,e.length-1),{heading:e[e.length-1].heading,content:e[e.length-1].content.concat(t)}],[])},methods:{componentFor(e){return e.heading?tt:"div"},depthFor(e){switch(e.level){case 1:case 2:return 0;default:return 1}},propsFor(e){return e.heading?{anchor:e.heading.anchor,depth:this.depthFor(e.heading),title:e.heading.text}:{}}}},it=st,rt=(n("aece"),Object(_["a"])(it,We,Qe,!1,null,"1f2be54b",null)),ot=rt.exports;const at={columns:"columns",contentAndMedia:"contentAndMedia",fullWidth:"fullWidth"};var ct={name:"BodyContent",props:{content:{type:Array,required:!0,validator:e=>e.every(({kind:e})=>Object.prototype.hasOwnProperty.call(at,e))}},methods:{componentFor(e){return{[at.columns]:Le,[at.contentAndMedia]:Ge,[at.fullWidth]:ot}[e.kind]},propsFor(e){const{content:t,kind:n,media:s,mediaPosition:i}=e;return{[at.columns]:{columns:t},[at.contentAndMedia]:{content:t,media:s,mediaPosition:i},[at.fullWidth]:{content:t}}[n]}},LayoutKind:at},lt=ct,ut=(n("1dd5"),Object(_["a"])(lt,Se,xe,!1,null,"4d5a806e",null)),dt=ut.exports,pt={name:"Body",components:{BodyContent:dt},props:dt.props},ht=pt,mt=(n("5237"),Object(_["a"])(ht,_e,ke,!1,null,"6499e2f2",null)),ft=mt.exports,vt=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("TutorialCTA",e._b({},"TutorialCTA",e.$props,!1))},bt=[],gt=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("BaseCTA",e._b({attrs:{label:e.$t("tutorials.next")}},"BaseCTA",e.baseProps,!1))},yt=[],Ct=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"call-to-action"},[n("Row",[n("LeftColumn",[n("span",{staticClass:"label"},[e._v(e._s(e.label))]),n("h2",[e._v(" "+e._s(e.title)+" ")]),e.abstract?n("ContentNode",{staticClass:"description",attrs:{content:[e.abstractParagraph]}}):e._e(),e.action?n("Button",{attrs:{action:e.action}}):e._e()],1),n("RightColumn",{staticClass:"right-column"},[e.media?n("Asset",{staticClass:"media",attrs:{identifier:e.media}}):e._e()],1)],1)],1)},wt=[],_t=n("0f00"),kt=n("620a"),St=n("c081"),xt={name:"CallToAction",components:{Asset:Ae["a"],Button:St["a"],ContentNode:Ne["default"],LeftColumn:{render(e){return e(kt["a"],{props:{span:{large:5,small:12}}},this.$slots.default)}},RightColumn:{render(e){return e(kt["a"],{props:{span:{large:6,small:12}}},this.$slots.default)}},Row:_t["a"]},props:{title:{type:String,required:!0},label:{type:String,required:!0},abstract:{type:Array,required:!1},action:{type:Object,required:!1},media:{type:String,required:!1}},computed:{abstractParagraph(){return{type:"paragraph",inlineContent:this.abstract}}}},Tt=xt,It=(n("c7dd"),Object(_["a"])(Tt,Ct,wt,!1,null,"042a0474",null)),At=It.exports,$t={name:"CallToAction",components:{BaseCTA:At},computed:{baseProps(){return{title:this.title,abstract:this.abstract,action:this.action,media:this.media}}},props:{title:{type:String,required:!0},abstract:{type:Array,required:!1},action:{type:Object,required:!1},media:{type:String,required:!1}}},Ot=$t,Nt=Object(_["a"])(Ot,gt,yt,!1,null,null,null),Pt=Nt.exports,qt={name:"CallToAction",components:{TutorialCTA:Pt},props:Pt.props},Dt=qt,jt=(n("3e1b"),Object(_["a"])(Dt,vt,bt,!1,null,"426a965c",null)),Rt=jt.exports,Mt=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("TutorialHero",e._b({},"TutorialHero",e.$props,!1))},Bt=[],Lt=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("LinkableSection",{staticClass:"tutorial-hero",attrs:{anchor:"introduction",title:e.sectionTitle}},[n("div",{staticClass:"hero dark"},[e.backgroundImageUrl?n("div",{staticClass:"bg",style:e.bgStyle}):e._e(),e._t("above-title"),n("Row",[n("Column",[n("Headline",{attrs:{level:1}},[e.chapter?n("template",{slot:"eyebrow"},[e._v(e._s(e.chapter))]):e._e(),e._v(" "+e._s(e.title)+" ")],2),e.content||e.video?n("div",{staticClass:"intro"},[e.content?n("ContentNode",{attrs:{content:e.content}}):e._e(),e.video?[n("p",[n("a",{staticClass:"call-to-action",attrs:{href:"#"},on:{click:function(t){return t.preventDefault(),e.toggleCallToActionModal.apply(null,arguments)}}},[e._v(" Watch intro video "),n("PlayIcon",{staticClass:"cta-icon icon-inline"})],1)]),n("GenericModal",{attrs:{visible:e.callToActionModalVisible,isFullscreen:"",theme:"dark"},on:{"update:visible":function(t){e.callToActionModalVisible=t}}},[n("Asset",{directives:[{name:"show",rawName:"v-show",value:e.callToActionModalVisible,expression:"callToActionModalVisible"}],ref:"asset",staticClass:"video-asset",attrs:{identifier:e.video},on:{videoEnded:e.handleVideoEnd}})],1)]:e._e()],2):e._e(),n("Metadata",{staticClass:"metadata",attrs:{projectFilesUrl:e.projectFilesUrl,estimatedTimeInMinutes:e.estimatedTimeInMinutes,xcodeRequirement:e.xcodeRequirementData}})],1)],1)],2)])},Et=[],Ft=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"headline"},[e.$slots.eyebrow?n("span",{staticClass:"eyebrow"},[e._t("eyebrow")],2):e._e(),n("Heading",{staticClass:"heading",attrs:{level:e.level}},[e._t("default")],2)],1)},Vt=[];const Ut=1,Ht=6,zt={type:Number,required:!0,validator:e=>e>=Ut&&e<=Ht},Gt={name:"Heading",render:function(e){return e("h"+this.level,this.$slots.default)},props:{level:zt}};var Wt={name:"Headline",components:{Heading:Gt},props:{level:zt}},Qt=Wt,Kt=(n("3976"),Object(_["a"])(Qt,Ft,Vt,!1,null,"d46a1474",null)),Xt=Kt.exports,Jt=n("c161"),Yt=n("c4dd"),Zt=n("748c"),en=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"metadata"},[e.estimatedTimeInMinutes?n("div",{staticClass:"item",attrs:{"aria-label":"\n      "+e.$tc("tutorials.time.minutes.full",e.estimatedTimeInMinutes,{count:e.estimatedTimeInMinutes})+"\n      "+e.$t("tutorials.estimated-time")+"\n    "}},[n("div",{staticClass:"content",attrs:{"aria-hidden":"true"}},[n("i18n",{staticClass:"duration",attrs:{path:"tutorials.time.format",tag:"div"},scopedSlots:e._u([{key:"number",fn:function(){return[e._v(" "+e._s(e.estimatedTimeInMinutes)+" ")]},proxy:!0},{key:"minutes",fn:function(){return[n("div",{staticClass:"minutes"},[e._v(e._s(e.$tc("tutorials.time.minutes.short",e.estimatedTimeInMinutes))+" ")])]},proxy:!0}],null,!1,3313752798)})],1),n("div",{staticClass:"bottom",attrs:{"aria-hidden":"true"}},[e._v(e._s(e.$t("tutorials.estimated-time")))])]):e._e(),e.projectFilesUrl?n("div",{staticClass:"item"},[n("DownloadIcon",{staticClass:"item-large-icon icon-inline"}),n("div",{staticClass:"content bottom"},[n("a",{staticClass:"content-link project-download",attrs:{href:e.projectFilesUrl}},[e._v(" "+e._s(e.$t("tutorials.project-files"))+" "),n("InlineDownloadIcon",{staticClass:"small-icon icon-inline"})],1)])],1):e._e(),e.xcodeRequirement?n("div",{staticClass:"item"},[n("XcodeIcon",{staticClass:"item-large-icon icon-inline"}),n("div",{staticClass:"content bottom"},[e.isTargetIDE?n("span",[e._v(e._s(e.xcodeRequirement.title))]):n("a",{staticClass:"content-link",attrs:{href:e.xcodeRequirement.url}},[e._v(" "+e._s(e.xcodeRequirement.title)+" "),n("InlineChevronRightIcon",{staticClass:"icon-inline small-icon xcode-icon"})],1)])],1):e._e()])},tn=[],nn=n("de60"),sn=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("SVGIcon",{staticClass:"xcode-icon",attrs:{viewBox:"0 0 14 14",themeId:"xcode"}},[n("path",{attrs:{d:"M2.668 4.452l-1.338-2.229 0.891-0.891 2.229 1.338 1.338 2.228 3.667 3.666 0.194-0.194 2.933 2.933c0.13 0.155 0.209 0.356 0.209 0.576 0 0.497-0.403 0.9-0.9 0.9-0.22 0-0.421-0.079-0.577-0.209l0.001 0.001-2.934-2.933 0.181-0.181-3.666-3.666z"}}),n("path",{attrs:{d:"M11.824 1.277l-0.908 0.908c-0.091 0.091-0.147 0.216-0.147 0.354 0 0.106 0.033 0.205 0.090 0.286l-0.001-0.002 0.058 0.069 0.185 0.185c0.090 0.090 0.215 0.146 0.353 0.146 0.107 0 0.205-0.033 0.286-0.090l-0.002 0.001 0.069-0.057 0.909-0.908c0.118 0.24 0.187 0.522 0.187 0.82 0 1.045-0.848 1.893-1.893 1.893-0.296 0-0.577-0.068-0.826-0.189l0.011 0.005-5.5 5.5c0.116 0.238 0.184 0.518 0.184 0.813 0 1.045-0.848 1.893-1.893 1.893-0.296 0-0.576-0.068-0.826-0.189l0.011 0.005 0.908-0.909c0.090-0.090 0.146-0.215 0.146-0.353 0-0.107-0.033-0.205-0.090-0.286l0.001 0.002-0.057-0.069-0.185-0.185c-0.091-0.091-0.216-0.147-0.354-0.147-0.106 0-0.205 0.033-0.286 0.090l0.002-0.001-0.069 0.058-0.908 0.908c-0.116-0.238-0.184-0.518-0.184-0.813 0-1.045 0.847-1.892 1.892-1.892 0.293 0 0.571 0.067 0.819 0.186l-0.011-0.005 5.5-5.5c-0.116-0.238-0.184-0.519-0.184-0.815 0-1.045 0.847-1.892 1.892-1.892 0.296 0 0.577 0.068 0.827 0.19l-0.011-0.005z"}})])},rn=[],on={name:"XcodeIcon",components:{SVGIcon:y["a"]}},an=on,cn=Object(_["a"])(an,sn,rn,!1,null,null,null),ln=cn.exports,un=n("34b0"),dn=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("SVGIcon",{staticClass:"inline-download-icon",attrs:{viewBox:"0 0 14 14",themeId:"inline-download"}},[n("path",{attrs:{d:"M7 0.5c3.59 0 6.5 2.91 6.5 6.5s-2.91 6.5-6.5 6.5c-3.59 0-6.5-2.91-6.5-6.5s2.91-6.5 6.5-6.5zM7 1.5c-3.038 0-5.5 2.462-5.5 5.5s2.462 5.5 5.5 5.5c3.038 0 5.5-2.462 5.5-5.5s-2.462-5.5-5.5-5.5z"}}),n("path",{attrs:{d:"M7.51 2.964l-0.001 5.431 1.308-2.041 0.842 0.539-2.664 4.162-2.633-4.164 0.845-0.534 1.303 2.059 0.001-5.452z"}})])},pn=[],hn={name:"InlineDownloadIcon",components:{SVGIcon:y["a"]}},mn=hn,fn=Object(_["a"])(mn,dn,pn,!1,null,null,null),vn=fn.exports,bn={name:"HeroMetadata",components:{InlineDownloadIcon:vn,InlineChevronRightIcon:un["a"],DownloadIcon:nn["a"],XcodeIcon:ln},inject:["isTargetIDE"],props:{projectFilesUrl:{type:String},estimatedTimeInMinutes:{type:Number},xcodeRequirement:{type:Object,required:!1}}},gn=bn,yn=(n("33ff"),Object(_["a"])(gn,en,tn,!1,null,"94ff76c0",null)),Cn=yn.exports,wn={name:"Hero",components:{PlayIcon:Yt["a"],GenericModal:Jt["a"],Column:{render(e){return e(kt["a"],{props:{span:{large:7,medium:9,small:12}}},this.$slots.default)}},ContentNode:Ne["default"],Headline:Xt,Metadata:Cn,Row:_t["a"],Asset:Ae["a"],LinkableSection:tt},mixins:[T["a"]],props:{title:{type:String,required:!0},chapter:{type:String},content:{type:Array},projectFiles:{type:String},estimatedTimeInMinutes:{type:Number},xcodeRequirement:{type:String,required:!1},video:{type:String},backgroundImage:{type:String}},computed:{backgroundImageUrl(){const e=this.references[this.backgroundImage]||{},{variants:t=[]}=e,n=t.find(e=>e.traits.includes("light"));return Object(Zt["c"])((n||{}).url)},projectFilesUrl(){return this.projectFiles?Object(Zt["c"])(this.references[this.projectFiles].url):null},bgStyle(){return{backgroundImage:Object(Zt["f"])(this.backgroundImageUrl)}},xcodeRequirementData(){return this.references[this.xcodeRequirement]},sectionTitle(){return"Introduction"}},data(){return{callToActionModalVisible:!1}},methods:{async toggleCallToActionModal(){this.callToActionModalVisible=!0,await this.$nextTick();const e=this.$refs.asset.$el.querySelector("video");if(e)try{await e.play(),e.muted=!1}catch(t){}},handleVideoEnd(){this.callToActionModalVisible=!1}}},_n=wn,kn=(n("7fae"),Object(_["a"])(_n,Lt,Et,!1,null,"55543c5a",null)),Sn=kn.exports,xn={name:"Hero",components:{TutorialHero:Sn},props:Sn.props},Tn=xn,In=(n("2f9d"),Object(_["a"])(Tn,Mt,Bt,!1,null,"35a9482f",null)),An=In.exports,$n=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("TutorialAssessments",e._b({},"TutorialAssessments",e.$props,!1),[n("p",{attrs:{slot:"success"},slot:"success"},[e._v("Great job, you've answered all the questions for this article.")])])},On=[],Nn=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("LinkableSection",{staticClass:"assessments-wrapper",attrs:{anchor:e.anchor,title:e.title}},[n("Row",{ref:"assessments",staticClass:"assessments"},[n("MainColumn",[n("Row",{staticClass:"banner"},[n("HeaderColumn",[n("h2",{staticClass:"title"},[e._v(e._s(e.title))])])],1),e.completed?n("div",{staticClass:"success"},[e._t("success",(function(){return[n("p",[e._v(e._s(e.SuccessMessage))])]}))],2):n("div",[n("Progress",e._b({ref:"progress"},"Progress",e.progress,!1)),n("Quiz",{key:e.activeIndex,attrs:{choices:e.activeAssessment.choices,content:e.activeAssessment.content,isLast:e.isLast,title:e.activeAssessment.title},on:{submit:e.onSubmit,advance:e.onAdvance,"see-results":e.onSeeResults}})],1),n("div",{staticClass:"visuallyhidden",attrs:{"aria-live":"assertive"}},[e.completed?e._t("success",(function(){return[e._v(" "+e._s(e.SuccessMessage)+" ")]})):e._e()],2)],1)],1)],1)},Pn=[],qn=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("Row",[n("p",{staticClass:"title"},[e._v(e._s(e.$t("tutorials.question-of",{index:e.index,total:e.total})))])])},Dn=[],jn={name:"AssessmentsProgress",components:{Row:_t["a"]},props:{index:{type:Number,required:!0},total:{type:Number,required:!0}}},Rn=jn,Mn=(n("6866"),Object(_["a"])(Rn,qn,Dn,!1,null,"28135d78",null)),Bn=Mn.exports,Ln=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"quiz"},[n("ContentNode",{staticClass:"title",attrs:{content:e.title}}),e.content?n("ContentNode",{staticClass:"question-content",attrs:{content:e.content}}):e._e(),n("div",{staticClass:"choices"},[e._l(e.choices,(function(t,s){return n("label",{key:s,class:e.choiceClasses[s]},[n(e.getIconComponent(s),{tag:"component",staticClass:"choice-icon"}),n("input",{directives:[{name:"model",rawName:"v-model",value:e.selectedIndex,expression:"selectedIndex"}],attrs:{type:"radio",name:"assessment"},domProps:{value:s,checked:e._q(e.selectedIndex,s)},on:{change:function(t){e.selectedIndex=s}}}),n("ContentNode",{staticClass:"question",attrs:{content:t.content}}),e.userChoices[s].checked?[n("ContentNode",{staticClass:"answer",attrs:{content:t.justification}}),t.reaction?n("p",{staticClass:"answer"},[e._v(e._s(t.reaction))]):e._e()]:e._e()],2)})),n("div",{staticClass:"visuallyhidden",attrs:{"aria-live":"assertive"}},[e._v(" "+e._s(e.ariaLiveText)+" ")])],2),n("div",{staticClass:"controls"},[n("ButtonLink",{staticClass:"check",attrs:{disabled:null===e.selectedIndex||e.showNextQuestion},nativeOn:{click:function(t){return e.submit.apply(null,arguments)}}},[e._v(" "+e._s(e.$t("tutorials.submit"))+" ")]),e.isLast?n("ButtonLink",{staticClass:"results",attrs:{disabled:!e.showNextQuestion},nativeOn:{click:function(t){return e.seeResults.apply(null,arguments)}}},[e._v(" "+e._s(e.$t("tutorials.next"))+" ")]):n("ButtonLink",{staticClass:"next",attrs:{disabled:!e.showNextQuestion},nativeOn:{click:function(t){return e.advance.apply(null,arguments)}}},[e._v(" "+e._s(e.$t("tutorials.assessment.next-question"))+" ")])],1)],1)},En=[],Fn=n("76ab"),Vn=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("SVGIcon",{staticClass:"reset-circle-icon",attrs:{viewBox:"0 0 14 14",themeId:"reset-circle"}},[n("path",{attrs:{d:"M7 0.5c3.59 0 6.5 2.91 6.5 6.5s-2.91 6.5-6.5 6.5c-3.59 0-6.5-2.91-6.5-6.5v0c0-3.59 2.91-6.5 6.5-6.5v0zM7 1.5c-3.038 0-5.5 2.462-5.5 5.5s2.462 5.5 5.5 5.5c3.038 0 5.5-2.462 5.5-5.5v0c0-3.038-2.462-5.5-5.5-5.5v0z"}}),n("path",{attrs:{d:"M3.828 4.539l0.707-0.707 5.657 5.657-0.707 0.707-5.657-5.657z"}}),n("path",{attrs:{d:"M3.828 9.489l5.657-5.657 0.707 0.707-5.657 5.657-0.707-0.707z"}})])},Un=[],Hn={name:"ResetCircleIcon",components:{SVGIcon:y["a"]}},zn=Hn,Gn=Object(_["a"])(zn,Vn,Un,!1,null,null,null),Wn=Gn.exports,Qn=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("SVGIcon",{staticClass:"check-circle-icon",attrs:{viewBox:"0 0 14 14",themeId:"check-circle"}},[n("path",{attrs:{d:"M7 0.5c3.59 0 6.5 2.91 6.5 6.5s-2.91 6.5-6.5 6.5c-3.59 0-6.5-2.91-6.5-6.5v0c0-3.59 2.91-6.5 6.5-6.5v0zM7 1.5c-3.038 0-5.5 2.462-5.5 5.5s2.462 5.5 5.5 5.5c3.038 0 5.5-2.462 5.5-5.5v0c0-3.038-2.462-5.5-5.5-5.5v0z"}}),n("path",{attrs:{d:"M9.626 3.719l0.866 0.5-3.5 6.062-3.464-2 0.5-0.866 2.6 1.5z"}})])},Kn=[],Xn={name:"CheckCircleIcon",components:{SVGIcon:y["a"]}},Jn=Xn,Yn=Object(_["a"])(Jn,Qn,Kn,!1,null,null,null),Zn=Yn.exports,es={name:"Quiz",components:{CheckCircleIcon:Zn,ResetCircleIcon:Wn,ContentNode:Ne["default"],ButtonLink:Fn["a"]},props:{content:{type:Array,required:!1},choices:{type:Array,required:!0},isLast:{type:Boolean,default:!1},title:{type:Array,required:!0}},data(){return{userChoices:this.choices.map(()=>({checked:!1})),selectedIndex:null,checkedIndex:null}},computed:{correctChoices(){return this.choices.reduce((e,t,n)=>t.isCorrect?e.add(n):e,new Set)},choiceClasses(){return this.userChoices.map((e,t)=>({choice:!0,active:this.selectedIndex===t,disabled:e.checked||this.showNextQuestion,correct:e.checked&&this.choices[t].isCorrect,incorrect:e.checked&&!this.choices[t].isCorrect}))},showNextQuestion(){return Array.from(this.correctChoices).every(e=>this.userChoices[e].checked)},ariaLiveText(){if(null===this.checkedIndex)return"";const{isCorrect:e}=this.choices[this.checkedIndex];return`${this.$t("tutorials.assessment.answer-number-is",{index:this.checkedIndex+1})} ${e?this.$t("tutorials.assessment.correct"):this.$t("tutorials.assessment.incorrect")}\n      `}},methods:{getIconComponent(e){const t=this.userChoices[e];if(t&&t.checked)return this.choices[e].isCorrect?Zn:Wn},submit(){this.$set(this.userChoices,this.selectedIndex,{checked:!0}),this.checkedIndex=this.selectedIndex,this.$emit("submit")},advance(){this.$emit("advance")},seeResults(){this.$emit("see-results")}}},ts=es,ns=(n("0503"),Object(_["a"])(ts,Ln,En,!1,null,"19ed40e2",null)),ss=ns.exports;const is=12,rs="tutorials.assessment.success-message";var os={name:"Assessments",constants:{SuccessMessage:rs},components:{LinkableSection:tt,Quiz:ss,Progress:Bn,Row:_t["a"],HeaderColumn:{render(e){return e(kt["a"],{props:{isCentered:{large:!0},span:{large:10}}},this.$slots.default)}},MainColumn:{render(e){return e(kt["a"],{props:{isCentered:{large:!0},span:{large:10,medium:10,small:12}}},this.$slots.default)}}},props:{assessments:{type:Array,required:!0},anchor:{type:String,required:!0}},inject:["navigationBarHeight"],data(){return{activeIndex:0,completed:!1,SuccessMessage:this.$t(rs)}},computed:{activeAssessment(){return this.assessments[this.activeIndex]},isLast(){return this.activeIndex===this.assessments.length-1},progress(){return{index:this.activeIndex+1,total:this.assessments.length}},title(){return this.$t("tutorials.assessment.check-your-understanding")}},methods:{scrollTo(e,t=0){e.scrollIntoView(!0),window.scrollBy(0,-this.navigationBarHeight-t)},onSubmit(){this.$nextTick(()=>{this.scrollTo(this.$refs.progress.$el,is)})},onAdvance(){this.activeIndex+=1,this.$nextTick(()=>{this.scrollTo(this.$refs.progress.$el,is)})},onSeeResults(){this.completed=!0,this.$nextTick(()=>{this.scrollTo(this.$refs.assessments.$el,is)})}}},as=os,cs=(n("da20"),Object(_["a"])(as,Nn,Pn,!1,null,"65e3c02c",null)),ls=cs.exports,us={name:"Assessments",components:{TutorialAssessments:ls},props:ls.props},ds=us,ps=(n("f264"),Object(_["a"])(ds,$n,On,!1,null,"3c94366b",null)),hs=ps.exports;const ms={articleBody:"articleBody",callToAction:"callToAction",hero:"hero",assessments:"assessments"};var fs={name:"Article",components:{NavigationBar:Ce,PortalTarget:h["PortalTarget"]},mixins:[we["a"]],inject:{isTargetIDE:{default:!1},store:{default(){return{reset(){},setReferences(){}}}}},props:{hierarchy:{type:Object,required:!0},metadata:{type:Object,required:!0},references:{type:Object,required:!0},sections:{type:Array,required:!0,validator:e=>e.every(({kind:e})=>Object.prototype.hasOwnProperty.call(ms,e))},identifierUrl:{type:String,required:!0}},computed:{heroSection(){return this.sections.find(this.isHero)},heroTitle(){return(this.heroSection||{}).title},pageTitle(){return this.heroTitle?`${this.heroTitle} — ${this.metadata.category} Tutorials`:void 0},pageDescription:({heroSection:e,extractFirstParagraphText:t})=>e?t(e.content):null},methods:{componentFor(e){const{kind:t}=e;return{[ms.articleBody]:ft,[ms.callToAction]:Rt,[ms.hero]:An,[ms.assessments]:hs}[t]},isHero(e){return e.kind===ms.hero},propsFor(e){const{abstract:t,action:n,anchor:s,assessments:i,backgroundImage:r,chapter:o,content:a,estimatedTimeInMinutes:c,kind:l,media:u,projectFiles:d,title:p,video:h,xcodeRequirement:m}=e;return{[ms.articleBody]:{content:a},[ms.callToAction]:{abstract:t,action:n,media:u,title:p},[ms.hero]:{backgroundImage:r,chapter:o,content:a,estimatedTimeInMinutes:c,projectFiles:d,title:p,video:h,xcodeRequirement:m},[ms.assessments]:{anchor:s,assessments:i}}[l]}},created(){m["a"].setAvailableLocales(this.metadata.availableLocales),this.store.reset(),this.store.setReferences(this.references)},watch:{references(e){this.store.setReferences(e)},"metadata.availableLocales":function(e){m["a"].setAvailableLocales(e)}},SectionKind:ms},vs=fs,bs=(n("79ef"),Object(_["a"])(vs,d,p,!1,null,"7d4562ea",null)),gs=bs.exports,ys=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"tutorial"},[e.isTargetIDE?e._e():n("NavigationBar",{attrs:{technology:e.metadata.category,chapters:e.hierarchy.modules,topic:e.tutorialTitle||"",rootReference:e.hierarchy.reference,identifierUrl:e.identifierUrl}}),n("main",{attrs:{id:"main",role:"main",tabindex:"0"}},[e._l(e.sections,(function(e,t){return n("Section",{key:t,attrs:{section:e}})})),n("BreakpointEmitter",{on:{change:e.handleBreakpointChange}})],2),n("PortalTarget",{attrs:{name:"modal-destination",multiple:""}})],1)},Cs=[],ws=n("66c9"),_s=n("7689"),ks=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"sections"},e._l(e.tasks,(function(t,s){return n("Section",e._b({key:s,attrs:{id:t.anchor,sectionNumber:s+1,isRuntimePreviewVisible:e.isRuntimePreviewVisible},on:{"runtime-preview-toggle":e.onRuntimePreviewToggle}},"Section",t,!1))})),1)},Ss=[],xs=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("LinkableSection",{staticClass:"section",attrs:{anchor:e.anchor,title:e.introProps.title}},[n("Intro",e._b({},"Intro",e.introProps,!1)),e.stepsSection.length>0?n("Steps",{attrs:{content:e.stepsSection,isRuntimePreviewVisible:e.isRuntimePreviewVisible,sectionNumber:e.sectionNumber},on:{"runtime-preview-toggle":e.onRuntimePreviewToggle}}):e._e()],1)},Ts=[],Is=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"intro-container"},[n("Row",{class:["intro","intro-"+e.sectionNumber,{ide:e.isTargetIDE}]},[n("Column",{staticClass:"left"},[n("Headline",{attrs:{level:2}},[n("router-link",{attrs:{slot:"eyebrow",to:e.sectionLink},slot:"eyebrow"},[e._v(" "+e._s(e.$t("sections.title",{number:e.sectionNumber}))+" ")]),e._v(" "+e._s(e.title)+" ")],1),n("ContentNode",{attrs:{content:e.content}})],1),n("Column",{staticClass:"right"},[n("div",{staticClass:"media"},[e.media?n("Asset",{attrs:{identifier:e.media,showsReplayButton:!e.isClientMobile,showsVideoControls:e.isClientMobile,videoAutoplays:!e.isClientMobile}}):e._e()],1)])],1),e.expandedSections.length>0?n("ExpandedIntro",{staticClass:"expanded-intro",attrs:{content:e.expandedSections}}):e._e()],1)},As=[],$s={name:"SectionIntro",inject:{isClientMobile:{default:()=>!1},isTargetIDE:{default:()=>!1}},components:{Asset:Ae["a"],ContentNode:Ne["default"],ExpandedIntro:dt,Headline:Xt,Row:_t["a"],Column:{render(e){return e(kt["a"],{props:{span:{large:6,small:12}}},this.$slots.default)}}},props:{sectionAnchor:{type:String,required:!0},content:{type:Array,required:!0},media:{type:String,required:!0},title:{type:String,required:!0},sectionNumber:{type:Number,required:!0},expandedSections:{type:Array,default:()=>[]}},methods:{focus(){this.$emit("focus",this.media)}},computed:{sectionLink(){return{path:this.$route.path,hash:this.sectionAnchor,query:this.$route.query}}}},Os=$s,Ns=(n("257c"),Object(_["a"])(Os,Is,As,!1,null,"7f9a8f65",null)),Ps=Ns.exports,qs=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"steps"},[n("div",{staticClass:"content-container"},e._l(e.contentNodes,(function(t,s){return n(t.component,e._b({key:s,ref:"contentNodes",refInFor:!0,tag:"component",class:e.contentClass(s),attrs:{currentIndex:e.activeStep}},"component",t.props,!1))})),1),e.isBreakpointSmall?e._e():n("BackgroundTheme",{staticClass:"asset-container",class:e.assetContainerClasses},[n("transition",{attrs:{name:"fade"}},[e.visibleAsset.media?n("div",{key:e.visibleAsset.media,class:["asset-wrapper",{ide:e.isTargetIDE}]},[n("Asset",{ref:"asset",staticClass:"step-asset",attrs:{identifier:e.visibleAsset.media,showsReplayButton:"",showsVideoControls:!1}})],1):e._e(),e.visibleAsset.code?n("CodePreview",{attrs:{code:e.visibleAsset.code,preview:e.visibleAsset.runtimePreview,isRuntimePreviewVisible:e.isRuntimePreviewVisible},on:{"runtime-preview-toggle":e.onRuntimePreviewToggle}},[e.visibleAsset.runtimePreview?n("transition",{attrs:{name:"fade"}},[n("Asset",{key:e.visibleAsset.runtimePreview,attrs:{identifier:e.visibleAsset.runtimePreview}})],1):e._e()],1):e._e()],1)],1)],1)},Ds=[],js=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{class:["code-preview",{ide:e.isTargetIDE}]},[n("CodeTheme",[e.code?n("CodeListing",e._b({attrs:{showLineNumbers:""}},"CodeListing",e.codeProps,!1)):e._e()],1),n("div",{staticClass:"runtime-preview",class:e.runtimePreviewClasses,style:e.previewStyles},[n("div",{staticClass:"runtimve-preview__container"},[n("button",{staticClass:"header",attrs:{disabled:!e.hasRuntimePreview,title:e.runtimePreviewTitle},on:{click:e.togglePreview}},[n("span",{staticClass:"runtime-preview-label",attrs:{"aria-label":e.textAriaLabel}},[e._v(e._s(e.togglePreviewText))]),n("DiagonalArrowIcon",{staticClass:"icon-inline preview-icon",class:[e.shouldDisplayHideLabel?"preview-hide":"preview-show"]})],1),n("transition",{on:{leave:e.handleLeave}},[n("div",{directives:[{name:"show",rawName:"v-show",value:e.shouldDisplayHideLabel,expression:"shouldDisplayHideLabel"}],staticClass:"runtime-preview-asset"},[e._t("default")],2)])],1)])],1)},Rs=[],Ms=n("7b69"),Bs=n("6667"),Ls=n("8590");const{BreakpointName:Es}=o["a"].constants;function Fs({width:e,height:t},n=1){const s=400,i=e<=s?1.75:3;return{width:e/(i/n),height:t/(i/n)}}var Vs={name:"CodePreview",inject:{isTargetIDE:{default:!1},store:{default(){return{state:{references:{}}}}}},components:{DiagonalArrowIcon:Bs["a"],CodeListing:Ms["a"],CodeTheme:Ls["a"]},props:{code:{type:String,required:!0},preview:{type:String,required:!1},isRuntimePreviewVisible:{type:Boolean,required:!0}},data(){return{tutorialState:this.store.state}},computed:{references:({tutorialState:e})=>e.references,currentBreakpoint(){return this.tutorialState.breakpoint},hasRuntimePreview(){return!!this.preview},previewAssetSize(){const e=this.hasRuntimePreview?this.references[this.preview]:{},t=(e.variants||[{}])[0]||{},n={width:900};let s=t.size||{};s.width||s.height||(s=n);const i=this.currentBreakpoint===Es.medium?.8:1;return Fs(s,i)},previewSize(){const e={width:102};return this.shouldDisplayHideLabel&&this.previewAssetSize?{width:this.previewAssetSize.width}:e},previewStyles(){const{width:e}=this.previewSize;return{width:e+"px"}},codeProps(){return this.references[this.code]},runtimePreviewClasses(){return{collapsed:!this.shouldDisplayHideLabel,disabled:!this.hasRuntimePreview,"runtime-preview-ide":this.isTargetIDE}},shouldDisplayHideLabel(){return this.hasRuntimePreview&&this.isRuntimePreviewVisible},runtimePreviewTitle(){return this.hasRuntimePreview?null:this.$t("tutorials.preview.no-preview-available-step")},togglePreviewText(){return this.$tc("tutorials.preview.title",this.hasRuntimePreview?1:0)},textAriaLabel(){return`${this.togglePreviewText}, ${this.shouldDisplayHideLabel?this.$t("verbs.hide"):this.$t("verbs.show")}`}},methods:{handleLeave(e,t){setTimeout(t,200)},togglePreview(){this.hasRuntimePreview&&this.$emit("runtime-preview-toggle",!this.isRuntimePreviewVisible)}}},Us=Vs,Hs=(n("b27b"),Object(_["a"])(Us,js,Rs,!1,null,"4f53426a",null)),zs=Hs.exports,Gs=n("3908"),Ws=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{style:e.backgroundStyle},[e._t("default")],2)},Qs=[],Ks={name:"BackgroundTheme",data(){return{codeThemeState:ws["a"].state}},computed:{backgroundStyle(){const{codeColors:e}=this.codeThemeState;return e?{"--background":e.background}:null}}},Xs=Ks,Js=Object(_["a"])(Xs,Ws,Qs,!1,null,null,null),Ys=Js.exports,Zs=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{class:["step-container","step-"+e.stepNumber]},[n("div",{ref:"step",staticClass:"step",class:{focused:e.isActive},attrs:{"data-index":e.index}},[n("p",{staticClass:"step-label"},[e._v(e._s(e.$t("tutorials.step",{number:e.stepNumber})))]),n("ContentNode",{attrs:{content:e.content}}),e.caption&&e.caption.length>0?n("ContentNode",{staticClass:"caption",attrs:{content:e.caption}}):e._e()],1),e.isBreakpointSmall||!e.isTargetIDE?n("div",{staticClass:"media-container"},[e.media?n("Asset",{attrs:{identifier:e.media,showsReplayButton:!e.isClientMobile,showsVideoControls:e.isClientMobile,videoAutoplays:!e.isClientMobile}}):e._e(),e.code?n("MobileCodePreview",{attrs:{code:e.code}},[e.runtimePreview?n("Asset",{staticClass:"preview",attrs:{identifier:e.runtimePreview}}):e._e()],1):e._e()],1):e._e()])},ei=[],ti=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("BackgroundTheme",{staticClass:"mobile-code-preview"},[e.code?n("GenericModal",{staticClass:"full-code-listing-modal",attrs:{theme:e.isTargetIDE?"code":"light",codeBackgroundColorOverride:e.modalBackgroundColor,isFullscreen:"",visible:e.fullCodeIsVisible},on:{"update:visible":function(t){e.fullCodeIsVisible=t}}},[n("div",{staticClass:"full-code-listing-modal-content"},[n("CodeTheme",[n("CodeListing",e._b({staticClass:"full-code-listing",attrs:{showLineNumbers:""}},"CodeListing",e.codeProps,!1))],1)],1)]):e._e(),n("CodeTheme",[e.code?n("MobileCodeListing",e._b({attrs:{showLineNumbers:""},on:{"file-name-click":e.toggleFullCode}},"MobileCodeListing",e.codeProps,!1)):e._e()],1),n("CodeTheme",{staticClass:"preview-toggle-container"},[n("PreviewToggle",{attrs:{isActionable:!!e.$slots.default},on:{click:e.togglePreview}})],1),e.$slots.default?n("GenericModal",{staticClass:"runtime-preview-modal",attrs:{theme:e.isTargetIDE?"dynamic":"light",isFullscreen:"",visible:e.previewIsVisible},on:{"update:visible":function(t){e.previewIsVisible=t}}},[n("div",{staticClass:"runtime-preview-modal-content"},[n("span",{staticClass:"runtime-preview-label"},[e._v(e._s(e.$tc("tutorials.preview.title",1)))]),e._t("default")],2)]):e._e()],1)},ni=[],si=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"code-listing-preview",attrs:{"data-syntax":e.syntax}},[n("CodeListing",{attrs:{fileName:e.fileName,syntax:e.syntax,fileType:e.fileType,content:e.previewedLines,startLineNumber:e.displayedRange.start,highlights:e.highlights,showLineNumbers:"",isFileNameActionable:""},on:{"file-name-click":function(t){return e.$emit("file-name-click")}}})],1)},ii=[],ri={name:"MobileCodeListing",components:{CodeListing:Ms["a"]},props:{fileName:String,syntax:String,fileType:String,content:{type:Array,required:!0},highlights:{type:Array,default:()=>[]}},computed:{highlightedLineNumbers(){return new Set(this.highlights.map(({line:e})=>e))},firstHighlightRange(){if(0===this.highlightedLineNumbers.size)return{start:1,end:this.content.length};const e=Math.min(...this.highlightedLineNumbers.values());let t=e;while(this.highlightedLineNumbers.has(t+1))t+=1;return{start:e,end:t}},displayedRange(){const e=this.firstHighlightRange,t=e.start-2<1?1:e.start-2,n=e.end+3>=this.content.length+1?this.content.length+1:e.end+3;return{start:t,end:n}},previewedLines(){return this.content.slice(this.displayedRange.start-1,this.displayedRange.end-1)}}},oi=ri,ai=(n("fae5"),Object(_["a"])(oi,si,ii,!1,null,"5ad4e037",null)),ci=ai.exports,li=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("span",{staticClass:"toggle-preview"},[e.isActionable?n("a",{staticClass:"toggle-text",attrs:{href:"#"},on:{click:function(t){return t.preventDefault(),e.$emit("click")}}},[e._v(" "+e._s(e.$tc("tutorials.preview.title",1))+" "),n("InlinePlusCircleIcon",{staticClass:"toggle-icon icon-inline"})],1):n("span",{staticClass:"toggle-text"},[e._v(" "+e._s(e.$tc("tutorials.preview.title",0))+" ")])])},ui=[],di=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("SVGIcon",{staticClass:"inline-plus-circle-icon",attrs:{viewBox:"0 0 14 14",themeId:"inline-plus-circle"}},[n("path",{attrs:{d:"M7 0.5c3.59 0 6.5 2.91 6.5 6.5s-2.91 6.5-6.5 6.5c-3.59 0-6.5-2.91-6.5-6.5v0c0-3.59 2.91-6.5 6.5-6.5v0zM7 1.5c-3.038 0-5.5 2.462-5.5 5.5s2.462 5.5 5.5 5.5c3.038 0 5.5-2.462 5.5-5.5v0c0-3.038-2.462-5.5-5.5-5.5v0z"}}),n("path",{attrs:{d:"M4 6.52h6v1h-6v-1z"}}),n("path",{attrs:{d:"M6.5 4.010h1v6h-1v-6z"}})])},pi=[],hi={name:"InlinePlusCircleIcon",components:{SVGIcon:y["a"]}},mi=hi,fi=Object(_["a"])(mi,di,pi,!1,null,null,null),vi=fi.exports,bi={name:"MobileCodePreviewToggle",components:{InlinePlusCircleIcon:vi},props:{isActionable:{type:Boolean,required:!0}}},gi=bi,yi=(n("bf0a"),Object(_["a"])(gi,li,ui,!1,null,"78763c14",null)),Ci=yi.exports,wi={name:"MobileCodePreview",inject:["isTargetIDE"],mixins:[T["a"]],components:{GenericModal:Jt["a"],CodeListing:Ms["a"],MobileCodeListing:ci,PreviewToggle:Ci,CodeTheme:Ls["a"],BackgroundTheme:Ys},props:{code:{type:String,required:!0}},computed:{codeProps(){return this.references[this.code]},modalBackgroundColor(){const{codeColors:e}=this.store.state;return e?e.background:null}},data(){return{previewIsVisible:!1,fullCodeIsVisible:!1}},methods:{togglePreview(){this.previewIsVisible=!this.previewIsVisible},toggleFullCode(){this.fullCodeIsVisible=!this.fullCodeIsVisible}}},_i=wi,ki=(n("3fcc"),Object(_["a"])(_i,ti,ni,!1,null,"1aed4baa",null)),Si=ki.exports;const{BreakpointName:xi}=o["a"].constants;var Ti={name:"Step",components:{Asset:Ae["a"],MobileCodePreview:Si,ContentNode:Ne["default"]},inject:["isTargetIDE","isClientMobile","store"],props:{code:{type:String,required:!1},content:{type:Array,required:!0},caption:{type:Array,required:!1},media:{type:String,required:!1},runtimePreview:{type:String,required:!1},sectionNumber:{type:Number,required:!0},stepNumber:{type:Number,required:!0},numberOfSteps:{type:Number,required:!0},index:{type:Number,required:!0},currentIndex:{type:Number,required:!0}},data(){return{tutorialState:this.store.state}},computed:{isBreakpointSmall(){return this.tutorialState.breakpoint===xi.small},isActive:({index:e,currentIndex:t})=>e===t}},Ii=Ti,Ai=(n("c643"),Object(_["a"])(Ii,Zs,ei,!1,null,"eb45ec92",null)),$i=Ai.exports;const{BreakpointName:Oi}=o["a"].constants,{IntersectionDirections:Ni}=Je["a"].constants,Pi="-35% 0% -65% 0%";var qi={name:"SectionSteps",components:{ContentNode:Ne["default"],Step:$i,Asset:Ae["a"],CodePreview:zs,BackgroundTheme:Ys},mixins:[Je["a"]],constants:{IntersectionMargins:Pi},inject:["isTargetIDE","store"],data(){const e=this.content.findIndex(this.isStepNode),{code:t,media:n,runtimePreview:s}=this.content[e]||{};return{tutorialState:this.store.state,visibleAsset:{media:n,code:t,runtimePreview:s},activeStep:e}},computed:{assetContainerClasses(){return{"for-step-code":!!this.visibleAsset.code,ide:this.isTargetIDE}},numberOfSteps(){return this.content.filter(this.isStepNode).length},contentNodes(){return this.content.reduce(({stepCounter:e,nodes:t},n,s)=>{const{type:i,...r}=n,o=this.isStepNode(n),a=o?e+1:e;return o?{stepCounter:e+1,nodes:t.concat({component:$i,type:i,props:{...r,stepNumber:a,index:s,numberOfSteps:this.numberOfSteps,sectionNumber:this.sectionNumber}})}:{stepCounter:e,nodes:t.concat({component:Ne["default"],type:i,props:{content:[n]}})}},{stepCounter:0,nodes:[]}).nodes},isBreakpointSmall(){return this.tutorialState.breakpoint===Oi.small},stepNodes:({contentNodes:e,isStepNode:t})=>e.filter(t),intersectionRootMargin:()=>Pi},async mounted(){await Object(Gs["b"])(8),this.findClosestStepNode()},methods:{isStepNode({type:e}){return"step"===e},contentClass(e){return{["interstitial interstitial-"+(e+1)]:!this.isStepNode(this.content[e])}},onReverseIntoLastStep(){const{asset:e}=this.$refs;if(e){const t=e.$el.querySelector("video");t&&(t.currentTime=0,t.play().catch(()=>{}))}},onFocus(e){const{code:t,media:n,runtimePreview:s}=this.content[e];this.activeStep=e,this.visibleAsset={code:t,media:n,runtimePreview:s}},onRuntimePreviewToggle(e){this.$emit("runtime-preview-toggle",e)},findClosestStepNode(){const e=.333*window.innerHeight;let t=null,n=0;this.stepNodes.forEach(s=>{const{index:i}=s.props,r=this.$refs.contentNodes[i].$refs.step;if(!r)return;const{top:o,bottom:a}=r.getBoundingClientRect(),c=o-e,l=a-e,u=Math.abs(c+l);(0===n||u<=n)&&(n=u,t=i)}),null!==t&&this.onFocus(t)},getIntersectionTargets(){const{stepNodes:e,$refs:t}=this;return e.map(({props:{index:e}})=>t.contentNodes[e].$refs.step)},onIntersect(e){const{target:t,isIntersecting:n}=e;if(!n)return;const s=parseFloat(t.getAttribute("data-index"));this.intersectionScrollDirection===Ni.down&&s===this.stepNodes[this.stepNodes.length-1].props.index&&this.onReverseIntoLastStep(),this.onFocus(s)}},props:{content:{type:Array,required:!0},isRuntimePreviewVisible:{type:Boolean,require:!0},sectionNumber:{type:Number,required:!0}}},Di=qi,ji=(n("d4f3"),Object(_["a"])(Di,qs,Ds,!1,null,"2786be2a",null)),Ri=ji.exports,Mi={name:"Section",components:{Intro:Ps,LinkableSection:tt,Steps:Ri},computed:{introProps(){const[{content:e,media:t},...n]=this.contentSection;return{content:e,expandedSections:n,media:t,sectionAnchor:this.anchor,sectionNumber:this.sectionNumber,title:this.title}}},props:{anchor:{type:String,required:!0},title:{type:String,required:!0},contentSection:{type:Array,required:!0},stepsSection:{type:Array,required:!0},sectionNumber:{type:Number,required:!0},isRuntimePreviewVisible:{type:Boolean,required:!0}},methods:{onRuntimePreviewToggle(e){this.$emit("runtime-preview-toggle",e)}}},Bi=Mi,Li=(n("9dc4"),Object(_["a"])(Bi,xs,Ts,!1,null,"6b3a0b3a",null)),Ei=Li.exports,Fi={name:"SectionList",components:{Section:Ei},data(){return{isRuntimePreviewVisible:!0}},props:{tasks:{type:Array,required:!0}},methods:{onRuntimePreviewToggle(e){this.isRuntimePreviewVisible=e}}},Vi=Fi,Ui=(n("4d07"),Object(_["a"])(Vi,ks,Ss,!1,null,"79a75e9e",null)),Hi=Ui.exports;const zi={assessments:ls,hero:Sn,tasks:Hi,callToAction:Pt},Gi=new Set(Object.keys(zi)),Wi={name:"TutorialSection",render:function(e){const{kind:t,...n}=this.section,s=zi[t];return s?e(s,{props:n}):null},props:{section:{type:Object,required:!0,validator:e=>Gi.has(e.kind)}}};var Qi={name:"Tutorial",mixins:[we["a"],_s["a"]],components:{NavigationBar:Ce,Section:Wi,PortalTarget:h["PortalTarget"],BreakpointEmitter:o["a"]},inject:["isTargetIDE","store"],computed:{heroSection(){return this.sections.find(({kind:e})=>"hero"===e)},tutorialTitle(){return(this.heroSection||{}).title},pageTitle(){return this.tutorialTitle?`${this.tutorialTitle} — ${this.metadata.category} Tutorials`:void 0},pageDescription:({heroSection:e,extractFirstParagraphText:t})=>e?t(e.content):null},props:{sections:{type:Array,required:!0},references:{type:Object,required:!0},hierarchy:{type:Object,required:!0},metadata:{type:Object,required:!0},identifierUrl:{type:String,required:!0}},methods:{handleBreakpointChange(e){this.store.updateBreakpoint(e)},handleCodeColorsChange(e){ws["a"].updateCodeColors(e)}},created(){m["a"].setAvailableLocales(this.metadata.availableLocales),this.store.reset(),this.store.setReferences(this.references)},watch:{references(e){this.store.setReferences(e)},"metadata.availableLocales":function(e){m["a"].setAvailableLocales(e)}},mounted(){this.$bridge.on("codeColors",this.handleCodeColorsChange),this.$bridge.send({type:"requestCodeColors"})},provide(){return{isClientMobile:this.isClientMobile}},beforeDestroy(){this.$bridge.off("codeColors",this.handleCodeColorsChange)}},Ki=Qi,Xi=(n("424f"),Object(_["a"])(Ki,ys,Cs,!1,null,"611d0574",null)),Ji=Xi.exports,Yi=n("0caf"),Zi=n("146e");const er={article:"article",tutorial:"project"};var tr={name:"Topic",inject:{isTargetIDE:{default:!1}},mixins:[Yi["a"],Zi["a"]],data(){return{topicData:null}},computed:{navigationBarHeight(){return this.isTargetIDE?0:52},store(){return u},hierarchy(){const{hierarchy:e={}}=this.topicData,{technologyNavigation:t=["overview","tutorials","resources"]}=e||{};return{...e,technologyNavigation:t}},topicKey:({$route:e,topicData:t})=>[e.path,t.identifier.interfaceLanguage].join()},beforeRouteEnter(e,t,n){e.meta.skipFetchingData?n(e=>e.newContentMounted()):Object(r["c"])(e,t,n).then(e=>n(t=>{t.topicData=e})).catch(n)},beforeRouteUpdate(e,t,n){Object(r["e"])(e,t)?Object(r["c"])(e,t,n).then(e=>{this.topicData=e,n()}).catch(n):n()},created(){this.store.reset()},mounted(){this.$bridge.on("contentUpdate",this.handleContentUpdateFromBridge)},beforeDestroy(){this.$bridge.off("contentUpdate",this.handleContentUpdateFromBridge)},methods:{componentFor(e){const{kind:t}=e;return{[er.article]:gs,[er.tutorial]:Ji}[t]},propsFor(e){const{hierarchy:t,kind:n,metadata:s,references:i,sections:r,identifier:o}=e;return{[er.article]:{hierarchy:t,metadata:s,references:i,sections:r,identifierUrl:o.url},[er.tutorial]:{hierarchy:t,metadata:s,references:i,sections:r,identifierUrl:o.url}}[n]}},provide(){return{navigationBarHeight:this.navigationBarHeight,store:this.store}},watch:{topicData(){this.$nextTick(()=>{this.newContentMounted()})}}},nr=tr,sr=Object(_["a"])(nr,s,i,!1,null,null,null);t["default"]=sr.exports},"32b1":function(e,t,n){},"33ff":function(e,t,n){"use strict";n("4e3e")},3976:function(e,t,n){"use strict";n("0c11")},"3e1b":function(e,t,n){"use strict";n("c5c1")},"3fcc":function(e,t,n){"use strict";n("0c14")},"424f":function(e,t,n){"use strict";n("99b6")},4718:function(e,t,n){},"4b4a":function(e,t,n){},"4d07":function(e,t,n){"use strict";n("b52e")},"4e3e":function(e,t,n){},5237:function(e,t,n){"use strict";n("4b4a")},"525c":function(e,t,n){},5892:function(e,t,n){},5913:function(e,t,n){},"63a8":function(e,t,n){},"653a":function(e,t,n){"use strict";var s=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("router-link",{staticClass:"nav-title-content",attrs:{to:e.to}},[n("span",{staticClass:"title"},[e._t("default")],2),n("span",{staticClass:"subhead"},[e._v(" "),e._t("subhead")],2)])},i=[],r={name:"NavTitleContainer",props:{to:{type:[String,Object],required:!0}}},o=r,a=(n("f1e6"),n("2877")),c=Object(a["a"])(o,s,i,!1,null,"854b4dd6",null);t["a"]=c.exports},6866:function(e,t,n){"use strict";n("5892")},7096:function(e,t,n){},"735b":function(e,t,n){},7748:function(e,t,n){},"79ef":function(e,t,n){"use strict";n("bcaa")},"7b17":function(e,t,n){},"7f7b":function(e,t,n){"use strict";n("735b")},"7fae":function(e,t,n){"use strict";n("a53d")},9924:function(e,t,n){},9938:function(e,t,n){"use strict";n("d513")},"99b6":function(e,t,n){},"9dc4":function(e,t,n){"use strict";n("fe9d")},a0d4:function(e,t,n){},a40c:function(e,t,n){},a53d:function(e,t,n){},a95e:function(e,t,n){},aa4c:function(e,t,n){},aece:function(e,t,n){"use strict";n("c0df")},b27b:function(e,t,n){"use strict";n("2a05")},b52e:function(e,t,n){},bcaa:function(e,t,n){},bf0a:function(e,t,n){"use strict";n("4718")},c0df:function(e,t,n){},c5c1:function(e,t,n){},c643:function(e,t,n){"use strict";n("9924")},c7dd:function(e,t,n){"use strict";n("aa4c")},cb8d:function(e,t,n){"use strict";n("0466")},d4f3:function(e,t,n){"use strict";n("fc13")},d513:function(e,t,n){},d86f:function(e,t,n){},da20:function(e,t,n){"use strict";n("a40c")},de60:function(e,t,n){"use strict";var s=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("SVGIcon",{staticClass:"download-icon",attrs:{viewBox:"0 0 14 14",themeId:"download"}},[n("path",{attrs:{d:"M7 0.5c3.59 0 6.5 2.91 6.5 6.5s-2.91 6.5-6.5 6.5c-3.59 0-6.5-2.91-6.5-6.5s2.91-6.5 6.5-6.5zM7 1.5c-3.038 0-5.5 2.462-5.5 5.5s2.462 5.5 5.5 5.5c3.038 0 5.5-2.462 5.5-5.5s-2.462-5.5-5.5-5.5z"}}),n("path",{attrs:{d:"M7.51 2.964l-0.001 5.431 1.308-2.041 0.842 0.539-2.664 4.162-2.633-4.164 0.845-0.534 1.303 2.059 0.001-5.452z"}})])},i=[],r=n("be08"),o={name:"DownloadIcon",components:{SVGIcon:r["a"]}},a=o,c=n("2877"),l=Object(c["a"])(a,s,i,!1,null,null,null);t["a"]=l.exports},e38e:function(e,t,n){},e688:function(e,t,n){"use strict";n("5913")},e84c:function(e,t,n){"use strict";n("d86f")},e9b0:function(e,t,n){"use strict";n("ee09")},ea88:function(e,t,n){"use strict";n("ed98")},ed71:function(e,t,n){"use strict";n("7096")},ed98:function(e,t,n){},ee09:function(e,t,n){},f1e6:function(e,t,n){"use strict";n("a0d4")},f264:function(e,t,n){"use strict";n("63a8")},fae5:function(e,t,n){"use strict";n("32b1")},fc13:function(e,t,n){},fe9d:function(e,t,n){}}]);