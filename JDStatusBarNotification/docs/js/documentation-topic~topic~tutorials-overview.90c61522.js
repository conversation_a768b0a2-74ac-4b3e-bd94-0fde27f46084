/*!
 * This source file is part of the Swift.org open source project
 * 
 * Copyright (c) 2021 Apple Inc. and the Swift project authors
 * Licensed under Apache License v2.0 with Runtime Library Exception
 * 
 * See https://swift.org/LICENSE.txt for license information
 * See https://swift.org/CONTRIBUTORS.txt for Swift project authors
 */
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["documentation-topic~topic~tutorials-overview"],{"007b":function(e,t,n){},"0444":function(e,t,n){},"05a1":function(e,t,n){},"0939":function(e,t,n){"use strict";n("9034")},"0caf":function(e,t,n){"use strict";t["a"]={inject:{performanceMetricsEnabled:{default:!1},isTargetIDE:{default:!1}},methods:{newContentMounted(){let e;this.performanceMetricsEnabled&&(e=Math.round(window.performance.now()),window.renderedTimes||(window.renderedTimes=[]),window.renderedTimes.push(e)),this.$bridge.send({type:"rendered",data:{time:e}})},handleContentUpdateFromBridge(e){this.topicData=e}}}},"0cb0":function(e,t,n){"use strict";const i=["input","select","textarea","button","optgroup","option","menuitem","fieldset","object","a[href]","*[tabindex]","*[contenteditable]"],a=i.join(",");t["a"]={getTabbableElements(e){const t=e.querySelectorAll(a),n=t.length;let i;const r=[];for(i=0;i<n;i+=1)this.isTabbableElement(t[i])&&r.push(t[i]);return r},isTabbableElement(e){if(!e.offsetParent)return!1;const t=parseFloat(e.getAttribute("tabindex"));return Number.isNaN(t)?this.isFocusableElement(e):t>=0},isFocusableElement(e){const t=e.nodeName.toLowerCase(),n=i.includes(t);return!("a"!==t||!e.getAttribute("href"))||(n?!e.disabled:"true"===e.getAttribute("contenteditable")||!Number.isNaN(parseFloat(e.getAttribute("tabindex"))))}}},"0cf0":function(e,t,n){},"0da1":function(e,t,n){"use strict";n("5c97")},"0eaa":function(e,t,n){},"0f00":function(e,t,n){"use strict";var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"row"},[e._t("default")],2)},a=[],r={name:"GridRow"},s=r,o=(n("2224"),n("2877")),c=Object(o["a"])(s,i,a,!1,null,"be73599c",null);t["a"]=c.exports},1020:function(e,t){var n={exports:{}};function i(e){return e instanceof Map?e.clear=e.delete=e.set=function(){throw new Error("map is read-only")}:e instanceof Set&&(e.add=e.clear=e.delete=function(){throw new Error("set is read-only")}),Object.freeze(e),Object.getOwnPropertyNames(e).forEach((function(t){var n=e[t];"object"!=typeof n||Object.isFrozen(n)||i(n)})),e}n.exports=i,n.exports.default=i;var a=n.exports;class r{constructor(e){void 0===e.data&&(e.data={}),this.data=e.data,this.isMatchIgnored=!1}ignoreMatch(){this.isMatchIgnored=!0}}function s(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;")}function o(e,...t){const n=Object.create(null);for(const i in e)n[i]=e[i];return t.forEach((function(e){for(const t in e)n[t]=e[t]})),n}const c="</span>",l=e=>!!e.kind,u=(e,{prefix:t})=>{if(e.includes(".")){const n=e.split(".");return[`${t}${n.shift()}`,...n.map((e,t)=>`${e}${"_".repeat(t+1)}`)].join(" ")}return`${t}${e}`};class d{constructor(e,t){this.buffer="",this.classPrefix=t.classPrefix,e.walk(this)}addText(e){this.buffer+=s(e)}openNode(e){if(!l(e))return;let t=e.kind;t=e.sublanguage?"language-"+t:u(t,{prefix:this.classPrefix}),this.span(t)}closeNode(e){l(e)&&(this.buffer+=c)}value(){return this.buffer}span(e){this.buffer+=`<span class="${e}">`}}class p{constructor(){this.rootNode={children:[]},this.stack=[this.rootNode]}get top(){return this.stack[this.stack.length-1]}get root(){return this.rootNode}add(e){this.top.children.push(e)}openNode(e){const t={kind:e,children:[]};this.add(t),this.stack.push(t)}closeNode(){if(this.stack.length>1)return this.stack.pop()}closeAllNodes(){while(this.closeNode());}toJSON(){return JSON.stringify(this.rootNode,null,4)}walk(e){return this.constructor._walk(e,this.rootNode)}static _walk(e,t){return"string"===typeof t?e.addText(t):t.children&&(e.openNode(t),t.children.forEach(t=>this._walk(e,t)),e.closeNode(t)),e}static _collapse(e){"string"!==typeof e&&e.children&&(e.children.every(e=>"string"===typeof e)?e.children=[e.children.join("")]:e.children.forEach(e=>{p._collapse(e)}))}}class h extends p{constructor(e){super(),this.options=e}addKeyword(e,t){""!==e&&(this.openNode(t),this.addText(e),this.closeNode())}addText(e){""!==e&&this.add(e)}addSublanguage(e,t){const n=e.root;n.kind=t,n.sublanguage=!0,this.add(n)}toHTML(){const e=new d(this,this.options);return e.value()}finalize(){return!0}}function m(e){return e?"string"===typeof e?e:e.source:null}function f(e){return v("(?=",e,")")}function g(e){return v("(?:",e,")*")}function b(e){return v("(?:",e,")?")}function v(...e){const t=e.map(e=>m(e)).join("");return t}function y(e){const t=e[e.length-1];return"object"===typeof t&&t.constructor===Object?(e.splice(e.length-1,1),t):{}}function w(...e){const t=y(e),n="("+(t.capture?"":"?:")+e.map(e=>m(e)).join("|")+")";return n}function _(e){return new RegExp(e.toString()+"|").exec("").length-1}function x(e,t){const n=e&&e.exec(t);return n&&0===n.index}const k=/\[(?:[^\\\]]|\\.)*\]|\(\??|\\([1-9][0-9]*)|\\./;function C(e,{joinWith:t}){let n=0;return e.map(e=>{n+=1;const t=n;let i=m(e),a="";while(i.length>0){const e=k.exec(i);if(!e){a+=i;break}a+=i.substring(0,e.index),i=i.substring(e.index+e[0].length),"\\"===e[0][0]&&e[1]?a+="\\"+String(Number(e[1])+t):(a+=e[0],"("===e[0]&&n++)}return a}).map(e=>`(${e})`).join(t)}const S=/\b\B/,E="[a-zA-Z]\\w*",O="[a-zA-Z_]\\w*",I="\\b\\d+(\\.\\d+)?",j="(-?)(\\b0[xX][a-fA-F0-9]+|(\\b\\d+(\\.\\d*)?|\\.\\d+)([eE][-+]?\\d+)?)",T="\\b(0b[01]+)",A="!|!=|!==|%|%=|&|&&|&=|\\*|\\*=|\\+|\\+=|,|-|-=|/=|/|:|;|<<|<<=|<=|<|===|==|=|>>>=|>>=|>=|>>>|>>|>|\\?|\\[|\\{|\\(|\\^|\\^=|\\||\\|=|\\|\\||~",L=(e={})=>{const t=/^#![ ]*\//;return e.binary&&(e.begin=v(t,/.*\b/,e.binary,/\b.*/)),o({scope:"meta",begin:t,end:/$/,relevance:0,"on:begin":(e,t)=>{0!==e.index&&t.ignoreMatch()}},e)},B={begin:"\\\\[\\s\\S]",relevance:0},$={scope:"string",begin:"'",end:"'",illegal:"\\n",contains:[B]},N={scope:"string",begin:'"',end:'"',illegal:"\\n",contains:[B]},M={begin:/\b(a|an|the|are|I'm|isn't|don't|doesn't|won't|but|just|should|pretty|simply|enough|gonna|going|wtf|so|such|will|you|your|they|like|more)\b/},P=function(e,t,n={}){const i=o({scope:"comment",begin:e,end:t,contains:[]},n);i.contains.push({scope:"doctag",begin:"[ ]*(?=(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):)",end:/(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):/,excludeBegin:!0,relevance:0});const a=w("I","a","is","so","us","to","at","if","in","it","on",/[A-Za-z]+['](d|ve|re|ll|t|s|n)/,/[A-Za-z]+[-][a-z]+/,/[A-Za-z][a-z]{2,}/);return i.contains.push({begin:v(/[ ]+/,"(",a,/[.]?[:]?([.][ ]|[ ])/,"){3}")}),i},R=P("//","$"),V=P("/\\*","\\*/"),D=P("#","$"),G={scope:"number",begin:I,relevance:0},z={scope:"number",begin:j,relevance:0},q={scope:"number",begin:T,relevance:0},F={begin:/(?=\/[^/\n]*\/)/,contains:[{scope:"regexp",begin:/\//,end:/\/[gimuy]*/,illegal:/\n/,contains:[B,{begin:/\[/,end:/\]/,relevance:0,contains:[B]}]}]},U={scope:"title",begin:E,relevance:0},W={scope:"title",begin:O,relevance:0},H={begin:"\\.\\s*"+O,relevance:0},K=function(e){return Object.assign(e,{"on:begin":(e,t)=>{t.data._beginMatch=e[1]},"on:end":(e,t)=>{t.data._beginMatch!==e[1]&&t.ignoreMatch()}})};var Z=Object.freeze({__proto__:null,MATCH_NOTHING_RE:S,IDENT_RE:E,UNDERSCORE_IDENT_RE:O,NUMBER_RE:I,C_NUMBER_RE:j,BINARY_NUMBER_RE:T,RE_STARTERS_RE:A,SHEBANG:L,BACKSLASH_ESCAPE:B,APOS_STRING_MODE:$,QUOTE_STRING_MODE:N,PHRASAL_WORDS_MODE:M,COMMENT:P,C_LINE_COMMENT_MODE:R,C_BLOCK_COMMENT_MODE:V,HASH_COMMENT_MODE:D,NUMBER_MODE:G,C_NUMBER_MODE:z,BINARY_NUMBER_MODE:q,REGEXP_MODE:F,TITLE_MODE:U,UNDERSCORE_TITLE_MODE:W,METHOD_GUARD:H,END_SAME_AS_BEGIN:K});function Y(e,t){const n=e.input[e.index-1];"."===n&&t.ignoreMatch()}function X(e,t){void 0!==e.className&&(e.scope=e.className,delete e.className)}function J(e,t){t&&e.beginKeywords&&(e.begin="\\b("+e.beginKeywords.split(" ").join("|")+")(?!\\.)(?=\\b|\\s)",e.__beforeBegin=Y,e.keywords=e.keywords||e.beginKeywords,delete e.beginKeywords,void 0===e.relevance&&(e.relevance=0))}function Q(e,t){Array.isArray(e.illegal)&&(e.illegal=w(...e.illegal))}function ee(e,t){if(e.match){if(e.begin||e.end)throw new Error("begin & end are not supported with match");e.begin=e.match,delete e.match}}function te(e,t){void 0===e.relevance&&(e.relevance=1)}const ne=(e,t)=>{if(!e.beforeMatch)return;if(e.starts)throw new Error("beforeMatch cannot be used with starts");const n=Object.assign({},e);Object.keys(e).forEach(t=>{delete e[t]}),e.keywords=n.keywords,e.begin=v(n.beforeMatch,f(n.begin)),e.starts={relevance:0,contains:[Object.assign(n,{endsParent:!0})]},e.relevance=0,delete n.beforeMatch},ie=["of","and","for","in","not","or","if","then","parent","list","value"],ae="keyword";function re(e,t,n=ae){const i=Object.create(null);return"string"===typeof e?a(n,e.split(" ")):Array.isArray(e)?a(n,e):Object.keys(e).forEach((function(n){Object.assign(i,re(e[n],t,n))})),i;function a(e,n){t&&(n=n.map(e=>e.toLowerCase())),n.forEach((function(t){const n=t.split("|");i[n[0]]=[e,se(n[0],n[1])]}))}}function se(e,t){return t?Number(t):oe(e)?0:1}function oe(e){return ie.includes(e.toLowerCase())}const ce={},le=e=>{console.error(e)},ue=(e,...t)=>{console.log("WARN: "+e,...t)},de=(e,t)=>{ce[`${e}/${t}`]||(console.log(`Deprecated as of ${e}. ${t}`),ce[`${e}/${t}`]=!0)},pe=new Error;function he(e,t,{key:n}){let i=0;const a=e[n],r={},s={};for(let o=1;o<=t.length;o++)s[o+i]=a[o],r[o+i]=!0,i+=_(t[o-1]);e[n]=s,e[n]._emit=r,e[n]._multi=!0}function me(e){if(Array.isArray(e.begin)){if(e.skip||e.excludeBegin||e.returnBegin)throw le("skip, excludeBegin, returnBegin not compatible with beginScope: {}"),pe;if("object"!==typeof e.beginScope||null===e.beginScope)throw le("beginScope must be object"),pe;he(e,e.begin,{key:"beginScope"}),e.begin=C(e.begin,{joinWith:""})}}function fe(e){if(Array.isArray(e.end)){if(e.skip||e.excludeEnd||e.returnEnd)throw le("skip, excludeEnd, returnEnd not compatible with endScope: {}"),pe;if("object"!==typeof e.endScope||null===e.endScope)throw le("endScope must be object"),pe;he(e,e.end,{key:"endScope"}),e.end=C(e.end,{joinWith:""})}}function ge(e){e.scope&&"object"===typeof e.scope&&null!==e.scope&&(e.beginScope=e.scope,delete e.scope)}function be(e){ge(e),"string"===typeof e.beginScope&&(e.beginScope={_wrap:e.beginScope}),"string"===typeof e.endScope&&(e.endScope={_wrap:e.endScope}),me(e),fe(e)}function ve(e){function t(t,n){return new RegExp(m(t),"m"+(e.case_insensitive?"i":"")+(e.unicodeRegex?"u":"")+(n?"g":""))}class n{constructor(){this.matchIndexes={},this.regexes=[],this.matchAt=1,this.position=0}addRule(e,t){t.position=this.position++,this.matchIndexes[this.matchAt]=t,this.regexes.push([t,e]),this.matchAt+=_(e)+1}compile(){0===this.regexes.length&&(this.exec=()=>null);const e=this.regexes.map(e=>e[1]);this.matcherRe=t(C(e,{joinWith:"|"}),!0),this.lastIndex=0}exec(e){this.matcherRe.lastIndex=this.lastIndex;const t=this.matcherRe.exec(e);if(!t)return null;const n=t.findIndex((e,t)=>t>0&&void 0!==e),i=this.matchIndexes[n];return t.splice(0,n),Object.assign(t,i)}}class i{constructor(){this.rules=[],this.multiRegexes=[],this.count=0,this.lastIndex=0,this.regexIndex=0}getMatcher(e){if(this.multiRegexes[e])return this.multiRegexes[e];const t=new n;return this.rules.slice(e).forEach(([e,n])=>t.addRule(e,n)),t.compile(),this.multiRegexes[e]=t,t}resumingScanAtSamePosition(){return 0!==this.regexIndex}considerAll(){this.regexIndex=0}addRule(e,t){this.rules.push([e,t]),"begin"===t.type&&this.count++}exec(e){const t=this.getMatcher(this.regexIndex);t.lastIndex=this.lastIndex;let n=t.exec(e);if(this.resumingScanAtSamePosition())if(n&&n.index===this.lastIndex);else{const t=this.getMatcher(0);t.lastIndex=this.lastIndex+1,n=t.exec(e)}return n&&(this.regexIndex+=n.position+1,this.regexIndex===this.count&&this.considerAll()),n}}function a(e){const t=new i;return e.contains.forEach(e=>t.addRule(e.begin,{rule:e,type:"begin"})),e.terminatorEnd&&t.addRule(e.terminatorEnd,{type:"end"}),e.illegal&&t.addRule(e.illegal,{type:"illegal"}),t}function r(n,i){const s=n;if(n.isCompiled)return s;[X,ee,be,ne].forEach(e=>e(n,i)),e.compilerExtensions.forEach(e=>e(n,i)),n.__beforeBegin=null,[J,Q,te].forEach(e=>e(n,i)),n.isCompiled=!0;let o=null;return"object"===typeof n.keywords&&n.keywords.$pattern&&(n.keywords=Object.assign({},n.keywords),o=n.keywords.$pattern,delete n.keywords.$pattern),o=o||/\w+/,n.keywords&&(n.keywords=re(n.keywords,e.case_insensitive)),s.keywordPatternRe=t(o,!0),i&&(n.begin||(n.begin=/\B|\b/),s.beginRe=t(s.begin),n.end||n.endsWithParent||(n.end=/\B|\b/),n.end&&(s.endRe=t(s.end)),s.terminatorEnd=m(s.end)||"",n.endsWithParent&&i.terminatorEnd&&(s.terminatorEnd+=(n.end?"|":"")+i.terminatorEnd)),n.illegal&&(s.illegalRe=t(n.illegal)),n.contains||(n.contains=[]),n.contains=[].concat(...n.contains.map((function(e){return we("self"===e?n:e)}))),n.contains.forEach((function(e){r(e,s)})),n.starts&&r(n.starts,i),s.matcher=a(s),s}if(e.compilerExtensions||(e.compilerExtensions=[]),e.contains&&e.contains.includes("self"))throw new Error("ERR: contains `self` is not supported at the top-level of a language.  See documentation.");return e.classNameAliases=o(e.classNameAliases||{}),r(e)}function ye(e){return!!e&&(e.endsWithParent||ye(e.starts))}function we(e){return e.variants&&!e.cachedVariants&&(e.cachedVariants=e.variants.map((function(t){return o(e,{variants:null},t)}))),e.cachedVariants?e.cachedVariants:ye(e)?o(e,{starts:e.starts?o(e.starts):null}):Object.isFrozen(e)?o(e):e}var _e="11.3.1";class xe extends Error{constructor(e,t){super(e),this.name="HTMLInjectionError",this.html=t}}const ke=s,Ce=o,Se=Symbol("nomatch"),Ee=7,Oe=function(e){const t=Object.create(null),n=Object.create(null),i=[];let s=!0;const o="Could not find the language '{}', did you forget to load/include a language module?",c={disableAutodetect:!0,name:"Plain text",contains:[]};let l={ignoreUnescapedHTML:!1,throwUnescapedHTML:!1,noHighlightRe:/^(no-?highlight)$/i,languageDetectRe:/\blang(?:uage)?-([\w-]+)\b/i,classPrefix:"hljs-",cssSelector:"pre code",languages:null,__emitter:h};function u(e){return l.noHighlightRe.test(e)}function d(e){let t=e.className+" ";t+=e.parentNode?e.parentNode.className:"";const n=l.languageDetectRe.exec(t);if(n){const t=$(n[1]);return t||(ue(o.replace("{}",n[1])),ue("Falling back to no-highlight mode for this block.",e)),t?n[1]:"no-highlight"}return t.split(/\s+/).find(e=>u(e)||$(e))}function p(e,t,n){let i="",a="";"object"===typeof t?(i=e,n=t.ignoreIllegals,a=t.language):(de("10.7.0","highlight(lang, code, ...args) has been deprecated."),de("10.7.0","Please use highlight(code, options) instead.\nhttps://github.com/highlightjs/highlight.js/issues/2277"),a=e,i=t),void 0===n&&(n=!0);const r={code:i,language:a};V("before:highlight",r);const s=r.result?r.result:m(r.language,r.code,n);return s.code=r.code,V("after:highlight",s),s}function m(e,n,i,a){const c=Object.create(null);function u(e,t){return e.keywords[t]}function d(){if(!j.keywords)return void A.addText(L);let e=0;j.keywordPatternRe.lastIndex=0;let t=j.keywordPatternRe.exec(L),n="";while(t){n+=L.substring(e,t.index);const i=E.case_insensitive?t[0].toLowerCase():t[0],a=u(j,i);if(a){const[e,r]=a;if(A.addText(n),n="",c[i]=(c[i]||0)+1,c[i]<=Ee&&(B+=r),e.startsWith("_"))n+=t[0];else{const n=E.classNameAliases[e]||e;A.addKeyword(t[0],n)}}else n+=t[0];e=j.keywordPatternRe.lastIndex,t=j.keywordPatternRe.exec(L)}n+=L.substr(e),A.addText(n)}function p(){if(""===L)return;let e=null;if("string"===typeof j.subLanguage){if(!t[j.subLanguage])return void A.addText(L);e=m(j.subLanguage,L,!0,T[j.subLanguage]),T[j.subLanguage]=e._top}else e=_(L,j.subLanguage.length?j.subLanguage:null);j.relevance>0&&(B+=e.relevance),A.addSublanguage(e._emitter,e.language)}function h(){null!=j.subLanguage?p():d(),L=""}function f(e,t){let n=1;while(void 0!==t[n]){if(!e._emit[n]){n++;continue}const i=E.classNameAliases[e[n]]||e[n],a=t[n];i?A.addKeyword(a,i):(L=a,d(),L=""),n++}}function g(e,t){return e.scope&&"string"===typeof e.scope&&A.openNode(E.classNameAliases[e.scope]||e.scope),e.beginScope&&(e.beginScope._wrap?(A.addKeyword(L,E.classNameAliases[e.beginScope._wrap]||e.beginScope._wrap),L=""):e.beginScope._multi&&(f(e.beginScope,t),L="")),j=Object.create(e,{parent:{value:j}}),j}function b(e,t,n){let i=x(e.endRe,n);if(i){if(e["on:end"]){const n=new r(e);e["on:end"](t,n),n.isMatchIgnored&&(i=!1)}if(i){while(e.endsParent&&e.parent)e=e.parent;return e}}if(e.endsWithParent)return b(e.parent,t,n)}function v(e){return 0===j.matcher.regexIndex?(L+=e[0],1):(P=!0,0)}function y(e){const t=e[0],n=e.rule,i=new r(n),a=[n.__beforeBegin,n["on:begin"]];for(const r of a)if(r&&(r(e,i),i.isMatchIgnored))return v(t);return n.skip?L+=t:(n.excludeBegin&&(L+=t),h(),n.returnBegin||n.excludeBegin||(L=t)),g(n,e),n.returnBegin?0:t.length}function w(e){const t=e[0],i=n.substr(e.index),a=b(j,e,i);if(!a)return Se;const r=j;j.endScope&&j.endScope._wrap?(h(),A.addKeyword(t,j.endScope._wrap)):j.endScope&&j.endScope._multi?(h(),f(j.endScope,e)):r.skip?L+=t:(r.returnEnd||r.excludeEnd||(L+=t),h(),r.excludeEnd&&(L=t));do{j.scope&&A.closeNode(),j.skip||j.subLanguage||(B+=j.relevance),j=j.parent}while(j!==a.parent);return a.starts&&g(a.starts,e),r.returnEnd?0:t.length}function k(){const e=[];for(let t=j;t!==E;t=t.parent)t.scope&&e.unshift(t.scope);e.forEach(e=>A.openNode(e))}let C={};function S(t,a){const r=a&&a[0];if(L+=t,null==r)return h(),0;if("begin"===C.type&&"end"===a.type&&C.index===a.index&&""===r){if(L+=n.slice(a.index,a.index+1),!s){const t=new Error(`0 width match regex (${e})`);throw t.languageName=e,t.badRule=C.rule,t}return 1}if(C=a,"begin"===a.type)return y(a);if("illegal"===a.type&&!i){const e=new Error('Illegal lexeme "'+r+'" for mode "'+(j.scope||"<unnamed>")+'"');throw e.mode=j,e}if("end"===a.type){const e=w(a);if(e!==Se)return e}if("illegal"===a.type&&""===r)return 1;if(M>1e5&&M>3*a.index){const e=new Error("potential infinite loop, way more iterations than matches");throw e}return L+=r,r.length}const E=$(e);if(!E)throw le(o.replace("{}",e)),new Error('Unknown language: "'+e+'"');const O=ve(E);let I="",j=a||O;const T={},A=new l.__emitter(l);k();let L="",B=0,N=0,M=0,P=!1;try{for(j.matcher.considerAll();;){M++,P?P=!1:j.matcher.considerAll(),j.matcher.lastIndex=N;const e=j.matcher.exec(n);if(!e)break;const t=n.substring(N,e.index),i=S(t,e);N=e.index+i}return S(n.substr(N)),A.closeAllNodes(),A.finalize(),I=A.toHTML(),{language:e,value:I,relevance:B,illegal:!1,_emitter:A,_top:j}}catch(R){if(R.message&&R.message.includes("Illegal"))return{language:e,value:ke(n),illegal:!0,relevance:0,_illegalBy:{message:R.message,index:N,context:n.slice(N-100,N+100),mode:R.mode,resultSoFar:I},_emitter:A};if(s)return{language:e,value:ke(n),illegal:!1,relevance:0,errorRaised:R,_emitter:A,_top:j};throw R}}function y(e){const t={value:ke(e),illegal:!1,relevance:0,_top:c,_emitter:new l.__emitter(l)};return t._emitter.addText(e),t}function _(e,n){n=n||l.languages||Object.keys(t);const i=y(e),a=n.filter($).filter(M).map(t=>m(t,e,!1));a.unshift(i);const r=a.sort((e,t)=>{if(e.relevance!==t.relevance)return t.relevance-e.relevance;if(e.language&&t.language){if($(e.language).supersetOf===t.language)return 1;if($(t.language).supersetOf===e.language)return-1}return 0}),[s,o]=r,c=s;return c.secondBest=o,c}function k(e,t,i){const a=t&&n[t]||i;e.classList.add("hljs"),e.classList.add("language-"+a)}function C(e){let t=null;const n=d(e);if(u(n))return;if(V("before:highlightElement",{el:e,language:n}),e.children.length>0&&(l.ignoreUnescapedHTML||(console.warn("One of your code blocks includes unescaped HTML. This is a potentially serious security risk."),console.warn("https://github.com/highlightjs/highlight.js/issues/2886"),console.warn(e)),l.throwUnescapedHTML)){const t=new xe("One of your code blocks includes unescaped HTML.",e.innerHTML);throw t}t=e;const i=t.textContent,a=n?p(i,{language:n,ignoreIllegals:!0}):_(i);e.innerHTML=a.value,k(e,n,a.language),e.result={language:a.language,re:a.relevance,relevance:a.relevance},a.secondBest&&(e.secondBest={language:a.secondBest.language,relevance:a.secondBest.relevance}),V("after:highlightElement",{el:e,result:a,text:i})}function S(e){l=Ce(l,e)}const E=()=>{j(),de("10.6.0","initHighlighting() deprecated.  Use highlightAll() now.")};function O(){j(),de("10.6.0","initHighlightingOnLoad() deprecated.  Use highlightAll() now.")}let I=!1;function j(){if("loading"===document.readyState)return void(I=!0);const e=document.querySelectorAll(l.cssSelector);e.forEach(C)}function T(){I&&j()}function A(n,i){let a=null;try{a=i(e)}catch(r){if(le("Language definition for '{}' could not be registered.".replace("{}",n)),!s)throw r;le(r),a=c}a.name||(a.name=n),t[n]=a,a.rawDefinition=i.bind(null,e),a.aliases&&N(a.aliases,{languageName:n})}function L(e){delete t[e];for(const t of Object.keys(n))n[t]===e&&delete n[t]}function B(){return Object.keys(t)}function $(e){return e=(e||"").toLowerCase(),t[e]||t[n[e]]}function N(e,{languageName:t}){"string"===typeof e&&(e=[e]),e.forEach(e=>{n[e.toLowerCase()]=t})}function M(e){const t=$(e);return t&&!t.disableAutodetect}function P(e){e["before:highlightBlock"]&&!e["before:highlightElement"]&&(e["before:highlightElement"]=t=>{e["before:highlightBlock"](Object.assign({block:t.el},t))}),e["after:highlightBlock"]&&!e["after:highlightElement"]&&(e["after:highlightElement"]=t=>{e["after:highlightBlock"](Object.assign({block:t.el},t))})}function R(e){P(e),i.push(e)}function V(e,t){const n=e;i.forEach((function(e){e[n]&&e[n](t)}))}function D(e){return de("10.7.0","highlightBlock will be removed entirely in v12.0"),de("10.7.0","Please use highlightElement now."),C(e)}"undefined"!==typeof window&&window.addEventListener&&window.addEventListener("DOMContentLoaded",T,!1),Object.assign(e,{highlight:p,highlightAuto:_,highlightAll:j,highlightElement:C,highlightBlock:D,configure:S,initHighlighting:E,initHighlightingOnLoad:O,registerLanguage:A,unregisterLanguage:L,listLanguages:B,getLanguage:$,registerAliases:N,autoDetection:M,inherit:Ce,addPlugin:R}),e.debugMode=function(){s=!1},e.safeMode=function(){s=!0},e.versionString=_e,e.regex={concat:v,lookahead:f,either:w,optional:b,anyNumberOfTimes:g};for(const r in Z)"object"===typeof Z[r]&&a(Z[r]);return Object.assign(e,Z),e};var Ie=Oe({});e.exports=Ie,Ie.HighlightJS=Ie,Ie.default=Ie},"12b1":function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));const i={list:"list",compactGrid:"compactGrid",detailedGrid:"detailedGrid",hidden:"hidden"}},1417:function(e,t,n){var i={"./markdown":["84cb","highlight-js-custom-markdown"],"./markdown.js":["84cb","highlight-js-custom-markdown"],"./swift":["81c8","highlight-js-custom-swift"],"./swift.js":["81c8","highlight-js-custom-swift"]};function a(e){if(!n.o(i,e))return Promise.resolve().then((function(){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}));var t=i[e],a=t[0];return n.e(t[1]).then((function(){return n(a)}))}a.keys=function(){return Object.keys(i)},a.id="1417",e.exports=a},"146e":function(e,t,n){"use strict";var i=n("e425"),a=n("dd18"),r=n("8a61");function s(e){return new Promise((t,n)=>{e.complete?t():(e.addEventListener("load",t,{once:!0}),e.addEventListener("error",n,{once:!0}))})}function o(){return Promise.allSettled([...document.getElementsByTagName("img")].map(s))}t["a"]={mixins:[r["a"]],mounted(){this.scrollToElementIfAnchorPresent()},updated(){this.scrollToElementIfAnchorPresent()},methods:{async scrollToElementIfAnchorPresent(){const{hash:e}=this.$route;if(!e)return;const{imageLoadingStrategy:t}=i["a"].state;i["a"].setImageLoadingStrategy(a["a"].eager),await this.$nextTick(),await o(),this.scrollToElement(e),i["a"].setImageLoadingStrategy(t)}}}},"159b":function(e,t,n){"use strict";n("2d6f")},"17a4":function(e,t,n){"use strict";n("bbe1")},"19cc":function(e,t,n){},"1f39":function(e,t,n){"use strict";n("007b")},2224:function(e,t,n){"use strict";n("b392")},"25a9":function(e,t,n){"use strict";n.d(t,"c",(function(){return d})),n.d(t,"e",(function(){return p})),n.d(t,"b",(function(){return h})),n.d(t,"a",(function(){return m})),n.d(t,"d",(function(){return f}));var i=n("748c"),a=n("d26a"),r=n("3bdd");class s extends Error{constructor({location:e,response:t}){super("Request redirected"),this.location=e,this.response=t}}class o extends Error{constructor(e){super("Unable to fetch data"),this.route=e}}async function c(e,t={},n={}){function i(e){return("ide"!==Object({NODE_ENV:"production",VUE_APP_TITLE:"Documentation",BASE_URL:"{{BASE_PATH}}/"}).VUE_APP_TARGET||0!==e.status)&&!e.ok}const o=Object(a["c"])(e),c=Object(a["d"])(t);c&&(o.search=c);const l=await fetch(o.href,n);if(i(l))throw l;if(l.redirected)throw new s({location:l.url,response:l});const u=await l.json();return Object(r["c"])(u.schemaVersion),u}function l(e){const t=e.replace(/\/$/,"");return Object(i["c"])(["/data",t])+".json"}function u(e){const{pathname:t,search:n}=new URL(e),i=/\/data(\/.*).json$/,a=i.exec(t);return a?a[1]+n:t+n}async function d(e,t,n){const i=l(e.path);let a;try{a=await c(i,e.query)}catch(r){if("ide"===Object({NODE_ENV:"production",VUE_APP_TITLE:"Documentation",BASE_URL:"{{BASE_PATH}}/"}).VUE_APP_TARGET)throw console.error(r),!1;if(r instanceof s)throw u(r.location);r.status&&404===r.status?n({name:"not-found",params:[e.path]}):n(new o(e))}return a}function p(e,t){return!Object(a["a"])(e,t)}async function h(e,t={}){const n=l(e);return c(n,{},t)}function m(e){return JSON.parse(JSON.stringify(e))}async function f({slug:e}){const t=Object(a["c"])(["/index/",e,"index.json"]);return c(t)}},"2cae":function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return s}));var i=n("31d4"),a=n("66cd");const r={blue:"blue",teal:"teal",orange:"orange",purple:"purple",green:"green",sky:"sky",pink:"pink"},s={[i["b"].article]:r.teal,[i["b"].init]:r.blue,[i["b"].case]:r.orange,[i["b"].class]:r.purple,[i["b"].collection]:r.pink,[a["a"].collectionGroup]:r.teal,[i["b"].dictionarySymbol]:r.purple,[i["b"].enum]:r.orange,[i["b"].extension]:r.orange,[i["b"].func]:r.green,[i["b"].op]:r.green,[i["b"].httpRequest]:r.green,[i["b"].module]:r.sky,[i["b"].method]:r.blue,[i["b"].macro]:r.pink,[i["b"].protocol]:r.purple,[i["b"].property]:r.teal,[i["b"].propertyListKey]:r.green,[i["b"].propertyListKeyReference]:r.green,[i["b"].sampleCode]:r.purple,[i["b"].struct]:r.purple,[i["b"].subscript]:r.blue,[i["b"].typealias]:r.orange,[i["b"].union]:r.purple,[i["b"].var]:r.purple}},"2d6f":function(e,t,n){},"2f34":function(e,t,n){"use strict";t["a"]={inject:{store:{default:()=>({state:{references:{}},setReferences(){},reset(){}})}},computed:{references:({store:e})=>e.state.references}}},3024:function(e,t,n){"use strict";var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("SVGIcon",{staticClass:"technology-icon",attrs:{viewBox:"0 0 14 14",themeId:"technology"}},[n("path",{attrs:{d:"M3.39,9l3.16,**********.47-.28L10.61,9l.45.26,1.08.63L7,12.91l-5.16-3,1.08-.64L3.39,9M7,0,0,4.1,2.47,5.55,0,7,2.47,8.44,0,9.9,7,14l7-4.1L11.53,8.45,14,7,11.53,5.56,14,4.1ZM7,7.12,5.87,6.45l-1.54-.9L3.39,5,1.85,4.1,7,1.08l5.17,3L10.6,5l-.93.55-1.54.91ZM7,10,3.39,7.9,1.85,7,3.4,6.09,4.94,7,7,8.2,9.06,7,10.6,6.1,12.15,7l-1.55.9Z"}})])},a=[],r=n("be08"),s={name:"TechnologyIcon",components:{SVGIcon:r["a"]}},o=s,c=n("2877"),l=Object(c["a"])(o,i,a,!1,null,null,null);t["a"]=l.exports},"308e":function(e,t,n){"use strict";var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"column",style:e.style},[e._t("default")],2)},a=[],r={name:"Column",props:{span:{type:Number,default:null}},computed:{style:({span:e})=>({"--col-span":e})}},s=r,o=(n("fe08"),n("2877")),c=Object(o["a"])(s,i,a,!1,null,"0f654188",null);t["a"]=c.exports},"31d4":function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"a",(function(){return a}));const i={article:"article",associatedtype:"associatedtype",buildSetting:"buildSetting",case:"case",collection:"collection",class:"class",container:"container",dictionarySymbol:"dictionarySymbol",enum:"enum",extension:"extension",func:"func",groupMarker:"groupMarker",httpRequest:"httpRequest",init:"init",languageGroup:"languageGroup",learn:"learn",macro:"macro",method:"method",module:"module",op:"op",overview:"overview",project:"project",property:"property",propertyListKey:"propertyListKey",propertyListKeyReference:"propertyListKeyReference",protocol:"protocol",resources:"resources",root:"root",sampleCode:"sampleCode",section:"section",struct:"struct",subscript:"subscript",symbol:"symbol",tutorial:"tutorial",typealias:"typealias",union:"union",var:"var"},a={[i.init]:i.method,[i.case]:i.enum,[i.propertyListKeyReference]:i.propertyListKey,[i.project]:i.tutorial}},"3b75":function(e,t,n){},"3b96":function(e,t,n){"use strict";var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("SVGIcon",{staticClass:"curly-brackets-icon",attrs:{viewBox:"0 0 14 14",themeId:"curly-brackets"}},[n("path",{attrs:{d:"M9.987 14h-0.814v-0.916h0.36c0.137 0 0.253-0.038 0.349-0.116 0.099-0.080 0.179-0.188 0.239-0.318 0.064-0.134 0.11-0.298 0.139-0.483 0.031-0.186 0.045-0.38 0.045-0.58v-2.115c0-0.417 0.046-0.781 0.139-1.083 0.092-0.3 0.2-0.554 0.322-0.754 0.127-0.203 0.246-0.353 0.366-0.458 0.087-0.076 0.155-0.131 0.207-0.169-0.052-0.037-0.12-0.093-0.207-0.167-0.12-0.105-0.239-0.255-0.366-0.459-0.122-0.2-0.23-0.453-0.322-0.754-0.093-0.3-0.139-0.665-0.139-1.082v-2.13c0-0.199-0.014-0.392-0.045-0.572-0.029-0.182-0.076-0.345-0.139-0.483-0.060-0.137-0.141-0.246-0.239-0.328-0.095-0.076-0.212-0.115-0.349-0.115h-0.36v-0.916h0.814c0.442 0 0.788 0.18 1.030 0.538 0.238 0.352 0.358 0.826 0.358 1.407v2.236c0 0.3 0.015 0.597 0.044 0.886 0.030 0.287 0.086 0.544 0.164 0.765 0.077 0.216 0.184 0.392 0.318 0.522 0.129 0.124 0.298 0.188 0.503 0.188h0.058v0.916h-0.058c-0.206 0-0.374 0.064-0.503 0.188-0.134 0.129-0.242 0.305-0.318 0.521-0.078 0.223-0.134 0.48-0.164 0.766-0.029 0.288-0.044 0.587-0.044 0.884v2.236c0 0.582-0.12 1.055-0.358 1.409-0.242 0.358-0.588 0.538-1.030 0.538z"}}),n("path",{attrs:{d:"M4.827 14h-0.814c-0.442 0-0.788-0.18-1.030-0.538-0.238-0.352-0.358-0.825-0.358-1.409v-2.221c0-0.301-0.015-0.599-0.045-0.886-0.029-0.287-0.085-0.544-0.163-0.764-0.077-0.216-0.184-0.393-0.318-0.522-0.131-0.127-0.296-0.188-0.503-0.188h-0.058v-0.916h0.058c0.208 0 0.373-0.063 0.503-0.188 0.135-0.129 0.242-0.304 0.318-0.522 0.078-0.22 0.134-0.477 0.163-0.765 0.030-0.286 0.045-0.585 0.045-0.886v-2.251c0-0.582 0.12-1.055 0.358-1.407 0.242-0.358 0.588-0.538 1.030-0.538h0.814v0.916h-0.36c-0.138 0-0.252 0.038-0.349 0.116-0.099 0.079-0.179 0.189-0.239 0.327-0.064 0.139-0.11 0.302-0.141 0.483-0.029 0.18-0.044 0.373-0.044 0.572v2.13c0 0.417-0.046 0.782-0.138 1.082-0.092 0.302-0.201 0.556-0.324 0.754-0.123 0.201-0.246 0.356-0.366 0.459-0.086 0.074-0.153 0.13-0.206 0.167 0.052 0.038 0.12 0.093 0.206 0.169 0.12 0.103 0.243 0.258 0.366 0.458s0.232 0.453 0.324 0.754c0.092 0.302 0.138 0.666 0.138 1.083v2.115c0 0.2 0.015 0.394 0.044 0.58 0.030 0.186 0.077 0.349 0.139 0.482 0.062 0.132 0.142 0.239 0.241 0.32 0.096 0.079 0.21 0.116 0.349 0.116h0.36z"}})])},a=[],r=n("be08"),s={name:"CurlyBracketsIcon",components:{SVGIcon:r["a"]}},o=s,c=n("2877"),l=Object(c["a"])(o,i,a,!1,null,null,null);t["a"]=l.exports},"3ba9":function(e,t,n){},"3bdd":function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return s})),n.d(t,"c",(function(){return d}));const i={major:0,minor:3,patch:0};function a({major:e,minor:t,patch:n}){return[e,t,n].join(".")}function r(e){const[t=0,n=0,i=0]=e.split(".");return[Number(t),Number(n),Number(i)]}function s(e,t){const n=r(e),i=r(t);for(let a=0;a<n.length;a+=1){if(n[a]>i[a])return 1;if(n[a]<i[a])return-1}return 0}const o=a(i);function c(e){return`[Swift-DocC-Render] The render node version for this page has a higher minor version (${e}) than Swift-DocC-Render supports (${o}). Compatibility is not guaranteed.`}const l=e=>`[Swift-DocC-Render] The render node version for this page (${e}) has a different major version component than Swift-DocC-Render supports (${o}). Compatibility is not guaranteed.`;function u(e){const{major:t,minor:n}=e,{major:r,minor:s}=i;return t!==r?l(a(e)):n>s?c(a(e)):""}function d(e){if(!e)return;const t=u(e);t&&console.warn(t)}},"43fe":function(e,t,n){"use strict";n("4573")},4573:function(e,t,n){},"47cc":function(e,t,n){},"49e3":function(e,t,n){},"517a":function(e,t,n){"use strict";n("8222")},5201:function(e,t,n){"use strict";n("0cf0")},"52e4":function(e,t,n){"use strict";var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("WordBreak",{attrs:{tag:"code"}},[e._t("default")],2)},a=[],r=n("7b1f"),s={name:"CodeVoice",components:{WordBreak:r["a"]}},o=s,c=(n("8c92"),n("2877")),l=Object(c["a"])(o,i,a,!1,null,"05f4a5b7",null);t["a"]=l.exports},5416:function(e,t,n){"use strict";var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"device-frame",class:e.classes,style:e.styles,attrs:{"data-device":e.device}},[n("div",{staticClass:"device-screen",class:{"with-device":e.currentDeviceAttrs}},[e._t("default")],2),n("div",{staticClass:"device"})])},a=[],r={},s=n("6842");const o=e=>e&&e!==1/0,c=(e,t=4)=>o(e)?+`${Math.round(`${e}e+${t}`)}e-${t}`:null;var l={name:"DeviceFrame",props:{device:{type:String,required:!0}},provide:{insideDeviceFrame:!0},computed:{currentDeviceAttrs:({device:e})=>Object(s["c"])(["theme","device-frames",e],r[e]),styles:({toPixel:e,toUrl:t,toPct:n,currentDeviceAttrs:i={}})=>{const{screenTop:a,screenLeft:r,screenWidth:s,frameWidth:o,lightUrl:l,darkUrl:u,screenHeight:d,frameHeight:p}=i;return{"--screen-top":n(a/p),"--screen-left":n(r/o),"--screen-width":n(s/o),"--screen-height":n(d/p),"--screen-aspect":c(s/d)||null,"--frame-width":e(o),"--frame-aspect":c(o/p)||null,"--device-light-url":t(l),"--device-dark-url":t(u)}},classes:({currentDeviceAttrs:e})=>({"no-device":!e})},methods:{toPixel:e=>o(e)?e+"px":null,toUrl:e=>o(e)?`url(${e})`:null,toPct:e=>o(e)?c(100*e)+"%":null}},u=l,d=(n("b2da"),n("2877")),p=Object(d["a"])(u,i,a,!1,null,"28ae8fd3",null);t["a"]=p.exports},5677:function(e,t,n){"use strict";n.r(t),n.d(t,"BlockType",(function(){return Ct}));var i=n("2f34"),a=n("e3ab"),r=n("7b69"),s=n("5dcc"),o=n("52e4"),c=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"DictionaryExample"},[e._t("default"),n("CollapsibleCodeListing",{attrs:{content:e.example.content,showLineNumbers:""}})],2)},l=[],u=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"collapsible-code-listing",class:{"single-line":1===e.content[0].code.length}},[n("pre",[n("CodeBlock",e._l(this.content,(function(t,i){return n("div",{key:i,class:["container-general",{collapsible:!0===t.collapsible},{collapsed:!0===t.collapsible&&e.collapsed}]},e._l(t.code,(function(t,i){return n("div",{key:i,staticClass:"code-line-container"},[e._v("\n    "),n("div",{directives:[{name:"show",rawName:"v-show",value:e.showLineNumbers,expression:"showLineNumbers"}],staticClass:"code-number"}),e._v("\n    "),n("div",{staticClass:"code-line"},[e._v(e._s(t))]),e._v("\n  ")])})),0)})),0)],1)])},d=[],p=n("800b"),h={name:"CollapsibleCodeListing",components:{CodeBlock:p["a"]},props:{collapsed:{type:Boolean,required:!1},content:{type:Array,required:!0},showLineNumbers:{type:Boolean,default:()=>!0}}},m=h,f=(n("efc1"),n("2877")),g=Object(f["a"])(m,u,d,!1,null,"25a17a0e",null),b=g.exports,v={name:"DictionaryExample",components:{CollapsibleCodeListing:b},props:{example:{type:Object,required:!0}}},y=v,w=Object(f["a"])(y,c,l,!1,null,null,null),_=w.exports,x=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("Row",{staticClass:"endpoint-example"},[n("Column",{staticClass:"example-code"},[e._t("default"),n("Tabnav",{model:{value:e.currentTab,callback:function(t){e.currentTab=t},expression:"currentTab"}},[n("TabnavItem",{attrs:{value:e.Tab.request}},[e._v(e._s(e.$t("tab.request")))]),n("TabnavItem",{attrs:{value:e.Tab.response}},[e._v(e._s(e.$t("tab.response")))])],1),n("div",{staticClass:"output"},[e.isCurrent(e.Tab.request)?n("div",{staticClass:"code"},[n("CollapsibleCodeListing",e._b({attrs:{collapsed:e.isCollapsed,showLineNumbers:""}},"CollapsibleCodeListing",e.request,!1))],1):e._e(),e.isCurrent(e.Tab.response)?n("div",{staticClass:"code"},[n("CollapsibleCodeListing",e._b({attrs:{collapsed:e.isCollapsed,showLineNumbers:""}},"CollapsibleCodeListing",e.response,!1))],1):e._e()]),e.isCollapsible?n("div",{staticClass:"controls"},[e.isCollapsed?n("a",{staticClass:"toggle",attrs:{href:"#"},on:{click:function(t){return t.preventDefault(),e.showMore.apply(null,arguments)}}},[n("InlinePlusCircleSolidIcon",{staticClass:"control-icon icon-inline"}),e._v(" "+e._s(e.$t("more"))+" ")],1):n("a",{staticClass:"toggle",attrs:{href:"#"},on:{click:function(t){return t.preventDefault(),e.showLess.apply(null,arguments)}}},[n("InlineMinusCircleSolidIcon",{staticClass:"control-icon icon-inline"}),e._v(" "+e._s(e.$t("less"))+" ")],1)]):e._e()],2)],1)},k=[],C=n("0f00"),S=n("620a"),E=function(){var e,t=this,n=t.$createElement,i=t._self._c||n;return i("nav",{staticClass:"tabnav",class:(e={},e["tabnav--"+t.position]=t.position,e["tabnav--vertical"]=t.vertical,e)},[i("ul",{staticClass:"tabnav-items"},[t._t("default")],2)])},O=[];const I="tabnavData";var j={name:"Tabnav",constants:{ProvideKey:I},provide(){const e={selectTab:this.selectTab};return Object.defineProperty(e,"activeTab",{enumerable:!0,get:()=>this.value}),{[I]:e}},props:{position:{type:String,required:!1,validator:e=>new Set(["start","center","end"]).has(e)},vertical:{type:Boolean,default:!1},value:{type:[String,Number],required:!0}},methods:{selectTab(e){this.$emit("input",e)}}},T=j,A=(n("fb8e"),Object(f["a"])(T,E,O,!1,null,"5283512a",null)),L=A.exports,B=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("li",{staticClass:"tabnav-item"},[n("a",{staticClass:"tabnav-link",class:{active:e.isActive},attrs:{href:"#","aria-current":e.isActive?"true":"false"},on:{click:function(t){return t.preventDefault(),e.tabnavData.selectTab(e.value)}}},[e._t("default")],2)])},$=[],N={name:"TabnavItem",inject:{tabnavData:{default:{activeTab:null,selectTab:()=>{}}}},props:{value:{type:[String,Number],default:null}},computed:{isActive({tabnavData:e,value:t}){return e.activeTab===t}}},M=N,P=(n("6869"),Object(f["a"])(M,B,$,!1,null,"6aa9882a",null)),R=P.exports,V=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("SVGIcon",{staticClass:"inline-plus-circle-solid-icon",attrs:{viewBox:"0 0 14 14",themeId:"inline-plus-circle-solid"}},[n("path",{attrs:{d:"M7.005 0.5h-0.008c-1.791 0.004-3.412 0.729-4.589 1.9l0-0c-1.179 1.177-1.908 2.803-1.908 4.6 0 3.59 2.91 6.5 6.5 6.5s6.5-2.91 6.5-6.5c0-3.587-2.906-6.496-6.492-6.5h-0zM4.005 7.52v-1h2.5v-2.51h1v2.51h2.5v1h-2.501v2.49h-1v-2.49z"}})])},D=[],G=n("be08"),z={name:"InlinePlusCircleSolidIcon",components:{SVGIcon:G["a"]}},q=z,F=Object(f["a"])(q,V,D,!1,null,null,null),U=F.exports,W=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("SVGIcon",{staticClass:"inline-minus-circle-solid-icon",attrs:{viewBox:"0 0 14 14",themeId:"inline-minus-circle-solid"}},[n("path",{attrs:{d:"m6.98999129.48999129c3.58985091 0 6.50000001 2.91014913 6.50000001 6.5 0 3.58985091-2.9101491 6.50000001-6.50000001 6.50000001-3.58985087 0-6.5-2.9101491-6.5-6.50000001 0-3.58985087 2.91014913-6.5 6.5-6.5zm3 6.02001742h-6v1h6z","fill-rule":"evenodd"}})])},H=[],K={name:"InlineMinusCircleSolidIcon",components:{SVGIcon:G["a"]}},Z=K,Y=Object(f["a"])(Z,W,H,!1,null,null,null),X=Y.exports;const J={request:"Request",response:"Response"};var Q={name:"EndpointExample",components:{InlineMinusCircleSolidIcon:X,InlinePlusCircleSolidIcon:U,TabnavItem:R,Tabnav:L,CollapsibleCodeListing:b,Row:C["a"],Column:S["a"]},constants:{Tab:J},props:{request:{type:Object,required:!0},response:{type:Object,required:!0}},data(){return{isCollapsed:!0,currentTab:J.request}},computed:{Tab:()=>J,isCollapsible:({response:e,request:t,currentTab:n})=>{const i={[J.request]:t.content,[J.response]:e.content}[n]||[];return i.some(({collapsible:e})=>e)}},methods:{isCurrent(e){return this.currentTab===e},showMore(){this.isCollapsed=!1},showLess(){this.isCollapsed=!0}}},ee=Q,te=(n("b0e7"),Object(f["a"])(ee,x,k,!1,null,"c84e62a6",null)),ne=te.exports,ie=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("figure",{attrs:{id:e.anchor}},[e._t("default")],2)},ae=[],re={name:"Figure",props:{anchor:{type:String,required:!1}}},se=re,oe=Object(f["a"])(se,ie,ae,!1,null,null,null),ce=oe.exports,le=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n(e.tag,{tag:"component",staticClass:"caption",class:{trailing:e.trailing}},[e.title?[n("strong",[e._v(e._s(e.title))]),e._v(" "),e._t("default")]:[e._t("default")]],2)},ue=[];const de={caption:"caption",figcaption:"figcaption"},pe={leading:"leading",trailing:"trailing"};var he={name:"Caption",constants:{CaptionPosition:pe,CaptionTag:de},props:{title:{type:String,required:!1},tag:{type:String,required:!0,validator:e=>Object.hasOwnProperty.call(de,e)},position:{type:String,default:()=>pe.leading,validator:e=>Object.hasOwnProperty.call(pe,e)}},computed:{trailing:({position:e})=>e===pe.trailing}},me=he,fe=(n("6209"),Object(f["a"])(me,le,ue,!1,null,"1b76f4e0",null)),ge=fe.exports,be=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("ImageAsset",{attrs:{alt:e.alt,variants:e.variants}})},ve=[],ye=n("8bd9"),we={name:"InlineImage",components:{ImageAsset:ye["a"]},props:{alt:{type:String,default:""},variants:{type:Array,required:!0}}},_e=we,xe=(n("cb92"),Object(f["a"])(_e,be,ve,!1,null,"3a939631",null)),ke=xe.exports,Ce=n("86d8"),Se=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"table-wrapper"},[n("table",{class:{spanned:e.spanned}},[e._t("default")],2)])},Ee=[],Oe={name:"Table",props:{spanned:{type:Boolean,default:!1}}},Ie=Oe,je=(n("ec71"),Object(f["a"])(Ie,Se,Ee,!1,null,"5ed73c89",null)),Te=je.exports,Ae=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("s",{attrs:{"data-before-text":e.$t("accessibility.strike.start"),"data-after-text":e.$t("accessibility.strike.end")}},[e._t("default")],2)},Le=[],Be={name:"StrikeThrough"},$e=Be,Ne=(n("d0da"),Object(f["a"])($e,Ae,Le,!1,null,"7fc51673",null)),Me=Ne.exports,Pe=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("small",[e._t("default")],2)},Re=[],Ve={name:"Small"},De=Ve,Ge=(n("b0f5"),Object(f["a"])(De,Pe,Re,!1,null,"77035f61",null)),ze=Ge.exports,qe=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("Asset",{attrs:{identifier:e.identifier,"video-autoplays":!1,"video-muted":!1,showsReplayButton:!e.isClientMobile,showsVideoControls:e.isClientMobile,deviceFrame:e.deviceFrame}})},Fe=[],Ue=n("80e4"),We=n("7689"),He={name:"BlockVideo",mixins:[We["a"]],components:{Asset:Ue["a"]},props:{identifier:{type:String,required:!0},deviceFrame:{type:String,required:!1}}},Ke=He,Ze=(n("fe1c"),Object(f["a"])(Ke,qe,Fe,!1,null,"035a093f",null)),Ye=Ze.exports,Xe=n("308e"),Je=n("ee9e"),Qe=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"TabNavigator",class:[{"tabs--vertical":e.vertical}]},[n("Tabnav",e._b({model:{value:e.currentTitle,callback:function(t){e.currentTitle=t},expression:"currentTitle"}},"Tabnav",{position:e.position,vertical:e.vertical},!1),e._l(e.titles,(function(t){return n("TabnavItem",{key:t,attrs:{value:t}},[e._v(" "+e._s(t)+" ")])})),1),n("div",{staticClass:"tabs-content"},[n("div",{staticClass:"tabs-content-container"},[n("transition-group",{attrs:{name:"fade"}},[e._l(e.titles,(function(t){return[n("div",{directives:[{name:"show",rawName:"v-show",value:t===e.currentTitle,expression:"title === currentTitle"}],key:t,staticClass:"tab-container",class:{active:t===e.currentTitle}},[e._t(t)],2)]}))],2)],1)])],1)},et=[],tt={name:"TabNavigator",components:{TabnavItem:R,Tabnav:L},props:{vertical:{type:Boolean,default:!1},position:{type:String,default:"start",validator:e=>new Set(["start","center","end"]).has(e)},titles:{type:Array,required:!0,default:()=>[]}},data(){return{currentTitle:this.titles[0]}},watch:{titles(e,t){if(e.length<t.length){if(!e.includes(this.currentTitle)){const[t]=e;this.currentTitle=t}}else{const n=e.find(e=>!t.includes(e));this.currentTitle=n||this.currentTitle}}}},nt=tt,it=(n("9ed5"),Object(f["a"])(nt,Qe,et,!1,null,"e671a734",null)),at=it.exports,rt=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("ul",{staticClass:"tasklist"},e._l(e.tasks,(function(t,i){return n("li",{key:i},[e.showCheckbox(t)?n("input",{attrs:{type:"checkbox",disabled:""},domProps:{checked:t.checked}}):e._e(),e._t("task",null,{task:t})],2)})),0)},st=[];const ot="checked",ct=e=>Object.hasOwnProperty.call(e,ot);var lt={name:"TaskList",props:{tasks:{required:!0,type:Array,validator:e=>e.some(ct)}},methods:{showCheckbox:ct}},ut=lt,dt=(n("c3da"),Object(f["a"])(ut,rt,st,!1,null,"6a56a858",null)),pt=dt.exports,ht=function(){var e=this,t=e.$createElement,n=e._self._c||t;return e.isListStyle?n("div",{staticClass:"links-block"},e._l(e.items,(function(e){return n("TopicsLinkBlock",{key:e.identifier,staticClass:"topic-link-block",attrs:{topic:e}})})),1):n("TopicsLinkCardGrid",{staticClass:"links-block",attrs:{items:e.items,"topic-style":e.blockStyle}})},mt=[],ft=n("70fb"),gt=n("12b1"),bt={name:"LinksBlock",mixins:[i["a"]],components:{TopicsLinkBlock:()=>n.e("chunk-c0335d80").then(n.bind(null,"2a18")),TopicsLinkCardGrid:ft["a"]},props:{identifiers:{type:Array,required:!0},blockStyle:{type:String,default:gt["a"].compactGrid}},computed:{isListStyle:({blockStyle:e})=>e===gt["a"].list,items:({identifiers:e,references:t})=>e.reduce((e,n)=>t[n]?e.concat(t[n]):e,[])}},vt=bt,yt=(n("6e71"),Object(f["a"])(vt,ht,mt,!1,null,"ce6f87f6",null)),wt=yt.exports,_t=n("5416");const{CaptionPosition:xt,CaptionTag:kt}=ge.constants,Ct={aside:"aside",codeListing:"codeListing",endpointExample:"endpointExample",heading:"heading",orderedList:"orderedList",paragraph:"paragraph",table:"table",termList:"termList",unorderedList:"unorderedList",dictionaryExample:"dictionaryExample",small:"small",video:"video",row:"row",tabNavigator:"tabNavigator",links:"links"},St={codeVoice:"codeVoice",emphasis:"emphasis",image:"image",inlineHead:"inlineHead",link:"link",newTerm:"newTerm",reference:"reference",strong:"strong",text:"text",superscript:"superscript",subscript:"subscript",strikethrough:"strikethrough"},Et={both:"both",column:"column",none:"none",row:"row"},Ot={left:"left",right:"right",center:"center",unset:"unset"},It=7;function jt(e,t){const n=n=>n.map(jt(e,t)),i=t=>t.map(t=>e("li",{},n(t.content))),c=(t,i,a,r,s,o,c)=>{const{colspan:l,rowspan:u}=o[`${s}_${r}`]||{};if(0===l||0===u)return null;const d=c[r]||Ot.unset;let p=null;return d!==Ot.unset&&(p=d+"-cell"),e(t,{attrs:{...i,colspan:l,rowspan:u},class:p},n(a))},l=(t,n=Et.none,i={},a=[])=>{switch(n){case Et.both:{const[n,...r]=t;return[e("thead",{},[e("tr",{},n.map((e,t)=>c("th",{scope:"col"},e,t,0,i,a)))]),e("tbody",{},r.map(([t,...n],r)=>e("tr",{},[c("th",{scope:"row"},t,0,r+1,i,a),...n.map((e,t)=>c("td",{},e,t+1,r+1,i,a))])))]}case Et.column:return[e("tbody",{},t.map(([t,...n],r)=>e("tr",{},[c("th",{scope:"row"},t,0,r,i,a),...n.map((e,t)=>c("td",{},e,t+1,r,i,a))])))];case Et.row:{const[n,...r]=t;return[e("thead",{},[e("tr",{},n.map((e,t)=>c("th",{scope:"col"},e,t,0,i,a)))]),e("tbody",{},r.map((t,n)=>e("tr",{},t.map((e,t)=>c("td",{},e,t,n+1,i,a)))))]}default:return[e("tbody",{},t.map((t,n)=>e("tr",{},t.map((e,t)=>c("td",{},e,t,n,i,a)))))]}},u=({metadata:{abstract:t=[],anchor:i,title:a,...r},...s})=>{const o={...s,metadata:r},c=[n([o])];if(a&&t.length||t.length){const i=a?xt.leading:xt.trailing,r=i===xt.trailing?1:0,s=kt.figcaption;c.splice(r,0,e(ge,{props:{title:a,position:i,tag:s}},n(t)))}return e(ce,{props:{anchor:i}},c)},d=({metadata:{deviceFrame:t},...i})=>e(_t["a"],{props:{device:t}},n([i]));return function(c){switch(c.type){case Ct.aside:{const t={kind:c.style,name:c.name};return e(a["a"],{props:t},n(c.content))}case Ct.codeListing:{if(c.metadata&&c.metadata.anchor)return u(c);const t={syntax:c.syntax,fileType:c.fileType,content:c.code,showLineNumbers:c.showLineNumbers};return e(r["a"],{props:t})}case Ct.endpointExample:{const t={request:c.request,response:c.response};return e(ne,{props:t},n(c.summary||[]))}case Ct.heading:{const t={anchor:c.anchor,level:c.level};return e(s["a"],{props:t},c.text)}case Ct.orderedList:return e("ol",{attrs:{start:c.start}},i(c.items));case Ct.paragraph:{const t=1===c.inlineContent.length&&c.inlineContent[0].type===St.image,i=t?{class:["inline-image-container"]}:{};return e("p",i,n(c.inlineContent))}case Ct.table:{const t=l(c.rows,c.header,c.extendedData,c.alignments);if(c.metadata&&c.metadata.abstract){const{title:i}=c.metadata,a=i?xt.leading:xt.trailing,r=kt.caption;t.unshift(e(ge,{props:{title:i,position:a,tag:r}},n(c.metadata.abstract)))}return e(Te,{attrs:{id:c.metadata&&c.metadata.anchor},props:{spanned:!!c.extendedData}},t)}case Ct.termList:return e("dl",{},c.items.map(({term:t,definition:i})=>[e("dt",{},n(t.inlineContent)),e("dd",{},n(i.content))]));case Ct.unorderedList:{const t=e=>pt.props.tasks.validator(e.items);return t(c)?e(pt,{props:{tasks:c.items},scopedSlots:{task:e=>n(e.task.content)}}):e("ul",{},i(c.items))}case Ct.dictionaryExample:{const t={example:c.example};return e(_,{props:t},n(c.summary||[]))}case Ct.small:return e("p",{},[e(ze,{},n(c.inlineContent))]);case Ct.video:{if(c.metadata&&c.metadata.abstract)return u(c);if(!t[c.identifier])return null;const{deviceFrame:n}=c.metadata||{};return e(Ye,{props:{identifier:c.identifier,deviceFrame:n}})}case Ct.row:{const t=c.numberOfColumns?{large:c.numberOfColumns}:void 0;return e(Je["a"],{props:{columns:t}},c.columns.map(t=>e(Xe["a"],{props:{span:t.size}},n(t.content))))}case Ct.tabNavigator:{const t=c.tabs.length>It,i=c.tabs.map(e=>e.title),a=c.tabs.reduce((e,t)=>({...e,[t.title]:()=>n(t.content)}),{});return e(at,{props:{titles:i,vertical:t},scopedSlots:a})}case Ct.links:return e(wt,{props:{blockStyle:c.style,identifiers:c.items}});case St.codeVoice:return e(o["a"],{},c.code);case St.emphasis:case St.newTerm:return e("em",n(c.inlineContent));case St.image:{if(c.metadata&&(c.metadata.anchor||c.metadata.abstract))return u(c);const n=t[c.identifier];return n?c.metadata&&c.metadata.deviceFrame?d(c):e(ke,{props:{alt:n.alt,variants:n.variants}}):null}case St.link:return e("a",{attrs:{href:c.destination}},c.title);case St.reference:{const i=t[c.identifier];if(!i)return null;const a=c.overridingTitleInlineContent||i.titleInlineContent,r=c.overridingTitle||i.title;return e(Ce["a"],{props:{url:i.url,kind:i.kind,role:i.role,isActive:c.isActive,ideTitle:i.ideTitle,titleStyle:i.titleStyle,hasInlineFormatting:!!a}},a?n(a):r)}case St.strong:case St.inlineHead:return e("strong",n(c.inlineContent));case St.text:return"\n"===c.text?e("br"):c.text;case St.superscript:return e("sup",n(c.inlineContent));case St.subscript:return e("sub",n(c.inlineContent));case St.strikethrough:return e(Me,n(c.inlineContent));default:return null}}}var Tt,At,Lt={name:"ContentNode",constants:{TableHeaderStyle:Et,TableColumnAlignments:Ot},mixins:[i["a"]],render:function(e){return e(this.tag,{class:"content"},this.content.map(jt(e,this.references),this))},props:{content:{type:Array,required:!0},tag:{type:String,default:()=>"div"}},methods:{map(e){function t(n=[]){return n.map(n=>{switch(n.type){case Ct.aside:return e({...n,content:t(n.content)});case Ct.dictionaryExample:return e({...n,summary:t(n.summary)});case Ct.paragraph:case St.emphasis:case St.strong:case St.inlineHead:case St.superscript:case St.subscript:case St.strikethrough:case St.newTerm:return e({...n,inlineContent:t(n.inlineContent)});case Ct.orderedList:case Ct.unorderedList:return e({...n,items:n.items.map(e=>({...e,content:t(e.content)}))});case Ct.table:return e({...n,rows:n.rows.map(e=>e.map(t))});case Ct.termList:return e({...n,items:n.items.map(e=>({...e,term:{inlineContent:t(e.term.inlineContent)},definition:{content:t(e.definition.content)}}))});default:return e(n)}})}return t(this.content)},forEach(e){function t(n=[]){n.forEach(n=>{switch(e(n),n.type){case Ct.aside:t(n.content);break;case Ct.paragraph:case St.emphasis:case St.strong:case St.inlineHead:case St.newTerm:case St.superscript:case St.subscript:case St.strikethrough:t(n.inlineContent);break;case Ct.orderedList:case Ct.unorderedList:n.items.forEach(e=>t(e.content));break;case Ct.dictionaryExample:t(n.summary);break;case Ct.table:n.rows.forEach(e=>{e.forEach(t)});break;case Ct.termList:n.items.forEach(e=>{t(e.term.inlineContent),t(e.definition.content)});break}})}return t(this.content)},reduce(e,t){let n=t;return this.forEach(t=>{n=e(n,t)}),n}},computed:{plaintext(){return this.reduce((e,t)=>t.type===Ct.paragraph?e+"\n":t.type===St.text?`${e}${t.text}`:e,"").trim()}},BlockType:Ct,InlineType:St},Bt=Lt,$t=Object(f["a"])(Bt,Tt,At,!1,null,null,null);t["default"]=$t.exports},"598a":function(e,t,n){},"5c97":function(e,t,n){},"5da3":function(e,t,n){e.exports=n.p+"img/<EMAIL>"},"5dcc":function(e,t,n){"use strict";var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("h"+e.level,{tag:"component",attrs:{id:e.anchor}},[e.shouldLink?n("router-link",{staticClass:"header-anchor",attrs:{to:{hash:"#"+e.anchor}},on:{click:function(t){return e.handleFocusAndScroll(e.anchor)}}},[e._t("default"),n("span",{staticClass:"visuallyhidden"},[e._v(e._s(e.$t("accessibility.in-page-link")))]),n("LinkIcon",{staticClass:"icon",attrs:{"aria-hidden":"true"}})],2):[e._t("default")]],2)},a=[],r=n("8a61"),s=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("SVGIcon",{staticClass:"link-icon",attrs:{viewBox:"0 0 20 20"}},[n("path",{attrs:{d:"M19.34,4.88L15.12,.66c-.87-.87-2.3-.87-3.17,0l-3.55,3.56-1.38,1.38-1.4,1.4c-.47,.47-.68,1.09-.64,1.7,.02,.29,.09,.58,.21,.84,.11,.23,.24,.44,.43,.63l4.22,4.22h0l.53-.53,.53-.53h0l-4.22-4.22c-.29-.29-.29-.77,0-1.06l1.4-1.4,.91-.91,.58-.58,.55-.55,2.9-2.9c.29-.29,.77-.29,1.06,0l4.22,4.22c.29,.29,.29,.77,0,1.06l-2.9,2.9c.14,.24,.24,.49,.31,.75,.08,.32,.11,.64,.09,.96l3.55-3.55c.87-.87,.87-2.3,0-3.17Z"}}),n("path",{attrs:{d:"M14.41,9.82s0,0,0,0l-4.22-4.22h0l-.53,.53-.53,.53h0l4.22,4.22c.29,.29,.29,.77,0,1.06l-1.4,1.4-.91,.91-.58,.58-.55,.55h0l-2.9,2.9c-.29,.29-.77,.29-1.06,0L1.73,14.04c-.29-.29-.29-.77,0-1.06l2.9-2.9c-.14-.24-.24-.49-.31-.75-.08-.32-.11-.64-.09-.97L.68,11.93c-.87,.87-.87,2.3,0,3.17l4.22,4.22c.87,.87,2.3,.87,3.17,0l3.55-3.55,1.38-1.38,1.4-1.4c.47-.47,.68-1.09,.64-1.7-.02-.29-.09-.58-.21-.84-.11-.22-.24-.44-.43-.62Z"}})])},o=[],c=n("be08"),l={name:"LinkIcon",components:{SVGIcon:c["a"]}},u=l,d=n("2877"),p=Object(d["a"])(u,s,o,!1,null,null,null),h=p.exports,m={name:"LinkableHeading",mixins:[r["a"]],components:{LinkIcon:h},props:{anchor:{type:String,required:!1},level:{type:Number,default:()=>2,validator:e=>e>=1&&e<=6}},inject:{enableMinimized:{default:()=>!1},isTargetIDE:{default:()=>!1}},computed:{shouldLink:({anchor:e,enableMinimized:t,isTargetIDE:n})=>!!e&&!t&&!n}},f=m,g=(n("bbf6"),Object(d["a"])(f,i,a,!1,null,"6007a8a4",null));t["a"]=g.exports},6058:function(e,t,n){},6209:function(e,t,n){"use strict";n("0eaa")},"620a":function(e,t,n){"use strict";var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"col",class:e.classes},[e._t("default")],2)},a=[];const r=0,s=12,o=new Set(["large","medium","small"]),c=e=>({type:Object,default:()=>({}),validator:t=>Object.keys(t).every(n=>o.has(n)&&e(t[n]))}),l=c(e=>"boolean"===typeof e),u=c(e=>"number"===typeof e&&e>=r&&e<=s);var d={name:"GridColumn",props:{isCentered:l,isUnCentered:l,span:{...u,default:()=>({large:s})}},computed:{classes:function(){return{["large-"+this.span.large]:void 0!==this.span.large,["medium-"+this.span.medium]:void 0!==this.span.medium,["small-"+this.span.small]:void 0!==this.span.small,"large-centered":!!this.isCentered.large,"medium-centered":!!this.isCentered.medium,"small-centered":!!this.isCentered.small,"large-uncentered":!!this.isUnCentered.large,"medium-uncentered":!!this.isUnCentered.medium,"small-uncentered":!!this.isUnCentered.small}}}},p=d,h=(n("6e4a"),n("2877")),m=Object(h["a"])(p,i,a,!1,null,"2ee3ad8b",null);t["a"]=m.exports},"636c":function(e,t,n){},"63e6":function(e,t,n){},"64b5":function(e,t,n){},6667:function(e,t,n){"use strict";var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("SVGIcon",{staticClass:"diagonal-arrow",attrs:{viewBox:"0 0 14 14",themeId:"diagonal-arrow"}},[n("path",{attrs:{d:"M0.010 12.881l10.429-10.477-3.764 0.824-0.339-1.549 7.653-1.679-1.717 7.622-1.546-0.349 0.847-3.759-10.442 10.487z"}})])},a=[],r=n("be08"),s={name:"DiagonalArrowIcon",components:{SVGIcon:r["a"]}},o=s,c=n("2877"),l=Object(c["a"])(o,i,a,!1,null,null,null);t["a"]=l.exports},"66cd":function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));const i={article:"article",codeListing:"codeListing",collection:"collection",collectionGroup:"collectionGroup",containerSymbol:"containerSymbol",devLink:"devLink",dictionarySymbol:"dictionarySymbol",generic:"generic",link:"link",media:"media",pseudoCollection:"pseudoCollection",pseudoSymbol:"pseudoSymbol",restRequestSymbol:"restRequestSymbol",sampleCode:"sampleCode",symbol:"symbol",table:"table",learn:"learn",overview:"overview",project:"project",tutorial:"tutorial",resources:"resources"}},6869:function(e,t,n){"use strict";n("9649")},"6e4a":function(e,t,n){"use strict";n("05a1")},"6e71":function(e,t,n){"use strict";n("3ba9")},"70fb":function(e,t,n){"use strict";var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"TopicsLinkCardGrid"},[n("Row",{attrs:{columns:{large:e.compactCards?3:2,medium:2}}},e._l(e.items,(function(t){return n("Column",{key:t.title},[n("TopicsLinkCardGridItem",{attrs:{item:t,compact:e.compactCards}})],1)})),1)],1)},a=[],r=n("ee9e"),s=n("308e"),o=n("12b1"),c=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("Card",{staticClass:"reference-card-grid-item",attrs:{url:e.item.url,image:e.imageReferences.card,title:e.item.title,"floating-style":"",size:e.cardSize,"link-text":e.compact?"":e.$t(e.linkText)},scopedSlots:e._u([e.imageReferences.card?null:{key:"cover",fn:function(t){var i=t.classes;return[n("div",{staticClass:"reference-card-grid-item__image",class:i},[n("TopicTypeIcon",{staticClass:"reference-card-grid-item__icon",attrs:{type:e.item.role,"image-override":e.references[e.imageReferences.icon]}})],1)]}}],null,!0)},[e.compact?e._e():n("ContentNode",{attrs:{content:e.item.abstract}})],1)},l=[],u=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("Reference",e._b({staticClass:"card",class:e.classes,attrs:{url:e.url}},"Reference",e.linkAriaTags,!1),[n("CardCover",{attrs:{variants:e.imageVariants,rounded:e.floatingStyle,alt:e.imageReference.alt,"aria-hidden":"true"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._t("cover",null,null,t)]}}],null,!0)}),n("div",{staticClass:"details",attrs:{"aria-hidden":"true"}},[e.eyebrow?n("div",{staticClass:"eyebrow",attrs:{id:e.eyebrowId,"aria-label":e.formatAriaLabel("- "+e.eyebrow)}},[e._v(" "+e._s(e.eyebrow)+" ")]):e._e(),n("div",{staticClass:"title",attrs:{id:e.titleId}},[e._v(" "+e._s(e.title)+" ")]),e.$slots.default?n("div",{staticClass:"card-content",attrs:{id:e.contentId}},[e._t("default")],2):e._e(),e.linkText?n(e.hasButton?"ButtonLink":"div",{tag:"component",staticClass:"link"},[e._v(" "+e._s(e.linkText)+" "),e.showExternalLinks?n("DiagonalArrowIcon",{staticClass:"icon-inline link-icon"}):e.hasButton?e._e():n("InlineChevronRightIcon",{staticClass:"icon-inline link-icon"})],1):e._e()],1)],1)},d=[],p=n("76ab"),h=n("34b0"),m=n("6667"),f=n("86d8"),g={small:"small",large:"large"},b=n("2f34"),v=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"card-cover-wrap",class:{rounded:e.rounded}},[e._t("default",(function(){return[n("ImageAsset",{staticClass:"card-cover",attrs:{variants:e.variants,alt:e.alt}})]}),{classes:"card-cover"})],2)},y=[],w=n("8bd9"),_={name:"CardCover",components:{ImageAsset:w["a"]},props:{variants:{type:Array,required:!0},rounded:{type:Boolean,default:!1},alt:{type:String,default:null}}},x=_,k=(n("850e"),n("2877")),C=Object(k["a"])(x,v,y,!1,null,"0c1c40a1",null),S=C.exports,E={name:"Card",components:{Reference:f["a"],DiagonalArrowIcon:m["a"],InlineChevronRightIcon:h["a"],CardCover:S,ButtonLink:p["a"]},constants:{CardSize:g},mixins:[b["a"]],computed:{titleId:({_uid:e})=>"card_title_"+e,contentId:({_uid:e})=>"card_content_"+e,eyebrowId:({_uid:e})=>"card_eyebrow_"+e,linkAriaTags:({titleId:e,eyebrowId:t,contentId:n,eyebrow:i,$slots:a})=>({"aria-labelledby":e.concat(i?" "+t:""),"aria-describedby":a.default?""+n:null}),classes:({size:e,floatingStyle:t})=>[e,{"floating-style":t}],imageReference:({image:e,references:t})=>t[e]||{},imageVariants:({imageReference:e})=>e.variants||[]},props:{linkText:{type:String,required:!1},url:{type:String,required:!1,default:""},eyebrow:{type:String,required:!1},image:{type:String,required:!1},size:{type:String,validator:e=>Object.prototype.hasOwnProperty.call(g,e)},title:{type:String,required:!0},hasButton:{type:Boolean,default:()=>!1},floatingStyle:{type:Boolean,default:!1},showExternalLinks:{type:Boolean,default:!1},formatAriaLabel:{type:Function,default:e=>e}}},O=E,I=(n("0939"),Object(k["a"])(O,u,d,!1,null,"328d568a",null)),j=I.exports,T=n("f12c"),A=n("66cd");const L={[A["a"].article]:"documentation.card.read-article",[A["a"].overview]:"documentation.card.start-tutorial",[A["a"].collection]:"documentation.card.view-api",[A["a"].symbol]:"documentation.card.view-symbol",[A["a"].sampleCode]:"documentation.card.view-sample-code"};var B={name:"TopicsLinkCardGridItem",components:{TopicTypeIcon:T["a"],Card:j,ContentNode:()=>Promise.resolve().then(n.bind(null,"5677"))},mixins:[b["a"]],props:{item:{type:Object,required:!0},compact:{type:Boolean,default:!0}},computed:{imageReferences:({item:e})=>(e.images||[]).reduce((e,t)=>(e[t.type]=t.identifier,e),{icon:null,card:null}),linkText:({item:e})=>L[e.role]||"documentation.card.learn-more",cardSize:({compact:e})=>e?void 0:g.large}},$=B,N=(n("8f26"),Object(k["a"])($,c,l,!1,null,"08a5e3f8",null)),M=N.exports,P={name:"TopicsLinkCardGrid",components:{TopicsLinkCardGridItem:M,Column:s["a"],Row:r["a"]},props:{items:{type:Array,required:!0},topicStyle:{type:String,default:o["a"].compactGrid,validator:e=>e===o["a"].compactGrid||e===o["a"].detailedGrid}},computed:{compactCards:({topicStyle:e})=>e===o["a"].compactGrid}},R=P,V=Object(k["a"])(R,i,a,!1,null,null,null);t["a"]=V.exports},"72e7":function(e,t,n){"use strict";const i={up:"up",down:"down"};t["a"]={constants:{IntersectionDirections:i},data(){return{intersectionObserver:null,intersectionPreviousScrollY:0,intersectionScrollDirection:i.down}},computed:{intersectionThreshold(){const e=[];for(let t=0;t<=1;t+=.01)e.push(t);return e},intersectionRoot(){return null},intersectionRootMargin(){return"0px 0px 0px 0px"},intersectionObserverOptions(){return{root:this.intersectionRoot,rootMargin:this.intersectionRootMargin,threshold:this.intersectionThreshold}}},async mounted(){await n.e("chunk-2d0d3105").then(n.t.bind(null,"5abe",7)),this.intersectionObserver=new IntersectionObserver(e=>{this.detectIntersectionScrollDirection();const t=this.onIntersect;t?e.forEach(t):console.warn("onIntersect not implemented")},this.intersectionObserverOptions),this.getIntersectionTargets().forEach(e=>{this.intersectionObserver.observe(e)})},beforeDestroy(){this.intersectionObserver&&this.intersectionObserver.disconnect()},methods:{getIntersectionTargets(){return[this.$el]},detectIntersectionScrollDirection(){window.scrollY<this.intersectionPreviousScrollY?this.intersectionScrollDirection=i.down:window.scrollY>this.intersectionPreviousScrollY&&(this.intersectionScrollDirection=i.up),this.intersectionPreviousScrollY=window.scrollY}}}},"74ea":function(e,t,n){"use strict";n("636c")},7689:function(e,t,n){"use strict";t["a"]={computed:{isClientMobile(){let e=!1;return e="maxTouchPoints"in navigator||"msMaxTouchPoints"in navigator?Boolean(navigator.maxTouchPoints||navigator.msMaxTouchPoints):window.matchMedia?window.matchMedia("(pointer:coarse)").matches:"orientation"in window,e}}}},"76ab":function(e,t,n){"use strict";var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n(e.resolvedComponent,e._b({tag:"component",staticClass:"button-cta",class:{"is-dark":e.isDark}},"component",e.componentProps,!1),[e._t("default")],2)},a=[],r=n("86d8"),s={name:"ButtonLink",components:{Reference:r["a"]},props:{url:{type:String,required:!1},isDark:{type:Boolean,default:!1}},computed:{resolvedComponent:({url:e})=>e?r["a"]:"button",componentProps:({url:e})=>e?{url:e}:{}}},o=s,c=(n("0da1"),n("2877")),l=Object(c["a"])(o,i,a,!1,null,"c9c81868",null);t["a"]=l.exports},"7b1f":function(e,t,n){"use strict";var i,a,r={functional:!0,name:"WordBreak",render(e,{props:t,slots:n,data:i}){const a=n().default||[],r=a.filter(e=>e.text&&!e.tag);if(0===r.length||r.length!==a.length)return e(t.tag,i,a);const s=r.map(({text:e})=>e).join(),o=[];let c=null,l=0;while(null!==(c=t.safeBoundaryPattern.exec(s))){const t=c.index+1;o.push(s.slice(l,t)),o.push(e("wbr",{key:c.index})),l=t}return o.push(s.slice(l,s.length)),e(t.tag,i,o)},props:{safeBoundaryPattern:{type:RegExp,default:()=>/([a-z](?=[A-Z])|(:)\w|\w(?=[._]\w))/g},tag:{type:String,default:()=>"span"}}},s=r,o=n("2877"),c=Object(o["a"])(s,i,a,!1,null,null,null);t["a"]=c.exports},"7b69":function(e,t,n){"use strict";var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"code-listing",class:{"single-line":1===e.syntaxHighlightedLines.length},attrs:{"data-syntax":e.syntaxNameNormalized}},[e.fileName?n("Filename",{attrs:{isActionable:e.isFileNameActionable,fileType:e.fileType},on:{click:function(t){return e.$emit("file-name-click")}}},[e._v(e._s(e.fileName)+" ")]):e._e(),n("div",{staticClass:"container-general"},[n("pre",[n("CodeBlock",[e._l(e.syntaxHighlightedLines,(function(t,i){return[n("span",{key:i,class:["code-line-container",{highlighted:e.isHighlighted(i)}]},[e.showLineNumbers?n("span",{staticClass:"code-number",attrs:{"data-line-number":e.lineNumberFor(i)}}):e._e(),n("span",{staticClass:"code-line",domProps:{innerHTML:e._s(t)}})]),e._v("\n")]}))],2)],1)])],1)},a=[],r=n("002d"),s=n("8649"),o=n("800b"),c=n("1020"),l=n.n(c);const u={objectivec:["objective-c"]},d={bash:["sh","zsh"],c:["h"],cpp:["cc","c++","h++","hpp","hh","hxx","cxx"],css:[],diff:["patch"],http:["https"],java:["jsp"],javascript:["js","jsx","mjs","cjs"],json:[],llvm:[],markdown:["md","mkdown","mkd"],objectivec:["mm","objc","obj-c"].concat(u.objectivec),perl:["pl","pm"],php:[],python:["py","gyp","ipython"],ruby:["rb","gemspec","podspec","thor","irb"],scss:[],shell:["console","shellsession"],swift:[],xml:["html","xhtml","rss","atom","xjb","xsd","xsl","plist","wsf","svg"],...Object({NODE_ENV:"production",VUE_APP_TITLE:"Documentation",BASE_URL:"{{BASE_PATH}}/"}).VUE_APP_HLJS_LANGUAGES?Object.fromEntries(Object({NODE_ENV:"production",VUE_APP_TITLE:"Documentation",BASE_URL:"{{BASE_PATH}}/"}).VUE_APP_HLJS_LANGUAGES.split(",").map(e=>[e,[]])):void 0},p=new Set(["markdown","swift"]),h=Object.entries(d),m=new Set(Object.keys(d)),f=new Map;async function g(e){const t=[e];try{return await t.reduce(async(e,t)=>{let i;await e,i=p.has(t)?await n("1417")("./"+t):await n("b7b0")("./"+t),l.a.registerLanguage(t,i.default)},Promise.resolve()),!0}catch(i){return console.error(`Could not load ${e} file`),!1}}function b(e){if(m.has(e))return e;const t=h.find(([,t])=>t.includes(e));return t?t[0]:null}function v(e){if(f.has(e))return f.get(e);const t=b(e);return f.set(e,t),t}l.a.configure({classPrefix:"syntax-",languages:[...m]});const y=async e=>{const t=v(e);return!(!t||l.a.listLanguages().includes(t))&&g(t)},w=/\r\n|\r|\n/g,_=/syntax-/;function x(e){return 0===e.length?[]:e.split(w)}function k(e){return(e.trim().match(w)||[]).length}function C(e){const t=document.createElement("template");return t.innerHTML=e,t.content.childNodes}function S(e){const{className:t}=e;if(!_.test(t))return null;const n=x(e.innerHTML).reduce((e,n)=>`${e}<span class="${t}">${n}</span>\n`,"");return C(n.trim())}function E(e){return Array.from(e.childNodes).forEach(e=>{if(k(e.textContent))try{const t=e.childNodes.length?E(e):S(e);t&&e.replaceWith(...t)}catch(t){console.error(t)}}),S(e)}function O(e,t){const n=b(t);if(!l.a.getLanguage(n))throw new Error("Unsupported language for syntax highlighting: "+t);return l.a.highlight(e,{language:n,ignoreIllegals:!0}).value}function I(e,t){const n=e.join("\n"),i=O(n,t),a=document.createElement("code");return a.innerHTML=i,E(a),x(a.innerHTML)}var j=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("span",{staticClass:"filename"},[e.isActionable?n("a",{attrs:{href:"#"},on:{click:function(t){return t.preventDefault(),e.$emit("click")}}},[n("FileIcon",{attrs:{fileType:e.fileType}}),e._t("default")],2):n("span",[n("FileIcon",{attrs:{fileType:e.fileType}}),e._t("default")],2)])},T=[],A=function(){var e=this,t=e.$createElement,n=e._self._c||t;return"swift"===e.fileType?n("SwiftFileIcon",{staticClass:"file-icon"}):n("GenericFileIcon",{staticClass:"file-icon"})},L=[],B=n("a88f"),$=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("SVGIcon",{staticClass:"generic-file-icon",attrs:{viewBox:"0 0 14 14",themeId:"generic-file"}},[n("path",{attrs:{d:"M8.033 1l3.967 4.015v7.985h-10v-12zM7.615 2h-4.615v10h8v-6.574z"}}),n("path",{attrs:{d:"M7 1h1v4h-1z"}}),n("path",{attrs:{d:"M7 5h5v1h-5z"}})])},N=[],M=n("be08"),P={name:"GenericFileIcon",components:{SVGIcon:M["a"]}},R=P,V=n("2877"),D=Object(V["a"])(R,$,N,!1,null,null,null),G=D.exports,z={name:"CodeListingFileIcon",components:{SwiftFileIcon:B["a"],GenericFileIcon:G},props:{fileType:String}},q=z,F=(n("e6db"),Object(V["a"])(q,A,L,!1,null,"7c381064",null)),U=F.exports,W={name:"CodeListingFilename",components:{FileIcon:U},props:{isActionable:{type:Boolean,default:()=>!1},fileType:String}},H=W,K=(n("8608"),Object(V["a"])(H,j,T,!1,null,"c8c40662",null)),Z=K.exports,Y={name:"CodeListing",components:{Filename:Z,CodeBlock:o["a"]},data(){return{syntaxHighlightedLines:[]}},props:{fileName:String,isFileNameActionable:{type:Boolean,default:()=>!1},syntax:String,fileType:String,content:{type:Array,required:!0},startLineNumber:{type:Number,default:()=>1},highlights:{type:Array,default:()=>[]},showLineNumbers:{type:Boolean,default:()=>!1}},computed:{escapedContent:({content:e})=>e.map(r["c"]),highlightedLineNumbers(){return new Set(this.highlights.map(({line:e})=>e))},syntaxNameNormalized(){const e={occ:s["a"].objectiveC.key.url};return e[this.syntax]||this.syntax}},watch:{content:{handler:"syntaxHighlightLines",immediate:!0}},methods:{isHighlighted(e){return this.highlightedLineNumbers.has(this.lineNumberFor(e))},lineNumberFor(e){return this.startLineNumber+e},async syntaxHighlightLines(){let e;try{await y(this.syntaxNameNormalized),e=I(this.content,this.syntaxNameNormalized)}catch(t){e=this.escapedContent}this.syntaxHighlightedLines=e.map(e=>""===e?"\n":e)}}},X=Y,J=(n("7e3a"),Object(V["a"])(X,i,a,!1,null,"59f42f5b",null));t["a"]=J.exports},"7e3a":function(e,t,n){"use strict";n("f030")},"800b":function(e,t,n){"use strict";var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("code",{attrs:{tabindex:"0","data-before-code":e.$t("accessibility.code.start"),"data-after-code":e.$t("accessibility.code.end")}},[e._t("default")],2)},a=[],r={name:"CodeBlock"},s=r,o=(n("159b"),n("2877")),c=Object(o["a"])(s,i,a,!1,null,"08295b2f",null);t["a"]=c.exports},"80c8":function(e,t,n){},"80e4":function(e,t,n){"use strict";var i,a,r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"asset"},[n(e.assetComponent,e._g(e._b({tag:"component"},"component",e.assetProps,!1),e.assetListeners))],1)},s=[],o=n("8bd9"),c=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("ConditionalWrapper",{ref:"wrapper",attrs:{tag:e.DeviceFrameComponent,"should-wrap":!!e.deviceFrame,device:e.deviceFrame}},[n("video",{ref:"video",attrs:{controls:e.showsControls,autoplay:e.autoplays,poster:e.normalisedPosterPath,width:e.optimalWidth,playsinline:""},domProps:{muted:e.muted},on:{playing:function(t){return e.$emit("playing")},pause:function(t){return e.$emit("pause")},ended:function(t){return e.$emit("ended")}}},[n("source",{attrs:{src:e.normalizePath(e.videoAttributes.url)}})])])},l=[],u=n("748c"),d=n("e425"),p=n("821b"),h={functional:!0,name:"ConditionalWrapper",props:{tag:[Object,String],shouldWrap:Boolean},render(e,t){return t.props.shouldWrap?e(t.props.tag,t.data,t.children):t.children}},m=h,f=n("2877"),g=Object(f["a"])(m,i,a,!1,null,null,null),b=g.exports,v=n("5416"),y={name:"VideoAsset",components:{ConditionalWrapper:b},props:{variants:{type:Array,required:!0},showsControls:{type:Boolean,default:()=>!0},autoplays:{type:Boolean,default:()=>!0},posterVariants:{type:Array,required:!1,default:()=>[]},muted:{type:Boolean,default:!0},deviceFrame:{type:String,required:!1}},data:()=>({appState:d["a"].state,optimalWidth:null}),computed:{DeviceFrameComponent:()=>v["a"],preferredColorScheme:({appState:e})=>e.preferredColorScheme,systemColorScheme:({appState:e})=>e.systemColorScheme,userPrefersDark:({preferredColorScheme:e,systemColorScheme:t})=>e===p["a"].dark||e===p["a"].auto&&t===p["a"].dark,shouldShowDarkVariant:({darkVideoVariantAttributes:e,userPrefersDark:t})=>e&&t,defaultVideoAttributes(){return this.videoVariantsGroupedByAppearance.light[0]||this.darkVideoVariantAttributes||{}},darkVideoVariantAttributes(){return this.videoVariantsGroupedByAppearance.dark[0]},videoVariantsGroupedByAppearance(){return Object(u["e"])(this.variants)},posterVariantsGroupedByAppearance(){const{light:e,dark:t}=Object(u["e"])(this.posterVariants);return{light:Object(u["a"])(e),dark:Object(u["a"])(t)}},defaultPosterAttributes:({posterVariantsGroupedByAppearance:e,userPrefersDark:t})=>t&&e.dark.length?e.dark[0]:e.light[0]||{},normalisedPosterPath:({defaultPosterAttributes:e})=>Object(u["c"])(e.src),videoAttributes:({darkVideoVariantAttributes:e,defaultVideoAttributes:t,shouldShowDarkVariant:n})=>n?e:t},watch:{normalisedPosterPath:{immediate:!0,handler:"getPosterDimensions"}},methods:{normalizePath:u["c"],async getPosterDimensions(e){if(!e)return void(this.optimalWidth=null);const{density:t}=this.defaultPosterAttributes,n=parseInt(t.match(/\d+/)[0],10),{width:i}=await Object(u["b"])(e);this.optimalWidth=i/n}}},w=y,_=Object(f["a"])(w,c,l,!1,null,null,null),x=_.exports,k=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"video-replay-container"},[n("VideoAsset",{ref:"asset",attrs:{variants:e.variants,autoplays:e.autoplays,showsControls:e.showsControls,muted:e.muted,posterVariants:e.posterVariants,deviceFrame:e.deviceFrame},on:{pause:e.onPause,playing:e.onVideoPlaying,ended:e.onVideoEnd}}),e.showsControls?e._e():n("a",{staticClass:"control-button",attrs:{href:"#"},on:{click:function(t){return t.preventDefault(),e.togglePlayStatus.apply(null,arguments)}}},[e._v(" "+e._s(e.text)+" "),e.videoEnded?n("InlineReplayIcon",{staticClass:"control-icon icon-inline"}):e.isPlaying?n("PauseIcon",{staticClass:"control-icon icon-inline"}):n("PlayIcon",{staticClass:"control-icon icon-inline"})],1)],1)},C=[],S=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("SVGIcon",{staticClass:"inline-replay-icon",attrs:{viewBox:"0 0 14 14",themeId:"inline-replay"}},[n("path",{attrs:{d:"M2.254 10.201c-1.633-2.613-0.838-6.056 1.775-7.689 2.551-1.594 5.892-0.875 7.569 1.592l0.12 0.184-0.848 0.53c-1.34-2.145-4.166-2.797-6.311-1.457s-2.797 4.166-1.457 6.311 4.166 2.797 6.311 1.457c1.006-0.629 1.71-1.603 2.003-2.723l0.056-0.242 0.98 0.201c-0.305 1.487-1.197 2.792-2.51 3.612-2.613 1.633-6.056 0.838-7.689-1.775z"}}),n("path",{attrs:{d:"M10.76 1.355l0.984-0.18 0.851 4.651-4.56-1.196 0.254-0.967 3.040 0.796z"}})])},E=[],O=n("be08"),I={name:"InlineReplayIcon",components:{SVGIcon:O["a"]}},j=I,T=Object(f["a"])(j,S,E,!1,null,null,null),A=T.exports,L=n("c4dd"),B=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("SVGIcon",{staticClass:"pause-icon",attrs:{viewBox:"0 0 14 14",themeId:"pause"}},[n("path",{attrs:{d:"M5 4h1v6h-1z"}}),n("path",{attrs:{d:"M8 4h1v6h-1z"}}),n("path",{attrs:{d:"M7 0.5c-3.6 0-6.5 2.9-6.5 6.5s2.9 6.5 6.5 6.5 6.5-2.9 6.5-6.5-2.9-6.5-6.5-6.5zM7 12.5c-3 0-5.5-2.5-5.5-5.5s2.5-5.5 5.5-5.5 5.5 2.5 5.5 5.5-2.5 5.5-5.5 5.5z"}})])},$=[],N={name:"PauseIcon",components:{SVGIcon:O["a"]}},M=N,P=Object(f["a"])(M,B,$,!1,null,null,null),R=P.exports,V={name:"ReplayableVideoAsset",components:{PauseIcon:R,PlayIcon:L["a"],InlineReplayIcon:A,VideoAsset:x},props:{variants:{type:Array,required:!0},showsControls:{type:Boolean,default:()=>!0},autoplays:{type:Boolean,default:()=>!0},muted:{type:Boolean,default:!0},posterVariants:{type:Array,default:()=>[]},deviceFrame:{type:String,required:!1}},computed:{text(){return this.videoEnded?this.$t("video.replay"):this.isPlaying?this.$t("video.pause"):this.$t("video.play")}},data(){return{isPlaying:!1,videoEnded:!1}},methods:{async togglePlayStatus(){const e=this.$refs.asset.$refs.video;e&&(this.isPlaying&&!this.videoEnded?await e.pause():await e.play())},onVideoEnd(){this.isPlaying=!1,this.videoEnded=!0},onVideoPlaying(){const{video:e}=this.$refs.asset.$refs;this.isPlaying=!e.paused,this.videoEnded=e.ended},onPause(){const{video:e}=this.$refs.asset.$refs;!this.showsControls&&this.isPlaying&&(this.isPlaying=!1),this.videoEnded=e.ended}}},D=V,G=(n("74ea"),Object(f["a"])(D,k,C,!1,null,"7653dfd0",null)),z=G.exports,q=n("2f34");const F={video:"video",image:"image"};var U={name:"Asset",components:{ImageAsset:o["a"],VideoAsset:x},constants:{AssetTypes:F},mixins:[q["a"]],props:{identifier:{type:String,required:!0},showsReplayButton:{type:Boolean,default:()=>!1},showsVideoControls:{type:Boolean,default:()=>!0},videoAutoplays:{type:Boolean,default:()=>!0},videoMuted:{type:Boolean,default:!0},deviceFrame:{type:String,required:!1}},computed:{rawAsset(){return this.references[this.identifier]||{}},isRawAssetVideo:({rawAsset:e})=>e.type===F.video,videoPoster(){return this.isRawAssetVideo&&this.references[this.rawAsset.poster]},asset(){return this.isRawAssetVideo&&this.prefersReducedMotion&&this.videoPoster||this.rawAsset},assetComponent(){switch(this.asset.type){case F.image:return o["a"];case F.video:return this.showsReplayButton?z:x;default:return}},prefersReducedMotion(){return window.matchMedia("(prefers-reduced-motion)").matches},assetProps(){return{[F.image]:this.imageProps,[F.video]:this.videoProps}[this.asset.type]},imageProps(){return{alt:this.asset.alt,variants:this.asset.variants}},videoProps(){return{variants:this.asset.variants,showsControls:this.showsVideoControls,muted:this.videoMuted,autoplays:!this.prefersReducedMotion&&this.videoAutoplays,posterVariants:this.videoPoster?this.videoPoster.variants:[],deviceFrame:this.deviceFrame}},assetListeners(){return{[F.image]:null,[F.video]:{ended:()=>this.$emit("videoEnded")}}[this.asset.type]}}},W=U,H=(n("5201"),Object(f["a"])(W,r,s,!1,null,"dcbc7b38",null));t["a"]=H.exports},"81c0":function(e,t,n){},8222:function(e,t,n){},"850e":function(e,t,n){"use strict";n("88d2")},8608:function(e,t,n){"use strict";n("a7f3")},"863d":function(e,t,n){"use strict";var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("li",{staticClass:"nav-menu-item",class:{"nav-menu-item--animated":e.animate}},[e._t("default")],2)},a=[],r={name:"NavMenuItemBase",props:{animate:{type:Boolean,default:!0}}},s=r,o=(n("43fe"),n("2877")),c=Object(o["a"])(s,i,a,!1,null,"66cbfe4c",null);t["a"]=c.exports},8649:function(e,t,n){"use strict";t["a"]={objectiveC:{name:"Objective-C",key:{api:"occ",url:"objc"}},swift:{name:"Swift",key:{api:"swift",url:"swift"}}}},"86d8":function(e,t,n){"use strict";var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n(e.refComponent,{tag:"component",attrs:{url:e.urlWithParams,"is-active":e.isActiveComputed}},[e._t("default")],2)},a=[],r=n("d26a"),s=n("66cd"),o=n("9895"),c=function(){var e=this,t=e.$createElement,n=e._self._c||t;return e.isActive?n("a",{attrs:{href:e.url}},[e._t("default")],2):n("span",[e._t("default")],2)},l=[],u={name:"ReferenceExternal",props:{url:{type:String,required:!0},isActive:{type:Boolean,default:!0}}},d=u,p=n("2877"),h=Object(p["a"])(d,c,l,!1,null,null,null),m=h.exports,f=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("ReferenceInternal",e._b({},"ReferenceInternal",e.$props,!1),[n("CodeVoice",[e._t("default")],2)],1)},g=[],b=function(){var e=this,t=e.$createElement,n=e._self._c||t;return e.isActive?n("router-link",{attrs:{to:e.url}},[e._t("default")],2):n("span",[e._t("default")],2)},v=[],y={name:"ReferenceInternal",props:{url:{type:String,required:!0},isActive:{type:Boolean,default:!0}}},w=y,_=Object(p["a"])(w,b,v,!1,null,null,null),x=_.exports,k=n("52e4"),C={name:"ReferenceInternalSymbol",props:x.props,components:{ReferenceInternal:x,CodeVoice:k["a"]}},S=C,E=Object(p["a"])(S,f,g,!1,null,null,null),O=E.exports,I={name:"Reference",computed:{isInternal({url:e}){if(!e.startsWith("/")&&!e.startsWith("#"))return!1;const{resolved:{name:t}={}}=this.$router.resolve(e)||{};return t!==o["b"]},isSymbolReference(){return"symbol"===this.kind&&!this.hasInlineFormatting&&(this.role===s["a"].symbol||this.role===s["a"].dictionarySymbol)},isDisplaySymbol({isSymbolReference:e,titleStyle:t,ideTitle:n}){return n?e&&"symbol"===t:e},refComponent(){return this.isInternal?this.isDisplaySymbol?O:x:m},urlWithParams({isInternal:e}){return e?Object(r["b"])(this.url,this.$route.query):this.url},isActiveComputed({url:e,isActive:t}){return!(!e||!t)}},props:{url:{type:String,required:!0},kind:{type:String,required:!1},role:{type:String,required:!1},isActive:{type:Boolean,required:!1,default:!0},ideTitle:{type:String,required:!1},titleStyle:{type:String,required:!1},hasInlineFormatting:{type:Boolean,default:!1}}},j=I,T=Object(p["a"])(j,i,a,!1,null,null,null);t["a"]=T.exports},"88d2":function(e,t,n){},"8a61":function(e,t,n){"use strict";var i=n("3908");t["a"]={methods:{async scrollToElement(e){await Object(i["b"])(8);const t=this.$router.resolve({hash:e}),{selector:n,offset:a}=await this.$router.options.scrollBehavior(t.route),r=document.querySelector(n);return r?(r.scrollIntoView(),window.scrollY+window.innerHeight<document.body.scrollHeight&&window.scrollBy(-a.x,-a.y),r):null},async handleFocusAndScroll(e){const t=document.getElementById(e);t&&(t.focus({preventScroll:!0}),await this.scrollToElement("#"+e))}}}},"8bd9":function(e,t,n){"use strict";var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return e.fallbackImageSrcSet?n("img",{staticClass:"fallback",attrs:{title:e.$t("error.image"),decoding:"async",alt:e.alt,srcset:e.fallbackImageSrcSet}}):n("picture",[e.prefersAuto&&e.darkVariantAttributes?n("source",{attrs:{media:"(prefers-color-scheme: dark)",srcset:e.darkVariantAttributes.srcSet}}):e._e(),e.prefersDark&&e.darkVariantAttributes?n("img",e._b({ref:"img",attrs:{decoding:"async",loading:e.loading,alt:e.alt,width:e.darkVariantAttributes.width||e.optimalWidth,height:e.darkVariantAttributes.width||e.optimalWidth?"auto":null},on:{error:e.handleImageLoadError}},"img",e.darkVariantAttributes,!1)):n("img",e._b({ref:"img",attrs:{decoding:"async",loading:e.loading,alt:e.alt,width:e.defaultAttributes.width||e.optimalWidth,height:e.defaultAttributes.width||e.optimalWidth?"auto":null},on:{error:e.handleImageLoadError}},"img",e.defaultAttributes,!1))])},a=[],r=n("748c"),s={props:{variants:{type:Array,required:!0}},computed:{variantsGroupedByAppearance(){return Object(r["e"])(this.variants)},lightVariants(){return Object(r["a"])(this.variantsGroupedByAppearance.light)},darkVariants(){return Object(r["a"])(this.variantsGroupedByAppearance.dark)}}},o=n("e425"),c=n("821b"),l=n("5da3"),u=n.n(l);const d=10;function p(e){if(!e.length)return null;const t=e.map(e=>`${Object(r["c"])(e.src)} ${e.density}`).join(", "),n=e[0],i={srcSet:t,src:Object(r["c"])(n.src)},{width:a}=n.size||{width:null};return a&&(i.width=a,i.height="auto"),i}var h={name:"ImageAsset",mixins:[s],inject:{imageLoadingStrategy:{default:null}},data:()=>({appState:o["a"].state,fallbackImageSrcSet:null,optimalWidth:null}),computed:{allVariants:({lightVariants:e=[],darkVariants:t=[]})=>e.concat(t),defaultAttributes:({lightVariantAttributes:e,darkVariantAttributes:t})=>e||t,darkVariantAttributes:({darkVariants:e})=>p(e),lightVariantAttributes:({lightVariants:e})=>p(e),loading:({appState:e,imageLoadingStrategy:t})=>t||e.imageLoadingStrategy,preferredColorScheme:({appState:e})=>e.preferredColorScheme,prefersAuto:({preferredColorScheme:e})=>e===c["a"].auto,prefersDark:({preferredColorScheme:e})=>e===c["a"].dark},props:{alt:{type:String,default:""},variants:{type:Array,required:!0},shouldCalculateOptimalWidth:{type:Boolean,default:!0}},methods:{handleImageLoadError(){this.fallbackImageSrcSet=u.a+" 2x"},async calculateOptimalWidth(){const{$refs:{img:{currentSrc:e}},allVariants:t}=this,{density:n}=t.find(({src:t})=>e.endsWith(t)),i=parseInt(n.match(/\d+/)[0],d),a=await Object(r["b"])(e),s=a.width/i;return s},async optimizeImageSize(){if(!this.defaultAttributes.width&&this.$refs.img)try{this.optimalWidth=await this.calculateOptimalWidth()}catch{console.error("Unable to calculate optimal image width")}}},mounted(){this.shouldCalculateOptimalWidth&&this.$refs.img.addEventListener("load",this.optimizeImageSize)}},m=h,f=n("2877"),g=Object(f["a"])(m,i,a,!1,null,null,null);t["a"]=g.exports},"8c92":function(e,t,n){"use strict";n("80c8")},"8d2d":function(e,t,n){"use strict";var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("SVGIcon",{staticClass:"tutorial-icon",attrs:{viewBox:"0 0 14 14",themeId:"tutorial"}},[n("path",{attrs:{d:"M0.933 6.067h3.733v1.867h-3.733v-1.867z"}}),n("path",{attrs:{d:"M0.933 1.867h3.733v1.867h-3.733v-1.867z"}}),n("path",{attrs:{d:"M13.067 1.867v10.267h-7.467v-10.267zM12.133 2.8h-5.6v8.4h5.6z"}}),n("path",{attrs:{d:"M0.933 10.267h3.733v1.867h-3.733v-1.867z"}})])},a=[],r=n("be08"),s={name:"TutorialIcon",components:{SVGIcon:r["a"]}},o=s,c=n("2877"),l=Object(c["a"])(o,i,a,!1,null,null,null);t["a"]=l.exports},"8d90":function(e,t,n){},"8f26":function(e,t,n){"use strict";n("0444")},9034:function(e,t,n){},"95da":function(e,t,n){"use strict";var i=n("0cb0");const a="data-original-",r="aria-hidden",s="tabindex";function o(e,t){const n=a+t;if(e.getAttribute(n))return;const i=e.getAttribute(t)||"";e.setAttribute(n,i)}function c(e,t){const n=a+t;if(!e.hasAttribute(n))return;const i=e.getAttribute(n);e.removeAttribute(n),i.length?e.setAttribute(t,i):e.removeAttribute(t)}function l(e,t){const n=document.body;let i=e,a=e;while(i=i.previousElementSibling)t(i);while(a=a.nextElementSibling)t(a);e.parentElement&&e.parentElement!==n&&l(e.parentElement,t)}const u=e=>{o(e,r),o(e,s),e.setAttribute(r,"true"),e.setAttribute(s,"-1");const t=i["a"].getTabbableElements(e);let n=t.length-1;while(n>=0)o(t[n],s),t[n].setAttribute(s,"-1"),n-=1},d=e=>{c(e,r),c(e,s);const t=e.querySelectorAll(`[${a+s}]`);let n=t.length-1;while(n>=0)c(t[n],s),n-=1};t["a"]={hide(e){l(e,u)},show(e){l(e,d)}}},9649:function(e,t,n){},"9a61":function(e,t,n){"use strict";n("19cc")},"9b30":function(e,t,n){"use strict";var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("ul",{staticClass:"nav-menu-items",attrs:{"data-previous-menu-children-count":e.previousSiblingChildren}},[e._t("default")],2)},a=[],r={name:"NavMenuItems",props:{previousSiblingChildren:{type:Number,default:0}}},s=r,o=(n("517a"),n("2877")),c=Object(o["a"])(s,i,a,!1,null,"67c1c0a5",null);t["a"]=c.exports},"9ed5":function(e,t,n){"use strict";n("a5f5")},a295:function(e,t,n){"use strict";var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("SVGIcon",{attrs:{viewBox:"0 0 14 14",themeId:"path"}},[n("path",{attrs:{d:"M0 0.948h2.8v2.8h-2.8z"}}),n("path",{attrs:{d:"M11.2 10.252h2.8v2.8h-2.8z"}}),n("path",{attrs:{d:"M6.533 1.852h0.933v10.267h-0.933z"}}),n("path",{attrs:{d:"M2.8 1.852h4.667v0.933h-4.667z"}}),n("path",{attrs:{d:"M6.533 11.186h4.667v0.933h-4.667z"}})])},a=[],r=n("be08"),s={name:"PathIcon",components:{SVGIcon:r["a"]}},o=s,c=n("2877"),l=Object(c["a"])(o,i,a,!1,null,null,null);t["a"]=l.exports},a4f0:function(e,t,n){},a5f5:function(e,t,n){},a7a5:function(e,t,n){},a7d8:function(e,t,n){},a7f3:function(e,t,n){},a88f:function(e,t,n){"use strict";var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("SVGIcon",{staticClass:"swift-file-icon",attrs:{viewBox:"0 0 15 14",themeId:"swift-file"}},[n("path",{attrs:{d:"M14.93,13.56A2.15,2.15,0,0,0,15,13a5.37,5.37,0,0,0-1.27-3.24A6.08,6.08,0,0,0,14,7.91,9.32,9.32,0,0,0,9.21.31a8.51,8.51,0,0,1,1.78,5,6.4,6.4,0,0,1-.41,2.18A45.06,45.06,0,0,1,3.25,1.54,44.57,44.57,0,0,0,7.54,6.9,45.32,45.32,0,0,1,1.47,2.32,35.69,35.69,0,0,0,8.56,9.94a6.06,6.06,0,0,1-3.26.85A9.48,9.48,0,0,1,0,8.91a10,10,0,0,0,8.1,4.72c2.55,0,3.25-1.2,4.72-1.2a2.09,2.09,0,0,1,1.91,1.15C14.79,13.69,14.88,13.75,14.93,13.56Z"}})])},a=[],r=n("be08"),s={name:"SwiftFileIcon",components:{SVGIcon:r["a"]}},o=s,c=(n("c3e5"),n("2877")),l=Object(c["a"])(o,i,a,!1,null,"c01a6890",null);t["a"]=l.exports},a97e:function(e,t,n){"use strict";var i=n("63b8");const a=e=>e?`(max-width: ${e}px)`:"",r=e=>e?`(min-width: ${e}px)`:"";function s({minWidth:e,maxWidth:t}){return["only screen",r(e),a(t)].filter(Boolean).join(" and ")}function o({maxWidth:e,minWidth:t}){return window.matchMedia(s({minWidth:t,maxWidth:e}))}var c,l,u={name:"BreakpointEmitter",constants:{BreakpointAttributes:i["a"],BreakpointName:i["b"],BreakpointScopes:i["c"]},props:{scope:{type:String,default:()=>i["c"].default,validator:e=>e in i["c"]}},render(){return this.$scopedSlots.default?this.$scopedSlots.default({matchingBreakpoint:this.matchingBreakpoint}):null},data:()=>({matchingBreakpoint:null}),methods:{initMediaQuery(e,t){const n=o(t),i=t=>this.handleMediaQueryChange(t,e);n.addListener(i),this.$once("hook:beforeDestroy",()=>{n.removeListener(i)}),i(n)},handleMediaQueryChange(e,t){e.matches&&(this.matchingBreakpoint=t,this.$emit("change",t))}},mounted(){const e=i["a"][this.scope]||{};Object.entries(e).forEach(([e,t])=>{this.initMediaQuery(e,t)})}},d=u,p=n("2877"),h=Object(p["a"])(d,c,l,!1,null,null,null);t["a"]=h.exports},a9f1:function(e,t,n){"use strict";var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("SVGIcon",{staticClass:"article-icon",attrs:{viewBox:"0 0 14 14",themeId:"article"}},[n("path",{attrs:{d:"M8.033 1l3.967 4.015v7.985h-10v-12zM7.615 2h-4.615v10h8v-6.574z"}}),n("path",{attrs:{d:"M7 1h1v4h-1z"}}),n("path",{attrs:{d:"M7 5h5v1h-5z"}})])},a=[],r=n("be08"),s={name:"ArticleIcon",components:{SVGIcon:r["a"]}},o=s,c=n("2877"),l=Object(c["a"])(o,i,a,!1,null,null,null);t["a"]=l.exports},aea0:function(e,t,n){},b0e7:function(e,t,n){"use strict";n("8d90")},b0f5:function(e,t,n){"use strict";n("49e3")},b2da:function(e,t,n){"use strict";n("e529")},b392:function(e,t,n){},b7b0:function(e,t,n){var i={"./bash":["f0f8","highlight-js-bash"],"./c":["1fe5","highlight-js-c"],"./cpp":["0209","highlight-js-cpp"],"./css":["ee8c","highlight-js-css"],"./diff":["48b8","highlight-js-diff"],"./http":["c01d","highlight-js-http"],"./java":["332f","highlight-js-java"],"./javascript":["4dd1","highlight-js-javascript"],"./json":["5ad2","highlight-js-json"],"./llvm":["7c30","highlight-js-llvm"],"./markdown":["04b0","highlight-js-markdown"],"./objectivec":["9bf2","highlight-js-objectivec"],"./perl":["6a51","highlight-js-perl"],"./php":["2907","highlight-js-php"],"./python":["9510","highlight-js-python"],"./ruby":["82cb","highlight-js-ruby"],"./scss":["6113","highlight-js-scss"],"./shell":["b65b","highlight-js-shell"],"./swift":["2a39","highlight-js-swift"],"./xml":["8dcb","highlight-js-xml"]};function a(e){if(!n.o(i,e))return Promise.resolve().then((function(){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}));var t=i[e],a=t[0];return n.e(t[1]).then((function(){return n.t(a,7)}))}a.keys=function(){return Object.keys(i)},a.id="b7b0",e.exports=a},b8f2:function(e,t,n){"use strict";n("a7a5")},bbe1:function(e,t,n){},bbf6:function(e,t,n){"use strict";n("63e6")},bf08:function(e,t,n){"use strict";var i=n("2788"),a=n("002d"),r=n("d26a"),s=n("5677");t["a"]={methods:{extractFirstParagraphText(e=[]){const t=s["default"].computed.plaintext.bind({...s["default"].methods,content:e})();return Object(a["e"])(t)}},computed:{pagePath:({$route:{path:e="/"}={}})=>e,pageURL:({pagePath:e="/"})=>Object(r["e"])(e),disableMetadata:()=>!1},mounted(){this.disableMetadata||Object(i["a"])({title:this.pageTitle,description:this.pageDescription,url:this.pageURL,currentLocale:this.$i18n.locale})}}},c081:function(e,t,n){"use strict";var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return e.action?n("DestinationDataProvider",{attrs:{destination:e.action},scopedSlots:e._u([{key:"default",fn:function(t){var i=t.url,a=t.title;return n("ButtonLink",{attrs:{url:i,isDark:e.isDark}},[e._v(" "+e._s(a)+" ")])}}],null,!1,**********)}):e._e()},a=[],r=n("76ab"),s=n("c7ea"),o={name:"CallToActionButton",components:{DestinationDataProvider:s["a"],ButtonLink:r["a"]},props:{action:{type:Object,required:!0},isDark:{type:Boolean,default:!1}}},c=o,l=n("2877"),u=Object(l["a"])(c,i,a,!1,null,null,null);t["a"]=u.exports},c3da:function(e,t,n){"use strict";n("fda2")},c3e5:function(e,t,n){"use strict";n("aea0")},c4dd:function(e,t,n){"use strict";var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("SVGIcon",{staticClass:"play-icon",attrs:{viewBox:"0 0 14 14",themeId:"play"}},[n("path",{attrs:{d:"M7 0.5c3.59 0 6.5 2.91 6.5 6.5s-2.91 6.5-6.5 6.5c-3.59 0-6.5-2.91-6.5-6.5v0c0-3.59 2.91-6.5 6.5-6.5v0zM7 1.5c-3.038 0-5.5 2.462-5.5 5.5s2.462 5.5 5.5 5.5c3.038 0 5.5-2.462 5.5-5.5v0c0-3.038-2.462-5.5-5.5-5.5v0z"}}),n("path",{attrs:{d:"M10.195 7.010l-5 3v-6l5 3z"}})])},a=[],r=n("be08"),s={name:"PlayIcon",components:{SVGIcon:r["a"]}},o=s,c=n("2877"),l=Object(c["a"])(o,i,a,!1,null,null,null);t["a"]=l.exports},c7ea:function(e,t,n){"use strict";var i=n("2f34");const a={link:"link",reference:"reference",text:"text"};var r,s,o={name:"DestinationDataProvider",mixins:[i["a"]],props:{destination:{type:Object,required:!0,default:()=>({})}},inject:{isTargetIDE:{default:()=>!1}},constants:{DestinationType:a},computed:{isExternal:({reference:e,destination:t})=>e.type===a.link||t.type===a.link,shouldAppendOpensInBrowser:({isExternal:e,isTargetIDE:t})=>e&&t,reference:({references:e,destination:t})=>e[t.identifier]||{},linkUrl:({destination:e,reference:t})=>({[a.link]:e.destination,[a.reference]:t.url,[a.text]:e.text}[e.type]),linkTitle:({reference:e,destination:t})=>({[a.link]:t.title,[a.reference]:t.overridingTitle||e.title,[a.text]:""}[t.type])},methods:{formatAriaLabel(e){return this.shouldAppendOpensInBrowser?e+" (opens in browser)":e}},render(){return this.$scopedSlots.default({url:this.linkUrl||"",title:this.linkTitle||"",formatAriaLabel:this.formatAriaLabel,isExternal:this.isExternal})}},c=o,l=n("2877"),u=Object(l["a"])(c,r,s,!1,null,null,null);t["a"]=u.exports},cb92:function(e,t,n){"use strict";n("598a")},cbcf:function(e,t,n){"use strict";var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("nav",{ref:"nav",staticClass:"nav",class:e.rootClasses,attrs:{role:"navigation"}},[n("div",{ref:"wrapper",staticClass:"nav__wrapper"},[n("div",{staticClass:"nav__background"}),e.hasOverlay?n("div",{staticClass:"nav-overlay",on:{click:e.closeNav}}):e._e(),n("div",{staticClass:"nav-content"},[e._t("pre-title",null,{className:"pre-title"},{closeNav:e.closeNav,inBreakpoint:e.inBreakpoint,currentBreakpoint:e.currentBreakpoint,isOpen:e.isOpen}),e.$slots.default?n("div",{staticClass:"nav-title"},[e._t("default")],2):e._e(),e._t("after-title"),n("div",{staticClass:"nav-menu"},[n("a",{ref:"axToggle",staticClass:"nav-ax-toggle",attrs:{href:"#",role:"button"},on:{click:function(t){return t.preventDefault(),e.toggleNav.apply(null,arguments)}}},[n("span",{staticClass:"visuallyhidden"},[e.isOpen?[e._v(" "+e._s(e.$t("documentation.nav.close-menu"))+" ")]:[e._v(" "+e._s(e.$t("documentation.nav.open-menu"))+" ")]],2)]),n("div",{ref:"tray",staticClass:"nav-menu-tray",on:{transitionend:function(t){return t.target!==t.currentTarget?null:e.onTransitionEnd.apply(null,arguments)},click:e.handleTrayClick}},[e._t("tray",(function(){return[n("NavMenuItems",[e._t("menu-items")],2)]}),{closeNav:e.closeNav})],2)]),n("div",{staticClass:"nav-actions"},[n("a",{ref:"toggle",staticClass:"nav-menucta",attrs:{href:"#",tabindex:"-1","aria-hidden":"true"},on:{click:function(t){return t.preventDefault(),e.toggleNav.apply(null,arguments)}}},[n("span",{staticClass:"nav-menucta-chevron"})])])],2),e._t("after-content")],2),n("BreakpointEmitter",{attrs:{scope:e.BreakpointScopes.nav},on:{change:e.onBreakpointChange}})],1)},a=[],r=n("72e7"),s=n("9b30"),o=n("a97e"),c=n("f2af"),l=n("942d"),u=n("63b8"),d=n("95da"),p=n("3908");const{noClose:h}=l["a"],{BreakpointName:m,BreakpointScopes:f}=o["a"].constants,g=8,b={isDark:"theme-dark",isOpen:"nav--is-open",inBreakpoint:"nav--in-breakpoint-range",isTransitioning:"nav--is-transitioning",isSticking:"nav--is-sticking",hasSolidBackground:"nav--solid-background",hasNoBorder:"nav--noborder",hasFullWidthBorder:"nav--fullwidth-border",isWideFormat:"nav--is-wide-format",noBackgroundTransition:"nav--no-bg-transition"};var v={name:"NavBase",components:{NavMenuItems:s["a"],BreakpointEmitter:o["a"]},constants:{NavStateClasses:b,NoBGTransitionFrames:g},props:{breakpoint:{type:String,default:m.small},hasOverlay:{type:Boolean,default:!0},hasSolidBackground:{type:Boolean,default:!1},hasNoBorder:{type:Boolean,default:!1},hasFullWidthBorder:{type:Boolean,default:!1},isDark:{type:Boolean,default:!1},isWideFormat:{type:Boolean,default:!1}},mixins:[r["a"]],data(){return{isOpen:!1,isTransitioning:!1,isSticking:!1,noBackgroundTransition:!0,currentBreakpoint:m.large}},computed:{BreakpointScopes:()=>f,inBreakpoint:({currentBreakpoint:e,breakpoint:t})=>!Object(u["d"])(e,t),rootClasses:({isOpen:e,inBreakpoint:t,isTransitioning:n,isSticking:i,hasSolidBackground:a,hasNoBorder:r,hasFullWidthBorder:s,isDark:o,isWideFormat:c,noBackgroundTransition:l})=>({[b.isDark]:o,[b.isOpen]:e,[b.inBreakpoint]:t,[b.isTransitioning]:n,[b.isSticking]:i,[b.hasSolidBackground]:a,[b.hasNoBorder]:r,[b.hasFullWidthBorder]:s,[b.isWideFormat]:c,[b.noBackgroundTransition]:l})},watch:{isOpen(e){this.$emit("change",e),e?this.onExpand():this.onClose()}},async mounted(){window.addEventListener("keydown",this.onEscape),window.addEventListener("popstate",this.closeNav),window.addEventListener("orientationchange",this.closeNav),document.addEventListener("click",this.handleClickOutside),this.handleFlashOnMount(),await this.$nextTick()},beforeDestroy(){window.removeEventListener("keydown",this.onEscape),window.removeEventListener("popstate",this.closeNav),window.removeEventListener("orientationchange",this.closeNav),document.removeEventListener("click",this.handleClickOutside),this.isOpen&&this.toggleScrollLock(!1)},methods:{getIntersectionTargets(){return[document.getElementById(l["e"])||this.$el]},toggleNav(){this.isOpen=!this.isOpen,this.isTransitioning=!0},closeNav(){const e=this.isOpen;return this.isOpen=!1,this.resolveOnceTransitionsEnd(e)},resolveOnceTransitionsEnd(e){return e&&this.inBreakpoint?(this.isTransitioning=!0,new Promise(e=>{const t=this.$watch("isTransitioning",()=>{e(),t()})})):Promise.resolve()},async onTransitionEnd({propertyName:e}){"max-height"===e&&(this.$emit("changed",this.isOpen),this.isTransitioning=!1,this.isOpen?(this.$emit("opened"),this.toggleScrollLock(!0)):this.$emit("closed"))},onBreakpointChange(e){this.currentBreakpoint=e,this.inBreakpoint||this.closeNav()},onIntersect({intersectionRatio:e}){window.scrollY<0||(this.isSticking=1!==e)},onEscape({key:e}){"Escape"===e&&this.isOpen&&(this.closeNav(),this.$refs.axToggle.focus())},handleTrayClick({target:e}){e.href&&!e.classList.contains(h)&&this.closeNav()},handleClickOutside({target:e}){this.$refs.nav.contains(e)||this.closeNav()},toggleScrollLock(e){e?c["b"].lockScroll(this.$refs.tray):c["b"].unlockScroll(this.$refs.tray)},onExpand(){this.$emit("open"),d["a"].hide(this.$refs.wrapper),document.activeElement===this.$refs.toggle&&document.activeElement.blur()},onClose(){this.$emit("close"),this.toggleScrollLock(!1),d["a"].show(this.$refs.wrapper)},async handleFlashOnMount(){await Object(p["b"])(g),this.noBackgroundTransition=!1}}},y=v,w=(n("1f39"),n("2877")),_=Object(w["a"])(y,i,a,!1,null,"5c0521d3",null);t["a"]=_.exports},d0da:function(e,t,n){"use strict";n("64b5")},e3ab:function(e,t,n){"use strict";var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("aside",{class:e.kind,attrs:{"aria-label":e.kind}},[n("p",{staticClass:"label"},[e._v(e._s(e.name||e.$t(e.label)))]),e._t("default")],2)},a=[];const r={deprecated:"deprecated",experiment:"experiment",important:"important",note:"note",tip:"tip",warning:"warning"};var s={name:"Aside",props:{kind:{type:String,required:!0,validator:e=>Object.prototype.hasOwnProperty.call(r,e)},name:{type:String,required:!1}},computed:{label:({kind:e})=>"aside-kind."+e}},o=s,c=(n("b8f2"),n("2877")),l=Object(c["a"])(o,i,a,!1,null,"3ccce809",null);t["a"]=l.exports},e529:function(e,t,n){},e6db:function(e,t,n){"use strict";n("47cc")},ec71:function(e,t,n){"use strict";n("a4f0")},ee9e:function(e,t,n){"use strict";var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"row",class:{"with-columns":e.columns},style:e.style},[e._t("default")],2)},a=[],r=n("63b8"),s={name:"Row",props:{columns:{type:Object,required:!1,validator:e=>Object.entries(e).every(([e,t])=>r["b"][e]&&"number"===typeof t)},gap:{type:Number,required:!1}},computed:{style:({columns:e={},gap:t})=>({"--col-count-large":e.large,"--col-count-medium":e.medium,"--col-count-small":e.small||1,"--col-gap":t&&t+"px"})}},o=s,c=(n("17a4"),n("2877")),l=Object(c["a"])(o,i,a,!1,null,"1bcb2d0f",null);t["a"]=l.exports},efc1:function(e,t,n){"use strict";n("3b75")},f030:function(e,t,n){},f12c:function(e,t,n){"use strict";var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"TopicTypeIcon",style:e.styles},[e.imageOverride?n("OverridableAsset",{staticClass:"icon-inline",attrs:{imageOverride:e.imageOverride,shouldCalculateOptimalWidth:e.shouldCalculateOptimalWidth}}):n(e.icon,e._b({tag:"component",staticClass:"icon-inline"},"component",e.iconProps,!1))],1)},a=[],r=n("a295"),s=n("3024"),o=n("a9f1"),c=n("8d2d"),l=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("SVGIcon",{attrs:{viewBox:"0 0 14 14",height:"14",themeId:"topic-func"}},[n("path",{attrs:{d:"M13 1v12h-12v-12zM12.077 1.923h-10.154v10.154h10.154z"}}),n("path",{attrs:{d:"M5.191 9.529c0.044 0.002 0.089 0.004 0.133 0.004 0.108 0 0.196-0.025 0.262-0.074s0.122-0.113 0.166-0.188c0.044-0.077 0.078-0.159 0.103-0.247s0.049-0.173 0.074-0.251l0.598-2.186h-0.709l0.207-0.702h0.702l0.288-1.086c0.083-0.384 0.256-0.667 0.517-0.849s0.591-0.273 0.99-0.273c0.108 0 0.212 0.007 0.314 0.022s0.203 0.027 0.306 0.037l-0.207 0.761c-0.054-0.006-0.106-0.011-0.155-0.018s-0.102-0.011-0.155-0.011c-0.108 0-0.196 0.016-0.262 0.048s-0.122 0.075-0.166 0.129-0.080 0.115-0.107 0.185c-0.028 0.068-0.055 0.14-0.085 0.214l-0.222 0.842h0.768l-0.192 0.702h-0.783l-0.628 2.319c-0.059 0.222-0.129 0.419-0.21 0.594s-0.182 0.322-0.303 0.443-0.269 0.214-0.443 0.281-0.385 0.1-0.631 0.1c-0.084 0-0.168-0.004-0.251-0.011s-0.168-0.014-0.251-0.018l0.207-0.768c0.040 0 0.081 0.001 0.126 0.004z"}})])},u=[],d=n("be08"),p={name:"TopicFuncIcon",components:{SVGIcon:d["a"]}},h=p,m=n("2877"),f=Object(m["a"])(h,l,u,!1,null,null,null),g=f.exports,b=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("SVGIcon",{staticClass:"collection-icon",attrs:{viewBox:"0 0 14 14",themeId:"collection"}},[n("path",{attrs:{d:"m1 1v12h12v-12zm11 11h-10v-10h10z"}}),n("path",{attrs:{d:"m3 4h8v1h-8zm0 2.5h8v1h-8zm0 2.5h8v1h-8z"}}),n("path",{attrs:{d:"m3 4h8v1h-8z"}}),n("path",{attrs:{d:"m3 6.5h8v1h-8z"}}),n("path",{attrs:{d:"m3 9h8v1h-8z"}})])},v=[],y={name:"CollectionIcon",components:{SVGIcon:d["a"]}},w=y,_=Object(m["a"])(w,b,v,!1,null,null,null),x=_.exports,k=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("SVGIcon",{attrs:{viewBox:"0 0 14 14",height:"14",themeId:"topic-func-op"}},[n("path",{attrs:{d:"M13 13h-12v-12h12zM1.923 12.077h10.154v-10.154h-10.154z"}}),n("path",{attrs:{d:"M5.098 4.968v-1.477h-0.738v1.477h-1.477v0.738h1.477v1.477h0.738v-1.477h1.477v-0.738z"}}),n("path",{attrs:{d:"M8.030 4.807l-2.031 5.538h0.831l2.031-5.538z"}}),n("path",{attrs:{d:"M8.894 8.805v0.923h2.215v-0.923z"}})])},C=[],S={name:"TopicFuncOpIcon",components:{SVGIcon:d["a"]}},E=S,O=Object(m["a"])(E,k,C,!1,null,null,null),I=O.exports,j=n("3b96"),T=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("SVGIcon",{attrs:{viewBox:"0 0 14 14",height:"14",themeId:"topic-subscript"}},[n("path",{attrs:{d:"M13 13h-12v-12h12zM1.923 12.077h10.154v-10.154h-10.154z"}}),n("path",{attrs:{d:"M4.133 3.633v6.738h1.938v-0.831h-0.923v-5.077h0.923v-0.831z"}}),n("path",{attrs:{d:"M9.856 10.371v-6.738h-1.938v0.831h0.923v5.077h-0.923v0.831z"}})])},A=[],L={name:"TopicSubscriptIcon",components:{SVGIcon:d["a"]}},B=L,$=Object(m["a"])(B,T,A,!1,null,null,null),N=$.exports,M=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("SVGIcon",{staticClass:"two-letter-icon",attrs:{width:"16px",height:"16px",viewBox:"0 0 16 16",themeId:"two-letter"}},[n("g",{attrs:{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"}},[n("g",{attrs:{transform:"translate(1.000000, 1.000000)"}},[n("rect",{attrs:{stroke:"currentColor",x:"0.5",y:"0.5",width:"13",height:"13"}}),n("text",{attrs:{"font-size":"8","font-weight":"bold",fill:"currentColor"}},[n("tspan",{attrs:{x:"8.2",y:"11"}},[e._v(e._s(e.second))])]),n("text",{attrs:{"font-size":"11","font-weight":"bold",fill:"currentColor"}},[n("tspan",{attrs:{x:"1.7",y:"11"}},[e._v(e._s(e.first))])])])])])},P=[],R={name:"TwoLetterSymbolIcon",components:{SVGIcon:d["a"]},props:{first:{type:String,required:!0},second:{type:String,required:!0}}},V=R,D=Object(m["a"])(V,M,P,!1,null,null,null),G=D.exports,z=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("SVGIcon",{staticClass:"single-letter-icon",attrs:{width:"16px",height:"16px",viewBox:"0 0 16 16",themeId:"single-letter"}},[n("g",{attrs:{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"}},[n("rect",{attrs:{stroke:"currentColor",x:"1",y:"1",width:"14",height:"14"}}),n("text",{attrs:{"font-size":"11","font-weight":"bold",fill:"currentColor",x:"49%",y:"12","text-anchor":"middle"}},[n("tspan",[e._v(e._s(e.symbol))])])])])},q=[],F={name:"SingleLetterSymbolIcon",components:{SVGIcon:d["a"]},props:{symbol:{type:String,required:!0}}},U=F,W=Object(m["a"])(U,z,q,!1,null,null,null),H=W.exports,K=n("31d4"),Z=n("2cae"),Y=n("fdd9");const X={[K["b"].article]:o["a"],[K["b"].associatedtype]:x,[K["b"].buildSetting]:x,[K["b"].class]:H,[K["b"].collection]:x,[K["b"].dictionarySymbol]:H,[K["b"].container]:x,[K["b"].enum]:H,[K["b"].extension]:G,[K["b"].func]:g,[K["b"].op]:I,[K["b"].httpRequest]:H,[K["b"].languageGroup]:x,[K["b"].learn]:r["a"],[K["b"].method]:H,[K["b"].macro]:H,[K["b"].module]:s["a"],[K["b"].overview]:r["a"],[K["b"].protocol]:G,[K["b"].property]:H,[K["b"].propertyListKey]:H,[K["b"].resources]:r["a"],[K["b"].sampleCode]:j["a"],[K["b"].struct]:H,[K["b"].subscript]:N,[K["b"].symbol]:x,[K["b"].tutorial]:c["a"],[K["b"].typealias]:H,[K["b"].union]:H,[K["b"].var]:H},J={[K["b"].class]:{symbol:"C"},[K["b"].dictionarySymbol]:{symbol:"O"},[K["b"].enum]:{symbol:"E"},[K["b"].extension]:{first:"E",second:"x"},[K["b"].httpRequest]:{symbol:"E"},[K["b"].method]:{symbol:"M"},[K["b"].macro]:{symbol:"#"},[K["b"].protocol]:{first:"P",second:"r"},[K["b"].property]:{symbol:"P"},[K["b"].propertyListKey]:{symbol:"K"},[K["b"].struct]:{symbol:"S"},[K["b"].typealias]:{symbol:"T"},[K["b"].union]:{symbol:"U"},[K["b"].var]:{symbol:"V"}};var Q={name:"TopicTypeIcon",components:{OverridableAsset:Y["a"],SVGIcon:d["a"],SingleLetterSymbolIcon:H},constants:{TopicTypeIcons:X,TopicTypeProps:J},props:{type:{type:String,required:!0},withColors:{type:Boolean,default:!1},imageOverride:{type:Object,default:null},shouldCalculateOptimalWidth:{type:Boolean,default:!0}},computed:{normalisedType:({type:e})=>K["a"][e]||e,icon:({normalisedType:e})=>X[e]||x,iconProps:({normalisedType:e})=>J[e]||{},color:({normalisedType:e})=>Z["b"][e],styles:({color:e,withColors:t})=>t&&e?{"--icon-color":`var(--color-type-icon-${e})`}:{}}},ee=Q,te=(n("9a61"),Object(m["a"])(ee,i,a,!1,null,"18b61706",null));t["a"]=te.exports},f2af:function(e,t,n){"use strict";n.d(t,"a",(function(){return s}));let i=!1,a=-1,r=0;const s="data-scroll-lock-disable",o=()=>window.navigator&&window.navigator.platform&&(/iP(ad|hone|od)/.test(window.navigator.platform)||"MacIntel"===window.navigator.platform&&window.navigator.maxTouchPoints>1);function c(e){e.touches.length>1||e.preventDefault()}const l=e=>!!e&&e.scrollHeight-e.scrollTop<=e.clientHeight;function u(){r=document.body.getBoundingClientRect().top,document.body.style.overflow="hidden scroll",document.body.style.top=r+"px",document.body.style.position="fixed",document.body.style.width="100%"}function d(e){e&&(e.ontouchstart=null,e.ontouchmove=null),document.removeEventListener("touchmove",c)}function p(e,t){const n=e.targetTouches[0].clientY-a,i=e.target.closest(`[${s}]`)||t;return 0===i.scrollTop&&n>0||l(i)&&n<0?c(e):(e.stopPropagation(),!0)}function h(e){document.addEventListener("touchmove",c,{passive:!1}),e&&(e.ontouchstart=e=>{1===e.targetTouches.length&&(a=e.targetTouches[0].clientY)},e.ontouchmove=t=>{1===t.targetTouches.length&&p(t,e)})}t["b"]={lockScroll(e){i||(o()?h(e):u(),i=!0)},unlockScroll(e){i&&(o()?d(e):(document.body.style.removeProperty("overflow"),document.body.style.removeProperty("top"),document.body.style.removeProperty("position"),document.body.style.removeProperty("width"),window.scrollTo(0,Math.abs(r))),i=!1)}}},fb8e:function(e,t,n){"use strict";n("6058")},fda2:function(e,t,n){},fdd9:function(e,t,n){"use strict";var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return e.shouldUseAsset?n("ImageAsset",e._b({},"ImageAsset",{variants:e.variants,loading:null,shouldCalculateOptimalWidth:e.shouldCalculateOptimalWidth,alt:e.alt},!1)):n("SVGIcon",{attrs:{"icon-url":e.iconUrl,themeId:e.themeId}})},a=[],r=n("8bd9"),s=n("be08"),o={name:"OverridableAsset",components:{SVGIcon:s["a"],ImageAsset:r["a"]},props:{imageOverride:{type:Object,default:null},shouldCalculateOptimalWidth:{type:Boolean,default:!0}},computed:{variants:({imageOverride:e})=>e?e.variants:[],alt:({imageOverride:e})=>e.alt,firstVariant:({variants:e})=>e[0],iconUrl:({firstVariant:e})=>e&&e.url,themeId:({firstVariant:e})=>e&&e.svgID,isSameOrigin:({iconUrl:e,sameOrigin:t})=>t(e),shouldUseAsset:({isSameOrigin:e,themeId:t})=>!e||!t},methods:{sameOrigin(e){if(!e)return!1;const t=new URL(e,window.location),n=new URL(window.location);return t.origin===n.origin}}},c=o,l=n("2877"),u=Object(l["a"])(c,i,a,!1,null,null,null);t["a"]=u.exports},fe08:function(e,t,n){"use strict";n("a7d8")},fe1c:function(e,t,n){"use strict";n("81c0")}}]);