/*!
 * This source file is part of the Swift.org open source project
 * 
 * Copyright (c) 2021 Apple Inc. and the Swift project authors
 * Licensed under Apache License v2.0 with Runtime Library Exception
 * 
 * See https://swift.org/LICENSE.txt for license information
 * See https://swift.org/CONTRIBUTORS.txt for Swift project authors
 */
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["documentation-topic~topic"],{"1c3f":function(t,e,n){"use strict";n("b826")},"2b88":function(t,e,n){"use strict";
/*! 
  * portal-vue © Thorsten Lünborg, 2019 
  * 
  * Version: 2.1.7
  * 
  * LICENCE: MIT 
  * 
  * https://github.com/linusborg/portal-vue
  * 
 */function s(t){return t&&"object"===typeof t&&"default"in t?t["default"]:t}Object.defineProperty(e,"__esModule",{value:!0});var r=s(n("2b0e"));function o(t){return o="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"===typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},o(t)}function a(t){return i(t)||l(t)||c()}function i(t){if(Array.isArray(t)){for(var e=0,n=new Array(t.length);e<t.length;e++)n[e]=t[e];return n}}function l(t){if(Symbol.iterator in Object(t)||"[object Arguments]"===Object.prototype.toString.call(t))return Array.from(t)}function c(){throw new TypeError("Invalid attempt to spread non-iterable instance")}var u="undefined"!==typeof window;function h(t){return Array.isArray(t)||"object"===o(t)?Object.freeze(t):t}function d(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return t.reduce((function(t,n){var s=n.passengers[0],r="function"===typeof s?s(e):n.passengers;return t.concat(r)}),[])}function f(t,e){return t.map((function(t,e){return[e,t]})).sort((function(t,n){return e(t[1],n[1])||t[0]-n[0]})).map((function(t){return t[1]}))}function p(t,e){return e.reduce((function(e,n){return t.hasOwnProperty(n)&&(e[n]=t[n]),e}),{})}var m={},g={},y={},b=r.extend({data:function(){return{transports:m,targets:g,sources:y,trackInstances:u}},methods:{open:function(t){if(u){var e=t.to,n=t.from,s=t.passengers,o=t.order,a=void 0===o?1/0:o;if(e&&n&&s){var i={to:e,from:n,passengers:h(s),order:a},l=Object.keys(this.transports);-1===l.indexOf(e)&&r.set(this.transports,e,[]);var c=this.$_getTransportIndex(i),d=this.transports[e].slice(0);-1===c?d.push(i):d[c]=i,this.transports[e]=f(d,(function(t,e){return t.order-e.order}))}}},close:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=t.to,s=t.from;if(n&&(s||!1!==e)&&this.transports[n])if(e)this.transports[n]=[];else{var r=this.$_getTransportIndex(t);if(r>=0){var o=this.transports[n].slice(0);o.splice(r,1),this.transports[n]=o}}},registerTarget:function(t,e,n){u&&(this.trackInstances&&!n&&this.targets[t]&&console.warn("[portal-vue]: Target ".concat(t," already exists")),this.$set(this.targets,t,Object.freeze([e])))},unregisterTarget:function(t){this.$delete(this.targets,t)},registerSource:function(t,e,n){u&&(this.trackInstances&&!n&&this.sources[t]&&console.warn("[portal-vue]: source ".concat(t," already exists")),this.$set(this.sources,t,Object.freeze([e])))},unregisterSource:function(t){this.$delete(this.sources,t)},hasTarget:function(t){return!(!this.targets[t]||!this.targets[t][0])},hasSource:function(t){return!(!this.sources[t]||!this.sources[t][0])},hasContentFor:function(t){return!!this.transports[t]&&!!this.transports[t].length},$_getTransportIndex:function(t){var e=t.to,n=t.from;for(var s in this.transports[e])if(this.transports[e][s].from===n)return+s;return-1}}}),v=new b(m),T=1,S=r.extend({name:"portal",props:{disabled:{type:Boolean},name:{type:String,default:function(){return String(T++)}},order:{type:Number,default:0},slim:{type:Boolean},slotProps:{type:Object,default:function(){return{}}},tag:{type:String,default:"DIV"},to:{type:String,default:function(){return String(Math.round(1e7*Math.random()))}}},created:function(){var t=this;this.$nextTick((function(){v.registerSource(t.name,t)}))},mounted:function(){this.disabled||this.sendUpdate()},updated:function(){this.disabled?this.clear():this.sendUpdate()},beforeDestroy:function(){v.unregisterSource(this.name),this.clear()},watch:{to:function(t,e){e&&e!==t&&this.clear(e),this.sendUpdate()}},methods:{clear:function(t){var e={from:this.name,to:t||this.to};v.close(e)},normalizeSlots:function(){return this.$scopedSlots.default?[this.$scopedSlots.default]:this.$slots.default},normalizeOwnChildren:function(t){return"function"===typeof t?t(this.slotProps):t},sendUpdate:function(){var t=this.normalizeSlots();if(t){var e={from:this.name,to:this.to,passengers:a(t),order:this.order};v.open(e)}else this.clear()}},render:function(t){var e=this.$slots.default||this.$scopedSlots.default||[],n=this.tag;return e&&this.disabled?e.length<=1&&this.slim?this.normalizeOwnChildren(e)[0]:t(n,[this.normalizeOwnChildren(e)]):this.slim?t():t(n,{class:{"v-portal":!0},style:{display:"none"},key:"v-portal-placeholder"})}}),w=r.extend({name:"portalTarget",props:{multiple:{type:Boolean,default:!1},name:{type:String,required:!0},slim:{type:Boolean,default:!1},slotProps:{type:Object,default:function(){return{}}},tag:{type:String,default:"div"},transition:{type:[String,Object,Function]}},data:function(){return{transports:v.transports,firstRender:!0}},created:function(){var t=this;this.$nextTick((function(){v.registerTarget(t.name,t)}))},watch:{ownTransports:function(){this.$emit("change",this.children().length>0)},name:function(t,e){v.unregisterTarget(e),v.registerTarget(t,this)}},mounted:function(){var t=this;this.transition&&this.$nextTick((function(){t.firstRender=!1}))},beforeDestroy:function(){v.unregisterTarget(this.name)},computed:{ownTransports:function(){var t=this.transports[this.name]||[];return this.multiple?t:0===t.length?[]:[t[t.length-1]]},passengers:function(){return d(this.ownTransports,this.slotProps)}},methods:{children:function(){return 0!==this.passengers.length?this.passengers:this.$scopedSlots.default?this.$scopedSlots.default(this.slotProps):this.$slots.default||[]},noWrapper:function(){var t=this.slim&&!this.transition;return t&&this.children().length>1&&console.warn("[portal-vue]: PortalTarget with `slim` option received more than one child element."),t}},render:function(t){var e=this.noWrapper(),n=this.children(),s=this.transition||this.tag;return e?n[0]:this.slim&&!s?t():t(s,{props:{tag:this.transition&&this.tag?this.tag:void 0},class:{"vue-portal-target":!0}},n)}}),C=0,$=["disabled","name","order","slim","slotProps","tag","to"],k=["multiple","transition"],x=r.extend({name:"MountingPortal",inheritAttrs:!1,props:{append:{type:[Boolean,String]},bail:{type:Boolean},mountTo:{type:String,required:!0},disabled:{type:Boolean},name:{type:String,default:function(){return"mounted_"+String(C++)}},order:{type:Number,default:0},slim:{type:Boolean},slotProps:{type:Object,default:function(){return{}}},tag:{type:String,default:"DIV"},to:{type:String,default:function(){return String(Math.round(1e7*Math.random()))}},multiple:{type:Boolean,default:!1},targetSlim:{type:Boolean},targetSlotProps:{type:Object,default:function(){return{}}},targetTag:{type:String,default:"div"},transition:{type:[String,Object,Function]}},created:function(){if("undefined"!==typeof document){var t=document.querySelector(this.mountTo);if(t){var e=this.$props;if(v.targets[e.name])e.bail?console.warn("[portal-vue]: Target ".concat(e.name," is already mounted.\n        Aborting because 'bail: true' is set")):this.portalTarget=v.targets[e.name];else{var n=e.append;if(n){var s="string"===typeof n?n:"DIV",r=document.createElement(s);t.appendChild(r),t=r}var o=p(this.$props,k);o.slim=this.targetSlim,o.tag=this.targetTag,o.slotProps=this.targetSlotProps,o.name=this.to,this.portalTarget=new w({el:t,parent:this.$parent||this,propsData:o})}}else console.error("[portal-vue]: Mount Point '".concat(this.mountTo,"' not found in document"))}},beforeDestroy:function(){var t=this.portalTarget;if(this.append){var e=t.$el;e.parentNode.removeChild(e)}t.$destroy()},render:function(t){if(!this.portalTarget)return console.warn("[portal-vue] Target wasn't mounted"),t();if(!this.$scopedSlots.manual){var e=p(this.$props,$);return t(S,{props:e,attrs:this.$attrs,on:this.$listeners,scopedSlots:this.$scopedSlots},this.$slots.default)}var n=this.$scopedSlots.manual({to:this.to});return Array.isArray(n)&&(n=n[0]),n||t()}});function P(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};t.component(e.portalName||"Portal",S),t.component(e.portalTargetName||"PortalTarget",w),t.component(e.MountingPortalName||"MountingPortal",x)}var I={install:P};e.default=I,e.Portal=S,e.PortalTarget=w,e.MountingPortal=x,e.Wormhole=v},"66c9":function(t,e,n){"use strict";e["a"]={state:{codeColors:null},reset(){this.state.codeColors=null},updateCodeColors(t){const e=t=>t?`rgba(${t.red}, ${t.green}, ${t.blue}, ${t.alpha})`:null;this.state.codeColors=Object.entries(t).reduce((t,[n,s])=>({...t,[n]:e(s)}),{})}}},7948:function(t,e,n){"use strict";var s=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("SVGIcon",{staticClass:"inline-chevron-down-icon",attrs:{viewBox:"0 0 14 14",themeId:"inline-chevron-down"}},[n("path",{attrs:{d:"M12.634 2.964l0.76 0.649-6.343 7.426-6.445-7.423 0.755-0.655 5.683 6.545 5.59-6.542z"}})])},r=[],o=n("be08"),a={name:"InlineChevronDownIcon",components:{SVGIcon:o["a"]}},i=a,l=n("2877"),c=Object(l["a"])(i,s,r,!1,null,null,null);e["a"]=c.exports},8590:function(t,e,n){"use strict";var s=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{style:t.codeStyle},[t._t("default")],2)},r=[],o=n("66c9");const a=0,i=255;function l(t){const e=t.match(/rgba\((\d+),\s*(\d+),\s*(\d+),\s*(\d+\.?\d*|\.\d+)\s*\)/);if(!e)throw new Error("invalid rgba() input");const n=10;return{r:parseInt(e[1],n),g:parseInt(e[2],n),b:parseInt(e[3],n),a:parseFloat(e[4])}}function c(t){const{r:e,g:n,b:s}=l(t);return.2126*e+.7152*n+.0722*s}function u(t,e){const n=Math.round(i*e),s=l(t),{a:r}=s,[o,c,u]=[s.r,s.g,s.b].map(t=>Math.max(a,Math.min(i,t+n)));return`rgba(${o}, ${c}, ${u}, ${r})`}function h(t,e){return u(t,e)}function d(t,e){return u(t,-1*e)}var f={name:"CodeTheme",data(){return{codeThemeState:o["a"].state}},computed:{codeStyle(){const{codeColors:t}=this.codeThemeState;return t?{"--text":t.text,"--background":t.background,"--line-highlight":t.lineHighlight,"--url":t.commentURL,"--syntax-comment":t.comment,"--syntax-quote":t.comment,"--syntax-keyword":t.keyword,"--syntax-literal":t.keyword,"--syntax-selector-tag":t.keyword,"--syntax-string":t.stringLiteral,"--syntax-bullet":t.stringLiteral,"--syntax-meta":t.keyword,"--syntax-number":t.stringLiteral,"--syntax-symbol":t.stringLiteral,"--syntax-tag":t.stringLiteral,"--syntax-attr":t.typeAnnotation,"--syntax-built_in":t.typeAnnotation,"--syntax-builtin-name":t.typeAnnotation,"--syntax-class":t.typeAnnotation,"--syntax-params":t.typeAnnotation,"--syntax-section":t.typeAnnotation,"--syntax-title":t.typeAnnotation,"--syntax-type":t.typeAnnotation,"--syntax-attribute":t.keyword,"--syntax-identifier":t.text,"--syntax-subst":t.text,"--color-syntax-param-internal-name":this.internalParamNameColor}:null},internalParamNameColor(){const{background:t,text:e}=this.codeThemeState.codeColors;try{const n=c(t),s=n<i/2?d:h,r=.25;return s(e,r)}catch{return e}}}},p=f,m=n("2877"),g=Object(m["a"])(p,s,r,!1,null,null,null);e["a"]=g.exports},b826:function(t,e,n){},c161:function(t,e,n){"use strict";var s=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("PortalSource",{attrs:{to:"modal-destination",disabled:!t.isVisible}},[n("div",{directives:[{name:"show",rawName:"v-show",value:t.isVisible,expression:"isVisible"}],staticClass:"generic-modal",class:[t.stateClasses,t.themeClass],style:t.modalColors,attrs:{role:"dialog"}},[n("div",{staticClass:"backdrop",on:{click:t.onClickOutside}}),n("div",{ref:"container",staticClass:"container",style:{width:t.width}},[t.showClose?n("button",{ref:"close",staticClass:"close",attrs:{"aria-label":t.$t("verbs.close")},on:{click:function(e){return e.preventDefault(),t.closeModal.apply(null,arguments)}}},[n("CloseIcon")],1):t._e(),n("div",{ref:"content",staticClass:"modal-content"},[t._t("default")],2)])])])},r=[],o=n("f2af"),a=n("c8e2"),i=n("95da"),l=n("2b88"),c=n("a919");const u={light:"light",dark:"dark",dynamic:"dynamic",code:"code"};var h={name:"GenericModal",model:{prop:"visible",event:"update:visible"},components:{CloseIcon:c["a"],PortalSource:l["Portal"]},props:{visible:{type:Boolean,default:!1},isFullscreen:{type:Boolean,default:!1},theme:{type:String,validator:t=>Object.keys(u).includes(t),default:u.light},codeBackgroundColorOverride:{type:String,default:""},backdropBackgroundColorOverride:{type:String,default:""},width:{type:String,default:null},showClose:{type:Boolean,default:!0}},data(){return{lastFocusItem:null,prefersDarkStyle:!1,focusTrapInstance:null}},computed:{isVisible:{get:({visible:t})=>t,set(t){this.$emit("update:visible",t)}},modalColors(){return{"--code-background":this.codeBackgroundColorOverride,"--backdrop-background":this.backdropBackgroundColorOverride}},themeClass({theme:t,prefersDarkStyle:e,isThemeDynamic:n}){let s={};return n&&(s={"theme-light":!e,"theme-dark":e}),["theme-"+t,s]},stateClasses:({isFullscreen:t,isVisible:e,showClose:n})=>({"modal-fullscreen":t,"modal-standard":!t,"modal-open":e,"modal-with-close":n}),isThemeDynamic:({theme:t})=>t===u.dynamic||t===u.code},watch:{isVisible(t){t?this.onShow():this.onHide()}},mounted(){if(this.focusTrapInstance=new a["a"],document.addEventListener("keydown",this.onKeydown),this.isThemeDynamic){const t=window.matchMedia("(prefers-color-scheme: dark)");t.addListener(this.onColorSchemePreferenceChange),this.$once("hook:beforeDestroy",()=>{t.removeListener(this.onColorSchemePreferenceChange)}),this.onColorSchemePreferenceChange(t)}},beforeDestroy(){this.isVisible&&o["b"].unlockScroll(this.$refs.container),document.removeEventListener("keydown",this.onKeydown),this.focusTrapInstance.destroy()},methods:{async onShow(){await this.$nextTick(),o["b"].lockScroll(this.$refs.container),await this.focusCloseButton(),this.focusTrapInstance.updateFocusContainer(this.$refs.container),this.focusTrapInstance.start(),i["a"].hide(this.$refs.container)},onHide(){o["b"].unlockScroll(this.$refs.container),this.focusTrapInstance.stop(),this.lastFocusItem&&(this.lastFocusItem.focus({preventScroll:!0}),this.lastFocusItem=null),this.$emit("close"),i["a"].show(this.$refs.container)},closeModal(){this.isVisible=!1},selectContent(){window.getSelection().selectAllChildren(this.$refs.content)},onClickOutside(){this.closeModal()},onKeydown(t){const{metaKey:e=!1,ctrlKey:n=!1,key:s}=t;this.isVisible&&("a"===s&&(e||n)&&(t.preventDefault(),this.selectContent()),"Escape"===s&&(t.preventDefault(),this.closeModal()))},onColorSchemePreferenceChange({matches:t}){this.prefersDarkStyle=t},async focusCloseButton(){this.lastFocusItem=document.activeElement,await this.$nextTick(),this.$refs.close&&this.$refs.close.focus(),this.$emit("open")}}},d=h,f=(n("1c3f"),n("2877")),p=Object(f["a"])(d,s,r,!1,null,"795f7b59",null);e["a"]=p.exports},c8e2:function(t,e,n){"use strict";function s(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}n.d(e,"a",(function(){return o}));var r=n("0cb0");class o{constructor(t){s(this,"focusContainer",null),s(this,"tabTargets",[]),s(this,"firstTabTarget",null),s(this,"lastTabTarget",null),s(this,"lastFocusedElement",null),this.focusContainer=t,this.onFocus=this.onFocus.bind(this)}updateFocusContainer(t){this.focusContainer=t}start(){this.collectTabTargets(),this.firstTabTarget?this.focusContainer.contains(document.activeElement)&&r["a"].isTabbableElement(document.activeElement)||this.firstTabTarget.focus():console.warn("There are no focusable elements. FocusTrap needs at least one."),this.lastFocusedElement=document.activeElement,document.addEventListener("focus",this.onFocus,!0)}stop(){document.removeEventListener("focus",this.onFocus,!0)}collectTabTargets(){this.tabTargets=r["a"].getTabbableElements(this.focusContainer),this.firstTabTarget=this.tabTargets[0],this.lastTabTarget=this.tabTargets[this.tabTargets.length-1]}onFocus(t){if(this.focusContainer.contains(t.target))this.lastFocusedElement=t.target;else{if(t.preventDefault(),this.collectTabTargets(),this.lastFocusedElement===this.lastTabTarget||!this.lastFocusedElement||!document.contains(this.lastFocusedElement))return this.firstTabTarget.focus(),void(this.lastFocusedElement=this.firstTabTarget);this.lastFocusedElement===this.firstTabTarget&&(this.lastTabTarget.focus(),this.lastFocusedElement=this.lastTabTarget)}}destroy(){this.stop(),this.focusContainer=null,this.tabTargets=[],this.firstTabTarget=null,this.lastTabTarget=null,this.lastFocusedElement=null}}}}]);