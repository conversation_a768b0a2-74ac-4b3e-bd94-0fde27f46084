/*!
 * This source file is part of the Swift.org open source project
 * 
 * Copyright (c) 2021 Apple Inc. and the Swift project authors
 * Licensed under Apache License v2.0 with Runtime Library Exception
 * 
 * See https://swift.org/LICENSE.txt for license information
 * See https://swift.org/CONTRIBUTORS.txt for Swift project authors
 */(function(e){function t(t){for(var i,o,s=t[0],c=t[1],l=t[2],u=0,h=[];u<s.length;u++)o=s[u],Object.prototype.hasOwnProperty.call(r,o)&&r[o]&&h.push(r[o][0]),r[o]=0;for(i in c)Object.prototype.hasOwnProperty.call(c,i)&&(e[i]=c[i]);d&&d(t);while(h.length)h.shift()();return a.push.apply(a,l||[]),n()}function n(){for(var e,t=0;t<a.length;t++){for(var n=a[t],i=!0,o=1;o<n.length;o++){var s=n[o];0!==r[s]&&(i=!1)}i&&(a.splice(t--,1),e=c(c.s=n[0]))}return e}var i={},o={index:0},r={index:0},a=[];function s(e){return c.p+"js/"+({"documentation-topic~topic~tutorials-overview":"documentation-topic~topic~tutorials-overview","documentation-topic~topic":"documentation-topic~topic","documentation-topic":"documentation-topic",topic:"topic","tutorials-overview":"tutorials-overview","highlight-js-bash":"highlight-js-bash","highlight-js-c":"highlight-js-c","highlight-js-cpp":"highlight-js-cpp","highlight-js-css":"highlight-js-css","highlight-js-custom-markdown":"highlight-js-custom-markdown","highlight-js-custom-swift":"highlight-js-custom-swift","highlight-js-diff":"highlight-js-diff","highlight-js-http":"highlight-js-http","highlight-js-java":"highlight-js-java","highlight-js-javascript":"highlight-js-javascript","highlight-js-json":"highlight-js-json","highlight-js-llvm":"highlight-js-llvm","highlight-js-markdown":"highlight-js-markdown","highlight-js-objectivec":"highlight-js-objectivec","highlight-js-perl":"highlight-js-perl","highlight-js-php":"highlight-js-php","highlight-js-python":"highlight-js-python","highlight-js-ruby":"highlight-js-ruby","highlight-js-scss":"highlight-js-scss","highlight-js-shell":"highlight-js-shell","highlight-js-swift":"highlight-js-swift","highlight-js-xml":"highlight-js-xml"}[e]||e)+"."+{"documentation-topic~topic~tutorials-overview":"90c61522","chunk-c0335d80":"76a68cc5","documentation-topic~topic":"1679ec90","documentation-topic":"57e91f8a",topic:"8cd0c0c4","tutorials-overview":"2a32cd6f","chunk-2d0d3105":"cd72cc8e","highlight-js-bash":"1b52852f","highlight-js-c":"d1db3f17","highlight-js-cpp":"eaddddbe","highlight-js-css":"75eab1fe","highlight-js-custom-markdown":"7cffc4b3","highlight-js-custom-swift":"5cda5c20","highlight-js-diff":"62d66733","highlight-js-http":"163e45b6","highlight-js-java":"8326d9d8","highlight-js-javascript":"acb8a8eb","highlight-js-json":"471128d2","highlight-js-llvm":"6100b125","highlight-js-markdown":"90077643","highlight-js-objectivec":"bcdf5156","highlight-js-perl":"757d7b6f","highlight-js-php":"cc8d6c27","highlight-js-python":"c214ed92","highlight-js-ruby":"f889d392","highlight-js-scss":"62ee18da","highlight-js-shell":"dd7f411f","highlight-js-swift":"84f3e88c","highlight-js-xml":"9c3688c7"}[e]+".js"}function c(t){if(i[t])return i[t].exports;var n=i[t]={i:t,l:!1,exports:{}};return e[t].call(n.exports,n,n.exports,c),n.l=!0,n.exports}c.e=function(e){var t=[],n={"documentation-topic~topic~tutorials-overview":1,"chunk-c0335d80":1,"documentation-topic~topic":1,"documentation-topic":1,topic:1,"tutorials-overview":1};o[e]?t.push(o[e]):0!==o[e]&&n[e]&&t.push(o[e]=new Promise((function(t,n){for(var i="css/"+({"documentation-topic~topic~tutorials-overview":"documentation-topic~topic~tutorials-overview","documentation-topic~topic":"documentation-topic~topic","documentation-topic":"documentation-topic",topic:"topic","tutorials-overview":"tutorials-overview","highlight-js-bash":"highlight-js-bash","highlight-js-c":"highlight-js-c","highlight-js-cpp":"highlight-js-cpp","highlight-js-css":"highlight-js-css","highlight-js-custom-markdown":"highlight-js-custom-markdown","highlight-js-custom-swift":"highlight-js-custom-swift","highlight-js-diff":"highlight-js-diff","highlight-js-http":"highlight-js-http","highlight-js-java":"highlight-js-java","highlight-js-javascript":"highlight-js-javascript","highlight-js-json":"highlight-js-json","highlight-js-llvm":"highlight-js-llvm","highlight-js-markdown":"highlight-js-markdown","highlight-js-objectivec":"highlight-js-objectivec","highlight-js-perl":"highlight-js-perl","highlight-js-php":"highlight-js-php","highlight-js-python":"highlight-js-python","highlight-js-ruby":"highlight-js-ruby","highlight-js-scss":"highlight-js-scss","highlight-js-shell":"highlight-js-shell","highlight-js-swift":"highlight-js-swift","highlight-js-xml":"highlight-js-xml"}[e]||e)+"."+{"documentation-topic~topic~tutorials-overview":"d6f5411c","chunk-c0335d80":"10a2f091","documentation-topic~topic":"b6287bcf","documentation-topic":"1d1eec04",topic:"d8c126f3","tutorials-overview":"c249c765","chunk-2d0d3105":"31d6cfe0","highlight-js-bash":"31d6cfe0","highlight-js-c":"31d6cfe0","highlight-js-cpp":"31d6cfe0","highlight-js-css":"31d6cfe0","highlight-js-custom-markdown":"31d6cfe0","highlight-js-custom-swift":"31d6cfe0","highlight-js-diff":"31d6cfe0","highlight-js-http":"31d6cfe0","highlight-js-java":"31d6cfe0","highlight-js-javascript":"31d6cfe0","highlight-js-json":"31d6cfe0","highlight-js-llvm":"31d6cfe0","highlight-js-markdown":"31d6cfe0","highlight-js-objectivec":"31d6cfe0","highlight-js-perl":"31d6cfe0","highlight-js-php":"31d6cfe0","highlight-js-python":"31d6cfe0","highlight-js-ruby":"31d6cfe0","highlight-js-scss":"31d6cfe0","highlight-js-shell":"31d6cfe0","highlight-js-swift":"31d6cfe0","highlight-js-xml":"31d6cfe0"}[e]+".css",r=c.p+i,a=document.getElementsByTagName("link"),s=0;s<a.length;s++){var l=a[s],u=l.getAttribute("data-href")||l.getAttribute("href");if("stylesheet"===l.rel&&(u===i||u===r))return t()}var h=document.getElementsByTagName("style");for(s=0;s<h.length;s++){l=h[s],u=l.getAttribute("data-href");if(u===i||u===r)return t()}var d=document.createElement("link");d.rel="stylesheet",d.type="text/css",d.onload=t,d.onerror=function(t){var i=t&&t.target&&t.target.src||r,a=new Error("Loading CSS chunk "+e+" failed.\n("+i+")");a.code="CSS_CHUNK_LOAD_FAILED",a.request=i,delete o[e],d.parentNode.removeChild(d),n(a)},d.href=r;var g=document.getElementsByTagName("head")[0];g.appendChild(d)})).then((function(){o[e]=0})));var i=r[e];if(0!==i)if(i)t.push(i[2]);else{var a=new Promise((function(t,n){i=r[e]=[t,n]}));t.push(i[2]=a);var l,u=document.createElement("script");u.charset="utf-8",u.timeout=120,c.nc&&u.setAttribute("nonce",c.nc),u.src=s(e);var h=new Error;l=function(t){u.onerror=u.onload=null,clearTimeout(d);var n=r[e];if(0!==n){if(n){var i=t&&("load"===t.type?"missing":t.type),o=t&&t.target&&t.target.src;h.message="Loading chunk "+e+" failed.\n("+i+": "+o+")",h.name="ChunkLoadError",h.type=i,h.request=o,n[1](h)}r[e]=void 0}};var d=setTimeout((function(){l({type:"timeout",target:u})}),12e4);u.onerror=u.onload=l,document.head.appendChild(u)}return Promise.all(t)},c.m=e,c.c=i,c.d=function(e,t,n){c.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},c.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},c.t=function(e,t){if(1&t&&(e=c(e)),8&t)return e;if(4&t&&"object"===typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(c.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)c.d(n,i,function(t){return e[t]}.bind(null,i));return n},c.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return c.d(t,"a",t),t},c.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},c.p="{{BASE_PATH}}/",c.oe=function(e){throw console.error(e),e};var l=window["webpackJsonp"]=window["webpackJsonp"]||[],u=l.push.bind(l);l.push=t,l=l.slice();for(var h=0;h<l.length;h++)t(l[h]);var d=u;a.push([0,"chunk-vendors"]),n()})({0:function(e,t,n){e.exports=n("f161")},"002d":function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"c",(function(){return s})),n.d(t,"b",(function(){return c})),n.d(t,"d",(function(){return l})),n.d(t,"g",(function(){return u})),n.d(t,"f",(function(){return h})),n.d(t,"e",(function(){return d}));n("e7a5");const i=/(?:\s+|[`"<>])/g,o=/^-+/,r=/["'&<>]/g;function a(e){return e.trim().replace(i,"-").replace(o,"").toLowerCase()}function s(e){const t=e=>({'"':"&quot;","'":"&apos;","&":"&amp;","<":"&lt;",">":"&gt;"}[e]||e);return e.replace(r,t)}function c(e){return e.replace(/#(.*)/,(e,t)=>"#"+CSS.escape(t))}function l(e){return e.replace(/[.*+\-?^${}()|[\]\\]/g,"\\$&")}function u(e){let t,n;const i="\\s*",o=" ",r=e.trim(),a=r.length;if(!a)return o;const s=[];for(t=0;t<a;t+=1)n=r[t],"\\"===n?(s.push(`${0===t?"":i}${n}`),s.push(r[t+1]),t+=1):0===t?s.push(n):n!==o&&s.push(`${i}${n}`);return s.join("")}function h(e,t,n=0){return`${e.slice(0,n)}${t}${e.slice(n)}`}function d(e){const t=e.split(/(?:\r?\n)+/);return t[0]}},"01da":function(e,t,n){},"161e":function(e,t,n){},2788:function(e,t,n){"use strict";n.d(t,"a",(function(){return u})),n.d(t,"b",(function(){return h}));var i=n("6842"),o=n("d26a");const r=Object(i["c"])(["meta","title"],"Documentation"),a=({title:e,description:t,url:n,currentLocale:i})=>[{name:"description",content:t},{property:"og:locale",content:i},{property:"og:site_name",content:r},{property:"og:type",content:"website"},{property:"og:title",content:e},{property:"og:description",content:t},{property:"og:url",content:n},{property:"og:image",content:Object(o["e"])("/developer-og.jpg")},{name:"twitter:image",content:Object(o["e"])("/developer-og-twitter.jpg")},{name:"twitter:card",content:"summary_large_image"},{name:"twitter:description",content:t},{name:"twitter:title",content:e},{name:"twitter:url",content:n}],s=e=>[e,r].filter(Boolean).join(" | "),c=e=>{const{content:t}=e,n=e.property?"property":"name",i=e[n],o=document.querySelector(`meta[${n}="${i}"]`);if(o&&t)o.setAttribute("content",t);else if(o&&!t)o.remove();else if(t){const t=document.createElement("meta");t.setAttribute(n,e[n]),t.setAttribute("content",e.content),document.getElementsByTagName("head")[0].appendChild(t)}},l=e=>{document.title=e};function u({title:e,description:t,url:n,currentLocale:i}){const o=s(e);l(o),a({title:o,description:t,url:n,currentLocale:i}).forEach(e=>c(e))}function h(e){document.querySelector("html").setAttribute("lang",e)}},"34b0":function(e,t,n){"use strict";var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("SVGIcon",{staticClass:"inline-chevron-right-icon",attrs:{viewBox:"0 0 14 14",themeId:"inline-chevron-right"}},[n("path",{attrs:{d:"M2.964 1.366l0.649-0.76 7.426 6.343-7.423 6.445-0.655-0.755 6.545-5.683-6.542-5.59z"}})])},o=[],r=n("be08"),a={name:"InlineChevronRightIcon",components:{SVGIcon:r["a"]}},s=a,c=n("2877"),l=Object(c["a"])(s,i,o,!1,null,null,null);t["a"]=l.exports},3502:function(e,t,n){},3908:function(e,t,n){"use strict";function i(e){let t=null,n=e-1;const i=new Promise(e=>{t=e});return requestAnimationFrame((function e(){n-=1,n<=0?t():requestAnimationFrame(e)})),i}function o(e){return new Promise(t=>{setTimeout(t,e)})}n.d(t,"b",(function(){return i})),n.d(t,"a",(function(){return o}))},"3be4":function(e,t,n){},"3ca7":function(e){e.exports=JSON.parse('[{"code":"en-US","name":"English","slug":"en-US"},{"code":"zh-CN","name":"简体中文","slug":"zh-CN"},{"code":"ja-JP","name":"日本語","slug":"ja-JP"}]')},4009:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));const i="app-top"},"48b1":function(e,t,n){"use strict";n("e487")},5522:function(e,t,n){"use strict";n("a3e8")},"5c0b":function(e,t,n){"use strict";n("9c0c")},"5d2d":function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"c",(function(){return c})),n.d(t,"b",(function(){return l}));const i="developer.setting.";function o(e=localStorage){return{getItem:t=>{try{return e.getItem(t)}catch(n){return null}},setItem:(t,n)=>{try{e.setItem(t,n)}catch(i){}},removeItem:t=>{try{e.removeItem(t)}catch(n){}}}}function r(e){return{get:(t,n)=>{const o=JSON.parse(e.getItem(i+t));return null!==o?o:n},set:(t,n)=>e.setItem(i+t,JSON.stringify(n)),remove:t=>e.removeItem(i+t)}}const a=o(window.localStorage),s=o(window.sessionStorage),c=r(a),l=r(s)},"63b8":function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"c",(function(){return o})),n.d(t,"a",(function(){return r})),n.d(t,"d",(function(){return s}));const i={large:"large",medium:"medium",small:"small"},o={default:"default",nav:"nav"},r={[o.default]:{[i.large]:{minWidth:1069,contentWidth:980},[i.medium]:{minWidth:736,maxWidth:1068,contentWidth:692},[i.small]:{minWidth:320,maxWidth:735,contentWidth:280}},[o.nav]:{[i.large]:{minWidth:1024},[i.medium]:{minWidth:768,maxWidth:1023},[i.small]:{minWidth:320,maxWidth:767}}},a={[i.small]:0,[i.medium]:1,[i.large]:2};function s(e,t){return a[e]>a[t]}},6842:function(e,t,n){"use strict";function i(e,t,n){let i,o=e,r=t;for("string"===typeof r&&(r=[r]),i=0;i<r.length;i+=1){if("undefined"===typeof o[r[i]])return n;o=o[r[i]]}return o}n.d(t,"d",(function(){return r})),n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return s})),n.d(t,"c",(function(){return c}));var o=n("d26a");const r={meta:{},theme:{},features:{}},{baseUrl:a}=window;async function s(){const e=Object(o["e"])("/theme-settings.json");return fetch(e).then(e=>e.json()).catch(()=>({}))}const c=(e,t)=>i(r,e,t)},"748c":function(e,t,n){"use strict";function i(e){return e.reduce((e,t)=>(t.traits.includes("dark")?e.dark.push(t):e.light.push(t),e),{light:[],dark:[]})}function o(e){const t=["1x","2x","3x"];return t.reduce((t,n)=>{const i=e.find(e=>e.traits.includes(n));return i?t.concat({density:n,src:i.url,size:i.size}):t},[])}function r(e){const t="/",n=new RegExp(t+"+","g");return e.join(t).replace(n,t)}function a(e){const{baseUrl:t}=window,n=Array.isArray(e)?r(e):e;return n&&"string"===typeof n&&!n.startsWith(t)&&n.startsWith("/")?r([t,n]):n}function s(e){return e?e.startsWith("/")?e:"/"+e:e}function c(e){return e?`url('${a(e)}')`:void 0}function l(e){return new Promise((t,n)=>{const i=new Image;i.src=e,i.onerror=n,i.onload=()=>t({width:i.width,height:i.height})})}n.d(t,"e",(function(){return i})),n.d(t,"a",(function(){return o})),n.d(t,"c",(function(){return a})),n.d(t,"d",(function(){return s})),n.d(t,"f",(function(){return c})),n.d(t,"b",(function(){return l}))},"7ac6":function(e,t,n){},"7d8d":function(e){e.exports=JSON.parse('{"view-in":"日本語で表示","continue-viewing":"日本語で表示を続ける","language":"言語","video":{"replay":"リプレイ","play":"再生","pause":"一時停止","watch":"概要のビデオを観る"},"tutorials":{"title":"チュートリアル | チュートリアル","step":"手順{number}","submit":"送信","next":"次へ","preview":{"title":"プレビューなし | プレビュー | プレビュー","no-preview-available-step":"この手順では利用可能なプレビューがありません。"},"nav":{"chapters":"章","current":"現在の{thing}"},"assessment":{"check-your-understanding":"理解度を確認する","success-message":"よくできました。このチュートリアルの問題にすべて回答しました。","answer-number-is":"問題番号{index}は","correct":"正解です","incorrect":"不正解です","next-question":"次の問題"},"project-files":"プロジェクトファイル","estimated-time":"予測時間","sections":{"chapter":"{number}章"},"question-of":"{total}問中の{index}問","section-of":"{total}件中の{number}件","overriding-title":"{title}の{newTitle}","time":{"format":"{number} {minutes}","minutes":{"full":"分 | 分 | {count}分","short":"分 | 分"},"hours":{"full":"時間 | 時間"}}},"documentation":{"title":"ドキュメント","nav":{"breadcrumbs":"パンくずリスト","menu":"メニュー","open-menu":"メニューを開く","close-menu":"メニューを閉じる"},"current-page":"現在のページは{title}です","card":{"learn-more":"詳しい情報","read-article":"記事を読む","start-tutorial":"チュートリアルを開始","view-api":"APIのコレクションを表示","view-symbol":"記号を表示","view-sample-code":"サンプルコードを表示"}},"aside-kind":{"beta":"ベータ版","experiment":"試験運用版","important":"重要","note":"注意","tip":"ヒント","warning":"警告","deprecated":"非推奨"},"change-type":{"added":"追加","modified":"変更","deprecated":"非推奨"},"verbs":{"hide":"非表示","show":"表示","close":"閉じる"},"sections":{"title":"セクション{number}","on-this-page":"このページの内容","topics":"トピック","default-implementations":"デフォルト実装","relationships":"関連項目","see-also":"参照","declaration":"宣言","details":"詳細","parameters":"パラメータ","possible-values":"使用できる値","parts":"パーツ","availability":"利用可能","resources":"リソース"},"metadata":{"details":{"name":"名前","key":"キー","type":"タイプ"},"beta":{"legal":"このドキュメントはベータ版のソフトウェアのもので、変更される可能性があります。","software":"ベータ版ソフトウェア"},"default-implementation":"デフォルト実装あり。| デフォルト実装あり。"},"availability":{"introduced-and-deprecated":"{name} {introducedAt}で導入され、{name} {deprecatedAt}で非推奨になりました","available-on":"{name} {introducedAt}以降で使用できます"},"more":"さらに表示","less":"表示を減らす","api-reference":"APIリファレンス","filter":{"title":"フィルタ","search-symbols":"{technology}でシンボルを検索","suggested-tags":"提案されたタグ | 提案されたタグ","selected-tags":"選択したタグ | 選択したタグ","add-tag":"タグを追加","tag-select-remove":"タグ。選択してリストから削除します。","navigate":"シンボルを移動するには、上下左右の矢印キーを押します。","siblings-label":"{total-siblings}個中{number-siblings}個のシンボルが{parent-siblings}の中にあります","parent-label":"{total-siblings}個中{number-siblings}個のシンボルが1個のシンボルを含む{parent-siblings}の中にあります | {total-siblings}個中{number-siblings}個のシンボルが{number-parent}個のシンボルを含む{parent-siblings}の中にあります","reset-filter":"フィルタをリセット"},"navigator":{"title":"ドキュメントナビゲータ","open-navigator":"ドキュメントナビゲータを開く","close-navigator":"ドキュメントナビゲータを閉じる","no-results":"結果が見つかりません。","no-children":"使用できるデータがありません。","error-fetching":"データを取得する際にエラーが起きました。","items-found":"項目が見つかりません | 1個の項目が見つかりました | {number}個の項目が見つかりましたTabキーを押すと項目をナビゲートできます。","navigator-is":"ナビゲータは{state}です","state":{"loading":"読み込み中","ready":"準備完了"},"tags":{"hide-deprecated":"非推奨の項目を非表示"}},"tab":{"request":"リクエスト","response":"レスポンス"},"required":"必須","parameters":{"default":"デフォルト","minimum":"最小","maximum":"最大","possible-types":"タイプ | 使用できるタイプ","possible-values":"値 | 使用できる値"},"content-type":"Content-Type: {value}","read-only":"読み出し専用","error":{"unknown":"原因不明のエラーが起きました。","image":"イメージを読み込めませんでした"},"color-scheme":{"select":"カラースキーム環境設定を選択","auto":"自動","dark":"ダーク","light":"ライト"},"accessibility":{"strike":{"start":"取り消し線テキストの開始","end":"取り消し線テキストの終了"},"code":{"start":"コードブロックの開始","end":"コードブロックの終了"},"skip-navigation":"ナビゲーションをスキップ"},"select-language":"このページの言語を選択","icons":{"clear":"消去","web-service-endpoint":"Webサービスのエンドポイント","search":"検索"},"formats":{"parenthesis":"（{content}）","colon":"{content}: "},"quicknav":{"button":{"label":"クイックナビゲーションを開く","title":"クリックするか「/」を入力すると素早く移動します"}}}')},"821b":function(e,t,n){"use strict";t["a"]={auto:"auto",dark:"dark",light:"light"}},"942d":function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"c",(function(){return o})),n.d(t,"e",(function(){return r})),n.d(t,"d",(function(){return a})),n.d(t,"a",(function(){return s}));const i=52,o=48,r="nav-sticky-anchor",a="nav-open-navigator",s={noClose:"noclose"}},"94a8":function(e){e.exports=JSON.parse('{"view-in":"View in English","continue-viewing":"Continue viewing in English","language":"Language","video":{"replay":"Replay","play":"Play","pause":"Pause","watch":"Watch intro video"},"tutorials":{"title":"Tutorial | Tutorials","step":"Step {number}","submit":"Submit","next":"Next","preview":{"title":"No Preview | Preview | Previews","no-preview-available-step":"No preview available for this step."},"nav":{"chapters":"Chapters","current":"Current {thing}"},"assessment":{"check-your-understanding":"Check Your Understanding","success-message":"Great job, you\'ve answered all the questions for this tutorial.","answer-number-is":"Answer number {index} is","correct":"correct","incorrect":"incorrect","next-question":"Next question"},"project-files":"Project files","estimated-time":"Estimated Time","sections":{"chapter":"Chapter {number}"},"question-of":"Question {index} of {total}","section-of":"{number} of {total}","overriding-title":"{newTitle} with {title}","time":{"format":"{number} {minutes}","minutes":{"full":"minute | minutes | {count} minutes","short":"min | mins"},"hours":{"full":"hour | hours"}}},"documentation":{"title":"Documentation","nav":{"breadcrumbs":"Breadcrumbs","menu":"Menu","open-menu":"Open Menu","close-menu":"Close Menu"},"current-page":"Current page is {title}","card":{"learn-more":"Learn More","read-article":"Read article","start-tutorial":"Start tutorial","view-api":"View API collection","view-symbol":"View symbol","view-sample-code":"View sample code"}},"aside-kind":{"beta":"Beta","experiment":"Experiment","important":"Important","note":"Note","tip":"Tip","warning":"Warning","deprecated":"Deprecated"},"change-type":{"added":"Added","modified":"Modified","deprecated":"Deprecated"},"verbs":{"hide":"Hide","show":"Show","close":"Close"},"sections":{"title":"Section {number}","on-this-page":"On this page","topics":"Topics","default-implementations":"Default Implementations","relationships":"Relationships","see-also":"See Also","declaration":"Declaration","details":"Details","parameters":"Parameters","possible-values":"Possible Values","parts":"Parts","availability":"Availability","resources":"Resources"},"metadata":{"details":{"name":"Name","key":"Key","type":"Type"},"beta":{"legal":"This documentation refers to beta software and may be changed.","software":"Beta Software"},"default-implementation":"Default implementation provided. | Default implementations provided."},"availability":{"introduced-and-deprecated":"Introduced in {name} {introducedAt} and deprecated in {name} {deprecatedAt}","available-on":"Available on {name} {introducedAt} and later"},"more":"More","less":"Less","api-reference":"API Reference","filter":{"title":"Filter","search-symbols":"Search symbols in {technology}","suggested-tags":"Suggested tag | Suggested tags","selected-tags":"Selected tag | Selected tags","add-tag":"Add tag","tag-select-remove":"Tag. Select to remove from list.","navigate":"To navigate the symbols, press Up Arrow, Down Arrow, Left Arrow or Right Arrow","siblings-label":"{number-siblings} of {total-siblings} symbols inside {parent-siblings}","parent-label":"{number-siblings} of {total-siblings} symbols inside {parent-siblings} containing one symbol | {number-siblings} of {total-siblings} symbols inside {parent-siblings} containing {number-parent} symbols","reset-filter":"Reset Filter"},"navigator":{"title":"Documentation Navigator","open-navigator":"Open Documentation Navigator","close-navigator":"Close Documentation Navigator","no-results":"No results found.","no-children":"No data available.","error-fetching":"There was an error fetching the data.","items-found":"No items were found | 1 item was found | {number} items were found. Tab back to navigate through them.","navigator-is":"Navigator is {state}","state":{"loading":"loading","ready":"ready"},"tags":{"hide-deprecated":"Hide Deprecated"}},"tab":{"request":"Request","response":"Response"},"required":"Required","parameters":{"default":"Default","minimum":"Minimum","maximum":"Maximum","possible-types":"Type | Possible types","possible-values":"Value | Possible Values"},"content-type":"Content-Type: {value}","read-only":"Read-only","error":{"unknown":"An unknown error occurred.","image":"Image failed to load","not-found":"The page you\'re looking for can\'t be found."},"color-scheme":{"select":"Select a color scheme preference","auto":"Auto","dark":"Dark","light":"Light"},"accessibility":{"strike":{"start":"start of stricken text","end":"end of stricken text"},"code":{"start":"start of code block","end":"end of code block"},"skip-navigation":"Skip Navigation","in-page-link":"in page link"},"select-language":"Select the language for this page","icons":{"clear":"Clear","web-service-endpoint":"Web Service Endpoint","search":"Search"},"formats":{"parenthesis":"({content})","colon":"{content}: "},"quicknav":{"button":{"label":"Open Quick Navigation","title":"Click or type / for quick navigation"}}}')},9895:function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"a",(function(){return o}));const i="not-found",o="documentation-topic"},9923:function(e,t,n){"use strict";n.r(t),n.d(t,"defaultLocale",(function(){return a})),n.d(t,"messages",(function(){return s}));var i=n("94a8"),o=n("d9c4"),r=n("7d8d");const a="en-US",s={"en-US":i,"zh-CN":o,"ja-JP":r}},"9b56":function(e,t,n){"use strict";n.d(t,"a",(function(){return s})),n.d(t,"b",(function(){return l})),n.d(t,"c",(function(){return u}));var i=n("3ca7"),o=n("9923"),r=n("2788");const a=i.reduce((e,t)=>({...e,[t.slug]:t.code}),{});function s(e){return a[e]}function c(e){return!!a[e]}function l(e){return{params:{locale:e===o["defaultLocale"]?void 0:e}}}function u(e=o["defaultLocale"],t){if(!c(e))return;t.$i18n.locale=e;const n=s(e);Object(r["b"])(n)}},"9c0c":function(e,t,n){},"9dba":function(e,t,n){"use strict";n("3502")},a2be:function(e,t,n){"use strict";n("01da")},a3e8:function(e,t,n){},a919:function(e,t,n){"use strict";var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("SVGIcon",{staticClass:"close-icon",attrs:{viewBox:"0 0 14 14",themeId:"close"}},[n("path",{attrs:{d:"M12.73,0l1.27,1.27-5.74,5.73,5.72,5.72-1.27,1.27-5.72-5.72L1.28,13.99,.01,12.72,5.72,7.01,0,1.28,1.27,.01,6.99,5.73,12.73,0Z"}})])},o=[],r=n("be08"),a={name:"CloseIcon",components:{SVGIcon:r["a"]}},s=a,c=n("2877"),l=Object(c["a"])(s,i,o,!1,null,null,null);t["a"]=l.exports},be08:function(e,t,n){"use strict";var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("svg",{staticClass:"svg-icon",attrs:{"aria-hidden":"true",xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink"}},[e.themeOverrideURL?n("use",{attrs:{href:e.themeOverrideURL+"#"+e.themeId,width:"100%",height:"100%"}}):e._t("default")],2)},o=[],r=n("6842"),a={name:"SVGIcon",props:{themeId:{type:String,required:!1},iconUrl:{type:String,default:null}},computed:{themeOverrideURL:({iconUrl:e,themeId:t})=>e||Object(r["c"])(["theme","icons",t],void 0)}},s=a,c=(n("c2c4"),n("2877")),l=Object(c["a"])(s,i,o,!1,null,"33d3200a",null);t["a"]=l.exports},c2c4:function(e,t,n){"use strict";n("161e")},c83d:function(e,t,n){},d26a:function(e,t,n){"use strict";n.d(t,"d",(function(){return r})),n.d(t,"b",(function(){return a})),n.d(t,"a",(function(){return s})),n.d(t,"c",(function(){return c})),n.d(t,"e",(function(){return l}));var i=n("748c"),o={input:"input",tags:"tags"};function r(e={}){return Object.entries(e).reduce((e,[t,n])=>n?e.concat(`${encodeURIComponent(t)}=${encodeURIComponent(n)}`):e,[]).join("&")}function a(e,{changes:t,language:n,context:i}={}){const[o,a]=e.split("#"),s=o.match(/\?.*/),c=r({changes:t,language:n,context:i}),l=s?"&":"?",u=a?o:e,h=c?`${l}${c}`:"",d=a?"#"+a:"";return`${u}${h}${d}`}function s(e,t){const{query:{changes:n,[o.input]:i,[o.tags]:r,...a}={}}=e,{query:{changes:s,[o.input]:c,[o.tags]:l,...u}={}}=t;return e.name===t.name&&JSON.stringify({path:e.path,query:a})===JSON.stringify({path:t.path,query:u})}function c(e,t=window.location.href){return new URL(Object(i["c"])(e),t)}function l(e,t){return c(e,t).href}},d369:function(e,t,n){"use strict";var i=n("5d2d");const o={preferredColorScheme:"developer.setting.preferredColorScheme",preferredLocale:"developer.setting.preferredLocale",preferredLanguage:"docs.setting.preferredLanguage"},r={preferredColorScheme:"docs.setting.preferredColorScheme"};t["a"]=Object.defineProperties({},Object.keys(o).reduce((e,t)=>({...e,[t]:{get:()=>{const e=r[t],n=i["a"].getItem(o[t]);return e?n||i["a"].getItem(e):n},set:e=>i["a"].setItem(o[t],e)}}),{}))},d9c4:function(e){e.exports=JSON.parse('{"view-in":"以中文查看","continue-viewing":"继续以中文查看","language":"语言","video":{"replay":"重新播放","play":"播放","pause":"暂停","watch":"观看介绍视频"},"tutorials":{"title":"教程","step":"第 {number} 步","submit":"提交","next":"下一步","preview":{"title":"无预览 | 预览","no-preview-available-step":"这一步没有预览。"},"nav":{"chapters":"章节","current":"当前{thing}"},"assessment":{"check-your-understanding":"检查你的理解程度","success-message":"很棒，你回答了此教程的所有问题。","answer-number-is":"第 {index} 个答案","correct":"正确","incorrect":"错误","next-question":"下一个问题"},"project-files":"项目文件","estimated-time":"预计时间","sections":{"chapter":"第 {number} 章"},"question-of":"第 {index} 个问题（共 {total} 个）","section-of":"{number}/{total}","overriding-title":"{newTitle}{title}","time":{"format":"{number} {minutes}","minutes":{"full":"分钟 | {count} 分钟","short":"分钟"},"hours":{"full":"小时"}}},"documentation":{"title":"文档","nav":{"breadcrumbs":"面包屑导航","menu":"菜单","open-menu":"打开菜单","close-menu":"关闭菜单"},"current-page":"当前页面为：{title}","card":{"learn-more":"进一步了解","read-article":"阅读文章","start-tutorial":"开始教程","view-api":"查看 API 集合","view-symbol":"查看符号","view-sample-code":"查看示例代码"}},"aside-kind":{"beta":"Beta 版","experiment":"试验","important":"重要事项","note":"注","tip":"提示","warning":"警告","deprecated":"已弃用"},"change-type":{"added":"已添加","modified":"已修改","deprecated":"已弃用"},"verbs":{"hide":"隐藏","show":"显示","close":"关闭"},"sections":{"title":"第 {number} 部分","on-this-page":"在此页面上","topics":"主题","default-implementations":"默认实现","relationships":"关系","see-also":"另请参阅","declaration":"声明","details":"详细信息","parameters":"参数","possible-values":"可能值","parts":"部件","availability":"可用性","resources":"资源"},"metadata":{"details":{"name":"名称","key":"密钥","type":"类型"},"beta":{"legal":"此文档涉及 Beta 版软件且可能会改动。","software":"Beta 版软件"},"default-implementation":"提供默认实现。| 提供默认实现方法。"},"availability":{"introduced-and-deprecated":"{name} {introducedAt} 中引入，{name} {deprecatedAt} 中弃用","available-on":"{name} {introducedAt} 及更高版本中可用"},"more":"更多","less":"更少","api-reference":"API 参考","filter":{"title":"过滤","search-symbols":"在 {technology} 搜索符号","suggested-tags":"建议标签","selected-tags":"所选标签","add-tag":"添加标签","tag-select-remove":"标签。选择以从列表中移除。","navigate":"若要导航符号，请按下上箭头、下箭头、左箭头或右箭头。","siblings-label":"{parent-siblings} 内含 {number-siblings} 个符号（共 {total-siblings} 个）","parent-label":"{parent-siblings} 内含 {number-siblings} 个符号（共 {total-siblings} 个）包含一个符号 | {parent-siblings} 内含 {number-siblings} 个符号（共 {total-siblings} 个）包含 {number-parent} 个符号","reset-filter":"还原过滤条件"},"navigator":{"title":"文档导航器","open-navigator":"打开文档导航器","close-navigator":"关闭文档导航器","no-results":"未找到结果。","no-children":"无可用数据。","error-fetching":"获取数据时出错。","items-found":"未找到任何项目 | 找到 1 个项目 | 找到 {number} 个项目。按下 Tab 键导航。","navigator-is":"导航器{state}","state":{"loading":"正在载入","ready":"准备就绪"},"tags":{"hide-deprecated":"隐藏已弃用"}},"tab":{"request":"请求","response":"回复"},"required":"必需","parameters":{"default":"默认","minimum":"最小值","maximum":"最大值","possible-types":"类型 | 可能类型","possible-values":"值 | 可能值"},"content-type":"内容类型：{value}","read-only":"只读","error":{"unknown":"出现未知错误。","image":"图像无法载入"},"color-scheme":{"select":"选择首选颜色方案","auto":"自动","dark":"深色","light":"浅色"},"accessibility":{"strike":{"start":"删除线文本开始","end":"删除线文本结束"},"code":{"start":"代码块开头","end":"代码块结尾"},"skip-navigation":"跳过导航"},"select-language":"选择此页面的语言","icons":{"clear":"清除","web-service-endpoint":"网络服务端点","search":"搜索"},"formats":{"parenthesis":"({content})","colon":"{content}： "},"quicknav":{"button":{"label":"打开快速导航","title":"点按或键入 / 进行快速导航"}}}')},dd18:function(e,t,n){"use strict";t["a"]={eager:"eager",lazy:"lazy"}},e425:function(e,t,n){"use strict";var i=n("821b"),o=n("dd18"),r=n("d369"),a=n("3ca7");const s="undefined"!==typeof window.matchMedia&&[i["a"].light,i["a"].dark,"no-preference"].some(e=>window.matchMedia(`(prefers-color-scheme: ${e})`).matches),c=s?i["a"].auto:i["a"].light;t["a"]={state:{imageLoadingStrategy:"ide"===Object({NODE_ENV:"production",VUE_APP_TITLE:"Documentation",BASE_URL:"{{BASE_PATH}}/"}).VUE_APP_TARGET?o["a"].eager:o["a"].lazy,preferredColorScheme:r["a"].preferredColorScheme||c,preferredLocale:r["a"].preferredLocale,supportsAutoColorScheme:s,systemColorScheme:i["a"].light,availableLocales:[]},reset(){this.state.imageLoadingStrategy="ide"===Object({NODE_ENV:"production",VUE_APP_TITLE:"Documentation",BASE_URL:"{{BASE_PATH}}/"}).VUE_APP_TARGET?o["a"].eager:o["a"].lazy,this.state.preferredColorScheme=r["a"].preferredColorScheme||c,this.state.supportsAutoColorScheme=s,this.state.systemColorScheme=i["a"].light},setImageLoadingStrategy(e){this.state.imageLoadingStrategy=e},setPreferredColorScheme(e){this.state.preferredColorScheme=e,r["a"].preferredColorScheme=e},setAllLocalesAreAvailable(){const e=a.map(e=>e.code);this.state.availableLocales=e},setAvailableLocales(e=[]){this.state.availableLocales=e},setPreferredLocale(e){this.state.preferredLocale=e,r["a"].preferredLocale=this.state.preferredLocale},setSystemColorScheme(e){this.state.systemColorScheme=e},syncPreferredColorScheme(){r["a"].preferredColorScheme&&r["a"].preferredColorScheme!==this.state.preferredColorScheme&&(this.state.preferredColorScheme=r["a"].preferredColorScheme)}}},e487:function(e,t,n){},e759:function(e,t,n){"use strict";n("3be4")},e9e6:function(e,t,n){"use strict";n("7ac6")},ed78:function(e,t,n){"use strict";n("c83d")},ed96:function(e,t,n){n.p=window.baseUrl},f161:function(e,t,n){"use strict";n.r(t);n("ed96");var i=n("2b0e"),o=n("a925"),r=n("8c4f"),a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{class:{fromkeyboard:e.fromKeyboard,hascustomheader:e.hasCustomHeader},attrs:{id:"app"}},[n("div",{attrs:{id:e.AppTopID}}),e.isTargetIDE?e._e():n("a",{attrs:{href:"#main",id:"skip-nav"}},[e._v(e._s(e.$t("accessibility.skip-navigation")))]),n("InitialLoadingPlaceholder"),e._t("header",(function(){return[e.enablei18n?n("SuggestLang"):e._e(),e.hasCustomHeader?n("custom-header",{attrs:{"data-color-scheme":e.preferredColorScheme}}):e._e()]}),{isTargetIDE:e.isTargetIDE}),n("div",{attrs:{id:e.baseNavStickyAnchorId}}),e._t("default",(function(){return[n("router-view",{staticClass:"router-content"}),e.hasCustomFooter?n("custom-footer",{attrs:{"data-color-scheme":e.preferredColorScheme}}):e.isTargetIDE?e._e():n("Footer",{scopedSlots:e._u([{key:"default",fn:function(t){var i=t.className;return[e.enablei18n?n("div",{class:i},[n("LocaleSelector")],1):e._e()]}}])})]}),{isTargetIDE:e.isTargetIDE}),e._t("footer",null,{isTargetIDE:e.isTargetIDE})],2)},s=[],c=n("e425"),l=n("821b"),u=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("footer",{staticClass:"footer"},[n("div",{staticClass:"row"},[n("ColorSchemeToggle")],1),e._t("default",null,{className:"row"})],2)},h=[],d=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"color-scheme-toggle",attrs:{"aria-label":e.$t("color-scheme.select"),role:"radiogroup"}},e._l(e.options,(function(t){return n("label",{key:t},[n("input",{attrs:{type:"radio"},domProps:{checked:t==e.preferredColorScheme,value:t},on:{input:e.setPreferredColorScheme}}),n("div",{staticClass:"text"},[e._v(e._s(e.$t("color-scheme."+t)))])])})),0)},g=[],m={name:"ColorSchemeToggle",data:()=>({appState:c["a"].state}),computed:{options:({supportsAutoColorScheme:e})=>[l["a"].light,l["a"].dark,...e?[l["a"].auto]:[]],preferredColorScheme:({appState:e})=>e.preferredColorScheme,supportsAutoColorScheme:({appState:e})=>e.supportsAutoColorScheme},methods:{setPreferredColorScheme:e=>{c["a"].setPreferredColorScheme(e.target.value)}},watch:{preferredColorScheme:{immediate:!0,handler(e){document.body.dataset.colorScheme=e}}}},p=m,f=(n("9dba"),n("2877")),b=Object(f["a"])(p,d,g,!1,null,"02a6f6ec",null),v=b.exports,w={name:"Footer",components:{ColorSchemeToggle:v}},y=w,j=(n("e9e6"),Object(f["a"])(y,u,h,!1,null,"4e049dbd",null)),S=j.exports,E=function(){var e=this,t=e.$createElement,n=e._self._c||t;return e.loaded?e._e():n("div",{staticClass:"InitialLoadingPlaceholder",attrs:{id:"loading-placeholder"}})},C=[],_={name:"InitialLoadingPlaceholder",data(){return{loaded:!1}},created(){const e=()=>{this.loaded=!0};this.$router.onReady(e,e)}},P=_,k=(n("48b1"),Object(f["a"])(P,E,C,!1,null,"35c356b6",null)),L=k.exports,A=n("942d"),T=n("6842");function x(e,t){return e&&"object"===typeof e&&Object.prototype.hasOwnProperty.call(e,t)&&"string"===typeof e[t]}function O(e,t,n,i){if(!t||"object"!==typeof t||i&&(x(t,"light")||x(t,"dark"))){let o=t;if(x(t,i)&&(o=t[i]),"object"===typeof o)return;n[e]=o}else Object.entries(t).forEach(([t,o])=>{const r=[e,t].join("-");O(r,o,n,i)})}function I(e,t="light"){const n={},i=e||{};return O("-",i,n,t),n}var $=n("4009"),D=function(){var e=this,t=e.$createElement,n=e._self._c||t;return e.displaySuggestLang?n("div",{staticClass:"suggest-lang"},[n("div",{staticClass:"suggest-lang__wrapper"},[n("router-link",{staticClass:"suggest-lang__link",attrs:{to:e.getLocaleParam(e.preferredLocale),lang:e.getCodeForSlug(e.preferredLocale)},nativeOn:{click:function(t){return e.setPreferredLocale(e.preferredLocale)}}},[e._v(e._s(e.$i18n.messages[e.preferredLocale]["view-in"])),n("InlineChevronRightIcon",{staticClass:"icon-inline"})],1),n("div",{staticClass:"suggest-lang__close-icon-wrapper"},[n("button",{staticClass:"suggest-lang__close-icon-button",attrs:{"aria-label":e.$t("continue-viewing")},on:{click:function(t){return e.setPreferredLocale(e.$i18n.locale)}}},[n("CloseIcon",{staticClass:"icon-inline"})],1)])],1)]):e._e()},N=[],R=n("34b0"),U=n("a919"),q=n("3ca7"),V=n("9b56"),B={name:"SuggestLang",components:{InlineChevronRightIcon:R["a"],CloseIcon:U["a"]},computed:{preferredLocale:()=>{const e=c["a"].state.preferredLocale;if(e)return e;const t=q.find(e=>{const t=e.code.split("-")[0],n=window.navigator.language.split("-")[0];return n===t});return t?t.slug:null},displaySuggestLang:({preferredLocale:e,$i18n:t})=>e&&t.locale!==e},methods:{setPreferredLocale:e=>{c["a"].setPreferredLocale(e)},getCodeForSlug:V["a"],getLocaleParam:V["b"]}},M=B,W=(n("ed78"),Object(f["a"])(M,D,N,!1,null,"ad72c62e",null)),F=W.exports,G=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"locale-selector"},[n("select",{attrs:{"aria-label":e.$t("select-language")},domProps:{value:e.$i18n.locale},on:{change:e.updateRouter}},e._l(e.locales,(function(t){var i=t.slug,o=t.name,r=t.code;return n("option",{key:i,attrs:{lang:r},domProps:{value:i}},[e._v(" "+e._s(o)+" ")])})),0),n("ChevronThickIcon",{staticClass:"icon-inline"})],1)},H=[],J=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("SVGIcon",{staticClass:"chevron-thick-icon",attrs:{viewBox:"0 0 14 10.5",themeId:"chevron-thick"}},[n("path",{attrs:{d:"M12.43,0l1.57,1.22L7,10.5,0,1.23,1.58,0,7,7,12.43,0Z"}})])},K=[],z=n("be08"),Y={name:"ChevronThickIcon",components:{SVGIcon:z["a"]}},Q=Y,Z=Object(f["a"])(Q,J,K,!1,null,null,null),X=Z.exports,ee={name:"LocaleSelector",components:{ChevronThickIcon:X},methods:{updateRouter({target:{value:e}}){this.$router.push(Object(V["b"])(e)),c["a"].setPreferredLocale(e),Object(V["c"])(e,this)}},computed:{availableLocales:()=>c["a"].state.availableLocales,locales:({availableLocales:e})=>q.filter(({code:t})=>e.includes(t))}},te=ee,ne=(n("e759"),Object(f["a"])(te,G,H,!1,null,"7e4d9b69",null)),ie=ne.exports,oe={name:"CoreApp",components:{Footer:S,InitialLoadingPlaceholder:L,SuggestLang:F,LocaleSelector:ie},provide(){return{isTargetIDE:this.isTargetIDE,performanceMetricsEnabled:"true"===Object({NODE_ENV:"production",VUE_APP_TITLE:"Documentation",BASE_URL:"{{BASE_PATH}}/"}).VUE_APP_PERFORMANCE_ENABLED}},data(){return{AppTopID:$["a"],appState:c["a"].state,fromKeyboard:!1,isTargetIDE:"ide"===Object({NODE_ENV:"production",VUE_APP_TITLE:"Documentation",BASE_URL:"{{BASE_PATH}}/"}).VUE_APP_TARGET,themeSettings:T["d"],baseNavStickyAnchorId:A["e"]}},computed:{currentColorScheme:({appState:e})=>e.systemColorScheme,preferredColorScheme:({appState:e})=>e.preferredColorScheme,availableLocales:({appState:e})=>e.availableLocales,CSSCustomProperties:({currentColorScheme:e,preferredColorScheme:t,themeSettings:n})=>I(n.theme,t===l["a"].auto?e:t),hasCustomHeader:()=>!!window.customElements.get("custom-header"),hasCustomFooter:()=>!!window.customElements.get("custom-footer"),enablei18n:({availableLocales:e})=>Object(T["c"])(["features","docs","i18n","enable"],!1)&&e.length>1},props:{enableThemeSettings:{type:Boolean,default:!0}},watch:{CSSCustomProperties:{immediate:!0,handler(e){this.detachStylesFromRoot(e),this.attachStylesToRoot(e)}}},async created(){window.addEventListener("keydown",this.onKeyDown),this.$bridge.on("navigation",this.handleNavigationRequest),this.enableThemeSettings&&Object.assign(this.themeSettings,await Object(T["b"])()),window.addEventListener("pageshow",this.syncPreferredColorScheme),this.$once("hook:beforeDestroy",()=>{window.removeEventListener("pageshow",this.syncPreferredColorScheme)})},mounted(){(document.querySelector(".footer-current-year")||{}).innerText=(new Date).getFullYear(),this.attachColorSchemeListeners()},beforeDestroy(){this.fromKeyboard?window.removeEventListener("mousedown",this.onMouseDown):window.removeEventListener("keydown",this.onKeyDown),this.$bridge.off("navigation",this.handleNavigationRequest),this.detachStylesFromRoot(this.CSSCustomProperties)},methods:{onKeyDown(){this.fromKeyboard=!0,window.addEventListener("mousedown",this.onMouseDown),window.removeEventListener("keydown",this.onKeyDown)},onMouseDown(){this.fromKeyboard=!1,window.addEventListener("keydown",this.onKeyDown),window.removeEventListener("mousedown",this.onMouseDown)},handleNavigationRequest(e){this.$router.push(e)},attachColorSchemeListeners(){if(!window.matchMedia)return;const e=window.matchMedia("(prefers-color-scheme: dark)");e.addListener(this.onColorSchemePreferenceChange),this.$once("hook:beforeDestroy",()=>{e.removeListener(this.onColorSchemePreferenceChange)}),this.onColorSchemePreferenceChange(e)},onColorSchemePreferenceChange({matches:e}){const t=e?l["a"].dark:l["a"].light;c["a"].setSystemColorScheme(t)},attachStylesToRoot(e){const t=document.body;Object.entries(e).filter(([,e])=>Boolean(e)).forEach(([e,n])=>{t.style.setProperty(e,n)})},detachStylesFromRoot(e){const t=document.body;Object.entries(e).forEach(([e])=>{t.style.removeProperty(e)})},syncPreferredColorScheme(){c["a"].syncPreferredColorScheme()}}},re=oe,ae=(n("5c0b"),n("a2be"),Object(f["a"])(re,a,s,!1,null,"7d594ed9",null)),se=ae.exports;class ce{constructor(){this.$send=()=>{}}send(e){this.$send(e)}}class le{constructor(){const{webkit:{messageHandlers:{bridge:e={}}={}}={}}=window;this.bridge=e;const{postMessage:t=(()=>{})}=e;this.$send=t.bind(e)}send(e){this.$send(e)}}class ue{constructor(e=new ce){this.backend=e,this.listeners={}}send(e){this.backend.send(e)}receive(e){this.emit(e.type,e.data)}emit(e,t){this.listeners[e]&&this.listeners[e].forEach(e=>e(t))}on(e,t){this.listeners[e]||(this.listeners[e]=new Set),this.listeners[e].add(t)}off(e,t){this.listeners[e]&&this.listeners[e].delete(t)}}var he={install(e,t){let n;n=t.performanceMetricsEnabled||"ide"===t.appTarget?new le:new ce,e.prototype.$bridge=new ue(n)}};function de(e){return"custom-"+e}function ge(e){return class extends HTMLElement{constructor(){super();const t=this.attachShadow({mode:"open"}),n=e.content.cloneNode(!0);t.appendChild(n)}}}function me(e){const t=de(e),n=document.getElementById(t);n&&window.customElements.define(t,ge(n))}function pe(e,t={names:["header","footer"]}){const{names:n}=t;e.config.ignoredElements=/^custom-/,n.forEach(me)}function fe(e,t){const{value:n=!1}=t;e.style.display=n?"none":""}var be={hide:fe};function ve(e,{performanceMetrics:t=!1}={}){e.config.productionTip=!1,e.use(pe),e.directive("hide",be.hide),e.use(he,{appTarget:Object({NODE_ENV:"production",VUE_APP_TITLE:"Documentation",BASE_URL:"{{BASE_PATH}}/"}).VUE_APP_TARGET,performanceMetricsEnabled:t}),window.bridge=e.prototype.$bridge,e.config.performance=t}var we=n("9895"),ye=n("63b8"),je=n("3908"),Se=n("002d"),Ee=n("d26a");const Ce=10;function _e(e){const{name:t}=e,n=t.includes(we["a"]);return n?Ce:0}function Pe(){const{location:e}=window;return e.pathname+e.search+e.hash}function ke(){const e=Math.max(document.documentElement.clientWidth||0,window.innerWidth||0);return e<ye["a"].nav.small.maxWidth?A["c"]:A["b"]}async function Le(e,t,n){if(n)return await this.app.$nextTick(),n;if(e.meta&&e.meta.preventScrolling)return!1;if(e.hash){const{name:t,query:n,hash:i}=e,o=t.includes(we["a"]),r=!!n.changes,a=ke(),s=o&&r?a:0,c=a+s+_e(e),l="ide"===Object({NODE_ENV:"production",VUE_APP_TITLE:"Documentation",BASE_URL:"{{BASE_PATH}}/"}).VUE_APP_TARGET?0:c;return{selector:Object(Se["b"])(i),offset:{x:0,y:l}}}return!Object(Ee["a"])(e,t)&&{x:0,y:0}}async function Ae(){let e=window.sessionStorage.getItem("scrollPosition");if(e){try{e=JSON.parse(e)}catch(t){return void console.error("Error parsing scrollPosition from sessionStorage",t)}Pe()===e.location&&(await Object(je["b"])(2),window.scrollTo(e.x,e.y))}}function Te(){window.location.hash||sessionStorage.setItem("scrollPosition",JSON.stringify({x:window.pageXOffset,y:window.pageYOffset,location:Pe()}))}var xe=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("GenericError")},Oe=[],Ie=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"generic-error"},[n("div",{staticClass:"container"},[n("h1",{staticClass:"title error-content"},[e._v(e._s(e.message||e.$t("error.unknown")))]),e._t("default")],2)])},$e=[],De={name:"GenericError",props:{message:{type:String,required:!1}}},Ne=De,Re=(n("5522"),Object(f["a"])(Ne,Ie,$e,!1,null,"1f05d9ec",null)),Ue=Re.exports,qe={name:"ServerError",components:{GenericError:Ue},created(){c["a"].setAllLocalesAreAvailable()}},Ve=qe,Be=Object(f["a"])(Ve,xe,Oe,!1,null,null,null),Me=Be.exports,We=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("GenericError",{attrs:{message:e.$t("error.not-found")}},[e._t("default")],2)},Fe=[],Ge={name:"NotFound",components:{GenericError:Ue},created(){c["a"].setAllLocalesAreAvailable()}},He=Ge,Je=Object(f["a"])(He,We,Fe,!1,null,null,null),Ke=Je.exports,ze=[{path:"/tutorials/:id",name:"tutorials-overview",component:()=>Promise.all([n.e("documentation-topic~topic~tutorials-overview"),n.e("tutorials-overview")]).then(n.bind(null,"f025"))},{path:"/tutorials/:id/*",name:"topic",component:()=>Promise.all([n.e("documentation-topic~topic~tutorials-overview"),n.e("documentation-topic~topic"),n.e("topic")]).then(n.bind(null,"3213"))},{path:"/documentation/*",name:we["a"],component:()=>Promise.all([n.e("documentation-topic~topic~tutorials-overview"),n.e("chunk-c0335d80"),n.e("documentation-topic~topic"),n.e("documentation-topic")]).then(n.bind(null,"f8ac"))},{path:"*",name:we["b"],component:Ke},{path:"*",name:"server-error",component:Me}];const Ye=[{pathPrefix:"/:locale?",nameSuffix:"-locale"}];function Qe(e,t=[],n=Ye){return n.reduce((n,i)=>n.concat(e.filter(e=>!t.includes(e.name)).map(e=>({...e,path:i.pathPrefix+e.path,name:e.name+i.nameSuffix}))),[])}const Ze=[...Qe(ze,[we["b"]]),...ze];function Xe(e={}){const t=new r["a"]({mode:"history",base:T["a"],scrollBehavior:Le,...e,routes:e.routes||Ze});return t.onReady(()=>{"scrollRestoration"in window.history&&(window.history.scrollRestoration="manual"),Ae()}),"ide"!==Object({NODE_ENV:"production",VUE_APP_TITLE:"Documentation",BASE_URL:"{{BASE_PATH}}/"}).VUE_APP_TARGET&&t.onError(e=>{const{route:n={path:"/"}}=e;t.replace({name:"server-error",params:[n.path]})}),window.addEventListener("unload",Te),t}var et=n("9923");function tt(e=et){const{defaultLocale:t,messages:n,dateTimeFormats:i={}}=e,r=new o["a"]({dateTimeFormats:i,locale:t,fallbackLocale:t,messages:n});return r}i["default"].use(ve),i["default"].use(r["a"]),i["default"].use(o["a"]),new i["default"]({router:Xe(),render:e=>e(se),i18n:tt()}).$mount("#app")}});