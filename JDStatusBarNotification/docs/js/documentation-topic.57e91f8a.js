/*!
 * This source file is part of the Swift.org open source project
 * 
 * Copyright (c) 2021 Apple Inc. and the Swift project authors
 * Licensed under Apache License v2.0 with Runtime Library Exception
 * 
 * See https://swift.org/LICENSE.txt for license information
 * See https://swift.org/CONTRIBUTORS.txt for Swift project authors
 */
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["documentation-topic"],{"00f2":function(e,t,n){"use strict";n("2089")},"0b72":function(e,t,n){},"115d":function(e,t,n){"use strict";n("20dd")},1347:function(e,t,n){"use strict";n("367e")},"14d4":function(e,t,n){},"18f4":function(e,t,n){},"1a39":function(e,t,n){"use strict";n("a7e9")},"1e0b":function(e,t,n){"use strict";n("412b")},"1fde":function(e,t,n){},2089:function(e,t,n){},"20dd":function(e,t,n){},"22f6":function(e,t,n){},2482:function(e,t,n){},2521:function(e,t,n){},"252c":function(e,t,n){"use strict";(function(e){function i(){var e=window.navigator.userAgent,t=e.indexOf("MSIE ");if(t>0)return parseInt(e.substring(t+5,e.indexOf(".",t)),10);var n=e.indexOf("Trident/");if(n>0){var i=e.indexOf("rv:");return parseInt(e.substring(i+3,e.indexOf(".",i)),10)}var a=e.indexOf("Edge/");return a>0?parseInt(e.substring(a+5,e.indexOf(".",a)),10):-1}n.d(t,"a",(function(){return r}));var a=void 0;function s(){s.init||(s.init=!0,a=-1!==i())}var r={render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"resize-observer",attrs:{tabindex:"-1"}})},staticRenderFns:[],_scopeId:"data-v-b329ee4c",name:"resize-observer",methods:{compareAndNotify:function(){this._w===this.$el.offsetWidth&&this._h===this.$el.offsetHeight||(this._w=this.$el.offsetWidth,this._h=this.$el.offsetHeight,this.$emit("notify"))},addResizeHandlers:function(){this._resizeObject.contentDocument.defaultView.addEventListener("resize",this.compareAndNotify),this.compareAndNotify()},removeResizeHandlers:function(){this._resizeObject&&this._resizeObject.onload&&(!a&&this._resizeObject.contentDocument&&this._resizeObject.contentDocument.defaultView.removeEventListener("resize",this.compareAndNotify),delete this._resizeObject.onload)}},mounted:function(){var e=this;s(),this.$nextTick((function(){e._w=e.$el.offsetWidth,e._h=e.$el.offsetHeight}));var t=document.createElement("object");this._resizeObject=t,t.setAttribute("aria-hidden","true"),t.setAttribute("tabindex",-1),t.onload=this.addResizeHandlers,t.type="text/html",a&&this.$el.appendChild(t),t.data="about:blank",a||this.$el.appendChild(t)},beforeDestroy:function(){this.removeResizeHandlers()}};function o(e){e.component("resize-observer",r),e.component("ResizeObserver",r)}var l={version:"0.4.5",install:o},c=null;"undefined"!==typeof window?c=window.Vue:"undefined"!==typeof e&&(c=e.Vue),c&&c.use(l)}).call(this,n("c8ba"))},2591:function(e,t,n){"use strict";n("f6d7")},"260a":function(e,t,n){"use strict";n("9a8a")},"264a":function(e,t,n){},2822:function(e,t,n){"use strict";n("2521")},"2c54":function(e,t,n){},"2ca2":function(e,t,n){"use strict";n("98e2")},"2d12":function(e,t,n){"use strict";n("b324")},"2eeb":function(e,t,n){},"2f87":function(e,t,n){"use strict";n("b0a0")},3396:function(e,t,n){"use strict";n("cdce")},"367e":function(e,t,n){},3702:function(e,t,n){},"370f":function(e,t,n){},"374e":function(e,t,n){"use strict";n("0b72")},"37dc":function(e,t,n){},"39d3":function(e,t,n){"use strict";n("2c54")},"3c37":function(e,t,n){},"3d94":function(e,t,n){},"3e80":function(e,t,n){},4125:function(e,t,n){},"412b":function(e,t,n){},"45b2":function(e,t,n){"use strict";n("264a")},"464f":function(e,t,n){},"46c5":function(e,t,n){"use strict";n("dff0")},5079:function(e,t,n){},"509b":function(e,t,n){},"51f2":function(e,t,n){},5208:function(e,t,n){"use strict";n("3d94")},5228:function(e,t,n){},"533e":function(e,t,n){},5561:function(e,t,n){"use strict";n("a2cc")},"5a73":function(e,t,n){"use strict";n("f9c9")},"5c57":function(e,t,n){"use strict";n("f0ff")},"645a":function(e,t,n){},6513:function(e,t,n){},6742:function(e,t,n){},"6a35":function(e,t,n){"use strict";n("d436")},"6ca9":function(e,t,n){"use strict";n("8429")},"6d05":function(e,t,n){"use strict";n("d7f6")},"719b":function(e,t,n){"use strict";n("8b3c")},"72a9":function(e,t,n){"use strict";n("d551")},7309:function(e,t,n){"use strict";n("c227")},"73a8":function(e,t,n){"use strict";n("3c37")},7649:function(e,t,n){"use strict";n("37dc")},"7a2c":function(e,t,n){"use strict";n("c4c1")},8429:function(e,t,n){},"857c":function(e,t,n){"use strict";n("645a")},"85fe":function(e,t,n){"use strict";(function(e){function i(e){return i="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i(e)}function a(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}function r(e,t,n){return t&&s(e.prototype,t),n&&s(e,n),e}function o(e){return l(e)||c(e)||d()}function l(e){if(Array.isArray(e)){for(var t=0,n=new Array(e.length);t<e.length;t++)n[t]=e[t];return n}}function c(e){if(Symbol.iterator in Object(e)||"[object Arguments]"===Object.prototype.toString.call(e))return Array.from(e)}function d(){throw new TypeError("Invalid attempt to spread non-iterable instance")}function u(e){var t;return t="function"===typeof e?{callback:e}:e,t}function h(e,t){var n,i,a,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=function(r){for(var l=arguments.length,c=new Array(l>1?l-1:0),d=1;d<l;d++)c[d-1]=arguments[d];if(a=c,!n||r!==i){var u=s.leading;"function"===typeof u&&(u=u(r,i)),n&&r===i||!u||e.apply(void 0,[r].concat(o(a))),i=r,clearTimeout(n),n=setTimeout((function(){e.apply(void 0,[r].concat(o(a))),n=0}),t)}};return r._clear=function(){clearTimeout(n),n=null},r}function p(e,t){if(e===t)return!0;if("object"===i(e)){for(var n in e)if(!p(e[n],t[n]))return!1;return!0}return!1}n.d(t,"a",(function(){return v}));var g=function(){function e(t,n,i){a(this,e),this.el=t,this.observer=null,this.frozen=!1,this.createObserver(n,i)}return r(e,[{key:"createObserver",value:function(e,t){var n=this;if(this.observer&&this.destroyObserver(),!this.frozen){if(this.options=u(e),this.callback=function(e,t){n.options.callback(e,t),e&&n.options.once&&(n.frozen=!0,n.destroyObserver())},this.callback&&this.options.throttle){var i=this.options.throttleOptions||{},a=i.leading;this.callback=h(this.callback,this.options.throttle,{leading:function(e){return"both"===a||"visible"===a&&e||"hidden"===a&&!e}})}this.oldResult=void 0,this.observer=new IntersectionObserver((function(e){var t=e[0];if(e.length>1){var i=e.find((function(e){return e.isIntersecting}));i&&(t=i)}if(n.callback){var a=t.isIntersecting&&t.intersectionRatio>=n.threshold;if(a===n.oldResult)return;n.oldResult=a,n.callback(a,t)}}),this.options.intersection),t.context.$nextTick((function(){n.observer&&n.observer.observe(n.el)}))}}},{key:"destroyObserver",value:function(){this.observer&&(this.observer.disconnect(),this.observer=null),this.callback&&this.callback._clear&&(this.callback._clear(),this.callback=null)}},{key:"threshold",get:function(){return this.options.intersection&&this.options.intersection.threshold||0}}]),e}();function f(e,t,n){var i=t.value;if(i)if("undefined"===typeof IntersectionObserver)console.warn("[vue-observe-visibility] IntersectionObserver API is not available in your browser. Please install this polyfill: https://github.com/w3c/IntersectionObserver/tree/master/polyfill");else{var a=new g(e,i,n);e._vue_visibilityState=a}}function m(e,t,n){var i=t.value,a=t.oldValue;if(!p(i,a)){var s=e._vue_visibilityState;i?s?s.createObserver(i,n):f(e,{value:i},n):y(e)}}function y(e){var t=e._vue_visibilityState;t&&(t.destroyObserver(),delete e._vue_visibilityState)}var v={bind:f,update:m,unbind:y};function b(e){e.directive("observe-visibility",v)}var T={version:"0.4.6",install:b},S=null;"undefined"!==typeof window?S=window.Vue:"undefined"!==typeof e&&(S=e.Vue),S&&S.use(T)}).call(this,n("c8ba"))},"8b3c":function(e,t,n){},"8e4d":function(e,t,n){"use strict";n("bdc3")},9475:function(e,t,n){"use strict";n("1fde")},"98e2":function(e,t,n){},"9a8a":function(e,t,n){},"9c7e":function(e,t,n){"use strict";n("5228")},"9c92":function(e,t,n){},"9cea":function(e,t,n){"use strict";n("a61f")},"9f0d":function(e,t,n){},"9f11":function(e,t,n){},"9f17":function(e,t,n){"use strict";n("6742")},a2cc:function(e,t,n){},a34a:function(e,t,n){"use strict";n("51f2")},a61f:function(e,t,n){},a7e9:function(e,t,n){},b0a0:function(e,t,n){},b324:function(e,t,n){},b32a:function(e,t,n){"use strict";n("3e80")},b39c:function(e,t,n){"use strict";n("18f4")},b831:function(e,t,n){"use strict";n("533e")},b857:function(e,t,n){"use strict";n("3702")},bdc3:function(e,t,n){},c1f5:function(e,t,n){"use strict";n("f4ae")},c227:function(e,t,n){},c4bc:function(e,t,n){"use strict";n("2eeb")},c4c1:function(e,t,n){},c61f:function(e,t,n){"use strict";n("509b")},c80b:function(e,t,n){"use strict";n("6513")},ca3d:function(e,t,n){"use strict";n("5079")},cb1f:function(e,t,n){"use strict";n("dd53")},cd88:function(e,t,n){},cdce:function(e,t,n){},d1ac:function(e,t,n){"use strict";n("9f11")},d1b4:function(e,t,n){"use strict";n("4125")},d436:function(e,t,n){},d551:function(e,t,n){},d71b:function(e,t,n){"use strict";n("9f0d")},d7f6:function(e,t,n){},dd53:function(e,t,n){},dfa5:function(e,t,n){"use strict";n("2482")},dff0:function(e,t,n){},e47c:function(e,t,n){"use strict";n("cd88")},e508:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return L})),n.d(t,"b",(function(){return M})),n.d(t,"c",(function(){return z}));var i=n("252c"),a=n("85fe"),s=n("ed83"),r=n.n(s),o=n("2b0e"),l={itemsLimit:1e3};const c={items:{type:Array,required:!0},keyField:{type:String,default:"id"},direction:{type:String,default:"vertical",validator:e=>["vertical","horizontal"].includes(e)},listTag:{type:String,default:"div"},itemTag:{type:String,default:"div"}};function d(){return this.items.length&&"object"!==typeof this.items[0]}let u=!1;if("undefined"!==typeof window){u=!1;try{var h=Object.defineProperty({},"passive",{get(){u=!0}});window.addEventListener("test",null,h)}catch(H){}}let p=0;var g={name:"RecycleScroller",components:{ResizeObserver:i["a"]},directives:{ObserveVisibility:a["a"]},props:{...c,itemSize:{type:Number,default:null},gridItems:{type:Number,default:void 0},itemSecondarySize:{type:Number,default:void 0},minItemSize:{type:[Number,String],default:null},sizeField:{type:String,default:"size"},typeField:{type:String,default:"type"},buffer:{type:Number,default:200},pageMode:{type:Boolean,default:!1},prerender:{type:Number,default:0},emitUpdate:{type:Boolean,default:!1},skipHover:{type:Boolean,default:!1},listTag:{type:String,default:"div"},itemTag:{type:String,default:"div"},listClass:{type:[String,Object,Array],default:""},itemClass:{type:[String,Object,Array],default:""}},data(){return{pool:[],totalSize:0,ready:!1,hoverKey:null}},computed:{sizes(){if(null===this.itemSize){const e={"-1":{accumulator:0}},t=this.items,n=this.sizeField,i=this.minItemSize;let a,s=1e4,r=0;for(let o=0,l=t.length;o<l;o++)a=t[o][n]||i,a<s&&(s=a),r+=a,e[o]={accumulator:r,size:a};return this.$_computedMinItemSize=s,e}return[]},simpleArray:d},watch:{items(){this.updateVisibleItems(!0)},pageMode(){this.applyPageMode(),this.updateVisibleItems(!1)},sizes:{handler(){this.updateVisibleItems(!1)},deep:!0},gridItems(){this.updateVisibleItems(!0)},itemSecondarySize(){this.updateVisibleItems(!0)}},created(){this.$_startIndex=0,this.$_endIndex=0,this.$_views=new Map,this.$_unusedViews=new Map,this.$_scrollDirty=!1,this.$_lastUpdateScrollPosition=0,this.prerender&&(this.$_prerender=!0,this.updateVisibleItems(!1)),this.gridItems&&!this.itemSize&&console.error("[vue-recycle-scroller] You must provide an itemSize when using gridItems")},mounted(){this.applyPageMode(),this.$nextTick(()=>{this.$_prerender=!1,this.updateVisibleItems(!0),this.ready=!0})},activated(){const e=this.$_lastUpdateScrollPosition;"number"===typeof e&&this.$nextTick(()=>{this.scrollToPosition(e)})},beforeDestroy(){this.removeListeners()},methods:{addView(e,t,n,i,a){const s={item:n,position:0},r={id:p++,index:t,used:!0,key:i,type:a};return Object.defineProperty(s,"nr",{configurable:!1,value:r}),e.push(s),s},unuseView(e,t=!1){const n=this.$_unusedViews,i=e.nr.type;let a=n.get(i);a||(a=[],n.set(i,a)),a.push(e),t||(e.nr.used=!1,e.position=-9999,this.$_views.delete(e.nr.key))},handleResize(){this.$emit("resize"),this.ready&&this.updateVisibleItems(!1)},handleScroll(e){this.$_scrollDirty||(this.$_scrollDirty=!0,requestAnimationFrame(()=>{this.$_scrollDirty=!1;const{continuous:e}=this.updateVisibleItems(!1,!0);e||(clearTimeout(this.$_refreshTimout),this.$_refreshTimout=setTimeout(this.handleScroll,100))}))},handleVisibilityChange(e,t){this.ready&&(e||0!==t.boundingClientRect.width||0!==t.boundingClientRect.height?(this.$emit("visible"),requestAnimationFrame(()=>{this.updateVisibleItems(!1)})):this.$emit("hidden"))},updateVisibleItems(e,t=!1){const n=this.itemSize,i=this.gridItems||1,a=this.itemSecondarySize||n,s=this.$_computedMinItemSize,r=this.typeField,o=this.simpleArray?null:this.keyField,c=this.items,d=c.length,u=this.sizes,h=this.$_views,p=this.$_unusedViews,g=this.pool;let f,m,y,v,b,T;if(d)if(this.$_prerender)f=v=0,m=b=Math.min(this.prerender,c.length),y=null;else{const e=this.getScroll();if(t){let t=e.start-this.$_lastUpdateScrollPosition;if(t<0&&(t=-t),null===n&&t<s||t<n)return{continuous:!0}}this.$_lastUpdateScrollPosition=e.start;const a=this.buffer;e.start-=a,e.end+=a;let r=0;if(this.$refs.before&&(r=this.$refs.before.scrollHeight,e.start-=r),this.$refs.after){const t=this.$refs.after.scrollHeight;e.end+=t}if(null===n){let t,n,i=0,a=d-1,s=~~(d/2);do{n=s,t=u[s].accumulator,t<e.start?i=s:s<d-1&&u[s+1].accumulator>e.start&&(a=s),s=~~((i+a)/2)}while(s!==n);for(s<0&&(s=0),f=s,y=u[d-1].accumulator,m=s;m<d&&u[m].accumulator<e.end;m++);for(-1===m?m=c.length-1:(m++,m>d&&(m=d)),v=f;v<d&&r+u[v].accumulator<e.start;v++);for(b=v;b<d&&r+u[b].accumulator<e.end;b++);}else{f=~~(e.start/n*i);const t=f%i;f-=t,m=Math.ceil(e.end/n*i),v=Math.max(0,Math.floor((e.start-r)/n*i)),b=Math.floor((e.end-r)/n*i),f<0&&(f=0),m>d&&(m=d),v<0&&(v=0),b>d&&(b=d),y=Math.ceil(d/i)*n}}else f=m=v=b=y=0;m-f>l.itemsLimit&&this.itemsLimitError(),this.totalSize=y;const S=f<=this.$_endIndex&&m>=this.$_startIndex;if(this.$_continuous!==S){if(S){h.clear(),p.clear();for(let e=0,t=g.length;e<t;e++)T=g[e],this.unuseView(T)}this.$_continuous=S}else if(S)for(let l=0,x=g.length;l<x;l++)T=g[l],T.nr.used&&(e&&(T.nr.index=c.indexOf(T.item)),(-1===T.nr.index||T.nr.index<f||T.nr.index>=m)&&this.unuseView(T));const _=S?null:new Map;let C,k,w,I;for(let l=f;l<m;l++){C=c[l];const e=o?C[o]:C;if(null==e)throw new Error(`Key is ${e} on item (keyField is '${o}')`);T=h.get(e),n||u[l].size?(T?(T.nr.used=!0,T.item=C):(l===c.length-1&&this.$emit("scroll-end"),0===l&&this.$emit("scroll-start"),k=C[r],w=p.get(k),S?w&&w.length?(T=w.pop(),T.item=C,T.nr.used=!0,T.nr.index=l,T.nr.key=e,T.nr.type=k):T=this.addView(g,l,C,e,k):(I=_.get(k)||0,(!w||I>=w.length)&&(T=this.addView(g,l,C,e,k),this.unuseView(T,!0),w=p.get(k)),T=w[I],T.item=C,T.nr.used=!0,T.nr.index=l,T.nr.key=e,T.nr.type=k,_.set(k,I+1),I++),h.set(e,T)),null===n?(T.position=u[l-1].accumulator,T.offset=0):(T.position=Math.floor(l/i)*n,T.offset=l%i*a)):T&&this.unuseView(T)}return this.$_startIndex=f,this.$_endIndex=m,this.emitUpdate&&this.$emit("update",f,m,v,b),clearTimeout(this.$_sortTimer),this.$_sortTimer=setTimeout(this.sortViews,300),{continuous:S}},getListenerTarget(){let e=r()(this.$el);return!window.document||e!==window.document.documentElement&&e!==window.document.body||(e=window),e},getScroll(){const{$el:e,direction:t}=this,n="vertical"===t;let i;if(this.pageMode){const t=e.getBoundingClientRect(),a=n?t.height:t.width;let s=-(n?t.top:t.left),r=n?window.innerHeight:window.innerWidth;s<0&&(r+=s,s=0),s+r>a&&(r=a-s),i={start:s,end:s+r}}else i=n?{start:e.scrollTop,end:e.scrollTop+e.clientHeight}:{start:e.scrollLeft,end:e.scrollLeft+e.clientWidth};return i},applyPageMode(){this.pageMode?this.addListeners():this.removeListeners()},addListeners(){this.listenerTarget=this.getListenerTarget(),this.listenerTarget.addEventListener("scroll",this.handleScroll,!!u&&{passive:!0}),this.listenerTarget.addEventListener("resize",this.handleResize)},removeListeners(){this.listenerTarget&&(this.listenerTarget.removeEventListener("scroll",this.handleScroll),this.listenerTarget.removeEventListener("resize",this.handleResize),this.listenerTarget=null)},scrollToItem(e){let t;t=null===this.itemSize?e>0?this.sizes[e-1].accumulator:0:Math.floor(e/this.gridItems)*this.itemSize,this.scrollToPosition(t)},scrollToPosition(e){const t="vertical"===this.direction?{scroll:"scrollTop",start:"top"}:{scroll:"scrollLeft",start:"left"};let n,i,a;if(this.pageMode){const s=r()(this.$el),o="HTML"===s.tagName?0:s[t.scroll],l=s.getBoundingClientRect(),c=this.$el.getBoundingClientRect(),d=c[t.start]-l[t.start];n=s,i=t.scroll,a=e+o+d}else n=this.$el,i=t.scroll,a=e;n[i]=a},itemsLimitError(){throw setTimeout(()=>{console.log("It seems the scroller element isn't scrolling, so it tries to render all the items at once.","Scroller:",this.$el),console.log("Make sure the scroller has a fixed height (or width) and 'overflow-y' (or 'overflow-x') set to 'auto' so it can scroll correctly and only render the items visible in the scroll viewport.")}),new Error("Rendered items limit reached")},sortViews(){this.pool.sort((e,t)=>e.nr.index-t.nr.index)}}};function f(e,t,n,i,a,s,r,o,l,c){"boolean"!==typeof r&&(l=o,o=r,r=!1);const d="function"===typeof n?n.options:n;let u;if(e&&e.render&&(d.render=e.render,d.staticRenderFns=e.staticRenderFns,d._compiled=!0,a&&(d.functional=!0)),i&&(d._scopeId=i),s?(u=function(e){e=e||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext,e||"undefined"===typeof __VUE_SSR_CONTEXT__||(e=__VUE_SSR_CONTEXT__),t&&t.call(this,l(e)),e&&e._registeredComponents&&e._registeredComponents.add(s)},d._ssrRegister=u):t&&(u=r?function(e){t.call(this,c(e,this.$root.$options.shadowRoot))}:function(e){t.call(this,o(e))}),u)if(d.functional){const e=d.render;d.render=function(t,n){return u.call(n),e(t,n)}}else{const e=d.beforeCreate;d.beforeCreate=e?[].concat(e,u):[u]}return n}const m=g;var y=function(){var e,t,n=this,i=n.$createElement,a=n._self._c||i;return a("div",{directives:[{name:"observe-visibility",rawName:"v-observe-visibility",value:n.handleVisibilityChange,expression:"handleVisibilityChange"}],staticClass:"vue-recycle-scroller",class:(e={ready:n.ready,"page-mode":n.pageMode},e["direction-"+n.direction]=!0,e),on:{"&scroll":function(e){return n.handleScroll.apply(null,arguments)}}},[n.$slots.before?a("div",{ref:"before",staticClass:"vue-recycle-scroller__slot"},[n._t("before")],2):n._e(),n._v(" "),a(n.listTag,{ref:"wrapper",tag:"component",staticClass:"vue-recycle-scroller__item-wrapper",class:n.listClass,style:(t={},t["vertical"===n.direction?"minHeight":"minWidth"]=n.totalSize+"px",t)},[n._l(n.pool,(function(e){return a(n.itemTag,n._g({key:e.nr.id,tag:"component",staticClass:"vue-recycle-scroller__item-view",class:[n.itemClass,{hover:!n.skipHover&&n.hoverKey===e.nr.key}],style:n.ready?{transform:"translate"+("vertical"===n.direction?"Y":"X")+"("+e.position+"px) translate"+("vertical"===n.direction?"X":"Y")+"("+e.offset+"px)",width:n.gridItems?("vertical"===n.direction&&n.itemSecondarySize||n.itemSize)+"px":void 0,height:n.gridItems?("horizontal"===n.direction&&n.itemSecondarySize||n.itemSize)+"px":void 0}:null},n.skipHover?{}:{mouseenter:function(){n.hoverKey=e.nr.key},mouseleave:function(){n.hoverKey=null}}),[n._t("default",null,{item:e.item,index:e.nr.index,active:e.nr.used})],2)})),n._v(" "),n._t("empty")],2),n._v(" "),n.$slots.after?a("div",{ref:"after",staticClass:"vue-recycle-scroller__slot"},[n._t("after")],2):n._e(),n._v(" "),a("ResizeObserver",{on:{notify:n.handleResize}})],1)},v=[];y._withStripped=!0;const b=void 0,T=void 0,S=void 0,_=!1,C=f({render:y,staticRenderFns:v},b,m,T,_,S,!1,void 0,void 0,void 0);var k={name:"DynamicScroller",components:{RecycleScroller:C},provide(){return"undefined"!==typeof ResizeObserver&&(this.$_resizeObserver=new ResizeObserver(e=>{requestAnimationFrame(()=>{if(Array.isArray(e))for(const t of e)if(t.target){const e=new CustomEvent("resize",{detail:{contentRect:t.contentRect}});t.target.dispatchEvent(e)}})})),{vscrollData:this.vscrollData,vscrollParent:this,vscrollResizeObserver:this.$_resizeObserver}},inheritAttrs:!1,props:{...c,minItemSize:{type:[Number,String],required:!0}},data(){return{vscrollData:{active:!0,sizes:{},validSizes:{},keyField:this.keyField,simpleArray:!1}}},computed:{simpleArray:d,itemsWithSize(){const e=[],{items:t,keyField:n,simpleArray:i}=this,a=this.vscrollData.sizes,s=t.length;for(let r=0;r<s;r++){const s=t[r],o=i?r:s[n];let l=a[o];"undefined"!==typeof l||this.$_undefinedMap[o]||(l=0),e.push({item:s,id:o,size:l})}return e},listeners(){const e={};for(const t in this.$listeners)"resize"!==t&&"visible"!==t&&(e[t]=this.$listeners[t]);return e}},watch:{items(){this.forceUpdate(!1)},simpleArray:{handler(e){this.vscrollData.simpleArray=e},immediate:!0},direction(e){this.forceUpdate(!0)},itemsWithSize(e,t){const n=this.$el.scrollTop;let i=0,a=0;const s=Math.min(e.length,t.length);for(let o=0;o<s;o++){if(i>=n)break;i+=t[o].size||this.minItemSize,a+=e[o].size||this.minItemSize}const r=a-i;0!==r&&(this.$el.scrollTop+=r)}},beforeCreate(){this.$_updates=[],this.$_undefinedSizes=0,this.$_undefinedMap={}},activated(){this.vscrollData.active=!0},deactivated(){this.vscrollData.active=!1},methods:{onScrollerResize(){const e=this.$refs.scroller;e&&this.forceUpdate(),this.$emit("resize")},onScrollerVisible(){this.$emit("vscroll:update",{force:!1}),this.$emit("visible")},forceUpdate(e=!0){(e||this.simpleArray)&&(this.vscrollData.validSizes={}),this.$emit("vscroll:update",{force:!0})},scrollToItem(e){const t=this.$refs.scroller;t&&t.scrollToItem(e)},getItemSize(e,t){const n=this.simpleArray?null!=t?t:this.items.indexOf(e):e[this.keyField];return this.vscrollData.sizes[n]||0},scrollToBottom(){if(this.$_scrollingToBottom)return;this.$_scrollingToBottom=!0;const e=this.$el;this.$nextTick(()=>{e.scrollTop=e.scrollHeight+5e3;const t=()=>{e.scrollTop=e.scrollHeight+5e3,requestAnimationFrame(()=>{e.scrollTop=e.scrollHeight+5e3,0===this.$_undefinedSizes?this.$_scrollingToBottom=!1:requestAnimationFrame(t)})};requestAnimationFrame(t)})}}};const w=k;var I=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("RecycleScroller",e._g(e._b({ref:"scroller",attrs:{items:e.itemsWithSize,"min-item-size":e.minItemSize,direction:e.direction,"key-field":"id","list-tag":e.listTag,"item-tag":e.itemTag},on:{resize:e.onScrollerResize,visible:e.onScrollerVisible},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.item,i=t.index,a=t.active;return[e._t("default",null,null,{item:n.item,index:i,active:a,itemWithSize:n})]}}],null,!0)},"RecycleScroller",e.$attrs,!1),e.listeners),[e._v(" "),n("template",{slot:"before"},[e._t("before")],2),e._v(" "),n("template",{slot:"after"},[e._t("after")],2),e._v(" "),n("template",{slot:"empty"},[e._t("empty")],2)],2)},x=[];I._withStripped=!0;const $=void 0,O=void 0,D=void 0,P=!1,L=f({render:I,staticRenderFns:x},$,w,O,P,D,!1,void 0,void 0,void 0);var A={name:"DynamicScrollerItem",inject:["vscrollData","vscrollParent","vscrollResizeObserver"],props:{item:{required:!0},watchData:{type:Boolean,default:!1},active:{type:Boolean,required:!0},index:{type:Number,default:void 0},sizeDependencies:{type:[Array,Object],default:null},emitResize:{type:Boolean,default:!1},tag:{type:String,default:"div"}},computed:{id(){if(this.vscrollData.simpleArray)return this.index;if(this.item.hasOwnProperty(this.vscrollData.keyField))return this.item[this.vscrollData.keyField];throw new Error(`keyField '${this.vscrollData.keyField}' not found in your item. You should set a valid keyField prop on your Scroller`)},size(){return this.vscrollData.validSizes[this.id]&&this.vscrollData.sizes[this.id]||0},finalActive(){return this.active&&this.vscrollData.active}},watch:{watchData:"updateWatchData",id(){this.size||this.onDataUpdate()},finalActive(e){this.size||(e?this.vscrollParent.$_undefinedMap[this.id]||(this.vscrollParent.$_undefinedSizes++,this.vscrollParent.$_undefinedMap[this.id]=!0):this.vscrollParent.$_undefinedMap[this.id]&&(this.vscrollParent.$_undefinedSizes--,this.vscrollParent.$_undefinedMap[this.id]=!1)),this.vscrollResizeObserver?e?this.observeSize():this.unobserveSize():e&&this.$_pendingVScrollUpdate===this.id&&this.updateSize()}},created(){if(!this.$isServer&&(this.$_forceNextVScrollUpdate=null,this.updateWatchData(),!this.vscrollResizeObserver)){for(const e in this.sizeDependencies)this.$watch(()=>this.sizeDependencies[e],this.onDataUpdate);this.vscrollParent.$on("vscroll:update",this.onVscrollUpdate),this.vscrollParent.$on("vscroll:update-size",this.onVscrollUpdateSize)}},mounted(){this.vscrollData.active&&(this.updateSize(),this.observeSize())},beforeDestroy(){this.vscrollParent.$off("vscroll:update",this.onVscrollUpdate),this.vscrollParent.$off("vscroll:update-size",this.onVscrollUpdateSize),this.unobserveSize()},methods:{updateSize(){this.finalActive?this.$_pendingSizeUpdate!==this.id&&(this.$_pendingSizeUpdate=this.id,this.$_forceNextVScrollUpdate=null,this.$_pendingVScrollUpdate=null,this.computeSize(this.id)):this.$_forceNextVScrollUpdate=this.id},updateWatchData(){this.watchData&&!this.vscrollResizeObserver?this.$_watchData=this.$watch("item",()=>{this.onDataUpdate()},{deep:!0}):this.$_watchData&&(this.$_watchData(),this.$_watchData=null)},onVscrollUpdate({force:e}){!this.finalActive&&e&&(this.$_pendingVScrollUpdate=this.id),this.$_forceNextVScrollUpdate!==this.id&&!e&&this.size||this.updateSize()},onDataUpdate(){this.updateSize()},computeSize(e){this.$nextTick(()=>{if(this.id===e){const e=this.$el.offsetWidth,t=this.$el.offsetHeight;this.applySize(e,t)}this.$_pendingSizeUpdate=null})},applySize(e,t){const n=~~("vertical"===this.vscrollParent.direction?t:e);n&&this.size!==n&&(this.vscrollParent.$_undefinedMap[this.id]&&(this.vscrollParent.$_undefinedSizes--,this.vscrollParent.$_undefinedMap[this.id]=void 0),this.$set(this.vscrollData.sizes,this.id,n),this.$set(this.vscrollData.validSizes,this.id,!0),this.emitResize&&this.$emit("resize",this.id))},observeSize(){this.vscrollResizeObserver&&this.$el.parentNode&&(this.vscrollResizeObserver.observe(this.$el.parentNode),this.$el.parentNode.addEventListener("resize",this.onResize))},unobserveSize(){this.vscrollResizeObserver&&(this.vscrollResizeObserver.unobserve(this.$el.parentNode),this.$el.parentNode.removeEventListener("resize",this.onResize))},onResize(e){const{width:t,height:n}=e.detail.contentRect;this.applySize(t,n)}},render(e){return e(this.tag,this.$slots.default)}};const N=A,E=void 0,j=void 0,B=void 0,R=void 0,M=f({},E,N,j,R,B,!1,void 0,void 0,void 0);function z({idProp:e=(e=>e.item.id)}={}){const t={},n=new o["default"]({data(){return{store:t}}});return{data(){return{idState:null}},created(){this.$_id=null,this.$_getId="function"===typeof e?()=>e.call(this,this):()=>this[e],this.$watch(this.$_getId,{handler(e){this.$nextTick(()=>{this.$_id=e})},immediate:!0}),this.$_updateIdState()},beforeUpdate(){this.$_updateIdState()},methods:{$_idStateInit(e){const i=this.$options.idState;if("function"===typeof i){const a=i.call(this,this);return n.$set(t,e,a),this.$_id=e,a}throw new Error("[mixin IdState] Missing `idState` function on component definition.")},$_updateIdState(){const n=this.$_getId();null==n&&console.warn(`No id found for IdState with idProp: '${e}'.`),n!==this.$_id&&(t[n]||this.$_idStateInit(n),this.idState=t[n])}}}}function K(e,t){e.component(t+"recycle-scroller",C),e.component(t+"RecycleScroller",C),e.component(t+"dynamic-scroller",L),e.component(t+"DynamicScroller",L),e.component(t+"dynamic-scroller-item",M),e.component(t+"DynamicScrollerItem",M)}const q={version:"1.1.2",install(e,t){const n=Object.assign({},{installComponents:!0,componentsPrefix:""},t);for(const i in n)"undefined"!==typeof n[i]&&(l[i]=n[i]);n.installComponents&&K(e,n.componentsPrefix)}};let F=null;"undefined"!==typeof window?F=window.Vue:"undefined"!==typeof e&&(F=e.Vue),F&&F.use(q)}).call(this,n("c8ba"))},e615:function(e,t,n){"use strict";n("14d4")},e81e:function(e,t,n){"use strict";n("370f")},eb6d:function(e,t,n){"use strict";n("9c92")},ed83:function(e,t,n){var i,a,s;(function(n,r){a=[],i=r,s="function"===typeof i?i.apply(t,a):i,void 0===s||(e.exports=s)})(0,(function(){var e=/(auto|scroll)/,t=function(e,n){return null===e.parentNode?n:t(e.parentNode,n.concat([e]))},n=function(e,t){return getComputedStyle(e,null).getPropertyValue(t)},i=function(e){return n(e,"overflow")+n(e,"overflow-y")+n(e,"overflow-x")},a=function(t){return e.test(i(t))},s=function(e){if(e instanceof HTMLElement||e instanceof SVGElement){for(var n=t(e.parentNode,[]),i=0;i<n.length;i+=1)if(a(n[i]))return n[i];return document.scrollingElement||document.documentElement}};return s}))},ede5:function(e,t,n){"use strict";n("f679")},f055:function(e,t,n){"use strict";n("464f")},f0ff:function(e,t,n){},f4ae:function(e,t,n){},f679:function(e,t,n){},f6d7:function(e,t,n){},f7c0:function(e,t,n){"use strict";n("22f6")},f8ac:function(e,t,n){"use strict";n.r(t);var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("CodeTheme",{staticClass:"doc-topic-view"},[e.topicData?[n(e.enableNavigator?"AdjustableSidebarWidth":"StaticContentWidth",e._g(e._b({tag:"component",staticClass:"full-width-container topic-wrapper",scopedSlots:e._u([{key:"aside",fn:function(t){var i=t.scrollLockID,a=t.breakpoint;return[n("NavigatorDataProvider",{ref:"NavigatorDataProvider",attrs:{"interface-language":e.topicProps.interfaceLanguage,technologyUrl:e.technology.url,"api-changes-version":e.store.state.selectedAPIChangesVersion},scopedSlots:e._u([{key:"default",fn:function(t){return[n("div",{staticClass:"doc-topic-aside"},[e.enableQuickNavigation?n("QuickNavigationModal",{attrs:{children:t.flatChildren,showQuickNavigationModal:e.showQuickNavigationModal,technology:e.technology.title},on:{"update:showQuickNavigationModal":function(t){e.showQuickNavigationModal=t},"update:show-quick-navigation-modal":function(t){e.showQuickNavigationModal=t}}}):e._e(),n("transition",{attrs:{name:"delay-hiding"}},[n("Navigator",{directives:[{name:"show",rawName:"v-show",value:e.sidenavVisibleOnMobile||a===e.BreakpointName.large,expression:"sidenavVisibleOnMobile || breakpoint === BreakpointName.large"}],attrs:{flatChildren:t.flatChildren,"parent-topic-identifiers":e.parentTopicIdentifiers,technology:t.technology||e.technology,"is-fetching":t.isFetching,"error-fetching":t.errorFetching,"api-changes":t.apiChanges,references:e.topicProps.references,"navigator-references":t.references,scrollLockID:i,"render-filter-on-top":a!==e.BreakpointName.large},on:{close:function(t){return e.handleToggleSidenav(a)}},scopedSlots:e._u([e.enableQuickNavigation?{key:"filter",fn:function(){return[n("QuickNavigationButton",{nativeOn:{click:function(t){return e.openQuickNavigationModal.apply(null,arguments)}}})]},proxy:!0}:null],null,!0)})],1)],1)]}}],null,!0)})]}}],null,!1,2308467884)},"component",e.sidebarProps,!1),e.sidebarListeners),[n("PortalTarget",{attrs:{name:"modal-destination",multiple:""}}),e.isTargetIDE?e._e():n("Nav",{attrs:{title:e.topicProps.title,diffAvailability:e.topicProps.diffAvailability,interfaceLanguage:e.topicProps.interfaceLanguage,objcPath:e.objcPath,swiftPath:e.swiftPath,parentTopicIdentifiers:e.parentTopicIdentifiers,isSymbolDeprecated:e.isSymbolDeprecated,isSymbolBeta:e.isSymbolBeta,currentTopicTags:e.topicProps.tags,references:e.topicProps.references,displaySidenav:e.enableNavigator,sidenavHiddenOnLarge:e.sidenavHiddenOnLarge},on:{"toggle-sidenav":e.handleToggleSidenav}}),n("Topic",e._b({key:e.topicKey,attrs:{objcPath:e.objcPath,swiftPath:e.swiftPath,isSymbolDeprecated:e.isSymbolDeprecated,isSymbolBeta:e.isSymbolBeta,languagePaths:e.languagePaths,enableOnThisPageNav:e.enableOnThisPageNav,enableMinimized:e.enableMinimized}},"Topic",e.topicProps,!1))],1)]:e._e()],2)},a=[];const s="/";function r(e){return e.replace(/~[0,1]/g,e=>({"~0":"~","~1":"/"}[e]||e))}function*o(e){const t=1;if(e.length<t||e.charAt(0)!==s)return;let n="",i=t;while(i<e.length){const t=e.charAt(i);t===s?(yield r(n),n=""):n+=t,i+=1}yield r(n)}const l=10,c="-",d="object";class u extends Error{constructor(e){super("invalid pointer "+e),this.pointer=e}}function h(e,{length:t}){if(e===c)return t||0;const n=parseInt(e,l);if(!Number.isInteger(n)||n>t)throw new Error("invalid array index "+e);return n}function*p(e,t,n={strict:!1}){let i=e;for(const a of o(t)){if(n.strict&&!Object.prototype.hasOwnProperty.call(i,a))throw new u(t);i=i[a],yield{node:i,token:a}}}function g(e,t){let n=e;for(const{node:i}of p(e,t,{strict:!0}))n=i;return n}function f(e,t,n){let i=null,a=e,s=null;for(const{node:o,token:l}of p(e,t))i=a,a=o,s=l;if(!i)throw new u(t);if(Array.isArray(i))try{const e=h(s,i);i.splice(e,0,n)}catch(r){throw new u(t)}else Object.assign(i,{[s]:n});return e}function m(e,t){let n=null,i=e,a=null;for(const{node:r,token:o}of p(e,t))n=i,i=r,a=o;if(!n)throw new u(t);if(Array.isArray(n))try{const e=h(a,n);n.splice(e,1)}catch(s){throw new u(t)}else{if(!i)throw new u(t);delete n[a]}return e}function y(e,t,n){return m(e,t),f(e,t,n),e}function v(e,t,n){const i=g(e,t);return m(e,t),f(e,n,i),e}function b(e,t,n){return f(e,n,g(e,t)),e}function T(e,t,n){function i(e,t){const n=typeof e,a=typeof t;if(n!==a)return!1;switch(n){case d:{const n=Object.keys(e),a=Object.keys(t);return n.length===a.length&&n.every((n,s)=>n===a[s]&&i(e[n],t[n]))}default:return e===t}}const a=g(e,t);if(!i(n,a))throw new Error("test failed");return e}const S={add:(e,{path:t,value:n})=>f(e,t,n),copy:(e,{from:t,path:n})=>b(e,t,n),move:(e,{from:t,path:n})=>v(e,t,n),remove:(e,{path:t})=>m(e,t),replace:(e,{path:t,value:n})=>y(e,t,n),test:(e,{path:t,value:n})=>T(e,t,n)};function _(e,{op:t,...n}){const i=S[t];if(!i)throw new Error("unknown operation");return i(e,n)}function C(e,t){return t.reduce(_,e)}var k=n("66cd"),w=n("25a9"),I=n("2b88"),x=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"doc-topic",class:{"with-on-this-page":e.enableOnThisPageNav&&e.isOnThisPageNavVisible}},[n(e.isTargetIDE?"div":"main",{tag:"component",staticClass:"main",attrs:{id:"main"}},[n("DocumentationHero",{attrs:{role:e.role,enhanceBackground:e.enhanceBackground,enableMinimized:e.enableMinimized,shortHero:e.shortHero,shouldShowLanguageSwitcher:e.shouldShowLanguageSwitcher,iconOverride:e.references[e.pageIcon],standardColorIdentifier:e.standardColorIdentifier},scopedSlots:e._u([{key:"above-content",fn:function(){return[e._t("above-hero-content")]},proxy:!0}],null,!0)},[e._t("above-title"),e.shouldShowLanguageSwitcher?n("LanguageSwitcher",{attrs:{interfaceLanguage:e.interfaceLanguage,objcPath:e.objcPath,swiftPath:e.swiftPath}}):e._e(),n("Title",{class:{"minimized-title":e.enableMinimized},attrs:{eyebrow:e.enableMinimized?null:e.roleHeading}},[n(e.titleBreakComponent,{tag:"component"},[e._v(e._s(e.title))]),e.isSymbolDeprecated||e.isSymbolBeta?n("small",{class:e.tagName,attrs:{slot:"after","data-tag-name":e.tagName},slot:"after"}):e._e()],1),e.abstract?n("Abstract",{class:{"minimized-abstract":e.enableMinimized},attrs:{content:e.abstract}}):e._e(),e.sampleCodeDownload?n("div",[n("DownloadButton",{staticClass:"sample-download",attrs:{action:e.sampleCodeDownload.action}})],1):e._e(),e.shouldShowAvailability?n("Availability",{attrs:{platforms:e.platforms,technologies:e.technologies}}):e._e(),e.declarations.length?n("div",{staticClass:"declarations-container",class:{"minimized-container":e.enableMinimized}},e._l(e.declarations,(function(t,i){return n("Declaration",{key:i,attrs:{conformance:e.conformance,declarations:t.declarations,source:e.remoteSource}})})),1):e._e()],2),n("div",{staticClass:"doc-content-wrapper"},[n("div",{staticClass:"doc-content",class:{"no-primary-content":!e.hasPrimaryContent&&e.enhanceBackground}},[e.hasPrimaryContent?n("div",{class:["container",{"minimized-container":e.enableMinimized}]},[n("div",{staticClass:"description",class:{"after-enhanced-hero":e.enhanceBackground}},[e.isRequirement?n("RequirementMetadata",{attrs:{defaultImplementationsCount:e.defaultImplementationsCount}}):e._e(),e.deprecationSummary&&e.deprecationSummary.length?n("Aside",{attrs:{kind:"deprecated"}},[n("ContentNode",{attrs:{content:e.deprecationSummary}})],1):e._e(),e.downloadNotAvailableSummary&&e.downloadNotAvailableSummary.length?n("Aside",{attrs:{kind:"note"}},[n("ContentNode",{attrs:{content:e.downloadNotAvailableSummary}})],1):e._e()],1),e.primaryContentSectionsSanitized&&e.primaryContentSectionsSanitized.length?n("PrimaryContent",{class:{"with-border":!e.enhanceBackground},attrs:{conformance:e.conformance,source:e.remoteSource,sections:e.primaryContentSectionsSanitized}}):e._e(),e.shouldShowViewMoreLink?n("ViewMore",{attrs:{url:e.viewMoreLink}}):e._e()],1):e._e(),e.shouldRenderTopicSection?n("Topics",{attrs:{sections:e.topicSections,isSymbolDeprecated:e.isSymbolDeprecated,isSymbolBeta:e.isSymbolBeta,topicStyle:e.topicSectionsStyle}}):e._e(),e.defaultImplementationsSections&&!e.enableMinimized?n("DefaultImplementations",{attrs:{sections:e.defaultImplementationsSections,isSymbolDeprecated:e.isSymbolDeprecated,isSymbolBeta:e.isSymbolBeta}}):e._e(),e.relationshipsSections&&!e.enableMinimized?n("Relationships",{attrs:{sections:e.relationshipsSections}}):e._e(),e.seeAlsoSections&&!e.enableMinimized?n("SeeAlso",{attrs:{sections:e.seeAlsoSections}}):e._e()],1),e.enableOnThisPageNav?[n("OnThisPageStickyContainer",{directives:[{name:"show",rawName:"v-show",value:e.isOnThisPageNavVisible,expression:"isOnThisPageNavVisible"}]},[e.topicState.onThisPageSections.length>2?n("OnThisPageNav"):e._e()],1)]:e._e()],2),!e.isTargetIDE&&e.hasBetaContent?n("BetaLegalText"):e._e()],1),n("div",{staticClass:"visuallyhidden",attrs:{"aria-live":"polite"}},[e._v(" "+e._s(e.$t("documentation.current-page",{title:e.pageTitle}))+" ")])],1)},$=[],O=n("8649"),D=n("bf08"),P=n("d26a"),L=n("748c"),A=n("e425"),N=n("e3ab"),E=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"betainfo"},[n("div",{staticClass:"betainfo-container"},[n("GridRow",[n("GridColumn",{attrs:{span:{large:12}}},[n("p",{staticClass:"betainfo-label"},[e._v(e._s(e.$t("metadata.beta.software")))]),n("div",{staticClass:"betainfo-content"},[e._t("content",(function(){return[n("p",[e._v(e._s(e.$t("metadata.beta.legal")))])]}))],2),e._t("after")],2)],1)],1)])},j=[],B=n("0f00"),R=n("620a"),M={name:"BetaLegalText",components:{GridColumn:R["a"],GridRow:B["a"]}},z=M,K=(n("9cea"),n("2877")),q=Object(K["a"])(z,E,j,!1,null,"e8fd2a92",null),F=q.exports,H=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("Section",{staticClass:"language",attrs:{role:"complementary","aria-label":e.$t("language")}},[n("Title",[e._v(e._s(e.$t("formats.colon",{content:e.$t("language")})))]),n("div",{staticClass:"language-list"},[n("LanguageSwitcherLink",{staticClass:"language-option swift",class:{active:e.swift.active},attrs:{url:e.swift.active?null:e.swift.url},on:{click:function(t){return e.chooseLanguage(e.swift)}}},[e._v(" "+e._s(e.swift.name)+" ")]),n("LanguageSwitcherLink",{staticClass:"language-option objc",class:{active:e.objc.active},attrs:{url:e.objc.active?null:e.objc.url},on:{click:function(t){return e.chooseLanguage(e.objc)}}},[e._v(" "+e._s(e.objc.name)+" ")])],1)],1)},V=[],W=function(){var e=this,t=e.$createElement,n=e._self._c||t;return e.url?n("a",{attrs:{href:e.url},on:{click:function(t){return t.preventDefault(),e.$emit("click")}}},[e._t("default")],2):n("span",[e._t("default")],2)},U=[],G={name:"LanguageSwitcherLink",props:{url:[String,Object]}},Q=G,X=Object(K["a"])(Q,W,U,!1,null,null,null),Y=X.exports,J=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"summary-section"},[e._t("default")],2)},Z=[],ee={name:"Section"},te=ee,ne=(n("1347"),Object(K["a"])(te,J,Z,!1,null,"3aa6f694",null)),ie=ne.exports,ae=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("p",{staticClass:"title"},[e._t("default")],2)},se=[],re={name:"Title"},oe=re,le=(n("ede5"),Object(K["a"])(oe,ae,se,!1,null,"6796f6ea",null)),ce=le.exports,de={name:"LanguageSwitcher",components:{LanguageSwitcherLink:Y,Section:ie,Title:ce},inject:{isTargetIDE:{default:()=>!1},store:{default(){return{setPreferredLanguage(){}}}}},props:{interfaceLanguage:{type:String,required:!0},objcPath:{type:String,required:!0},swiftPath:{type:String,required:!0}},computed:{objc:({interfaceLanguage:e,objcPath:t,$route:{query:n}})=>({...O["a"].objectiveC,active:O["a"].objectiveC.key.api===e,url:Object(P["b"])(Object(L["d"])(t),{...n,language:O["a"].objectiveC.key.url})}),swift:({interfaceLanguage:e,swiftPath:t,$route:{query:n}})=>({...O["a"].swift,active:O["a"].swift.key.api===e,url:Object(P["b"])(Object(L["d"])(t),{...n,language:void 0})})},methods:{chooseLanguage(e){this.isTargetIDE||this.store.setPreferredLanguage(e.key.url),this.$router.push(e.url)}}},ue=de,he=(n("a34a"),Object(K["a"])(ue,H,V,!1,null,"1a36493d",null)),pe=he.exports,ge=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"view-more-link"},[n("router-link",{staticClass:"base-link",attrs:{to:e.url}},[e._t("default",(function(){return[e._v("View more")]}))],2)],1)},fe=[],me={name:"ViewMore",props:{url:{type:String,required:!0}}},ye=me,ve=(n("2591"),Object(K["a"])(ye,ge,fe,!1,null,"0d14b62a",null)),be=ve.exports,Te=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{class:["documentation-hero",{"documentation-hero--disabled":!e.enhanceBackground,"theme-dark":e.enhanceBackground}],style:e.styles},[n("div",{staticClass:"icon"},[e.enhanceBackground?n("TopicTypeIcon",{key:"first",staticClass:"background-icon first-icon",attrs:{type:e.type,"image-override":e.iconOverride,"with-colors":""}}):e._e()],1),n("div",{staticClass:"documentation-hero__above-content"},[e._t("above-content")],2),n("div",{staticClass:"documentation-hero__content",class:{"short-hero":e.shortHero,"extra-bottom-padding":e.shouldShowLanguageSwitcher,"minimized-hero":e.enableMinimized}},[e._t("default")],2)])},Se=[],_e=n("f12c"),Ce=n("31d4"),ke=n("2cae");const we={red:"red",orange:"orange",yellow:"yellow",blue:"blue",green:"green",purple:"purple",gray:"gray"};var Ie={name:"DocumentationHero",components:{TopicTypeIcon:_e["a"]},props:{role:{type:String,required:!0},enhanceBackground:{type:Boolean,required:!0},enableMinimized:{type:Boolean,default:!1},shortHero:{type:Boolean,required:!0},shouldShowLanguageSwitcher:{type:Boolean,required:!0},iconOverride:{type:Object,required:!1},standardColorIdentifier:{type:String,required:!1,validator:e=>Object.prototype.hasOwnProperty.call(we,e)}},computed:{color:({type:e})=>ke["b"][Ce["a"][e]||e]||ke["a"].teal,styles:({color:e,standardColorIdentifier:t})=>({"--accent-color":`var(--color-documentation-intro-accent, var(--color-type-icon-${e}))`,"--standard-accent-color":t&&`var(--color-standard-${t}-documentation-intro-fill, var(--color-standard-${t}))`}),type:({role:e})=>{switch(e){case k["a"].collection:return Ce["b"].module;case k["a"].collectionGroup:return Ce["b"].collection;default:return e}}}},xe=Ie,$e=(n("c80b"),Object(K["a"])(xe,Te,Se,!1,null,"6540c364",null)),Oe=$e.exports,De=n("7b1f"),Pe=n("12b1"),Le=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"OnThisPageNav"},[n("ul",{staticClass:"items"},e._l(e.onThisPageSections,(function(t){return n("li",{key:t.anchor,class:e.getItemClasses(t)},[n("router-link",{staticClass:"base-link",attrs:{to:t.url},nativeOn:{click:function(n){return e.handleFocusAndScroll(t.anchor)}}},[n(e.getWrapperComponent(t),{tag:"component"},[e._v(" "+e._s(e.getTextContent(t))+" ")])],1)],1)})),0)])},Ae=[];function Ne(e,t){let n,i;return function(...a){const s=this;if(!i)return e.apply(s,a),void(i=Date.now());clearTimeout(n),n=setTimeout(()=>{Date.now()-i>=t&&(e.apply(s,a),i=Date.now())},t-(Date.now()-i))}}var Ee=n("3908"),je=n("8a61"),Be={name:"OnThisPageNav",components:{WordBreak:De["a"]},mixins:[je["a"]],inject:{store:{default(){return{state:{onThisPageSections:[],currentPageAnchor:null}}}}},computed:{onThisPageSections:({store:e,$route:t})=>e.state.onThisPageSections.map(e=>({...e,url:Object(P["b"])("#"+e.anchor,t.query)})),currentPageAnchor:({store:e})=>e.state.currentPageAnchor},async mounted(){window.addEventListener("scroll",this.onScroll,!1),this.$once("hook:beforeDestroy",()=>{window.removeEventListener("scroll",this.onScroll)})},watch:{onThisPageSections:{immediate:!0,async handler(){await Object(Ee["b"])(8),this.onScroll()}}},methods:{onScroll:Ne((function(){const e=this.onThisPageSections.length;if(!e)return;const{scrollY:t,innerHeight:n}=window,{scrollHeight:i}=document.body,a=t+n>=i,s=t<=0,r=.3*n+t;if(s||a){const t=s?0:e-1;return void this.store.setCurrentPageSection(this.onThisPageSections[t].anchor)}let o,l,c=null;for(o=0;o<e;o+=1){l=this.onThisPageSections[o];const{offsetTop:e}=document.getElementById(l.anchor);if(!(e<r))break;c=l.anchor}null!==c&&this.store.setCurrentPageSection(c)}),100),checkIsActive(e){return e.anchor===this.currentPageAnchor},getItemClasses(e){return{active:this.checkIsActive(e),"parent-item":e.level<=2,"child-item":3===e.level}},getTextContent(e){return e.i18n?this.$t(e.title):e.title},getWrapperComponent(e){return e.isSymbol?De["a"]:"span"}}},Re=Be,Me=(n("e47c"),Object(K["a"])(Re,Le,Ae,!1,null,"068842ec",null)),ze=Me.exports;const Ke={content:"content",declarations:"declarations",details:"details",parameters:"parameters",possibleValues:"possibleValues",properties:"properties",restBody:"restBody",restCookies:"restCookies",restEndpoint:"restEndpoint",restHeaders:"restHeaders",restParameters:"restParameters",restResponses:"restResponses"};var qe=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("section",{staticClass:"declaration"},[e.hasModifiedChanges?[n("DeclarationDiff",{class:[e.changeClasses,e.multipleLinesClass],attrs:{changes:e.declarationChanges,changeType:e.changeType}})]:e._l(e.declarations,(function(t,i){return n("DeclarationGroup",{key:i,class:e.changeClasses,attrs:{declaration:t,shouldCaption:e.hasPlatformVariants,changeType:e.changeType}})})),e.source?n("DeclarationSourceLink",{attrs:{url:e.source.url,fileName:e.source.fileName}}):e._e(),e.conformance?n("ConditionalConstraints",{attrs:{constraints:e.conformance.constraints,prefix:e.conformance.availabilityPrefix}}):e._e()],2)},Fe=[],He=n("64cf"),Ve=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{ref:"apiChangesDiff",staticClass:"declaration-group",class:e.classes},[e.shouldCaption?n("p",{staticClass:"platforms"},[n("strong",[e._v(e._s(e.caption))])]):e._e(),n("Source",{attrs:{tokens:e.declaration.tokens,language:e.interfaceLanguage}})],1)},We=[],Ue=function(){var e,t=this,n=t.$createElement,i=t._self._c||n;return i("pre",{ref:"declarationGroup",staticClass:"source",class:(e={},e[t.multipleLinesClass]=t.displaysMultipleLines,e["has-multiple-lines"]=t.hasMultipleLines,e)},[i("CodeBlock",{ref:"code"},t._l(t.formattedTokens,(function(e,n){return i("Token",t._b({key:n},"Token",t.propsFor(e),!1))})),1)],1)},Ge=[];function Qe(e){const t=e.getElementsByClassName("token-identifier");if(t.length<2)return;const n=e.textContent.indexOf(":")+1;for(let i=1;i<t.length;i++){const e=t[i].innerHTML.trim(),a=t[i].textContent.trim(),s=a.length,r=Math.max(0,n-s);t[i].innerHTML=`\n${" ".repeat(r)}${e}`}"\n"!==e.innerHTML.charAt(e.innerHTML.length-1)&&(e.innerHTML=e.innerHTML+"\n")}function Xe(e,t){const n=e.innerHTML;try{switch(t){case O["a"].objectiveC.key.api:Qe(e);break}}catch(i){e.innerHTML=n}}var Ye=n("beb1"),Je=n("9055"),Ze=n("6842"),et=n("800b"),tt=n("00b4");const{TokenKind:nt}=tt["a"].constants,it=4;var at={name:"DeclarationSource",data(){return{displaysMultipleLines:!1,multipleLinesClass:Je["a"]}},components:{Token:tt["a"],CodeBlock:et["a"]},props:{tokens:{type:Array,required:!0},language:{type:String,required:!1}},computed:{indentationWidth:()=>Object(Ze["c"])(["theme","code","indentationWidth"],it),formattedTokens:({language:e,formattedSwiftTokens:t,tokens:n})=>e===O["a"].swift.key.api?t:n,formattedSwiftTokens:({indentationWidth:e,tokens:t})=>{const n=" ".repeat(e);let i=!1;const a=[];let s=0,r=null,o=null,l=null,c=null,d=0,u=null;while(s<t.length){const e=t[s],h={...e},p=t[s-1],g=t[s+1];if(u||e.kind!==nt.keyword||(u=s),null!==u){const t=(e.text||"").length;for(let n=0;n<t;n++)if("("===e.text.charAt(n)&&(d+=1,null==o&&(o=n,r=s)),")"===e.text.charAt(n)&&(d-=1,null!==r&&null==l&&0===d)){c=n,l=s;break}}e.kind===nt.text&&0===d&&p&&p.kind===nt.attribute&&g&&g.kind===nt.keyword&&(h.text=e.text.trimEnd()+"\n");const f=({kind:e})=>e===nt.attribute||e===nt.externalParam;e.text&&e.text.endsWith(", ")&&g&&f(g)&&(h.text=`${e.text.trimEnd()}\n${n}`,i=!0),a.push(h),s+=1}if(i&&null!==r){const e=a[r].text;a[r].text=`${e}\n${n}`}if(i&&null!==l){const e=a[l].text,t=e.slice(0,c),n=e.slice(c),i=`${t}\n${n}`;a[l].text=i}return a},hasMultipleLines({formattedTokens:e}){return e.reduce((t,n,i)=>{let a=/\n/g;return i===e.length-1&&(a=/\n(?!$)/g),n.text?t+(n.text.match(a)||[]).length:t},1)>=2}},methods:{propsFor(e){return{kind:e.kind,identifier:e.identifier,text:e.text,tokens:e.tokens}},handleWindowResize(){this.displaysMultipleLines=Object(Ye["a"])(this.$refs.declarationGroup)}},async mounted(){window.addEventListener("resize",this.handleWindowResize),this.language===O["a"].objectiveC.key.api&&(await this.$nextTick(),Xe(this.$refs.code.$el,this.language)),this.handleWindowResize()},beforeDestroy(){window.removeEventListener("resize",this.handleWindowResize)}},st=at,rt=(n("72a9"),Object(K["a"])(st,Ue,Ge,!1,null,"d22a3f50",null)),ot=rt.exports,lt=n("5d59"),ct={name:"DeclarationGroup",components:{Source:ot},mixins:[lt["a"]],inject:{languages:{default:()=>new Set},interfaceLanguage:{default:()=>O["a"].swift.key.api},symbolKind:{default:()=>{}}},props:{declaration:{type:Object,required:!0},shouldCaption:{type:Boolean,default:!1},changeType:{type:String,required:!1}},computed:{classes:({changeType:e,multipleLinesClass:t,displaysMultipleLinesAfterAPIChanges:n})=>({["declaration-group--changed declaration-group--"+e]:e,[t]:n}),caption(){return this.declaration.platforms.join(", ")},isSwift:({interfaceLanguage:e})=>e===O["a"].swift.key.api}},dt=ct,ut=(n("c4bc"),Object(K["a"])(dt,Ve,We,!1,null,"4f51d8d2",null)),ht=ut.exports,pt=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"declaration-diff"},[n("div",{staticClass:"declaration-diff-current"},[n("div",{staticClass:"declaration-diff-version"},[e._v("Current")]),e._l(e.currentDeclarations,(function(t,i){return n("DeclarationGroup",{key:i,attrs:{declaration:t,"should-caption":e.currentDeclarations.length>1,changeType:e.changeType}})}))],2),n("div",{staticClass:"declaration-diff-previous"},[n("div",{staticClass:"declaration-diff-version"},[e._v("Previous")]),e._l(e.previousDeclarations,(function(t,i){return n("DeclarationGroup",{key:i,attrs:{declaration:t,"should-caption":e.previousDeclarations.length>1,changeType:e.changeType}})}))],2)])},gt=[],ft={name:"DeclarationDiff",components:{DeclarationGroup:ht},props:{changes:{type:Object,required:!0},changeType:{type:String,required:!0}},computed:{previousDeclarations:({changes:e})=>e.declaration.previous||[],currentDeclarations:({changes:e})=>e.declaration.new||[]}},mt=ft,yt=(n("7a2c"),Object(K["a"])(mt,pt,gt,!1,null,"b3e21c4a",null)),vt=yt.exports,bt=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("a",{staticClass:"declaration-source-link",attrs:{href:e.url,title:"Open source file for "+e.fileName,target:"_blank"}},[e.isSwiftFile?n("SwiftFileIcon",{staticClass:"declaration-icon"}):e._e(),n("WordBreak",[e._v(e._s(e.fileName))])],1)},Tt=[],St=n("a88f"),_t={name:"DeclarationSourceLink",components:{WordBreak:De["a"],SwiftFileIcon:St["a"]},props:{url:{type:String,required:!0},fileName:{type:String,required:!0}},computed:{isSwiftFile:({fileName:e})=>e.endsWith(".swift")}},Ct=_t,kt=(n("e615"),Object(K["a"])(Ct,bt,Tt,!1,null,"5863919c",null)),wt=kt.exports,It=n("b5cf"),xt={name:"Declaration",components:{DeclarationDiff:vt,DeclarationGroup:ht,DeclarationSourceLink:wt,ConditionalConstraints:He["a"]},constants:{ChangeTypes:It["c"],multipleLinesClass:Je["a"]},inject:["identifier","store"],data:({store:{state:e}})=>({state:e,multipleLinesClass:Je["a"]}),props:{conformance:{type:Object,required:!1},source:{type:Object,required:!1},declarations:{type:Array,required:!0}},computed:{hasPlatformVariants(){return this.declarations.length>1},hasModifiedChanges({declarationChanges:e}){if(!e||!e.declaration)return!1;const t=e.declaration;return!(!(t.new||[]).length||!(t.previous||[]).length)},declarationChanges:({state:{apiChanges:e},identifier:t})=>e&&e[t],changeType:({declarationChanges:e,hasModifiedChanges:t})=>{if(!e)return;const n=e.declaration;return n?t?It["c"].modified:e.change:e.change===It["c"].added?It["c"].added:void 0},changeClasses:({changeType:e})=>({["changed changed-"+e]:e})}},$t=xt,Ot=(n("39d3"),Object(K["a"])($t,qe,Fe,!1,null,"2ab6251b",null)),Dt=Ot.exports,Pt=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("ContentNode",e._b({staticClass:"abstract"},"ContentNode",e.$props,!1))},Lt=[],At=n("6359"),Nt={name:"Abstract",components:{ContentNode:At["a"]},props:At["a"].props},Et=Nt,jt=(n("374e"),Object(K["a"])(Et,Pt,Lt,!1,null,"702ec04e",null)),Bt=jt.exports,Rt=n("c081"),Mt=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("TopicsTable",{attrs:{anchor:e.contentSectionData.anchor,title:e.$t(e.contentSectionData.title),isSymbolDeprecated:e.isSymbolDeprecated,isSymbolBeta:e.isSymbolBeta,sections:e.sections,wrapTitle:!0}})},zt=[];const Kt={topics:{title:"sections.topics",anchor:"topics",level:2},defaultImplementations:{title:"sections.default-implementations",anchor:"default-implementations",level:2},relationships:{title:"sections.relationships",anchor:"relationships",level:2},seeAlso:{title:"sections.see-also",anchor:"see-also",level:2}},qt={[Ke.details]:{title:"sections.details",anchor:"details",level:2},[Ke.parameters]:{title:"sections.parameters",anchor:"parameters",level:2},[Ke.possibleValues]:{title:"sections.possible-values",anchor:"possibleValues",level:2}};var Ft=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("ContentTable",{attrs:{anchor:e.anchor,title:e.title}},e._l(e.sectionsWithTopics,(function(t,i){return n("ContentTableSection",{key:t.title+"_"+i,class:{"no-title":!t.title},attrs:{title:t.title,anchor:t.anchor},scopedSlots:e._u([t.title&&e.wrapTitle?{key:"title",fn:function(i){var a=i.className;return[n("LinkableHeading",{class:a,attrs:{level:3,anchor:t.anchor}},[n("WordBreak",[e._v(e._s(t.title))])],1)]}}:null],null,!0)},[t.abstract?n("template",{slot:"abstract"},[n("ContentNode",{attrs:{content:t.abstract}})],1):e._e(),t.discussion?n("template",{slot:"discussion"},[n("ContentNode",{attrs:{content:t.discussion.content}})],1):e._e(),e.shouldRenderList?e._l(t.topics,(function(t){return n("TopicsLinkBlock",{key:t.identifier,staticClass:"topic",attrs:{topic:t,isSymbolDeprecated:e.isSymbolDeprecated,isSymbolBeta:e.isSymbolBeta}})})):n("TopicsLinkCardGrid",{staticClass:"topic",attrs:{items:t.topics,topicStyle:e.topicStyle}})],2)})),1)},Ht=[],Vt=n("70fb"),Wt=n("5dcc"),Ut=n("2f34"),Gt=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("section",{staticClass:"contenttable alt-light"},[n("div",{staticClass:"container"},[n("LinkableHeading",{staticClass:"title",attrs:{anchor:e.anchor}},[e._v(e._s(e.title))]),e._t("default")],2)])},Qt=[],Xt={name:"ContentTable",components:{LinkableHeading:Wt["a"]},props:{anchor:{type:String,required:!0},title:{type:String,required:!0}}},Yt=Xt,Jt=(n("e81e"),Object(K["a"])(Yt,Gt,Qt,!1,null,"6e075935",null)),Zt=Jt.exports,en=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"contenttable-section"},[n("div",{staticClass:"section-title"},[e._t("title",(function(){return[e.title?n("LinkableHeading",{class:e.className,attrs:{level:3,anchor:e.anchorComputed}},[e._v(e._s(e.title))]):e._e()]}),{className:e.className})],2),n("div",{staticClass:"section-content"},[e._t("abstract"),e._t("discussion"),e._t("default")],2)])},tn=[],nn=n("002d");const an="contenttable-title";var sn={name:"ContentTableSection",components:{LinkableHeading:Wt["a"]},props:{title:{type:String,required:!1},anchor:{type:String,default:null}},computed:{anchorComputed:({title:e,anchor:t})=>t||Object(nn["a"])(e||""),className:()=>an}},rn=sn,on=(n("46c5"),Object(K["a"])(rn,en,tn,!1,null,"4aae1079",null)),ln=on.exports,cn=n("2a18"),dn={name:"TopicsTable",mixins:[Ut["a"]],components:{TopicsLinkCardGrid:Vt["a"],WordBreak:De["a"],ContentTable:Zt,TopicsLinkBlock:cn["default"],ContentNode:At["a"],ContentTableSection:ln,LinkableHeading:Wt["a"]},props:{isSymbolDeprecated:Boolean,isSymbolBeta:Boolean,sections:{type:Array,required:!0},title:{type:String,required:!1,default(){return"Topics"}},anchor:{type:String,required:!1,default(){return"topics"}},wrapTitle:{type:Boolean,default:!1},topicStyle:{type:String,default:Pe["a"].list}},computed:{shouldRenderList:({topicStyle:e})=>e===Pe["a"].list,sectionsWithTopics(){return this.sections.map(e=>({...e,topics:e.identifiers.reduce((e,t)=>this.references[t]?e.concat(this.references[t]):e,[])}))}}},un=dn,hn=(n("00f2"),Object(K["a"])(un,Ft,Ht,!1,null,"3ccf02e9",null)),pn=hn.exports,gn={name:"DefaultImplementations",components:{TopicsTable:pn},computed:{contentSectionData:()=>Kt.defaultImplementations},props:{isSymbolDeprecated:Boolean,isSymbolBeta:Boolean,sections:pn.props.sections}},fn=gn,mn=Object(K["a"])(fn,Mt,zt,!1,null,null,null),yn=mn.exports,vn=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"primary-content"},e._l(e.sections,(function(t,i){return n(e.componentFor(t),e._b({key:i,tag:"component"},"component",e.propsFor(t),!1))})),1)},bn=[],Tn=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("section",[n("LinkableHeading",{attrs:{anchor:e.contentSectionData.anchor}},[e._v(" "+e._s(e.$t(e.contentSectionData.title))+" ")]),n("dl",{staticClass:"datalist"},[e._l(e.values,(function(t){return[n("dt",{key:t.name+":name",staticClass:"param-name"},[n("WordBreak",{attrs:{tag:"code"}},[e._v(e._s(t.name))])],1),t.content?n("dd",{key:t.name+":content",staticClass:"value-content"},[n("ContentNode",{attrs:{content:t.content}})],1):e._e()]}))],2)],1)},Sn=[],_n=n("5677"),Cn={name:"PossibleValues",components:{ContentNode:_n["default"],LinkableHeading:Wt["a"],WordBreak:De["a"]},props:{values:{type:Array,required:!0}},computed:{contentSectionData:()=>qt[Ke.possibleValues]}},kn=Cn,wn=(n("719b"),Object(K["a"])(kn,Tn,Sn,!1,null,null,null)),In=wn.exports,xn=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("section",[n("LinkableHeading",{attrs:{anchor:e.anchor}},[e._v(e._s(e.title))]),n("DeclarationSource",{attrs:{tokens:e.tokens}})],1)},$n=[],On={name:"RestEndpoint",components:{DeclarationSource:ot,LinkableHeading:Wt["a"]},props:{title:{type:String,required:!0},tokens:{type:Array,required:!0}},computed:{anchor:({title:e})=>Object(nn["a"])(e)}},Dn=On,Pn=Object(K["a"])(Dn,xn,$n,!1,null,null,null),Ln=Pn.exports,An=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("section",{staticClass:"details"},[n("LinkableHeading",{attrs:{anchor:e.contentSectionData.anchor}},[e._v(" "+e._s(e.$t(e.contentSectionData.title))+" ")]),n("dl",[e.isSymbol?[n("dt",{key:e.details.name+":name",staticClass:"detail-type"},[e._v(" "+e._s(e.$t("metadata.details.name"))+" ")]),n("dd",{key:e.details.ideTitle+":content",staticClass:"detail-content"},[e._v(" "+e._s(e.details.ideTitle)+" ")])]:e._e(),e.isTitle?[n("dt",{key:e.details.name+":key",staticClass:"detail-type"},[e._v(" "+e._s(e.$t("metadata.details.key"))+" ")]),n("dd",{key:e.details.ideTitle+":content",staticClass:"detail-content"},[e._v(" "+e._s(e.details.name)+" ")])]:e._e(),n("dt",{key:e.details.name+":type",staticClass:"detail-type"},[e._v(" "+e._s(e.$t("metadata.details.type"))+" ")]),n("dd",{staticClass:"detail-content"},[n("PropertyListKeyType",{attrs:{types:e.details.value}})],1)],2)],1)},Nn=[],En=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"type"},[e._v(e._s(e.typeOutput))])},jn=[],Bn={name:"PropertyListKeyType",props:{types:{type:Array,required:!0}},computed:{englishTypes(){return this.types.map(({arrayMode:e,baseType:t="*"})=>e?"array of "+this.pluralizeKeyType(t):t)},typeOutput(){return this.englishTypes.length>2?[this.englishTypes.slice(0,this.englishTypes.length-1).join(", "),this.englishTypes[this.englishTypes.length-1]].join(", or "):this.englishTypes.join(" or ")}},methods:{pluralizeKeyType(e){switch(e){case"dictionary":return"dictionaries";case"array":case"number":case"string":return e+"s";default:return e}}}},Rn=Bn,Mn=(n("f7c0"),Object(K["a"])(Rn,En,jn,!1,null,"791bac44",null)),zn=Mn.exports,Kn={name:"PropertyListKeyDetails",components:{PropertyListKeyType:zn,LinkableHeading:Wt["a"]},props:{details:{type:Object,required:!0}},computed:{contentSectionData:()=>qt[Ke.details],isTitle(){return"title"===this.details.titleStyle&&this.details.ideTitle},isSymbol(){return"symbol"===this.details.titleStyle&&this.details.ideTitle}}},qn=Kn,Fn=(n("45b2"),Object(K["a"])(qn,An,Nn,!1,null,"d66cd00c",null)),Hn=Fn.exports,Vn=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("section",{staticClass:"parameters"},[n("LinkableHeading",{attrs:{anchor:e.contentSectionData.anchor}},[e._v(" "+e._s(e.$t(e.contentSectionData.title))+" ")]),n("dl",[e._l(e.parameters,(function(t){return[n("dt",{key:t.name+":name",staticClass:"param-name"},[n("code",[e._v(e._s(t.name))])]),n("dd",{key:t.name+":content",staticClass:"param-content"},[n("ContentNode",{attrs:{content:t.content}})],1)]}))],2)],1)},Wn=[],Un={name:"Parameters",components:{ContentNode:At["a"],LinkableHeading:Wt["a"]},props:{parameters:{type:Array,required:!0}},computed:{contentSectionData:()=>qt[Ke.parameters]}},Gn=Un,Qn=(n("1a39"),Object(K["a"])(Gn,Vn,Wn,!1,null,"53cac581",null)),Xn=Qn.exports,Yn=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("section",[n("LinkableHeading",{attrs:{anchor:e.anchor}},[e._v(e._s(e.title))]),n("ParametersTable",{staticClass:"property-table",attrs:{parameters:e.properties,changes:e.propertyChanges},scopedSlots:e._u([{key:"symbol",fn:function(t){var i=t.name,a=t.type,s=t.content,r=t.changes,o=t.deprecated;return[n("div",{staticClass:"property-name",class:{deprecated:o}},[n("WordBreak",{attrs:{tag:"code"}},[e._v(e._s(i))])],1),e.shouldShiftType({name:i,content:s})?e._e():n("PossiblyChangedType",{attrs:{type:a,changes:r.type}})]}},{key:"description",fn:function(t){var i=t.name,a=t.type,s=t.attributes,r=t.content,o=t.required,l=t.changes,c=t.deprecated,d=t.readOnly;return[e.shouldShiftType({name:i,content:r})?n("PossiblyChangedType",{attrs:{type:a,changes:l.type}}):e._e(),c?[n("Badge",{staticClass:"property-deprecated",attrs:{variant:"deprecated"}}),e._v("  ")]:e._e(),n("PossiblyChangedTextAttribute",{attrs:{changes:l.required,value:o}},[e._v(" "+e._s(e.$t("formats.parenthesis",{content:e.$t("required")}))+" ")]),n("PossiblyChangedTextAttribute",{attrs:{changes:l.readOnly,value:d}},[e._v(" "+e._s(e.$t("formats.parenthesis",{content:e.$t("read-only")}))+" ")]),r?n("ContentNode",{attrs:{content:r}}):e._e(),n("ParameterAttributes",{attrs:{attributes:s,changes:l.attributes}})]}}])})],1)},Jn=[],Zn={inject:["identifier","store"],data:({store:{state:e}})=>({state:e}),computed:{apiChanges:({state:{apiChanges:e},identifier:t})=>e&&e[t]}},ei=n("a0fd"),ti=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"parameters-table"},e._l(e.parameters,(function(t){return n("Row",{key:t[e.keyBy],staticClass:"param",class:e.changedClasses(t[e.keyBy])},[n("Column",{staticClass:"param-symbol",attrs:{span:{large:3,small:12}}},[e._t("symbol",null,null,e.getProps(t,e.changes[t[e.keyBy]]))],2),n("Column",{staticClass:"param-content",attrs:{span:{large:9,small:12}}},[e._t("description",null,null,e.getProps(t,e.changes[t[e.keyBy]]))],2)],1)})),1)},ni=[],ii={name:"ParametersTable",components:{Row:B["a"],Column:R["a"]},props:{parameters:{type:Array,required:!0},changes:{type:Object,default:()=>({})},keyBy:{type:String,default:"name"}},methods:{getProps(e,t={}){return{...e,changes:t}},changedClasses(e){const{changes:t}=this,{change:n}=t[e]||{};return{["changed changed-"+n]:n}}}},ai=ii,si=(n("5561"),Object(K["a"])(ai,ti,ni,!1,null,"3f89f723",null)),ri=si.exports,oi=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"parameter-attributes"},[e.shouldRender(e.AttributeKind.default)?n("ParameterMetaAttribute",e._b({scopedSlots:e._u([{key:"default",fn:function(t){var i=t.attribute;return[e._v(" "+e._s(e.$t("formats.colon",{content:i.title||e.$t("parameters.default")}))),n("code",[e._v(e._s(i.value))])]}}],null,!1,2998238055)},"ParameterMetaAttribute",{kind:e.AttributeKind.default,attributes:e.attributesObject,changes:e.changes},!1)):e._e(),e.shouldRender(e.AttributeKind.minimum)?n("ParameterMetaAttribute",e._b({scopedSlots:e._u([{key:"default",fn:function(t){var i=t.attribute;return[e._v(" "+e._s(e.$t("formats.colon",{content:i.title||e.$t("parameters.minimum")}))),n("code",[e._v(e._s(i.value))])]}}],null,!1,859757818)},"ParameterMetaAttribute",{kind:e.AttributeKind.minimum,attributes:e.attributesObject,changes:e.changes},!1)):e._e(),e.shouldRender(e.AttributeKind.minimumExclusive)?n("ParameterMetaAttribute",e._b({scopedSlots:e._u([{key:"default",fn:function(t){var i=t.attribute;return[e._v(" "+e._s(e.$t("formats.colon",{content:i.title||e.$t("parameters.minimum")}))),n("code",[e._v("> "+e._s(i.value))])]}}],null,!1,770347247)},"ParameterMetaAttribute",{kind:e.AttributeKind.minimumExclusive,attributes:e.attributesObject,changes:e.changes},!1)):e._e(),e.shouldRender(e.AttributeKind.maximum)?n("ParameterMetaAttribute",e._b({scopedSlots:e._u([{key:"default",fn:function(t){var i=t.attribute;return[e._v(" "+e._s(e.$t("formats.colon",{content:i.title||e.$t("parameters.maximum")}))),n("code",[e._v(e._s(i.value))])]}}],null,!1,1190666532)},"ParameterMetaAttribute",{kind:e.AttributeKind.maximum,attributes:e.attributesObject,changes:e.changes},!1)):e._e(),e.shouldRender(e.AttributeKind.maximumExclusive)?n("ParameterMetaAttribute",e._b({scopedSlots:e._u([{key:"default",fn:function(t){var i=t.attribute;return[e._v(" "+e._s(e.$t("formats.colon",{content:i.title||e.$t("parameters.maximum")}))),n("code",[e._v("< "+e._s(i.value))])]}}],null,!1,1156490099)},"ParameterMetaAttribute",{kind:e.AttributeKind.maximumExclusive,attributes:e.attributesObject,changes:e.changes},!1)):e._e(),e.shouldRender(e.AttributeKind.allowedTypes)?n("ParameterMetaAttribute",e._b({scopedSlots:e._u([{key:"default",fn:function(t){var i=t.attribute;return[e._v(" "+e._s(e.$t("formats.colon",{content:e.$tc("parameters.possible-types",e.fallbackToValues(i).length)}))),n("code",[e._l(e.fallbackToValues(i),(function(t,a){return[e._l(t,(function(t,s){return[n("DeclarationToken",e._b({key:a+"-"+s},"DeclarationToken",t,!1)),a+1<e.fallbackToValues(i).length?[e._v(", ")]:e._e()]}))]}))],2)]}}],null,!1,387374398)},"ParameterMetaAttribute",{kind:e.AttributeKind.allowedTypes,attributes:e.attributesObject,changes:e.changes},!1)):e._e(),e.shouldRender(e.AttributeKind.allowedValues)?n("ParameterMetaAttribute",e._b({scopedSlots:e._u([{key:"default",fn:function(t){var i=t.attribute;return[e._v(" "+e._s(e.$t("formats.colon",{content:e.$tc("parameters.possible-values",e.fallbackToValues(i).length)}))),n("code",[e._v(e._s(e.fallbackToValues(i).join(", ")))])]}}],null,!1,3308368915)},"ParameterMetaAttribute",{kind:e.AttributeKind.allowedValues,attributes:e.attributesObject,changes:e.changes},!1)):e._e()],1)},li=[],ci=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("RenderChanged",{attrs:{value:e.attributes[e.kind],changes:e.changes[e.kind]},scopedSlots:e._u([{key:"default",fn:function(t){var i=t.value;return n("div",{staticClass:"property-metadata"},[e._t("default",null,{attribute:i})],2)}}],null,!0)})},di=[];const ui={added:"change-added",removed:"change-removed"};var hi,pi,gi={name:"RenderChanged",constants:{ChangedClasses:ui},props:{changes:{type:Object,default:()=>({new:null,previous:null})},value:{type:[Object,Array,String,Boolean],default:null},wrapChanges:{type:Boolean,default:!0},renderSingleChange:{type:Boolean,default:!1}},render(e){const{value:t,changes:n={},wrapChanges:i,renderSingleChange:a}=this,{new:s,previous:r}=n,o=(t,n)=>{const a=this.$scopedSlots.default({value:t});return n&&i?e("div",{class:n},[a]):a?a[0]:null};if(s||r){const t=o(s,ui.added),n=o(r,ui.removed);return a?s&&!r?t:n:e("div",{class:"property-changegroup"},[s?t:"",r?n:""])}return o(t)}},fi=gi,mi=Object(K["a"])(fi,hi,pi,!1,null,null,null),yi=mi.exports,vi={name:"ParameterMetaAttribute",components:{RenderChanged:yi},props:{kind:{type:String,required:!0},attributes:{type:Object,required:!0},changes:{type:Object,default:()=>({})}}},bi=vi,Ti=(n("2822"),Object(K["a"])(bi,ci,di,!1,null,"8590589e",null)),Si=Ti.exports;const _i={allowedTypes:"allowedTypes",allowedValues:"allowedValues",default:"default",maximum:"maximum",maximumExclusive:"maximumExclusive",minimum:"minimum",minimumExclusive:"minimumExclusive"};var Ci={name:"ParameterAttributes",components:{ParameterMetaAttribute:Si,DeclarationToken:tt["a"]},constants:{AttributeKind:_i},props:{attributes:{type:Array,default:()=>[]},changes:{type:Object,default:()=>({})}},computed:{AttributeKind:()=>_i,attributesObject:({attributes:e})=>e.reduce((e,t)=>({...e,[t.kind]:t}),{})},methods:{shouldRender(e){return Object.prototype.hasOwnProperty.call(this.attributesObject,e)},fallbackToValues:e=>{const t=e||[];return Array.isArray(t)?t:t.values}}},ki=Ci,wi=Object(K["a"])(ki,oi,li,!1,null,null,null),Ii=wi.exports,xi=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("RenderChanged",{attrs:{renderSingleChange:"",value:e.value,changes:e.changes},scopedSlots:e._u([{key:"default",fn:function(t){var i=t.value;return i?n("span",{staticClass:"property-text"},[e._t("default")],2):e._e()}}],null,!0)})},$i=[],Oi={name:"PossiblyChangedTextAttribute",components:{RenderChanged:yi},props:{changes:{type:Object,required:!1},value:{type:Boolean,default:!1}}},Di=Oi,Pi=(n("5c57"),Object(K["a"])(Di,xi,$i,!1,null,null,null)),Li=Pi.exports,Ai=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("RenderChanged",{attrs:{value:e.type,wrapChanges:!1,changes:e.changes},scopedSlots:e._u([{key:"default",fn:function(t){var i=t.value;return n("DeclarationTokenGroup",{staticClass:"property-metadata property-type",attrs:{type:e.getValues(i)}})}}])})},Ni=[],Ei=function(){var e=this,t=e.$createElement,n=e._self._c||t;return e.type&&e.type.length?n("div",[n("code",e._l(e.type,(function(t,i){return n("DeclarationToken",e._b({key:i},"DeclarationToken",t,!1))})),1)]):e._e()},ji=[],Bi={name:"DeclarationTokenGroup",components:{DeclarationToken:tt["a"]},props:{type:{type:Array,default:()=>[],required:!1}}},Ri=Bi,Mi=Object(K["a"])(Ri,Ei,ji,!1,null,null,null),zi=Mi.exports,Ki={name:"PossiblyChangedType",components:{DeclarationTokenGroup:zi,RenderChanged:yi},props:{type:{type:Array,required:!0},changes:{type:Object,required:!1}},methods:{getValues(e){return Array.isArray(e)?e:e.values}}},qi=Ki,Fi=(n("2f87"),Object(K["a"])(qi,Ai,Ni,!1,null,"0a648a1e",null)),Hi=Fi.exports,Vi={name:"PropertyTable",mixins:[Zn],components:{Badge:ei["a"],WordBreak:De["a"],PossiblyChangedTextAttribute:Li,PossiblyChangedType:Hi,ParameterAttributes:Ii,ContentNode:At["a"],ParametersTable:ri,LinkableHeading:Wt["a"]},props:{title:{type:String,required:!0},properties:{type:Array,required:!0}},computed:{anchor:({title:e})=>Object(nn["a"])(e),propertyChanges:({apiChanges:e})=>(e||{}).properties},methods:{shouldShiftType:({content:e=[],name:t})=>!e.length&&t}},Wi=Vi,Ui=(n("d71b"),Object(K["a"])(Wi,Yn,Jn,!1,null,"310f0b2c",null)),Gi=Ui.exports,Qi=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("section",[n("LinkableHeading",{attrs:{anchor:e.anchor}},[e._v(e._s(e.title))]),n("ParametersTable",{attrs:{parameters:[e.bodyParam],changes:e.bodyChanges,keyBy:"key"},scopedSlots:e._u([{key:"symbol",fn:function(t){var i=t.type,a=t.content,s=t.changes,r=t.name;return[e.shouldShiftType({name:r,content:a})?e._e():n("PossiblyChangedType",{attrs:{type:i,changes:s.type}})]}},{key:"description",fn:function(t){var i=t.name,a=t.content,s=t.mimeType,r=t.type,o=t.changes;return[e.shouldShiftType({name:i,content:a})?n("PossiblyChangedType",{attrs:{type:r,changes:o.type}}):e._e(),a?n("ContentNode",{attrs:{content:a}}):e._e(),s?n("PossiblyChangedMimetype",{attrs:{mimetype:s,changes:o.mimetype,change:o.change}}):e._e()]}}])}),e.parts.length?[n("h3",[e._v(e._s(e.$t("sections.parts")))]),n("ParametersTable",{staticClass:"parts",attrs:{parameters:e.parts,changes:e.partsChanges},scopedSlots:e._u([{key:"symbol",fn:function(t){var i=t.name,a=t.type,s=t.content,r=t.changes;return[n("div",{staticClass:"part-name"},[n("WordBreak",{attrs:{tag:"code"}},[e._v(e._s(i))])],1),s?n("PossiblyChangedType",{attrs:{type:a,changes:r.type}}):e._e()]}},{key:"description",fn:function(t){var i=t.content,a=t.mimeType,s=t.required,r=t.type,o=t.attributes,l=t.changes,c=t.readOnly;return[n("div",[i?e._e():n("PossiblyChangedType",{attrs:{type:r,changes:l.type}}),n("PossiblyChangedTextAttribute",{attrs:{changes:l.required,value:s}},[e._v("(Required) ")]),n("PossiblyChangedTextAttribute",{attrs:{changes:l.readOnly,value:c}},[e._v("(Read only) ")]),i?n("ContentNode",{attrs:{content:i}}):e._e(),a?n("PossiblyChangedMimetype",{attrs:{mimetype:a,changes:l.mimetype,change:l.change}}):e._e(),n("ParameterAttributes",{attrs:{attributes:o,changes:l.attributes}})],1)]}}],null,!1,1779956822)})]:e._e()],2)},Xi=[],Yi=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("RenderChanged",{attrs:{changes:e.changeValues,value:e.mimetype},scopedSlots:e._u([{key:"default",fn:function(t){var i=t.value;return n("div",{staticClass:"response-mimetype"},[e._v(" "+e._s(e.$t("content-type",{value:i}))+" ")])}}])})},Ji=[],Zi={name:"PossiblyChangedMimetype",components:{RenderChanged:yi},props:{mimetype:{type:String,required:!0},changes:{type:[Object,String],required:!1},change:{type:String,required:!1}},computed:{changeValues({change:e,changes:t}){return e===It["c"].modified&&"string"!==typeof t?t:void 0}}},ea=Zi,ta=(n("d1ac"),Object(K["a"])(ea,Yi,Ji,!1,null,"20293786",null)),na=ta.exports;const ia="restRequestBody";var aa={name:"RestBody",mixins:[Zn],components:{PossiblyChangedMimetype:na,PossiblyChangedTextAttribute:Li,PossiblyChangedType:Hi,WordBreak:De["a"],ParameterAttributes:Ii,ContentNode:At["a"],ParametersTable:ri,LinkableHeading:Wt["a"]},constants:{ChangesKey:ia},props:{bodyContentType:{type:Array,required:!0},content:{type:Array},mimeType:{type:String,required:!0},parts:{type:Array,default:()=>[]},title:{type:String,required:!0}},computed:{anchor:({title:e})=>Object(nn["a"])(e),bodyParam:({bodyContentType:e,content:t,mimeType:n})=>({key:ia,content:t,mimeType:n,type:e}),bodyChanges:({apiChanges:e})=>e||{},partsChanges:({bodyChanges:e})=>(e[ia]||{}).parts},methods:{shouldShiftType:({content:e=[],name:t})=>!e.length&&t}},sa=aa,ra=(n("6a35"),Object(K["a"])(sa,Qi,Xi,!1,null,"021cd63d",null)),oa=ra.exports,la=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("section",[n("LinkableHeading",{attrs:{anchor:e.anchor}},[e._v(e._s(e.title))]),n("ParametersTable",{attrs:{parameters:e.parameters,changes:e.parameterChanges},scopedSlots:e._u([{key:"symbol",fn:function(t){var i=t.name,a=t.type,s=t.content,r=t.changes,o=t.deprecated;return[n("div",{staticClass:"param-name",class:{deprecated:o}},[n("WordBreak",{attrs:{tag:"code"}},[e._v(e._s(i))])],1),e.shouldShiftType({content:s,name:i})?e._e():n("PossiblyChangedType",{attrs:{type:a,changes:r.type}})]}},{key:"description",fn:function(t){var i=t.name,a=t.type,s=t.content,r=t.required,o=t.attributes,l=t.changes,c=t.deprecated,d=t.readOnly;return[n("div",[e.shouldShiftType({content:s,name:i})?n("PossiblyChangedType",{attrs:{type:a,changes:l.type}}):e._e(),c?[n("Badge",{staticClass:"param-deprecated",attrs:{variant:"deprecated"}}),e._v("  ")]:e._e(),n("PossiblyChangedTextAttribute",{attrs:{changes:l.required,value:r}},[e._v(" "+e._s(e.$t("formats.parenthesis",{content:e.$t("required")}))+" ")]),n("PossiblyChangedTextAttribute",{attrs:{changes:l.readOnly,value:d}},[e._v(" "+e._s(e.$t("formats.parenthesis",{content:e.$t("read-only")}))+" ")]),s?n("ContentNode",{attrs:{content:s}}):e._e(),n("ParameterAttributes",{attrs:{attributes:o,changes:l}})],2)]}}])})],1)},ca=[],da={name:"RestParameters",mixins:[Zn],components:{Badge:ei["a"],PossiblyChangedType:Hi,PossiblyChangedTextAttribute:Li,ParameterAttributes:Ii,WordBreak:De["a"],ContentNode:At["a"],ParametersTable:ri,LinkableHeading:Wt["a"]},props:{title:{type:String,required:!0},parameters:{type:Array,required:!0}},computed:{anchor:({title:e})=>Object(nn["a"])(e),parameterChanges:({apiChanges:e})=>(e||{}).restParameters},methods:{shouldShiftType:({content:e=[],name:t})=>!e.length&&t}},ua=da,ha=(n("eb6d"),Object(K["a"])(ua,la,ca,!1,null,"03478142",null)),pa=ha.exports,ga=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("section",[n("LinkableHeading",{attrs:{anchor:e.anchor}},[e._v(e._s(e.title))]),n("ParametersTable",{attrs:{parameters:e.responses,changes:e.propertyChanges,"key-by":"status"},scopedSlots:e._u([{key:"symbol",fn:function(t){var i=t.status,a=t.type,s=t.reason,r=t.content,o=t.changes;return[n("div",{staticClass:"response-name"},[n("code",[e._v(" "+e._s(i)+" "),n("span",{staticClass:"reason"},[e._v(e._s(s))])])]),e.shouldShiftType({content:r,reason:s,status:i})?e._e():n("PossiblyChangedType",{attrs:{type:a,changes:o.type}})]}},{key:"description",fn:function(t){var i=t.content,a=t.mimetype,s=t.reason,r=t.type,o=t.status,l=t.changes;return[e.shouldShiftType({content:i,reason:s,status:o})?n("PossiblyChangedType",{attrs:{type:r,changes:l.type}}):e._e(),n("div",{staticClass:"response-reason"},[n("code",[e._v(e._s(s))])]),i?n("ContentNode",{attrs:{content:i}}):e._e(),a?n("PossiblyChangedMimetype",{attrs:{mimetype:a,changes:l.mimetype,change:l.change}}):e._e()]}}])})],1)},fa=[],ma={name:"RestResponses",mixins:[Zn],components:{PossiblyChangedMimetype:na,PossiblyChangedType:Hi,ContentNode:At["a"],ParametersTable:ri,LinkableHeading:Wt["a"]},props:{title:{type:String,required:!0},responses:{type:Array,required:!0}},computed:{anchor:({title:e})=>Object(nn["a"])(e),propertyChanges:({apiChanges:e})=>(e||{}).restResponses},methods:{shouldShiftType:({content:e=[],reason:t,status:n})=>!(e.length||t)&&n}},ya=ma,va=(n("7649"),Object(K["a"])(ya,ga,fa,!1,null,"881189f4",null)),ba=va.exports,Ta={name:"PrimaryContent",components:{ContentNode:At["a"],Parameters:Xn,PropertyListKeyDetails:Hn,PropertyTable:Gi,RestBody:oa,RestEndpoint:Ln,RestParameters:pa,RestResponses:ba,PossibleValues:In},constants:{SectionKind:Ke},props:{sections:{type:Array,required:!0,validator:e=>e.every(({kind:e})=>Object.prototype.hasOwnProperty.call(Ke,e))}},computed:{span(){return{large:9,medium:9,small:12}}},methods:{componentFor(e){return{[Ke.content]:At["a"],[Ke.details]:Hn,[Ke.parameters]:Xn,[Ke.properties]:Gi,[Ke.restBody]:oa,[Ke.restParameters]:pa,[Ke.restHeaders]:pa,[Ke.restCookies]:pa,[Ke.restEndpoint]:Ln,[Ke.restResponses]:ba,[Ke.possibleValues]:In}[e.kind]},propsFor(e){const{bodyContentType:t,content:n,details:i,items:a,kind:s,mimeType:r,parameters:o,title:l,tokens:c,values:d}=e;return{[Ke.content]:{content:n},[Ke.details]:{details:i},[Ke.parameters]:{parameters:o},[Ke.possibleValues]:{values:d},[Ke.properties]:{properties:a,title:l},[Ke.restBody]:{bodyContentType:t,content:n,mimeType:r,parts:o,title:l},[Ke.restCookies]:{parameters:a,title:l},[Ke.restEndpoint]:{tokens:c,title:l},[Ke.restHeaders]:{parameters:a,title:l},[Ke.restParameters]:{parameters:a,title:l},[Ke.restResponses]:{responses:a,title:l}}[s]}}},Sa=Ta,_a=(n("73a8"),Object(K["a"])(Sa,vn,bn,!1,null,"2baae7e0",null)),Ca=_a.exports,ka=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("ContentTable",{attrs:{anchor:e.contentSectionData.anchor,title:e.$t(e.contentSectionData.title)}},e._l(e.sectionsWithSymbols,(function(e){return n("Section",{key:e.type,attrs:{title:e.title,anchor:e.anchor}},[n("List",{attrs:{symbols:e.symbols,type:e.type}})],1)})),1)},wa=[],Ia=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("ul",{ref:"apiChangesDiff",staticClass:"relationships-list",class:e.classes},e._l(e.symbols,(function(t){return n("li",{key:t.identifier,staticClass:"relationships-item"},[t.url?n("router-link",{staticClass:"link",attrs:{to:e.buildUrl(t.url,e.$route.query)}},[n("WordBreak",{attrs:{tag:"code"}},[e._v(e._s(t.title))])],1):n("WordBreak",{attrs:{tag:"code"}},[e._v(e._s(t.title))]),t.conformance?n("ConditionalConstraints",{attrs:{constraints:t.conformance.constraints,prefix:t.conformance.conformancePrefix}}):e._e()],1)})),0)},xa=[];const $a=3,Oa={conformsTo:"conformance",inheritsFrom:"inheritance",inheritedBy:"inheritedBy"};var Da={name:"RelationshipsList",components:{ConditionalConstraints:He["a"],WordBreak:De["a"]},inject:["store","identifier"],mixins:[lt["b"],lt["a"]],props:{symbols:{type:Array,required:!0},type:{type:String,required:!0}},data(){return{state:this.store.state}},computed:{classes({changeType:e,multipleLinesClass:t,displaysMultipleLinesAfterAPIChanges:n}){return[{inline:this.shouldDisplayInline,column:!this.shouldDisplayInline,["changed changed-"+e]:!!e,[t]:n}]},hasAvailabilityConstraints(){return this.symbols.some(e=>!!(e.conformance||{}).constraints)},changes({identifier:e,state:{apiChanges:t}}){return(t||{})[e]||{}},changeType({changes:e,type:t}){const n=Oa[t];if(e.change!==It["c"].modified)return e.change;const i=e[n];if(!i)return;const a=(e,t)=>e.map((e,n)=>[e,t[n]]),s=a(i.previous,i.new).some(([e,t])=>e.content?0===e.content.length&&t.content.length>0:!!t.content);return s?It["c"].added:It["c"].modified},shouldDisplayInline(){const{hasAvailabilityConstraints:e,symbols:t}=this;return t.length<=$a&&!e}},methods:{buildUrl:P["b"]}},Pa=Da,La=(n("9475"),Object(K["a"])(Pa,Ia,xa,!1,null,"4c67b8c7",null)),Aa=La.exports,Na={name:"Relationships",mixins:[Ut["a"]],components:{ContentTable:Zt,List:Aa,Section:ln},props:{sections:{type:Array,required:!0}},computed:{contentSectionData:()=>Kt.relationships,sectionsWithSymbols(){return this.sections.map(e=>({...e,symbols:e.identifiers.reduce((e,t)=>this.references[t]?e.concat(this.references[t]):e,[])}))}}},Ea=Na,ja=Object(K["a"])(Ea,ka,wa,!1,null,null,null),Ba=ja.exports,Ra=n("e8ea"),Ma=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("Section",{staticClass:"availability",attrs:{role:"complementary","aria-label":e.$t("sections.availability")}},[e._l(e.technologies,(function(t){return n("Badge",{key:t,staticClass:"technology"},[n("TechnologyIcon",{staticClass:"tech-icon"}),e._v(" "+e._s(t)+" ")],1)})),e._l(e.platforms,(function(t){return n("Badge",{key:t.name,staticClass:"platform",class:e.changesClassesFor(t.name)},[n("AvailabilityRange",{attrs:{deprecatedAt:t.deprecatedAt,introducedAt:t.introducedAt,platformName:t.name}}),t.deprecatedAt?n("span",{staticClass:"deprecated"},[e._v(" "+e._s(e.$t("aside-kind.deprecated"))+" ")]):t.beta?n("span",{staticClass:"beta"},[e._v(e._s(e.$t("aside-kind.beta")))]):e._e()],1)}))],2)},za=[],Ka=n("3024"),qa=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("span",{attrs:{role:"text","aria-label":e.ariaLabel,title:e.description}},[e._v(" "+e._s(e.text)+" ")])},Fa=[],Ha={name:"AvailabilityRange",props:{deprecatedAt:{type:String,required:!1},introducedAt:{type:String,required:!0},platformName:{type:String,required:!0}},computed:{ariaLabel(){const{deprecatedAt:e,description:t,text:n}=this;return[n].concat(e?this.$t("change-type.deprecated"):[]).concat(t).join(", ")},description(){const{deprecatedAt:e,introducedAt:t,platformName:n}=this;return e?this.$t("availability.introduced-and-deprecated",{name:n,introducedAt:t,deprecatedAt:e}):this.$t("availability.available-on",{name:n,introducedAt:t})},text(){const{deprecatedAt:e,introducedAt:t,platformName:n}=this;return e?`${n} ${t}–${e}`:`${n} ${t}+`}}},Va=Ha,Wa=Object(K["a"])(Va,qa,Fa,!1,null,null,null),Ua=Wa.exports,Ga={name:"Availability",mixins:[lt["b"]],inject:["identifier","store"],components:{Badge:ei["a"],AvailabilityRange:Ua,Section:ie,TechnologyIcon:Ka["a"]},props:{platforms:{type:Array,required:!0},technologies:{type:Array,required:!1}},data(){return{state:this.store.state}},methods:{changeFor(e){const{identifier:t,state:{apiChanges:n}}=this,{availability:i={}}=(n||{})[t]||{},a=i[e];if(a)return a.deprecated?It["c"].deprecated:a.introduced&&!a.introduced.previous?It["c"].added:It["c"].modified}}},Qa=Ga,Xa=(n("2d12"),Object(K["a"])(Qa,Ma,za,!1,null,"602d8130",null)),Ya=Xa.exports,Ja=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("TopicsTable",{attrs:{anchor:e.contentSectionData.anchor,title:e.$t(e.contentSectionData.title),isSymbolDeprecated:e.isSymbolDeprecated,isSymbolBeta:e.isSymbolBeta,sections:e.sections}})},Za=[],es={name:"SeeAlso",components:{TopicsTable:pn},props:{isSymbolDeprecated:Boolean,isSymbolBeta:Boolean,sections:pn.props.sections},computed:{contentSectionData:()=>Kt.seeAlso}},ts=es,ns=Object(K["a"])(ts,Ja,Za,!1,null,null,null),is=ns.exports,as=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"topictitle"},[e.eyebrow?n("span",{staticClass:"eyebrow"},[e._v(e._s(e.eyebrow))]):e._e(),n("h1",{staticClass:"title"},[e._t("default"),e._t("after")],2)])},ss=[],rs={name:"Title",props:{eyebrow:{type:String,required:!1}}},os=rs,ls=(n("3396"),Object(K["a"])(os,as,ss,!1,null,"4492c658",null)),cs=ls.exports,ds=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("TopicsTable",{attrs:{anchor:e.contentSectionData.anchor,title:e.$t(e.contentSectionData.title),isSymbolDeprecated:e.isSymbolDeprecated,isSymbolBeta:e.isSymbolBeta,sections:e.sections,topicStyle:e.topicStyle}})},us=[],hs={name:"Topics",components:{TopicsTable:pn},computed:{contentSectionData:()=>Kt.topics},props:{isSymbolDeprecated:Boolean,isSymbolBeta:Boolean,sections:pn.props.sections,topicStyle:{type:String,required:!0,validator:e=>Object.hasOwnProperty.call(Pe["a"],e)}}},ps=hs,gs=Object(K["a"])(ps,ds,us,!1,null,null,null),fs=gs.exports,ms=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"OnThisPageStickyContainer"},[e._t("default")],2)},ys=[],vs={name:"OnThisPageStickyContainer"},bs=vs,Ts=(n("1e0b"),Object(K["a"])(bs,ms,ys,!1,null,"1b6d0048",null)),Ss=Ts.exports;const _s=1050;var Cs={name:"DocumentationTopic",mixins:[D["a"]],constants:{ON_THIS_PAGE_CONTAINER_BREAKPOINT:_s},inject:{isTargetIDE:{default(){return!1}},store:{default(){return{reset(){},state:{}}}}},components:{Declaration:Dt,OnThisPageStickyContainer:Ss,OnThisPageNav:ze,DocumentationHero:Oe,Abstract:Bt,Aside:N["a"],BetaLegalText:F,ContentNode:At["a"],DefaultImplementations:yn,DownloadButton:Rt["a"],LanguageSwitcher:pe,PrimaryContent:Ca,Relationships:Ba,RequirementMetadata:Ra["a"],Availability:Ya,SeeAlso:is,Title:cs,Topics:fs,ViewMore:be,WordBreak:De["a"]},props:{abstract:{type:Array,required:!1},conformance:{type:Object,required:!1},defaultImplementationsSections:{type:Array,required:!1},downloadNotAvailableSummary:{type:Array,required:!1},deprecationSummary:{type:Array,required:!1},diffAvailability:{type:Object,required:!1},modules:{type:Array,required:!1},hasNoExpandedDocumentation:{type:Boolean,required:!1},hierarchy:{type:Object,default:()=>({})},interfaceLanguage:{type:String,required:!0},identifier:{type:String,required:!0},isRequirement:{type:Boolean,default:()=>!1},platforms:{type:Array,required:!1},primaryContentSections:{type:Array,required:!1},references:{type:Object,required:!0},relationshipsSections:{type:Array,required:!1},roleHeading:{type:String,required:!1},title:{type:String,required:!0},topicSections:{type:Array,required:!1},topicSectionsStyle:{type:String,default:Pe["a"].list},sampleCodeDownload:{type:Object,required:!1},seeAlsoSections:{type:Array,required:!1},languagePaths:{type:Object,default:()=>({})},tags:{type:Array,required:!0},objcPath:{type:String,required:!1},swiftPath:{type:String,required:!1},isSymbolDeprecated:{type:Boolean,required:!1},isSymbolBeta:{type:Boolean,required:!1},symbolKind:{type:String,default:""},role:{type:String,default:""},remoteSource:{type:Object,required:!1},pageImages:{type:Array,required:!1},enableMinimized:{type:Boolean,default:!1},enableOnThisPageNav:{type:Boolean,default:!1},disableHeroBackground:{type:Boolean,default:!1},standardColorIdentifier:{type:String,required:!1,validator:e=>Object.prototype.hasOwnProperty.call(we,e)},availableLocales:{type:Array,required:!1}},provide(){return{identifier:this.identifier,languages:new Set(Object.keys(this.languagePaths)),interfaceLanguage:this.interfaceLanguage,symbolKind:this.symbolKind,enableMinimized:this.enableMinimized}},data(){return{topicState:this.store.state}},computed:{normalizedSwiftPath:({swiftPath:e})=>Object(L["d"])(e),normalizedObjcPath:({objcPath:e,swiftPath:t})=>Object(L["d"])(e&&t?Object(P["b"])(e,{language:O["a"].objectiveC.key.url}):e),defaultImplementationsCount(){return(this.defaultImplementationsSections||[]).reduce((e,t)=>e+t.identifiers.length,0)},shouldShowAvailability:({platforms:e,technologies:t,enableMinimized:n})=>((e||[]).length||(t||[]).length)&&!n,hasBetaContent:({platforms:e})=>e&&e.length&&e.some(e=>e.beta),pageTitle:({title:e})=>e,pageDescription:({abstract:e,extractFirstParagraphText:t})=>e?t(e):null,shouldShowLanguageSwitcher:({objcPath:e,swiftPath:t,isTargetIDE:n,enableMinimized:i})=>!!(e&&t&&n)&&!i,enhanceBackground:({symbolKind:e,disableHeroBackground:t,enableMinimized:n})=>!t&&!n&&(!e||"module"===e),shortHero:({roleHeading:e,abstract:t,sampleCodeDownload:n,hasAvailability:i,shouldShowLanguageSwitcher:a,declarations:s})=>!!e+!!t+!!n+!!s.length+!!i+a<=1,technologies({modules:e=[]}){const t=e.reduce((e,t)=>(e.push(t.name),e.concat(t.relatedModules||[])),[]);return t.length>1?t:[]},titleBreakComponent:({enhanceBackground:e})=>e?"span":De["a"],hasPrimaryContent:({isRequirement:e,deprecationSummary:t,downloadNotAvailableSummary:n,primaryContentSectionsSanitized:i,shouldShowViewMoreLink:a})=>e||t&&t.length||n&&n.length||i.length||a,viewMoreLink:({interfaceLanguage:e,normalizedObjcPath:t,normalizedSwiftPath:n})=>e===O["a"].objectiveC.key.api?t:n,shouldShowViewMoreLink:({enableMinimized:e,hasNoExpandedDocumentation:t,viewMoreLink:n})=>e&&!t&&n,tagName(){return this.isSymbolDeprecated?this.$t("aside-kind.deprecated"):this.$t("aside-kind.beta")},pageIcon:({pageImages:e=[]})=>{const t=e.find(({type:e})=>"icon"===e);return t?t.identifier:null},shouldRenderTopicSection:({topicSectionsStyle:e,topicSections:t,enableMinimized:n})=>t&&e!==Pe["a"].hidden&&!n,isOnThisPageNavVisible:({topicState:e})=>e.contentWidth>_s,disableMetadata:({enableMinimized:e})=>e,primaryContentSectionsSanitized({primaryContentSections:e=[]}){return e.filter(({kind:e})=>e!==Ke.declarations)},declarations({primaryContentSections:e=[]}){return e.filter(({kind:e})=>e===Ke.declarations)}},methods:{extractProps(e){const{abstract:t,defaultImplementationsSections:n,deprecationSummary:i,downloadNotAvailableSummary:a,diffAvailability:s,hierarchy:r,identifier:{interfaceLanguage:o,url:l},metadata:{conformance:c,hasNoExpandedDocumentation:d,modules:u,availableLocales:h,platforms:p,required:g=!1,roleHeading:f,title:m="",tags:y=[],role:v,symbolKind:b="",remoteSource:T,images:S=[],color:{standardColorIdentifier:_}={}}={},primaryContentSections:C,relationshipsSections:k,references:w={},sampleCodeDownload:I,topicSectionsStyle:x,topicSections:$,seeAlsoSections:D,variantOverrides:P,variants:L=[]}=e,A=L.reduce((e,t)=>t.traits.reduce((e,n)=>n.interfaceLanguage?{...e,[n.interfaceLanguage]:(e[n.interfaceLanguage]||[]).concat(t.paths)}:e,e),{}),{[O["a"].objectiveC.key.api]:[N]=[],[O["a"].swift.key.api]:[E]=[]}=A;return{abstract:t,conformance:c,defaultImplementationsSections:n,deprecationSummary:i,downloadNotAvailableSummary:a,diffAvailability:s,hasNoExpandedDocumentation:d,availableLocales:h,hierarchy:r,role:v,identifier:l,interfaceLanguage:o,isRequirement:g,modules:u,platforms:p,primaryContentSections:C,relationshipsSections:k,references:w,roleHeading:f,sampleCodeDownload:I,title:m,topicSections:$,topicSectionsStyle:x,seeAlsoSections:D,variantOverrides:P,symbolKind:b,tags:y.slice(0,1),remoteSource:T,pageImages:S,objcPath:N,swiftPath:E,standardColorIdentifier:_}}},created(){if(this.topicState.preferredLanguage===O["a"].objectiveC.key.url&&this.interfaceLanguage!==O["a"].objectiveC.key.api&&this.objcPath&&this.$route.query.language!==O["a"].objectiveC.key.url){const{query:e}=this.$route;this.$nextTick().then(()=>{this.$router.replace({path:Object(L["d"])(this.objcPath),query:{...e,language:O["a"].objectiveC.key.url}})})}A["a"].setAvailableLocales(this.availableLocales||[]),this.store.reset(),this.store.setReferences(this.references)},watch:{references(e){this.store.setReferences(e)},availableLocales(e){A["a"].setAvailableLocales(e)}}},ks=Cs,ws=(n("6d05"),Object(K["a"])(ks,x,$,!1,null,"43c74ad0",null)),Is=ws.exports,xs=n("2b0e");const $s=()=>({[It["c"].modified]:0,[It["c"].added]:0,[It["c"].deprecated]:0});var Os={state:{apiChanges:null,apiChangesCounts:$s(),selectedAPIChangesVersion:null},setAPIChanges(e){this.state.apiChanges=e},setSelectedAPIChangesVersion(e){this.state.selectedAPIChangesVersion=e},resetApiChanges(){this.state.apiChanges=null,this.state.apiChangesCounts=$s()},async updateApiChangesCounts(){await xs["default"].nextTick(),Object.keys(this.state.apiChangesCounts).forEach(e=>{this.state.apiChangesCounts[e]=this.countChangeType(e)})},countChangeType(e){if(document&&document.querySelectorAll){const t=`.changed-${e}:not(.changed-total)`;return document.querySelectorAll(t).length}return 0}},Ds={state:{onThisPageSections:[],currentPageAnchor:null},resetPageSections(){this.state.onThisPageSections=[],this.state.currentPageAnchor=null},addOnThisPageSection(e,{i18n:t=!0}={}){this.state.onThisPageSections.push({...e,i18n:t})},setCurrentPageSection(e){const t=this.state.onThisPageSections.findIndex(({anchor:t})=>t===e);-1!==t&&(this.state.currentPageAnchor=e)}},Ps=n("d369");const{state:Ls,...As}=Os,{state:Ns,...Es}=Ds;var js={state:{preferredLanguage:Ps["a"].preferredLanguage,contentWidth:0,...Ls,...Ns,references:{}},reset(){this.state.preferredLanguage=Ps["a"].preferredLanguage,this.state.references={},this.resetApiChanges()},setPreferredLanguage(e){this.state.preferredLanguage=e,Ps["a"].preferredLanguage=this.state.preferredLanguage},setContentWidth(e){this.state.contentWidth=e},setReferences(e){this.state.references=e},...As,...Es},Bs=n("8590"),Rs=n("66c9"),Ms=n("0caf"),zs=n("146e");const Ks="<root>",qs=32,Fs="navigator-hide-button";function Hs(e){return e.split("").reduce((e,t)=>(e<<5)-e+t.charCodeAt(0)|0,0)}function Vs(e){const t={},n=e.length;for(let i=0;i<n;i+=1)t[e[i].uid]=e[i];return t}function Ws(e,t=null,n=0,i=!1){let a=[];const s=e.length;let r,o=null;for(r=0;r<s;r+=1){const{children:l,...c}=e[r],{uid:d=Ks}=t||{};c.uid=Hs(`${d}+${c.path}_${n}_${r}`),c.parent=d,c.type===Ce["b"].groupMarker?(c.deprecatedChildrenCount=0,o=c):o&&(o.childUIDs.push(c.uid),c.groupMarkerUID=o.uid,c.deprecated&&(o.deprecatedChildrenCount+=1)),c.index=r,c.siblingsCount=s,c.depth=n,c.childUIDs=[],t&&t.childUIDs.push(c.uid),c.beta&&i&&(c.beta=!1),a.push(c),l&&(a=a.concat(Ws(l,c,n+1,i||c.beta)))}return a}function Us(e,t){const n=new Set([]),i=[e];let a=null;while(i.length){a=i.shift();const e=t[a];n.add(e),i.unshift(...e.childUIDs)}return[...n]}function Gs(e,t,n){if(e===Ks)return n.filter(e=>e.parent===Ks);const i=t[e];return i?(i.childUIDs||[]).map(e=>t[e]):[]}function Qs(e,t){const n=[],i=[e];let a=null;while(i.length){a=i.pop();const e=t[a];if(!e)return[];n.unshift(e),e.parent&&e.parent!==Ks&&i.push(e.parent)}return n}function Xs(e,t,n){const i=t[e];return i?Gs(i.parent,t,n):[]}var Ys,Js,Zs={name:"NavigatorDataProvider",props:{interfaceLanguage:{type:String,default:O["a"].swift.key.url},technologyUrl:{type:String,required:!0},apiChangesVersion:{type:String,default:""}},data(){return{isFetching:!1,errorFetching:!1,isFetchingAPIChanges:!1,navigationIndex:{[O["a"].swift.key.url]:[]},navigationReferences:{},diffs:null}},computed:{flatChildren:({technologyWithChildren:e={}})=>Ws(e.children||[],null,0,e.beta),technologyPath:({technologyUrl:e})=>{const t=/(\/documentation\/(?:[^/]+))\/?/.exec(e);return t?t[1]:""},technologyWithChildren({navigationIndex:e,interfaceLanguage:t,technologyPath:n}){let i=e[t]||[];return i.length||(i=e[O["a"].swift.key.url]||[]),i.find(e=>n.toLowerCase()===e.path.toLowerCase())}},methods:{async fetchIndexData(){try{this.isFetching=!0;const{interfaceLanguages:e,references:t}=await Object(w["d"])({slug:this.$route.params.locale||""});this.navigationIndex=Object.freeze(e),this.navigationReferences=Object.freeze(t)}catch(e){this.errorFetching=!0}finally{this.isFetching=!1}}},watch:{"$route.params.locale":{handler:"fetchIndexData",immediate:!0}},render(){return this.$scopedSlots.default({technology:this.technologyWithChildren,isFetching:this.isFetching,errorFetching:this.errorFetching,isFetchingAPIChanges:this.isFetchingAPIChanges,apiChanges:this.diffs,flatChildren:this.flatChildren,references:this.navigationReferences})}},er=Zs,tr=Object(K["a"])(er,Ys,Js,!1,null,null,null),nr=tr.exports,ir=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("button",{staticClass:"quick-navigation-open",attrs:{"aria-label":e.$t("quicknav.button.label"),title:e.$t("quicknav.button.title")}},[e._v(" / ")])},ar=[],sr={name:"QuickNavigationButton"},rr=sr,or=(n("5a73"),Object(K["a"])(rr,ir,ar,!1,null,"53faf852",null)),lr=or.exports,cr=function(){var e,t,n=this,i=n.$createElement,a=n._self._c||i;return a("GenericModal",{attrs:{isFullscreen:"",showClose:!1,visible:n.isVisible,backdropBackgroundColorOverride:"rgba(0, 0, 0, 0.7)"},on:{"update:visible":function(e){n.isVisible=e}}},[a("div",{staticClass:"quick-navigation",on:{keydown:[function(e){return!e.type.indexOf("key")&&n._k(e.keyCode,"down",40,e.key,["Down","ArrowDown"])||e.ctrlKey||e.shiftKey||e.altKey||e.metaKey?null:(e.preventDefault(),n.focusNext.apply(null,arguments))},function(e){return!e.type.indexOf("key")&&n._k(e.keyCode,"up",38,e.key,["Up","ArrowUp"])||e.ctrlKey||e.shiftKey||e.altKey||e.metaKey?null:(e.preventDefault(),n.focusPrev.apply(null,arguments))},function(e){return!e.type.indexOf("key")&&n._k(e.keyCode,"enter",13,e.key,"Enter")||e.ctrlKey||e.shiftKey||e.altKey||e.metaKey?null:n.handleKeyEnter.apply(null,arguments)}],click:function(e){return e.target!==e.currentTarget?null:n.closeQuickNavigationModal.apply(null,arguments)}}},[a("div",{staticClass:"quick-navigation__container",class:{focus:n.focusedInput}},[a("FilterInput",{staticClass:"quick-navigation__filter",attrs:{placeholder:n.$t("filter.search-symbols",{technology:n.technology}),focusInputWhenCreated:"",focusInputWhenEmpty:"",preventBorderStyle:"",selectInputOnFocus:""},on:{input:function(e){n.focusedIndex=0},focus:function(e){n.focusedInput=!0},blur:function(e){n.focusedInput=!1}},scopedSlots:n._u([{key:"icon",fn:function(){return[a("div",{staticClass:"quick-navigation__magnifier-icon-container",class:{blue:n.userInput.length}},[a("MagnifierIcon")],1)]},proxy:!0}]),model:{value:n.userInput,callback:function(e){n.userInput=e},expression:"userInput"}}),a("div",{staticClass:"quick-navigation__match-list",class:{active:n.processedUserInput.length}},[n.noResultsWereFound?a("div",{staticClass:"no-results"},[a("p",[n._v(" No results found. ")])]):[a("div",n._b({staticClass:"quick-navigation__refs"},"div",(e={},e[n.SCROLL_LOCK_DISABLE_ATTR]=!0,e),!1),n._l(n.filteredSymbols,(function(e,t){return a("Reference",{key:e.uid,staticClass:"quick-navigation__reference",attrs:{url:e.path},nativeOn:{click:function(e){return n.closeQuickNavigationModal.apply(null,arguments)},focus:function(e){return n.focusIndex(t)}}},[a("div",{ref:"match",refInFor:!0,staticClass:"quick-navigation__symbol-match",class:{selected:t==n.focusedIndex},attrs:{role:"list"}},[a("div",{staticClass:"symbol-info"},[a("div",{staticClass:"symbol-name"},[a("TopicTypeIcon",{staticClass:"navigator-icon",attrs:{type:e.type}}),a("div",{staticClass:"symbol-title"},[a("span",{domProps:{textContent:n._s(n.formatSymbolTitle(e.title,0,e.start))}}),a("QuickNavigationHighlighter",{attrs:{text:e.substring,matcherText:n.processedUserInput}}),a("span",{domProps:{textContent:n._s(n.formatSymbolTitle(e.title,e.start+e.matchLength))}})],1)],1),a("div",{staticClass:"symbol-path"},n._l(e.parents,(function(t,i){return a("div",{key:t.title},[a("span",{staticClass:"parent-path",domProps:{textContent:n._s(t.title)}}),i!==e.parents.length-1?a("span",{staticClass:"parent-path",domProps:{textContent:n._s("/")}}):n._e()])})),0)])])])})),1),n.previewState?a("Preview",n._b({staticClass:"quick-navigation__preview",attrs:{json:n.previewJSON,state:n.previewState}},"Preview",(t={},t[n.SCROLL_LOCK_DISABLE_ATTR]=!0,t),!1)):n._e()]],2)],1)])])},dr=[],ur=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"filter",class:{focus:e.showSuggestedTags&&!e.preventBorderStyle},attrs:{role:"search",tabindex:"0","aria-labelledby":e.searchAriaLabelledBy},on:{"!blur":function(t){return e.handleBlur.apply(null,arguments)},"!focus":function(t){return e.handleFocus.apply(null,arguments)}}},[n("div",{class:["filter__wrapper",{"filter__wrapper--reversed":e.positionReversed,"filter__wrapper--no-border-style":e.preventBorderStyle}]},[n("div",{staticClass:"filter__top-wrapper"},[n("button",{staticClass:"filter__filter-button",class:{blue:e.inputIsNotEmpty},attrs:{"aria-hidden":"true",tabindex:"-1"},on:{click:e.focusInput,mousedown:function(e){e.preventDefault()}}},[e._t("icon",(function(){return[n("FilterIcon")]}))],2),n("div",{class:["filter__input-box-wrapper",{scrolling:e.isScrolling}],on:{scroll:e.handleScroll}},[e.hasSelectedTags?n("TagList",e._g(e._b({ref:"selectedTags",staticClass:"filter__selected-tags",attrs:{id:e.SelectedTagsId,input:e.input,tags:e.selectedTags,ariaLabel:e.$tc("filter.selected-tags",e.suggestedTags.length),activeTags:e.activeTags,translatableTags:e.translatableTags,areTagsRemovable:""},on:{"focus-prev":e.handleFocusPrevOnSelectedTags,"focus-next":e.focusInputFromTags,"reset-filters":e.resetFilters,"prevent-blur":function(t){return e.$emit("update:preventedBlur",!0)}}},"TagList",e.virtualKeyboardBind,!1),e.selectedTagsMultipleSelectionListeners)):e._e(),n("label",{staticClass:"filter__input-label",attrs:{id:"filter-label",for:e.FilterInputId,"data-value":e.modelValue,"aria-label":e.placeholder}},[n("input",e._g(e._b({directives:[{name:"model",rawName:"v-model",value:e.modelValue,expression:"modelValue"}],ref:"input",staticClass:"filter__input",attrs:{id:e.FilterInputId,placeholder:e.hasSelectedTags?"":e.placeholder,"aria-expanded":e.displaySuggestedTags?"true":"false",disabled:e.disabled,type:"text"},domProps:{value:e.modelValue},on:{focus:function(t){e.selectInputOnFocus&&e.selectInputAndTags()},keydown:[function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"down",40,t.key,["Down","ArrowDown"])?null:(t.preventDefault(),e.downHandler.apply(null,arguments))},function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"up",38,t.key,["Up","ArrowUp"])?null:(t.preventDefault(),e.upHandler.apply(null,arguments))},function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"left",37,t.key,["Left","ArrowLeft"])||"button"in t&&0!==t.button?null:e.leftKeyInputHandler.apply(null,arguments)},function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"right",39,t.key,["Right","ArrowRight"])||"button"in t&&2!==t.button?null:e.rightKeyInputHandler.apply(null,arguments)},function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"delete",[8,46],t.key,["Backspace","Delete","Del"])?null:e.deleteHandler.apply(null,arguments)},function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"a",void 0,t.key,void 0)?null:t.metaKey?(t.preventDefault(),t.stopPropagation(),e.selectInputAndTags.apply(null,arguments)):null},function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"a",void 0,t.key,void 0)?null:t.ctrlKey?(t.preventDefault(),e.selectInputAndTags.apply(null,arguments)):null},function(t){return t.ctrlKey||t.shiftKey||t.altKey||t.metaKey?null:e.inputKeydownHandler.apply(null,arguments)},function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")||t.ctrlKey||t.shiftKey||t.altKey||t.metaKey?null:e.enterHandler.apply(null,arguments)},function(t){return t.shiftKey?t.ctrlKey||t.altKey||t.metaKey?null:e.inputKeydownHandler.apply(null,arguments):null},function(t){return t.shiftKey&&t.metaKey?t.ctrlKey||t.altKey?null:e.inputKeydownHandler.apply(null,arguments):null},function(t){return t.metaKey?t.ctrlKey||t.shiftKey||t.altKey?null:e.assignEventValues.apply(null,arguments):null},function(t){return t.ctrlKey?t.shiftKey||t.altKey||t.metaKey?null:e.assignEventValues.apply(null,arguments):null}],input:function(t){t.target.composing||(e.modelValue=t.target.value)}}},"input",e.AXinputProperties,!1),e.inputMultipleSelectionListeners))])],1),n("div",{staticClass:"filter__delete-button-wrapper"},[e.input.length||e.displaySuggestedTags||e.hasSelectedTags?n("button",{staticClass:"filter__delete-button",attrs:{"aria-label":e.$t("filter.reset-filter")},on:{click:function(t){return e.resetFilters(!0)},keydown:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")||t.ctrlKey||t.shiftKey||t.altKey||t.metaKey?null:(t.stopPropagation(),e.resetFilters(!0))},mousedown:function(e){e.preventDefault()}}},[n("ClearRoundedIcon")],1):e._e()])]),e.displaySuggestedTags?n("TagList",e._b({ref:"suggestedTags",staticClass:"filter__suggested-tags",attrs:{id:e.SuggestedTagsId,ariaLabel:e.$tc("filter.suggested-tags",e.suggestedTags.length),input:e.input,tags:e.suggestedTags,translatableTags:e.translatableTags},on:{"click-tags":function(t){return e.selectTag(t.tagName)},"prevent-blur":function(t){return e.$emit("update:preventedBlur",!0)},"focus-next":function(t){e.positionReversed?e.focusInput():e.$emit("focus-next")},"focus-prev":function(t){e.positionReversed?e.$emit("focus-prev"):e.focusInput()}}},"TagList",e.virtualKeyboardBind,!1)):e._e()],1)])},hr=[],pr=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("SVGIcon",{staticClass:"clear-rounded-icon",attrs:{viewBox:"0 0 16 16",themeId:"clear-rounded"}},[n("title",[e._v(e._s(e.$t("icons.clear")))]),n("path",{attrs:{d:"M14.55,0l1.45,1.45-6.56,6.55,6.54,6.54-1.45,1.45-6.53-6.53L1.47,15.99,.01,14.53l6.52-6.53L0,1.47,1.45,.02l6.55,6.54L14.55,0Z","fill-rule":"evenodd"}})])},gr=[],fr=n("be08"),mr={name:"ClearRoundedIcon",components:{SVGIcon:fr["a"]}},yr=mr,vr=Object(K["a"])(yr,pr,gr,!1,null,null,null),br=vr.exports;function Tr(){if(window.getSelection)try{const{activeElement:e}=document;return e&&e.value?e.value.substring(e.selectionStart,e.selectionEnd):window.getSelection().toString()}catch(e){return""}else if(document.selection&&"Control"!==document.selection.type)return document.selection.createRange().text;return""}function Sr(e){if("number"===typeof e.selectionStart)e.selectionStart=e.selectionEnd=e.value.length;else if("undefined"!==typeof e.createTextRange){e.focus();const t=e.createTextRange();t.collapse(!1),t.select()}}function _r(e){e.selectionStart=e.selectionEnd=0}function Cr(e){return/^[\w\W\s]$/.test(e)}function kr(e){const t=e.match(/<data (?:.*?)id="copy-data"(?:.*?)>(.*)<\/data>/);try{return t?JSON.parse(t[1]):null}catch(n){return null}}function wr(e){return"string"!==typeof e&&(e=JSON.stringify(e)),`<data id="copy-data">${e}</data>`}function Ir(e,t,n,i){let a,s;return function(...r){function o(){clearTimeout(a),a=null}function l(){o(),e.apply(s,r)}if(s=this,!a||!n&&!i){if(!n)return o(),void(a=setTimeout(l,t));a=setTimeout(o,t),e.apply(s,r)}}}const xr=280,$r=100;var Or={data(){return{keyboardIsVirtual:!1,activeTags:[],initTagIndex:null,focusedTagIndex:null,metaKey:!1,shiftKey:!1,tabbing:!1,debouncedHandleDeleteTag:null}},constants:{DebounceDelay:xr,VirtualKeyboardThreshold:$r},computed:{virtualKeyboardBind:({keyboardIsVirtual:e})=>({keyboardIsVirtual:e}),allSelectedTagsAreActive:({selectedTags:e,activeTags:t})=>e.every(e=>t.includes(e))},methods:{selectRangeActiveTags(e=this.focusedTagIndex,t=this.selectedTags.length){this.activeTags=this.selectedTags.slice(e,t)},selectTag(e){this.updateSelectedTags([e]),this.clearFilterOnTagSelect&&this.setFilterInput("")},unselectActiveTags(){this.activeTags.length&&(this.deleteTags(this.activeTags),this.resetActiveTags())},async deleteHandler(e){this.activeTags.length>0&&this.setSelectedTags(this.selectedTags.filter(e=>!this.activeTags.includes(e))),this.inputIsSelected()&&this.allSelectedTagsAreActive?(e.preventDefault(),await this.resetFilters()):0===this.$refs.input.selectionEnd&&this.hasSelectedTags&&(e.preventDefault(),this.keyboardIsVirtual?this.setSelectedTags(this.selectedTags.slice(0,-1)):this.$refs.selectedTags.focusLast()),this.unselectActiveTags()},leftKeyInputHandler(e){if(this.assignEventValues(e),this.hasSelectedTags){if(this.activeTags.length&&!this.shiftKey)return e.preventDefault(),void this.$refs.selectedTags.focusTag(this.activeTags[0]);if(this.shiftKey&&0===this.$refs.input.selectionStart&&"forward"!==this.$refs.input.selectionDirection)return null===this.focusedTagIndex&&(this.focusedTagIndex=this.selectedTags.length),this.focusedTagIndex>0&&(this.focusedTagIndex-=1),this.initTagIndex=this.selectedTags.length,void this.selectTagsPressingShift();(0===this.$refs.input.selectionEnd||this.inputIsSelected())&&this.$refs.selectedTags.focusLast()}},rightKeyInputHandler(e){if(this.assignEventValues(e),this.activeTags.length&&this.shiftKey&&this.focusedTagIndex<this.selectedTags.length){if(this.initTagIndex<this.selectedTags.length)return void this.selectRangeActiveTags(this.initTagIndex,this.focusedTagIndex+1);e.preventDefault(),this.focusedTagIndex+=1,this.selectRangeActiveTags()}},async enterHandler(){this.$refs.input.blur()},inputKeydownHandler(e){const{key:t}=e;this.inputIsSelected()&&Cr(t)&&this.allSelectedTagsAreActive&&this.resetFilters(),this.multipleTagsSelectionHandler({event:e,tagName:""})},selectedTagsKeydownHandler({event:e,tagName:t}){"Enter"===e.key&&e.preventDefault(),this.multipleTagsSelectionHandler({event:e,tagName:t})},selectInputTextToTags(){const{input:e}=this.$refs;e.selectionStart===e.selectionEnd?e.setSelectionRange(0,e.selectionEnd):e.setSelectionRange(e.selectionStart,e.selectionEnd),this.focusInput()},selectTagsPressingShift(){null!==this.initTagIndex&&this.shiftKey&&!this.metaKey&&(this.initTagIndex<this.focusedTagIndex?this.selectRangeActiveTags(this.initTagIndex,this.focusedTagIndex+1):this.selectRangeActiveTags(this.focusedTagIndex,this.initTagIndex+1))},focusTagHandler({event:e={},tagName:t}){this.focusedTagIndex=this.selectedTags.indexOf(t);const n=e.relatedTarget;if(n&&n.matches("input")&&this.shiftKey&&!this.metaKey&&!this.tabbing&&0!==this.$refs.input.selectionEnd)return this.selectInputTextToTags(),void this.selectRangeActiveTags();this.selectTagsPressingShift()},focusInputFromTags(){this.focusInput(),_r(this.$refs.input)},selectToDirections(e){this.metaKey&&this.shiftKey&&("ArrowRight"===e?(this.selectRangeActiveTags(this.initTagIndex,this.selectedTags.length),this.input.length?this.$refs.input.select():this.$refs.selectedTags.focusTag(this.selectedTags[this.selectedTags.length-1])):"ArrowLeft"===e&&(this.selectRangeActiveTags(0,this.initTagIndex+1),this.input.length||this.$refs.selectedTags.focusTag(this.selectedTags[0])))},metaKeyClickSelection(e,t){this.metaKey&&e instanceof MouseEvent&&(this.activeTags.includes(t)?(this.activeTags.splice(this.activeTags.indexOf(t),1),this.activeTags.length?this.$refs.selectedTags.focusTag(this.activeTags[0]):this.focusInput()):this.activeTags.push(t))},assignEventValues(e={}){const{shiftKey:t=!1,metaKey:n=!1,ctrlKey:i=!1,key:a}=e;this.tabbing="Tab"===a,this.metaKey=n||i,this.shiftKey=t},initTag(e){null!==this.initTagIndex||this.activeTags.includes(e)||(e?(this.initTagIndex=this.selectedTags.indexOf(e),this.activeTags.push(e)):this.initTagIndex=this.selectedTags.length)},multipleTagsSelectionHandler({event:e=new KeyboardEvent("keydown",{}),tagName:t}){const{key:n=""}=e;"Enter"!==n&&(this.assignEventValues(e),!this.shiftKey&&!this.metaKey||this.tabbing?"Backspace"!==n&&this.resetActiveTags():this.initTag(t),this.selectToDirections(n))},resetActiveTags(){this.activeTags=[],this.initTagIndex=null,this.metaKey=!1,this.tabbing=!1,this.shiftKey=!1,this.focusedTagIndex=null},selectInputAndTags(){this.activeTags=[...this.selectedTags],this.input.length?(this.$refs.input.select(),this.initTagIndex=this.activeTags.length,this.focusedTagIndex=0):this.activeTags.length&&(this.initTagIndex=this.activeTags.length-1,this.$refs.selectedTags.focusTag(this.activeTags[0]))},handleSingleTagClick({event:e,tagName:t}){this.keyboardIsVirtual?(this.debouncedHandleDeleteTag||(this.debouncedHandleDeleteTag=Ir(this.handleDeleteTag,xr)),this.debouncedHandleDeleteTag({tagName:t,event:e})):(this.assignEventValues(e),this.metaKeyClickSelection(e,t),this.multipleTagsSelectionHandler({event:e,tagName:t}))},inputIsSelected(){return this.input.length&&Tr()===this.input},inputHasPartialTextSelected(){const e=Tr();return!this.inputIsSelected()&&e.length&&this.input.includes(e)},updateKeyboardType:Ir((function(e){const t=window.innerHeight-e.target.height;t>=$r&&(this.keyboardIsVirtual=!0)}),xr),setFilterInput(e){this.$emit("update:input",e)},setSelectedTags(e){this.$emit("update:selectedTags",e)},updateSelectedTags(e){this.setSelectedTags([...new Set([...this.selectedTags,...e])])},handleCopy(e){e.preventDefault();const t=[],n={tags:[],input:Tr()};if(this.activeTags.length){const e=this.activeTags;n.tags=e,t.push(e.join(" "))}return t.push(n.input),n.tags.length||n.input.length?(e.clipboardData.setData("text/html",wr(n)),e.clipboardData.setData("text/plain",t.join(" ")),n):n},handleCut(e){e.preventDefault();const{input:t,tags:n}=this.handleCopy(e);if(!t&&!n.length)return;const i=this.selectedTags.filter(e=>!n.includes(e)),a=this.input.replace(t,"");this.setSelectedTags(i),this.setFilterInput(a)},handlePaste(e){e.preventDefault();const{types:t}=e.clipboardData;let n=[],i=e.clipboardData.getData("text/plain");if(t.includes("text/html")){const t=e.clipboardData.getData("text/html"),a=kr(t);a&&({tags:n=[],input:i=""}=a)}const a=Tr();i=a.length?this.input.replace(a,i):Object(nn["f"])(this.input,i,document.activeElement.selectionStart),this.setFilterInput(i.trim()),this.allSelectedTagsAreActive?this.setSelectedTags(n):this.updateSelectedTags(n),this.resetActiveTags()},async handleDeleteTag({tagName:e,event:t={}}){const{key:n}=t;this.activeTags.length||this.deleteTags([e]),this.unselectActiveTags(),await this.$nextTick(),Sr(this.$refs.input),this.hasSelectedTags&&(await this.focusInput(),"Backspace"===n&&_r(this.$refs.input))}},mounted(){window.visualViewport&&(window.visualViewport.addEventListener("resize",this.updateKeyboardType),this.$once("hook:beforeDestroy",()=>{window.visualViewport.removeEventListener("resize",this.updateKeyboardType)}))}};const Dr=1e3;var Pr={constants:{ScrollingDebounceDelay:Dr},data(){return{isScrolling:!1,scrollRemovedAt:0}},created(){this.deleteScroll=Ir(this.deleteScroll,Dr)},methods:{deleteScroll(){this.isScrolling=!1,this.scrollRemovedAt=Date.now()},handleScroll(e){const{target:t}=e;if(0!==t.scrollTop)return t.scrollTop=0,void e.preventDefault();const n=150,i=t.offsetWidth,a=i+n;if(t.scrollWidth<a)return;const s=Date.now()-this.scrollRemovedAt;s<Dr/10||(this.isScrolling=!0,t.style.getPropertyValue("--scroll-target-height")||t.style.setProperty("--scroll-target-height",t.offsetHeight+"px"),this.deleteScroll())}}},Lr=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("SVGIcon",{staticClass:"filter-icon",attrs:{height:"14",width:"14",viewBox:"0 0 14 14",fill:"currentColor",themeId:"filter"}},[n("path",{attrs:{d:"m2 6.5h10v1h-10z"}}),n("path",{attrs:{d:"m1 3h12v1h-12z"}}),n("path",{attrs:{d:"m3 10h8v1h-8z"}})])},Ar=[],Nr={name:"FilterIcon",components:{SVGIcon:fr["a"]}},Er=Nr,jr=Object(K["a"])(Er,Lr,Ar,!1,null,null,null),Br=jr.exports,Rr=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"tags"},[n("div",{ref:"scroll-wrapper",staticClass:"scroll-wrapper",class:{scrolling:e.isScrolling},on:{scroll:e.handleScroll}},[n("ul",{ref:"tags",attrs:{id:e.id+"-tags","aria-label":e.ariaLabel,tabindex:"0",role:"listbox","aria-multiselectable":e.areTagsRemovable?"true":"false","aria-orientation":"horizontal"},on:{"!keydown":[function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"left",37,t.key,["Left","ArrowLeft"])||"button"in t&&0!==t.button?null:(t.preventDefault(),e.focusPrev.apply(null,arguments))},function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"right",39,t.key,["Right","ArrowRight"])||"button"in t&&2!==t.button?null:(t.preventDefault(),e.focusNext.apply(null,arguments))},function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"up",38,t.key,["Up","ArrowUp"])?null:(t.preventDefault(),e.focusPrev.apply(null,arguments))},function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"down",40,t.key,["Down","ArrowDown"])?null:(t.preventDefault(),e.focusNext.apply(null,arguments))},function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"a",void 0,t.key,void 0)?null:t.metaKey?(t.preventDefault(),e.$emit("select-all")):null},function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"a",void 0,t.key,void 0)?null:t.ctrlKey?(t.preventDefault(),e.$emit("select-all")):null},function(t){return t.ctrlKey||t.shiftKey||t.altKey||t.metaKey?null:e.handleKeydown.apply(null,arguments)},function(t){return t.shiftKey?t.ctrlKey||t.altKey||t.metaKey?null:e.handleKeydown.apply(null,arguments):null}],keydown:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"delete",[8,46],t.key,["Backspace","Delete","Del"])?null:(t.preventDefault(),t.target!==t.currentTarget?null:e.$emit("reset-filters"))}}},e._l(e.tags,(function(t,i){return n("Tag",{key:t.id||i,ref:"tag",refInFor:!0,attrs:{name:t.label||t,isFocused:e.focusedIndex===i,isRemovableTag:e.areTagsRemovable,filterText:e.input,isTranslatableTag:e.translatableTags.includes(t),isActiveTag:e.activeTags.includes(t),activeTags:e.activeTags,keyboardIsVirtual:e.keyboardIsVirtual},on:{focus:function(t){return e.handleFocus(t,i)},click:function(t){return e.$emit("click-tags",t)},"delete-tag":function(t){return e.$emit("delete-tag",t)},"prevent-blur":function(t){return e.$emit("prevent-blur")},"paste-content":function(t){return e.$emit("paste-tags",t)},keydown:function(t){return e.$emit("keydown",t)}}})})),1)])])},Mr=[],zr={data(){return{focusedIndex:0,externalFocusChange:!1}},methods:{focusIndex(e){e<0||(this.focusedIndex=e)},focusPrev({metaKey:e,ctrlKey:t,shiftKey:n}){(e||t)&&n||(this.externalFocusChange=!1,this.focusedIndex>0?this.focusIndex(this.focusedIndex-1):this.startingPointHook())},focusNext({metaKey:e,ctrlKey:t,shiftKey:n}){(e||t)&&n||(this.externalFocusChange=!1,this.focusedIndex<this.totalItemsToNavigate-1?this.focusIndex(this.focusedIndex+1):this.endingPointHook())},async focusFirst(){this.externalFocusChange=!1,this.focusIndex(null),await this.$nextTick(),this.focusIndex(0),this.scrollToFocus()},async focusLast(){this.externalFocusChange=!1,this.focusIndex(null),await this.$nextTick(),this.focusIndex(this.totalItemsToNavigate-1),this.scrollToFocus()},startingPointHook(){},endingPointHook(){},scrollToFocus(){}},computed:{totalItemsToNavigate:()=>0}},Kr=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("li",{staticClass:"tag",attrs:{role:"presentation"}},[n("button",{ref:"button",class:{focus:e.isActiveTag},attrs:{role:"option","aria-selected":e.ariaSelected,"aria-roledescription":"tag"},on:{focus:function(t){return e.$emit("focus",{event:t,tagName:e.name})},click:function(t){return t.preventDefault(),e.$emit("click",{event:t,tagName:e.name})},dblclick:function(t){t.preventDefault(),!e.keyboardIsVirtual&&e.deleteTag()},keydown:[function(t){return t.ctrlKey||t.shiftKey||t.altKey||t.metaKey?null:e.$emit("keydown",{event:t,tagName:e.name})},function(t){return t.shiftKey?t.ctrlKey||t.altKey||t.metaKey?null:e.$emit("keydown",{event:t,tagName:e.name}):null},function(t){return t.shiftKey&&t.metaKey?t.ctrlKey||t.altKey?null:e.$emit("keydown",{event:t,tagName:e.name}):null},function(t){return t.metaKey?t.ctrlKey||t.shiftKey||t.altKey?null:e.$emit("keydown",{event:t,tagName:e.name}):null},function(t){return t.ctrlKey?t.shiftKey||t.altKey||t.metaKey?null:e.$emit("keydown",{event:t,tagName:e.name}):null},function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"delete",[8,46],t.key,["Backspace","Delete","Del"])?null:(t.preventDefault(),e.deleteTag.apply(null,arguments))}],mousedown:function(t){return t.preventDefault(),e.focusButton.apply(null,arguments)},copy:e.handleCopy}},[e.isRemovableTag?e._e():n("span",{staticClass:"visuallyhidden"},[e._v(" "+e._s(e.$t("filter.add-tag"))+" - ")]),e.isTranslatableTag?[e._v(" "+e._s(e.$t(e.name))+" ")]:[e._v(" "+e._s(e.name)+" ")],e.isRemovableTag?n("span",{staticClass:"visuallyhidden"},[e._v(" – "+e._s(e.$t("filter.tag-select-remove"))+" ")]):e._e()],2)])},qr=[],Fr={name:"Tag",props:{name:{type:String,required:!0},isFocused:{type:Boolean,default:()=>!1},isRemovableTag:{type:Boolean,default:!1},isTranslatableTag:{type:Boolean,default:!1},isActiveTag:{type:Boolean,default:!1},activeTags:{type:Array,required:!1},keyboardIsVirtual:{type:Boolean,default:!1}},watch:{isFocused(e){e&&this.focusButton()}},mounted(){document.addEventListener("copy",this.handleCopy),document.addEventListener("cut",this.handleCut),document.addEventListener("paste",this.handlePaste),this.$once("hook:beforeDestroy",()=>{document.removeEventListener("copy",this.handleCopy),document.removeEventListener("cut",this.handleCut),document.removeEventListener("paste",this.handlePaste)})},methods:{isCurrentlyActiveElement(){return document.activeElement===this.$refs.button},handleCopy(e){if(!this.isCurrentlyActiveElement())return;e.preventDefault();let t=[];t=this.activeTags.length>0?this.activeTags:[this.name],e.clipboardData.setData("text/html",wr({tags:t})),e.clipboardData.setData("text/plain",t.join(" "))},handleCut(e){this.isCurrentlyActiveElement()&&this.isRemovableTag&&(this.handleCopy(e),this.deleteTag(e))},handlePaste(e){this.isCurrentlyActiveElement()&&this.isRemovableTag&&(e.preventDefault(),this.deleteTag(e),this.$emit("paste-content",e))},deleteTag(e){this.$emit("delete-tag",{tagName:this.name,event:e}),this.$emit("prevent-blur")},focusButton(e={}){this.keyboardIsVirtual||this.$refs.button.focus(),0===e.buttons&&this.isFocused&&this.deleteTag(e)}},computed:{ariaSelected:({isActiveTag:e,isRemovableTag:t})=>t?e?"true":"false":null}},Hr=Fr,Vr=(n("f055"),Object(K["a"])(Hr,Kr,qr,!1,null,"7e76f326",null)),Wr=Vr.exports,Ur={name:"Tags",mixins:[Pr,zr],props:{tags:{type:Array,default:()=>[]},activeTags:{type:Array,default:()=>[]},translatableTags:{type:Array,default:()=>[]},ariaLabel:{type:String,required:!1},id:{type:String,required:!1},input:{type:String,default:null},areTagsRemovable:{type:Boolean,default:!1},keyboardIsVirtual:{type:Boolean,default:!1}},components:{Tag:Wr},methods:{focusTag(e){this.focusIndex(this.tags.indexOf(e))},startingPointHook(){this.$emit("focus-prev")},handleFocus(e,t){this.focusIndex(t),this.isScrolling=!1,this.$emit("focus",e)},endingPointHook(){this.$emit("focus-next")},resetScroll(){this.$refs["scroll-wrapper"].scrollLeft=0},handleKeydown(e){const{key:t}=e,n=this.tags[this.focusedIndex];Cr(t)&&n&&this.$emit("delete-tag",{tagName:n.label||n,event:e})}},computed:{totalItemsToNavigate:({tags:e})=>e.length}},Gr=Ur,Qr=(n("9f17"),Object(K["a"])(Gr,Rr,Mr,!1,null,"1f2bd813",null)),Xr=Qr.exports;const Yr=5,Jr="filter-input",Zr="selected-tags",eo="suggested-tags",to={autocorrect:"off",autocapitalize:"off",spellcheck:"false",role:"combobox","aria-haspopup":"true","aria-autocomplete":"none","aria-owns":"suggestedTags","aria-controls":"suggestedTags"};var no,io,ao={name:"FilterInput",mixins:[Pr,Or],constants:{FilterInputId:Jr,SelectedTagsId:Zr,SuggestedTagsId:eo,AXinputProperties:to,TagLimit:Yr},components:{TagList:Xr,ClearRoundedIcon:br,FilterIcon:Br},props:{positionReversed:{type:Boolean,default:()=>!1},tags:{type:Array,default:()=>[]},selectedTags:{type:Array,default:()=>[]},preventedBlur:{type:Boolean,default:()=>!1},placeholder:{type:String,default:()=>""},disabled:{type:Boolean,default:()=>!1},value:{type:String,default:()=>""},shouldTruncateTags:{type:Boolean,default:!1},focusInputWhenCreated:{type:Boolean,default:!1},focusInputWhenEmpty:{type:Boolean,default:!1},selectInputOnFocus:{type:Boolean,default:!1},clearFilterOnTagSelect:{type:Boolean,default:!0},preventBorderStyle:{type:Boolean,default:!1},translatableTags:{type:Array,default:()=>[]}},data(){return{resetedTagsViaDeleteButton:!1,FilterInputId:Jr,SelectedTagsId:Zr,SuggestedTagsId:eo,AXinputProperties:to,showSuggestedTags:!1}},computed:{hasSuggestedTags:({suggestedTags:e})=>e.length,hasSelectedTags:({selectedTags:e})=>e.length,inputIsNotEmpty:({input:e,hasSelectedTags:t})=>e.length||t,searchAriaLabelledBy:({hasSelectedTags:e})=>e?Jr.concat(" ",Zr):Jr,modelValue:{get:({value:e})=>e,set(e){this.$emit("input",e)}},input:({value:e})=>e,suggestedTags:({tags:e,selectedTags:t,shouldTruncateTags:n})=>{const i=e.filter(e=>!t.includes(e));return n?i.slice(0,Yr):i},displaySuggestedTags:({showSuggestedTags:e,suggestedTags:t})=>e&&t.length>0,inputMultipleSelectionListeners:({resetActiveTags:e,handleCopy:t,handleCut:n,handlePaste:i})=>({click:e,copy:t,cut:n,paste:i}),selectedTagsMultipleSelectionListeners:({handleSingleTagClick:e,selectInputAndTags:t,handleDeleteTag:n,selectedTagsKeydownHandler:i,focusTagHandler:a,handlePaste:s})=>({"click-tags":e,"select-all":t,"delete-tag":n,keydown:i,focus:a,"paste-tags":s})},watch:{async selectedTags(){this.resetedTagsViaDeleteButton?this.resetedTagsViaDeleteButton=!1:this.$el.contains(document.activeElement)&&await this.focusInput(),this.displaySuggestedTags&&this.hasSuggestedTags&&this.$refs.suggestedTags.resetScroll()},suggestedTags:{immediate:!0,handler(e){this.$emit("suggested-tags",e)}},showSuggestedTags(e){this.$emit("show-suggested-tags",e)}},methods:{async focusInput(){await this.$nextTick(),this.$refs.input.focus(),!this.input&&this.resetActiveTags&&this.resetActiveTags()},async resetFilters(e=!1){if(this.setFilterInput(""),this.setSelectedTags([]),!e)return this.$emit("update:preventedBlur",!0),this.resetActiveTags&&this.resetActiveTags(),void await this.focusInput();this.resetedTagsViaDeleteButton=!0,this.showSuggestedTags=!1,this.$refs.input.blur()},focusFirstTag(e=(()=>{})){this.showSuggestedTags||(this.showSuggestedTags=!0),this.hasSuggestedTags&&this.$refs.suggestedTags?this.$refs.suggestedTags.focusFirst():e()},setFilterInput(e){this.$emit("input",e)},setSelectedTags(e){this.$emit("update:selectedTags",e)},deleteTags(e){this.setSelectedTags(this.selectedTags.filter(t=>!e.includes(t)))},async handleBlur(e){const t=e.relatedTarget;t&&t.matches&&t.matches("button, input, ul")&&this.$el.contains(t)||(await this.$nextTick(),this.resetActiveTags(),this.preventedBlur?this.$emit("update:preventedBlur",!1):(this.showSuggestedTags=!1,this.$emit("blur")))},downHandler(e){const t=()=>this.$emit("focus-next",e);this.positionReversed?t():this.focusFirstTag(t)},upHandler(e){const t=()=>this.$emit("focus-prev",e);this.positionReversed?this.focusFirstTag(t):t()},handleFocusPrevOnSelectedTags(){this.positionReversed?this.focusFirstTag(()=>this.$emit("focus-prev")):this.$emit("focus-prev")},handleFocus(){this.showSuggestedTags=!0,this.$emit("focus")}},created(){this.focusInputWhenCreated&&document.activeElement!==this.$refs.input&&(this.inputIsNotEmpty||this.focusInputWhenEmpty)&&this.focusInput()}},so=ao,ro=(n("7309"),Object(K["a"])(so,ur,hr,!1,null,"3f01a546",null)),oo=ro.exports,lo=n("c161"),co={name:"QuickNavigationHighlighter",props:{text:{type:String,required:!0},matcherText:{type:String,default:""}},render(e){const{matcherText:t,text:n}=this,i=[];let a=0;return t?([...t].forEach(t=>{const s=n.toLowerCase().indexOf(t.toLowerCase(),a);a&&i.push(e("span",n.slice(a,s)));const r=s+1;i.push(e("span",{class:"match"},n.slice(s,r))),a=r}),e("p",{class:"highlight"},i)):e("span",{class:"highlight"},n)}},uo=co,ho=(n("ca3d"),Object(K["a"])(uo,no,io,!1,null,"1c4190f0",null)),po=ho.exports,go=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("SVGIcon",{staticClass:"magnifier-icon",attrs:{viewBox:"0 0 14 14",themeId:"magnifier"}},[n("path",{attrs:{d:"M15.0013 14.0319L10.9437 9.97424C11.8165 8.88933 12.2925 7.53885 12.2929 6.14645C12.2929 2.75841 9.53449 0 6.14645 0C2.75841 0 0 2.75841 0 6.14645C0 9.53449 2.75841 12.2929 6.14645 12.2929C7.57562 12.2929 8.89486 11.7932 9.94425 10.9637L14.0019 15.0213L15.0013 14.0319ZM6.13645 11.0736C4.83315 11.071 3.58399 10.5521 2.66241 9.63048C1.74084 8.70891 1.22194 7.45974 1.2193 6.15644C1.2193 3.44801 3.41802 1.23928 6.13645 1.23928C8.85488 1.23928 11.0536 3.44801 11.0536 6.15644C11.0636 8.86488 8.85488 11.0736 6.13645 11.0736Z"}})])},fo=[],mo={name:"MagnifierIcon",components:{SVGIcon:fr["a"]}},yo=mo,vo=Object(K["a"])(yo,go,fo,!1,null,null,null),bo=vo.exports,To=n("86d8"),So=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"preview"},[e.state===e.STATE.success?n("DocumentationTopic",e._b({attrs:{enableMinimized:""}},"DocumentationTopic",e.topicProps,!1)):e.state===e.STATE.loadingSlowly?n("div",{staticClass:"loading"},e._l(e.LOADER_ROW_STYLES,(function(e){return n("div",{key:e["--index"],staticClass:"loading-row",style:e})})),0):e.state===e.STATE.error?n("div",{staticClass:"unavailable"},[n("p",[e._v("Preview unavailable")])]):e._e()],1)},_o=[];const{extractProps:Co}=Is.methods,ko="hero",wo={error:"error",loading:"loading",loadingSlowly:"loadingSlowly",success:"success"},Io={...js,state:Object(w["a"])(js.state)};var xo={name:"QuickNavigationPreview",components:{DocumentationTopic:Is},constants:{PreviewState:wo,PreviewStore:Io},data(){return{store:Io}},provide(){return{store:this.store}},props:{json:{type:Object,required:!1},state:{type:String,required:!0,validator:e=>Object.hasOwnProperty.call(wo,e)}},computed:{LOADER_ROW_STYLES:()=>[{"--index":0,width:"30%"},{"--index":1,width:"80%"},{"--index":2,width:"50%"}],STATE:()=>wo,topicProps:({json:e})=>{const t=Co(e),{sections:n=[]}=e;let{abstract:i}=t;const a=n.find(({kind:e})=>e===ko);return!i&&a&&(i=a.content),{...t,abstract:i}}}},$o=xo,Oo=(n("c1f5"),Object(K["a"])($o,So,_o,!1,null,"6fb5ba95",null)),Do=Oo.exports;class Po{constructor(e){this.map=new Map,this.maxSize=e}get size(){return this.map.size}get(e){if(!this.map.has(e))return;const t=this.map.get(e);return this.map.delete(e),this.map.set(e,t),t}has(e){return this.map.has(e)}set(e,t){if(this.map.has(e)&&this.map.delete(e),this.map.set(e,t),this.map.size>this.maxSize){const e=this.map.keys().next().value;this.map.delete(e)}}*[Symbol.iterator](){yield*this.map}}var Lo=n("f2af");const{PreviewState:Ao}=Do.constants,No="AbortError",Eo=20,jo=1e3;var Bo={name:"QuickNavigationModal",components:{FilterInput:oo,GenericModal:lo["a"],MagnifierIcon:bo,TopicTypeIcon:_e["a"],QuickNavigationHighlighter:po,Reference:To["a"],Preview:Do},mixins:[zr],created(){this.abortController=null,this.$cachedSymbolResults=new Po(Eo),this.loadingTimeout=null},data(){return{debouncedInput:"",userInput:"",focusedInput:!1,cachedSymbolResults:{},previewIsLoadingSlowly:!1,SCROLL_LOCK_DISABLE_ATTR:Lo["a"]}},props:{children:{type:Array,required:!0},showQuickNavigationModal:{type:Boolean,required:!0},technology:{type:String,required:!0}},computed:{childrenMap({children:e}){return Vs(e)},filteredSymbols:({constructFuzzyRegex:e,children:t,fuzzyMatch:n,processedUserInput:i,childrenMap:a,orderSymbolsByPriority:s})=>{const r=t.filter(e=>"groupMarker"!==e.type&&null!=e.title);if(!i)return[];const o=n({inputLength:i.length,symbols:r,processedInputRegex:new RegExp(e(i),"i"),childrenMap:a}),l=[...new Map(o.map(e=>[e.path,e])).values()];return s(l).slice(0,Eo)},isVisible:{get:({showQuickNavigationModal:e})=>e,set(e){this.$emit("update:showQuickNavigationModal",e)}},noResultsWereFound:({processedUserInput:e,totalItemsToNavigate:t})=>e.length&&!t,processedUserInput:({debouncedInput:e})=>e.replace(/\s/g,""),totalItemsToNavigate:({filteredSymbols:e})=>e.length,selectedSymbol:({filteredSymbols:e,focusedIndex:t})=>null!==t?e[t]:null,nextSymbol:({filteredSymbols:e,focusedIndex:t})=>{if(null===t)return null;let n=t+1;return n>=e.length&&(n=0),e[n]},previewJSON:({cachedSymbolResults:e,selectedSymbol:t})=>t?(e[t.uid]||{}).json:null,previewState:({cachedSymbolResults:e,previewIsLoadingSlowly:t,selectedSymbol:n})=>n&&Object.hasOwnProperty.call(e,n.uid)?e[n.uid].success?Ao.success:Ao.error:t?Ao.loadingSlowly:Ao.loading},watch:{userInput:"debounceInput",focusedIndex:"scrollIntoView",selectedSymbol:"fetchSelectedSymbolData",$route:"closeQuickNavigationModal"},methods:{closeQuickNavigationModal(){this.$emit("update:showQuickNavigationModal",!1)},constructFuzzyRegex(e){return[...e].reduce((t,n,i)=>t.concat(`[${n}]`).concat(i<e.length-1?`[^${n.toLowerCase()}]*?`:""),"")},debounceInput:Ir((function(e){this.debouncedInput=e}),250),endingPointHook(){this.focusedIndex=0},formatSymbolTitle(e,t,n){return e.slice(t,n)},fuzzyMatch({inputLength:e,symbols:t,processedInputRegex:n,childrenMap:i}){return t.map(t=>{const a=n.exec(t.title);if(!a)return!1;const s=a[0].length;return!(s>3*e)&&{uid:t.uid,title:t.title,path:t.path,parents:Qs(t.parent,i),type:t.type,inputLengthDifference:t.title.length-e,matchLength:s,matchLengthDifference:s-e,start:a.index,substring:a[0]}}).filter(Boolean)},handleKeyEnter(){!this.noResultsWereFound&&this.userInput.length&&(this.$router.push(this.filteredSymbols[this.focusedIndex].path),this.closeQuickNavigationModal())},orderSymbolsByPriority(e){return e.sort((e,t)=>e.matchLengthDifference>t.matchLengthDifference?1:e.matchLengthDifference<t.matchLengthDifference?-1:e.start>t.start?1:e.start<t.start?-1:e.inputLengthDifference>t.inputLengthDifference?1:e.inputLengthDifference<t.inputLengthDifference?-1:0)},scrollIntoView(){this.$refs.match[this.focusedIndex].scrollIntoView({block:"nearest"})},startingPointHook(){this.focusedIndex=this.totalItemsToNavigate-1},async fetchSelectedSymbolData(){if(this.loadingTimeout=setTimeout(()=>{this.previewState===Ao.loading&&(this.previewIsLoadingSlowly=!0)},jo),!this.selectedSymbol||this.$cachedSymbolResults.has(this.selectedSymbol.uid))return clearTimeout(this.loadingTimeout),void(this.previewIsLoadingSlowly=!1);const e=async e=>{if(e&&!this.$cachedSymbolResults.has(e.uid))try{const t=await Object(w["b"])(e.path,{signal:this.abortController.signal});this.$cachedSymbolResults.set(e.uid,{success:!0,json:t})}catch(t){t.name!==No&&this.$cachedSymbolResults.set(e.uid,{success:!1})}finally{this.cachedSymbolResults=Object.freeze(Object.fromEntries(this.$cachedSymbolResults))}};this.abortController&&this.abortController.abort(),this.abortController=new AbortController,await Promise.all([e(this.selectedSymbol).finally(()=>{clearTimeout(this.loadingTimeout),this.previewIsLoadingSlowly=!1}),e(this.nextSymbol)])}}},Ro=Bo,Mo=(n("dfa5"),Object(K["a"])(Ro,cr,dr,!1,null,"71686791",null)),zo=Mo.exports,Ko=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"adjustable-sidebar-width",class:{dragging:e.isDragging,"sidebar-hidden":e.hiddenOnLarge}},[n("div",{ref:"sidebar",staticClass:"sidebar"},[n("div",{ref:"aside",staticClass:"aside",class:e.asideClasses,style:e.asideStyles,attrs:{"aria-hidden":e.hiddenOnLarge?"true":null},on:{transitionstart:function(t){return t.target!==t.currentTarget?null:e.trackTransitionStart.apply(null,arguments)},transitionend:function(t){return t.target!==t.currentTarget?null:e.trackTransitionEnd.apply(null,arguments)}}},[e._t("aside",null,{animationClass:"aside-animated-child",scrollLockID:e.scrollLockID,breakpoint:e.breakpoint})],2),e.fixedWidth?e._e():n("div",{staticClass:"resize-handle",on:{mousedown:function(t){return t.preventDefault(),e.startDrag.apply(null,arguments)},touchstart:function(t){return t.preventDefault(),e.startDrag.apply(null,arguments)}}})]),n("div",{ref:"content",staticClass:"content"},[e._t("default")],2),n("BreakpointEmitter",{attrs:{scope:e.BreakpointScopes.nav},on:{change:function(t){e.breakpoint=t}}})],1)},qo=[],Fo=n("5d2d"),Ho=n("a97e"),Vo=n("63b8"),Wo=n("c8e2"),Uo=n("95da"),Go=n("942d");const Qo="sidebar",Xo=1921,Yo=543,Jo=400,Zo={touch:{move:"touchmove",end:"touchend"},mouse:{move:"mousemove",end:"mouseup"}},el=(e,t=window.innerWidth)=>{const n=Math.min(t,Xo);return Math.floor(Math.min(n*(e/100),n))},tl={medium:30,large:20},nl={medium:50,large:50},il="sidebar-scroll-lock";var al={name:"AdjustableSidebarWidth",constants:{SCROLL_LOCK_ID:il},components:{BreakpointEmitter:Ho["a"]},inject:["store"],props:{shownOnMobile:{type:Boolean,default:!1},hiddenOnLarge:{type:Boolean,default:!1},fixedWidth:{type:Number,default:null}},data(){const e=window.innerWidth,t=window.innerHeight,n=Vo["b"].large,i=el(tl[n]),a=el(nl[n]),s=e>=Xo?Yo:Jo,r=Fo["c"].get(Qo,s);return{isDragging:!1,width:this.fixedWidth||Math.min(Math.max(r,i),a),isTouch:!1,windowWidth:e,windowHeight:t,breakpoint:n,noTransition:!1,isTransitioning:!1,isOpeningOnLarge:!1,focusTrapInstance:null,mobileTopOffset:0,topOffset:0}},computed:{minWidthPercent:({breakpoint:e})=>tl[e]||0,maxWidthPercent:({breakpoint:e})=>nl[e]||100,maxWidth:({maxWidthPercent:e,windowWidth:t,fixedWidth:n})=>Math.max(n,el(e,t)),minWidth:({minWidthPercent:e,windowWidth:t,fixedWidth:n})=>Math.min(n||t,el(e,t)),widthInPx:({width:e})=>e+"px",hiddenOnLargeThreshold:({minWidth:e})=>e/2,events:({isTouch:e})=>e?Zo.touch:Zo.mouse,asideStyles:({widthInPx:e,mobileTopOffset:t,topOffset:n,windowHeight:i})=>({width:e,"--top-offset":n?n+"px":null,"--top-offset-mobile":t+"px","--app-height":i+"px"}),asideClasses:({isDragging:e,shownOnMobile:t,noTransition:n,isTransitioning:i,hiddenOnLarge:a,mobileTopOffset:s,isOpeningOnLarge:r})=>({dragging:e,"show-on-mobile":t,"hide-on-large":a,"is-opening-on-large":r,"no-transition":n,"sidebar-transitioning":i,"has-mobile-top-offset":s}),scrollLockID:()=>il,BreakpointScopes:()=>Vo["c"]},async mounted(){window.addEventListener("keydown",this.onEscapeKeydown),window.addEventListener("resize",this.storeWindowSize,{passive:!0}),window.addEventListener("orientationchange",this.storeWindowSize,{passive:!0}),this.storeTopOffset(),0===this.topOffset&&0===window.scrollY||window.addEventListener("scroll",this.storeTopOffset,{passive:!0}),this.$once("hook:beforeDestroy",()=>{window.removeEventListener("keydown",this.onEscapeKeydown),window.removeEventListener("resize",this.storeWindowSize),window.removeEventListener("orientationchange",this.storeWindowSize),window.removeEventListener("scroll",this.storeTopOffset),this.shownOnMobile&&this.toggleScrollLock(!1),this.focusTrapInstance&&this.focusTrapInstance.destroy()}),await this.$nextTick(),this.focusTrapInstance=new Wo["a"](this.$refs.aside)},watch:{$route:"closeMobileSidebar",width:{immediate:!0,handler:Ne((function(e){this.emitEventChange(e)}),150)},windowWidth:"getWidthInCheck",async breakpoint(e){this.getWidthInCheck(),e===Vo["b"].large&&this.closeMobileSidebar(),this.noTransition=!0,await Object(Ee["b"])(5),this.noTransition=!1},shownOnMobile:"handleExternalOpen",async isTransitioning(e){e?(await Object(Ee["a"])(1e3),this.isTransitioning=!1):this.updateContentWidthInStore()},hiddenOnLarge(){this.isTransitioning=!0}},methods:{getWidthInCheck:Ir((function(){this.width>this.maxWidth?this.width=this.maxWidth:this.width<this.minWidth&&(this.width=this.minWidth)}),50),onEscapeKeydown({key:e}){"Escape"===e&&this.closeMobileSidebar()},storeWindowSize:Ne((async function(){await this.$nextTick(),this.windowWidth=window.innerWidth,this.windowHeight=window.innerHeight,this.updateContentWidthInStore()}),100),closeMobileSidebar(){this.shownOnMobile&&this.$emit("update:shownOnMobile",!1)},startDrag({type:e}){this.isTouch="touchstart"===e,this.isDragging||(this.isDragging=!0,document.addEventListener(this.events.move,this.handleDrag,{passive:this.isTouch}),document.addEventListener(this.events.end,this.stopDrag))},handleDrag(e){if(this.isTouch||e.preventDefault(),!this.isDragging)return;const{sidebar:t}=this.$refs,n=this.isTouch?e.touches[0].clientX:e.clientX;let i=n+window.scrollX-t.offsetLeft;i>this.maxWidth&&(i=this.maxWidth),this.hiddenOnLarge&&i>=this.hiddenOnLargeThreshold&&(this.$emit("update:hiddenOnLarge",!1),this.isOpeningOnLarge=!0),this.width=Math.max(i,this.minWidth),i<=this.hiddenOnLargeThreshold&&this.$emit("update:hiddenOnLarge",!0)},stopDrag(e){e.preventDefault(),this.isDragging&&(this.isDragging=!1,Fo["c"].set(Qo,this.width),document.removeEventListener(this.events.move,this.handleDrag),document.removeEventListener(this.events.end,this.stopDrag),this.emitEventChange(this.width))},emitEventChange(e){this.$emit("width-change",e),this.updateContentWidthInStore()},getTopOffset(){const e=document.getElementById(Go["e"]);if(!e)return 0;const{y:t}=e.getBoundingClientRect();return Math.max(t,0)},handleExternalOpen(e){e&&(this.mobileTopOffset=this.getTopOffset()),this.toggleScrollLock(e)},async updateContentWidthInStore(){await this.$nextTick(),this.store.setContentWidth(this.$refs.content.offsetWidth)},async toggleScrollLock(e){const t=document.getElementById(this.scrollLockID);e?(await this.$nextTick(),Lo["b"].lockScroll(t),this.focusTrapInstance.start(),Uo["a"].hide(this.$refs.aside)):(Lo["b"].unlockScroll(t),this.focusTrapInstance.stop(),Uo["a"].show(this.$refs.aside))},storeTopOffset:Ne((function(){this.topOffset=this.getTopOffset()}),60),async trackTransitionStart({propertyName:e}){"width"!==e&&"transform"!==e||(this.isTransitioning=!0)},trackTransitionEnd({propertyName:e}){"width"!==e&&"transform"!==e||(this.isTransitioning=!1,this.isOpeningOnLarge=!1)}}},sl=al,rl=(n("cb1f"),Object(K["a"])(sl,Ko,qo,!1,null,"f3c6416c",null)),ol=rl.exports,ll=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("nav",{staticClass:"navigator",attrs:{"aria-labelledby":e.INDEX_ROOT_KEY}},[e.isFetching?n("LoadingNavigatorCard",e._b({on:{close:function(t){return e.$emit("close")}}},"LoadingNavigatorCard",e.technologyProps,!1)):n("NavigatorCard",e._b({attrs:{type:e.type,children:e.flatChildren,"active-path":e.activePath,scrollLockID:e.scrollLockID,"error-fetching":e.errorFetching,"render-filter-on-top":e.renderFilterOnTop,"api-changes":e.apiChanges,"allow-hiding":e.allowHiding,"navigator-references":e.navigatorReferences},on:{close:function(t){return e.$emit("close")}},scopedSlots:e._u([{key:"filter",fn:function(){return[e._t("filter")]},proxy:!0}],null,!0)},"NavigatorCard",e.technologyProps,!1)),n("div",{staticClass:"visuallyhidden",attrs:{"aria-live":"polite"}},[e._v(" "+e._s(e.$t("navigator.navigator-is",{state:e.isFetching?e.$t("navigator.state.loading"):e.$t("navigator.state.ready")}))+" ")])],1)},cl=[],dl=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("BaseNavigatorCard",e._b({class:{"filter-on-top":e.renderFilterOnTop},on:{close:function(t){return e.$emit("close")},"head-click-alt":e.toggleAllNodes},scopedSlots:e._u([{key:"body",fn:function(t){var i=t.className;return[e._t("post-head"),n("div",{class:i,on:{"!keydown":[function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"up",38,t.key,["Up","ArrowUp"])?null:t.altKey?(t.preventDefault(),e.focusFirst.apply(null,arguments)):null},function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"down",40,t.key,["Down","ArrowDown"])?null:t.altKey?(t.preventDefault(),e.focusLast.apply(null,arguments)):null},function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"up",38,t.key,["Up","ArrowUp"])||t.ctrlKey||t.shiftKey||t.altKey||t.metaKey?null:(t.preventDefault(),e.focusPrev.apply(null,arguments))},function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"down",40,t.key,["Down","ArrowDown"])||t.ctrlKey||t.shiftKey||t.altKey||t.metaKey?null:(t.preventDefault(),e.focusNext.apply(null,arguments))}]}},[n("DynamicScroller",{directives:[{name:"show",rawName:"v-show",value:e.hasNodes,expression:"hasNodes"}],ref:"scroller",staticClass:"scroller",attrs:{id:e.scrollLockID,"aria-label":e.$t("navigator.title"),items:e.nodesToRender,"min-item-size":e.itemSize,"emit-update":"","key-field":"uid"},on:{update:e.handleScrollerUpdate,"!keydown":[function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"up",38,t.key,["Up","ArrowUp"])?null:t.altKey?(t.preventDefault(),e.focusFirst.apply(null,arguments)):null},function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"down",40,t.key,["Down","ArrowDown"])?null:t.altKey?(t.preventDefault(),e.focusLast.apply(null,arguments)):null},function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"up",38,t.key,["Up","ArrowUp"])||t.ctrlKey||t.shiftKey||t.altKey||t.metaKey?null:(t.preventDefault(),e.focusPrev.apply(null,arguments))},function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"down",40,t.key,["Down","ArrowDown"])||t.ctrlKey||t.shiftKey||t.altKey||t.metaKey?null:(t.preventDefault(),e.focusNext.apply(null,arguments))}]},nativeOn:{focusin:function(t){return e.handleFocusIn.apply(null,arguments)},focusout:function(t){return e.handleFocusOut.apply(null,arguments)}},scopedSlots:e._u([{key:"default",fn:function(t){var i=t.item,a=t.active,s=t.index;return[n("DynamicScrollerItem",e._b({ref:"dynamicScroller_"+i.uid},"DynamicScrollerItem",{active:a,item:i,dataIndex:s},!1),[n("NavigatorCardItem",{attrs:{item:i,isRendered:a,"filter-pattern":e.filterPattern,"is-active":i.uid===e.activeUID,"is-bold":e.activePathMap[i.uid],expanded:e.openNodes[i.uid],"api-change":e.apiChangesObject[i.path],isFocused:e.focusedIndex===s,enableFocus:!e.externalFocusChange,"navigator-references":e.navigatorReferences},on:{toggle:e.toggle,"toggle-full":e.toggleFullTree,"toggle-siblings":e.toggleSiblings,navigate:e.handleNavigationChange,"focus-parent":e.focusNodeParent}})],1)]}}],null,!0)}),n("div",{staticClass:"visuallyhidden",attrs:{"aria-live":"polite"}},[e._v(" "+e._s(e.politeAriaLive)+" ")]),n("div",{staticClass:"no-items-wrapper",attrs:{"aria-live":"assertive"}},[n("p",{staticClass:"no-items"},[e._v(" "+e._s(e.$t(e.assertiveAriaLive))+" ")])])],1),e.errorFetching?e._e():n("div",{staticClass:"filter-wrapper"},[n("div",{staticClass:"navigator-filter"},[n("div",{staticClass:"input-wrapper"},[n("FilterInput",{staticClass:"filter-component",attrs:{tags:e.availableTags,translatableTags:e.translatableTags,"selected-tags":e.selectedTagsModelValue,placeholder:e.$t("filter.title"),"should-keep-open-on-blur":!1,"position-reversed":!e.renderFilterOnTop,"clear-filter-on-tag-select":!1},on:{"update:selectedTags":function(t){e.selectedTagsModelValue=t},"update:selected-tags":function(t){e.selectedTagsModelValue=t},clear:e.clearFilters},model:{value:e.filter,callback:function(t){e.filter=t},expression:"filter"}})],1),e._t("filter")],2)])]}}],null,!0)},"BaseNavigatorCard",{technology:e.technology,isTechnologyBeta:e.isTechnologyBeta,technologyPath:e.technologyPath},!1))},ul=[],hl=n("e508");function pl(e){const t=Object(nn["g"])(Object(nn["d"])(e));return new RegExp(t,"ig")}var gl,fl,ml=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("BaseNavigatorCardItem",{staticClass:"navigator-card-item",class:{expanded:e.expanded,active:e.isActive,"is-group":e.isGroupMarker},style:{"--nesting-index":e.item.depth},attrs:{"data-nesting-index":e.item.depth,id:"container-"+e.item.uid,"aria-hidden":e.isRendered?null:"true",hideNavigatorIcon:e.isGroupMarker},nativeOn:{keydown:[function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"left",37,t.key,["Left","ArrowLeft"])||"button"in t&&0!==t.button?null:(t.preventDefault(),e.handleLeftKeydown.apply(null,arguments))},function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"right",39,t.key,["Right","ArrowRight"])||"button"in t&&2!==t.button||t.ctrlKey||t.shiftKey||t.altKey||t.metaKey?null:(t.preventDefault(),e.handleRightKeydown.apply(null,arguments))},function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:(t.preventDefault(),e.clickReference.apply(null,arguments))},function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"right",39,t.key,["Right","ArrowRight"])?null:t.altKey?"button"in t&&2!==t.button?null:(t.preventDefault(),e.toggleEntireTree.apply(null,arguments)):null}]},scopedSlots:e._u([{key:"depth-spacer",fn:function(){return[n("span",{attrs:{hidden:"",id:e.usageLabel}},[e._v(" "+e._s(e.$t("filter.navigate"))+" ")]),e.isParent?n("button",{staticClass:"tree-toggle",attrs:{tabindex:"-1","aria-labelledby":e.item.uid,"aria-expanded":e.expanded?"true":"false","aria-describedby":e.ariaDescribedBy},on:{click:[function(t){return t.ctrlKey||t.shiftKey||t.altKey||t.metaKey?null:(t.preventDefault(),e.toggleTree.apply(null,arguments))},function(t){return t.altKey?(t.preventDefault(),e.toggleEntireTree.apply(null,arguments)):null},function(t){return t.metaKey?(t.preventDefault(),e.toggleSiblings.apply(null,arguments)):null}]}},[n("InlineChevronRightIcon",{staticClass:"icon-inline chevron",class:{rotate:e.expanded,animating:e.idState.isOpening}})],1):e._e()]},proxy:!0},{key:"navigator-icon",fn:function(t){var i,a=t.className;return[e.apiChange?n("span",{class:[(i={},i["changed changed-"+e.apiChange]=e.apiChange,i),a]}):n("TopicTypeIcon",{key:e.item.uid,class:a,attrs:{type:e.item.type,"image-override":e.item.icon?e.navigatorReferences[e.item.icon]:null,shouldCalculateOptimalWidth:!1}})]}},{key:"title-container",fn:function(){return[e.isParent?n("span",{attrs:{hidden:"",id:e.parentLabel}},[e._v(e._s(e.$tc("filter.parent-label",e.item.childUIDs.length,{"number-siblings":e.item.index+1,"total-siblings":e.item.siblingsCount,"parent-siblings":e.item.parent,"number-parent":e.item.childUIDs.length})))]):e._e(),e.isParent?e._e():n("span",{attrs:{id:e.siblingsLabel,hidden:""}},[e._v(" "+e._s(e.$t("filter.siblings-label",{"number-siblings":e.item.index+1,"total-siblings":e.item.siblingsCount,"parent-siblings":e.item.parent}))+" ")]),n(e.refComponent,{ref:"reference",tag:"component",staticClass:"leaf-link",class:{bolded:e.isBold},attrs:{id:e.item.uid,url:e.isGroupMarker?null:e.item.path||"",tabindex:e.isFocused?"0":"-1","aria-describedby":e.ariaDescribedBy+" "+e.usageLabel},nativeOn:{click:[function(t){return t.ctrlKey||t.shiftKey||t.altKey||t.metaKey?null:e.handleClick.apply(null,arguments)},function(t){return t.altKey?(t.preventDefault(),e.toggleEntireTree.apply(null,arguments)):null}]}},[n("HighlightMatches",{attrs:{text:e.item.title,matcher:e.filterPattern}})],1),e.isDeprecated?n("Badge",{attrs:{variant:"deprecated"}}):e.isBeta?n("Badge",{attrs:{variant:"beta"}}):e._e()]},proxy:!0}])})},yl=[],vl=n("34b0"),bl=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"navigator-card-item"},[n("div",{staticClass:"head-wrapper"},[n("div",{staticClass:"depth-spacer"},[e._t("depth-spacer")],2),e.hideNavigatorIcon?e._e():n("div",{staticClass:"navigator-icon-wrapper"},[e._t("navigator-icon",null,{className:"navigator-icon"})],2),n("div",{staticClass:"title-container"},[e._t("title-container")],2)])])},Tl=[],Sl={name:"BaseNavigatorCardItem",props:{hideNavigatorIcon:{type:Boolean,default:()=>!1}}},_l=Sl,Cl=(n("b39c"),Object(K["a"])(_l,bl,Tl,!1,null,"0b9fe514",null)),kl=Cl.exports,wl={name:"HighlightMatch",props:{text:{type:String,required:!0},matcher:{type:RegExp,default:void 0}},render(e){const{matcher:t,text:n}=this;if(!t)return e("p",{class:"highlight"},n);const i=[];let a=0,s=null;const r=new RegExp(t,"gi");while(null!==(s=r.exec(n))){const t=s[0].length,r=s.index+t,o=n.slice(a,s.index);o&&i.push(e("span",o));const l=n.slice(s.index,r);l&&i.push(e("span",{class:"match"},l)),a=r}const o=n.slice(a,n.length);return o&&i.push(e("span",o)),e("p",{class:"highlight"},i)}},Il=wl,xl=(n("b831"),Object(K["a"])(Il,gl,fl,!1,null,"d75876e2",null)),$l=xl.exports,Ol={name:"NavigatorCardItem",mixins:[Object(hl["c"])({idProp:e=>e.item.uid})],components:{BaseNavigatorCardItem:kl,HighlightMatches:$l,TopicTypeIcon:_e["a"],InlineChevronRightIcon:vl["a"],Reference:To["a"],Badge:ei["a"]},props:{isRendered:{type:Boolean,default:!1},item:{type:Object,required:!0},expanded:{type:Boolean,default:!1},filterPattern:{type:RegExp,default:void 0},isActive:{type:Boolean,default:!1},isBold:{type:Boolean,default:!1},apiChange:{type:String,default:null,validator:e=>It["d"].includes(e)},isFocused:{type:Boolean,default:()=>!1},enableFocus:{type:Boolean,default:!0},navigatorReferences:{type:Object,default:()=>({})}},idState(){return{isOpening:!1}},computed:{isGroupMarker:({item:{type:e}})=>e===Ce["b"].groupMarker,isParent:({item:e,isGroupMarker:t})=>!!e.childUIDs.length&&!t,parentLabel:({item:e})=>"label-parent-"+e.uid,siblingsLabel:({item:e})=>"label-"+e.uid,usageLabel:({item:e})=>"usage-"+e.uid,ariaDescribedBy:({isParent:e,parentLabel:t,siblingsLabel:n})=>e?""+t:""+n,isBeta:({item:{beta:e}})=>!!e,isDeprecated:({item:{deprecated:e}})=>!!e,refComponent:({isGroupMarker:e})=>e?"h3":To["a"]},methods:{toggleTree(){this.idState.isOpening=!0,this.$emit("toggle",this.item)},toggleEntireTree(){this.idState.isOpening=!0,this.$emit("toggle-full",this.item)},toggleSiblings(){this.idState.isOpening=!0,this.$emit("toggle-siblings",this.item)},handleLeftKeydown(){this.expanded?this.toggleTree():this.$emit("focus-parent",this.item)},handleRightKeydown(){!this.expanded&&this.isParent&&this.toggleTree()},clickReference(){(this.$refs.reference.$el||this.$refs.reference).click()},focusReference(){(this.$refs.reference.$el||this.$refs.reference).focus()},handleClick(){this.isGroupMarker||this.$emit("navigate",this.item.uid)}},watch:{async isFocused(e){await Object(Ee["b"])(8),e&&this.isRendered&&this.enableFocus&&this.focusReference()},async expanded(){await Object(Ee["b"])(9),this.idState.isOpening=!1}}},Dl=Ol,Pl=(n("6ca9"),Object(K["a"])(Dl,ml,yl,!1,null,"0c96ff75",null)),Ll=Pl.exports,Al=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"navigator-card"},[n("div",{staticClass:"navigator-card-full-height"},[n("div",{staticClass:"navigator-card-inner"},[n("div",{staticClass:"head-wrapper"},[n("div",{staticClass:"head-inner"},[n("button",{staticClass:"close-card",class:{"hide-on-large":!e.allowHiding},attrs:{id:e.SIDEBAR_HIDE_BUTTON_ID,"aria-label":e.$t("navigator.close-navigator")},on:{click:e.handleHideClick}},[n("SidenavIcon",{staticClass:"icon-inline close-icon"})],1),n("Reference",{staticClass:"navigator-head",attrs:{id:e.INDEX_ROOT_KEY,url:e.technologyPath},nativeOn:{click:function(t){return t.altKey?(t.preventDefault(),e.$emit("head-click-alt")):null}}},[n("h2",{staticClass:"card-link"},[e._v(" "+e._s(e.technology)+" ")]),e.isTechnologyBeta?n("Badge",{attrs:{variant:"beta"}}):e._e()],1)],1)]),e._t("body",null,{className:"card-body"})],2)])])},Nl=[],El=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("SVGIcon",{staticClass:"sidenav-icon",attrs:{viewBox:"0 0 14 14",height:"14",themeId:"sidenav"}},[n("path",{attrs:{d:"M6.533 1.867h-6.533v10.267h14v-10.267zM0.933 11.2v-8.4h4.667v8.4zM13.067 11.2h-6.533v-8.4h6.533z"}}),n("path",{attrs:{d:"M1.867 5.133h2.8v0.933h-2.8z"}}),n("path",{attrs:{d:"M1.867 7.933h2.8v0.933h-2.8z"}})])},jl=[],Bl={name:"SidenavIcon",components:{SVGIcon:fr["a"]}},Rl=Bl,Ml=Object(K["a"])(Rl,El,jl,!1,null,null,null),zl=Ml.exports,Kl={name:"BaseNavigatorCard",components:{SidenavIcon:zl,Reference:To["a"],Badge:ei["a"]},props:{allowHiding:{type:Boolean,default:!0},technologyPath:{type:String,default:""},technology:{type:String,required:!0},isTechnologyBeta:{type:Boolean,default:!1}},data(){return{SIDEBAR_HIDE_BUTTON_ID:Fs,INDEX_ROOT_KEY:Ks}},methods:{async handleHideClick(){this.$emit("close"),await this.$nextTick();const e=document.getElementById(Go["d"]);e&&e.focus()}}},ql=Kl,Fl=(n("b32a"),Object(K["a"])(ql,Al,Nl,!1,null,"24789ed0",null)),Hl=Fl.exports;const Vl=e=>e[e.length-1],Wl=(e,t)=>JSON.stringify(e)===JSON.stringify(t),Ul="navigator.state",Gl={sampleCode:"sampleCode",tutorials:"tutorials",articles:"articles"},Ql={[Gl.sampleCode]:"Sample Code",[Gl.tutorials]:"Tutorials",[Gl.articles]:"Articles"},Xl=Object.fromEntries(Object.entries(Ql).map(([e,t])=>[t,e])),Yl={[Ce["b"].article]:Gl.articles,[Ce["b"].learn]:Gl.tutorials,[Ce["b"].overview]:Gl.tutorials,[Ce["b"].resources]:Gl.tutorials,[Ce["b"].sampleCode]:Gl.sampleCode,[Ce["b"].section]:Gl.tutorials,[Ce["b"].tutorial]:Gl.tutorials,[Ce["b"].project]:Gl.tutorials},Jl="navigator.no-results",Zl="navigator.no-children",ec="navigator.error-fetching",tc="navigator.items-found",nc="navigator.tags.hide-deprecated";var ic={name:"NavigatorCard",constants:{STORAGE_KEY:Ul,FILTER_TAGS:Gl,FILTER_TAGS_TO_LABELS:Ql,FILTER_LABELS_TO_TAGS:Xl,TOPIC_TYPE_TO_TAG:Yl,ERROR_FETCHING:ec,ITEMS_FOUND:tc,HIDE_DEPRECATED:nc},components:{FilterInput:oo,NavigatorCardItem:Ll,DynamicScroller:hl["a"],DynamicScrollerItem:hl["b"],BaseNavigatorCard:Hl},props:{...Hl.props,children:{type:Array,required:!0},activePath:{type:Array,required:!0},type:{type:String,required:!0},scrollLockID:{type:String,default:""},errorFetching:{type:Boolean,default:!1},apiChanges:{type:Object,default:null},isTechnologyBeta:{type:Boolean,default:!1},navigatorReferences:{type:Object,default:()=>{}},renderFilterOnTop:{type:Boolean,default:!1},hideAvailableTags:{type:Boolean,default:!1}},mixins:[zr],data(){return{filter:"",debouncedFilter:"",selectedTags:[],openNodes:Object.freeze({}),nodesToRender:Object.freeze([]),activeUID:null,lastFocusTarget:null,allNodesToggled:!1,translatableTags:[nc]}},computed:{politeAriaLive(){const{hasNodes:e,nodesToRender:t}=this;return e?this.$tc(tc,t.length,{number:t.length}):""},assertiveAriaLive:({hasNodes:e,hasFilter:t,errorFetching:n})=>e?"":t?Jl:n?ec:Zl,availableTags({selectedTags:e,renderableChildNodesMap:t,apiChangesObject:n,hideAvailableTags:i}){if(i||e.length)return[];const a=new Set(Object.values(n)),s=new Set(Object.values(Ql)),r=new Set([nc]);a.size&&r.delete(nc);const o={type:[],changes:[],other:[]};for(const l in t){if(!Object.hasOwnProperty.call(t,l))continue;if(!s.size&&!a.size&&!r.size)break;const{type:e,path:i,deprecated:c}=t[l],d=Ql[Yl[e]],u=n[i];s.has(d)&&(o.type.push(d),s.delete(d)),u&&a.has(u)&&(o.changes.push(this.$t(It["b"][u])),a.delete(u)),c&&r.has(nc)&&(o.other.push(nc),r.delete(nc))}return o.type.concat(o.changes,o.other)},selectedTagsModelValue:{get(){return this.selectedTags.map(e=>Ql[e]||this.$t(It["b"][e])||e)},set(e){(this.selectedTags.length||e.length)&&(this.selectedTags=e.map(e=>Xl[e]||It["a"][e]||e))}},filterPattern:({debouncedFilter:e})=>e?new RegExp(pl(e),"i"):null,itemSize:()=>qs,childrenMap({children:e}){return Vs(e)},activePathChildren({activeUID:e,childrenMap:t}){return e&&t[e]?Qs(e,t):[]},activePathMap:({activePathChildren:e})=>Object.fromEntries(e.map(({uid:e})=>[e,!0])),activeIndex:({activeUID:e,nodesToRender:t})=>t.findIndex(t=>t.uid===e),filteredChildren({hasFilter:e,children:t,filterPattern:n,selectedTags:i,apiChanges:a}){if(!e)return[];const s=new Set(i);return t.filter(({title:e,path:t,type:i,deprecated:r,deprecatedChildrenCount:o,childUIDs:l})=>{const c=r||o===l.length,d=!n||n.test(e);let u=!0;s.size&&(u=s.has(Yl[i]),a&&!u&&(u=s.has(a[t])),!c&&s.has(nc)&&(u=!0));const h=!a||!!a[t];return d&&u&&h})},renderableChildNodesMap({hasFilter:e,childrenMap:t,deprecatedHidden:n,filteredChildren:i,removeDeprecated:a}){if(!e)return t;const s=i.length-1,r=new Set([]);for(let o=s;o>=0;o-=1){const e=i[o],s=t[e.groupMarkerUID];if(s&&r.add(s),r.has(e))continue;if(r.has(t[e.parent])&&e.type!==Ce["b"].groupMarker){r.add(e);continue}let l=[];e.childUIDs.length&&(l=a(Us(e.uid,t),n)),l.concat(Qs(e.uid,t)).forEach(e=>r.add(e))}return Vs([...r])},nodeChangeDeps:({filteredChildren:e,activePathChildren:t,debouncedFilter:n,selectedTags:i})=>[e,t,n,i],hasFilter({debouncedFilter:e,selectedTags:t,apiChanges:n}){return Boolean(e.length||t.length||n)},deprecatedHidden:({selectedTags:e})=>e[0]===nc,apiChangesObject(){return this.apiChanges||{}},hasNodes:({nodesToRender:e})=>!!e.length,totalItemsToNavigate:({nodesToRender:e})=>e.length,lastActivePathItem:({activePath:e})=>Vl(e)},created(){this.restorePersistedState()},watch:{filter:"debounceInput",nodeChangeDeps:"trackOpenNodes",activePath:"handleActivePathChange",apiChanges(e){e||(this.selectedTags=this.selectedTags.filter(e=>!this.$t(It["b"][e])))},async activeUID(e,t){await this.$nextTick();const n=this.$refs["dynamicScroller_"+t];n&&n.updateSize&&n.updateSize()}},methods:{setUnlessEqual(e,t){Wl(t,this[e])||(this[e]=Object.freeze(t))},toggleAllNodes(){const e=this.children.filter(e=>e.parent===Ks&&e.type!==Ce["b"].groupMarker&&e.childUIDs.length);this.allNodesToggled=!this.allNodesToggled,this.allNodesToggled&&(this.openNodes={},this.generateNodesToRender()),e.forEach(e=>{this.toggleFullTree(e)})},clearFilters(){this.filter="",this.debouncedFilter="",this.selectedTags=[]},scrollToFocus(){this.$refs.scroller.scrollToItem(this.focusedIndex)},debounceInput:Ir((function(e){this.debouncedFilter=e,this.lastFocusTarget=null}),200),trackOpenNodes([e,t,n,i],[,a=[],s="",r=[]]=[]){if(n!==s&&!s&&this.getFromStorage("filter")||!Wl(i,r)&&!r.length&&this.getFromStorage("selectedTags",[]).length)return;const o=!Wl(a,t),{childrenMap:l}=this;let c=t;if(!(this.deprecatedHidden&&!this.debouncedFilter.length||o&&this.hasFilter)&&this.hasFilter){const t=new Set,n=e.length-1;for(let i=n;i>=0;i-=1){const n=e[i];t.has(l[n.parent])||t.has(n)||Qs(n.uid,l).slice(0,-1).forEach(e=>t.add(e))}c=[...t]}const d=o?{...this.openNodes}:{},u=c.reduce((e,t)=>(e[t.uid]=!0,e),d);this.setUnlessEqual("openNodes",u),this.generateNodesToRender(),this.updateFocusIndexExternally()},toggle(e){const t=this.openNodes[e.uid];let n=[],i=[];if(t){const t=Object(w["a"])(this.openNodes),n=Us(e.uid,this.childrenMap);n.forEach(({uid:e})=>{delete t[e]}),this.setUnlessEqual("openNodes",t),i=n.slice(1)}else this.setUnlessEqual("openNodes",{...this.openNodes,[e.uid]:!0}),n=Gs(e.uid,this.childrenMap,this.children).filter(e=>this.renderableChildNodesMap[e.uid]);this.augmentRenderNodes({uid:e.uid,include:n,exclude:i})},toggleFullTree(e){const t=this.openNodes[e.uid],n=Object(w["a"])(this.openNodes),i=Us(e.uid,this.childrenMap);let a=[],s=[];i.forEach(({uid:e})=>{t?delete n[e]:n[e]=!0}),t?a=i.slice(1):s=i.slice(1).filter(e=>this.renderableChildNodesMap[e.uid]),this.setUnlessEqual("openNodes",n),this.augmentRenderNodes({uid:e.uid,exclude:a,include:s})},toggleSiblings(e){const t=this.openNodes[e.uid],n=Object(w["a"])(this.openNodes),i=Xs(e.uid,this.childrenMap,this.children);i.forEach(({uid:e,childUIDs:i,type:a})=>{if(i.length&&a!==Ce["b"].groupMarker)if(t){const t=Us(e,this.childrenMap);t.forEach(e=>{delete n[e.uid]}),delete n[e],this.augmentRenderNodes({uid:e,exclude:t.slice(1),include:[]})}else{n[e]=!0;const t=Gs(e,this.childrenMap,this.children).filter(e=>this.renderableChildNodesMap[e.uid]);this.augmentRenderNodes({uid:e,exclude:[],include:t})}}),this.setUnlessEqual("openNodes",n),this.persistState()},removeDeprecated(e,t){return t?e.filter(({deprecated:e})=>!e):e},generateNodesToRender(){const{children:e,openNodes:t,renderableChildNodesMap:n}=this;this.setUnlessEqual("nodesToRender",e.filter(e=>n[e.uid]&&(e.parent===Ks||t[e.parent]))),this.persistState(),this.scrollToElement()},augmentRenderNodes({uid:e,include:t=[],exclude:n=[]}){const i=this.nodesToRender.findIndex(t=>t.uid===e);if(t.length){const e=t.filter(e=>!this.nodesToRender.includes(e)),n=this.nodesToRender.slice(0);n.splice(i+1,0,...e),this.setUnlessEqual("nodesToRender",n)}else if(n.length){const e=new Set(n);this.setUnlessEqual("nodesToRender",this.nodesToRender.filter(t=>!e.has(t)))}this.persistState()},getFromStorage(e,t=null){const n=Fo["b"].get(Ul,{}),i=n[this.technologyPath];return i?e?i[e]||t:i:t},persistState(){const e={path:this.lastActivePathItem},{path:t}=this.activeUID&&this.childrenMap[this.activeUID]||e,n={technology:this.technology,path:t,hasApiChanges:!!this.apiChanges,openNodes:Object.keys(this.openNodes).map(Number),nodesToRender:this.nodesToRender.map(({uid:e})=>e),activeUID:this.activeUID,filter:this.filter,selectedTags:this.selectedTags},i={...Fo["b"].get(Ul,{}),[this.technologyPath]:n};Fo["b"].set(Ul,i)},clearPersistedState(){const e={...Fo["b"].get(Ul,{}),[this.technologyPath]:{}};Fo["b"].set(Ul,e)},restorePersistedState(){const e=this.getFromStorage();if(!e||e.path!==this.lastActivePathItem)return this.clearPersistedState(),void this.handleActivePathChange(this.activePath);const{technology:t,nodesToRender:n=[],filter:i="",hasAPIChanges:a=!1,activeUID:s=null,selectedTags:r=[],openNodes:o}=e;if(!n.length&&!i&&!r.length)return this.clearPersistedState(),void this.handleActivePathChange(this.activePath);const{childrenMap:l}=this,c=n.every(e=>l[e]),d=s?(this.childrenMap[s]||{}).path===this.lastActivePathItem:1===this.activePath.length;if(t!==this.technology||!c||a!==Boolean(this.apiChanges)||!d||s&&!i&&!r.length&&!n.includes(s))return this.clearPersistedState(),void this.handleActivePathChange(this.activePath);this.setUnlessEqual("openNodes",Object.fromEntries(o.map(e=>[e,!0]))),this.setUnlessEqual("nodesToRender",n.map(e=>l[e])),this.selectedTags=r,this.filter=i,this.debouncedFilter=this.filter,this.activeUID=s,this.scrollToElement()},async scrollToElement(){if(await Object(Ee["b"])(1),!this.$refs.scroller)return;if(this.hasFilter&&!this.deprecatedHidden)return void this.$refs.scroller.scrollToItem(0);const e=document.getElementById(this.activeUID);if(e&&0===this.getChildPositionInScroller(e))return;const t=this.nodesToRender.findIndex(e=>e.uid===this.activeUID);-1!==t&&this.$refs.scroller.scrollToItem(t)},getChildPositionInScroller(e){if(!e)return 0;const{paddingTop:t,paddingBottom:n}=getComputedStyle(this.$refs.scroller.$el),i={top:parseInt(t,10)||0,bottom:parseInt(n,10)||0},{y:a,height:s}=this.$refs.scroller.$el.getBoundingClientRect(),{y:r}=e.getBoundingClientRect(),o=e.offsetParent.offsetHeight,l=r-a-i.top;return l<0?-1:l+o>=s-i.bottom?1:0},isInsideScroller(e){return this.$refs.scroller.$el.contains(e)},handleFocusIn({target:e}){this.lastFocusTarget=e;const t=this.getChildPositionInScroller(e);if(0===t)return;const{offsetHeight:n}=e.offsetParent;this.$refs.scroller.$el.scrollBy({top:n*t,left:0})},handleFocusOut(e){e.relatedTarget&&(this.isInsideScroller(e.relatedTarget)||(this.lastFocusTarget=null))},handleScrollerUpdate:Ir((async function(){await Object(Ee["a"])(300),this.lastFocusTarget&&this.isInsideScroller(this.lastFocusTarget)&&document.activeElement!==this.lastFocusTarget&&this.lastFocusTarget.focus({preventScroll:!0})}),50),setActiveUID(e){this.activeUID=e},handleNavigationChange(e){this.childrenMap[e].path.startsWith(this.technologyPath)&&this.setActiveUID(e)},pathsToFlatChildren(e){const t=e.slice(0).reverse(),{childrenMap:n}=this;let i=this.children;const a=[];while(t.length){const e=t.pop(),s=i.find(t=>t.path===e);if(!s)break;a.push(s),t.length&&(i=s.childUIDs.map(e=>n[e]))}return a},handleActivePathChange(e){const t=this.childrenMap[this.activeUID],n=Vl(e);if(t){if(n===t.path)return;const e=Xs(this.activeUID,this.childrenMap,this.children),i=Gs(this.activeUID,this.childrenMap,this.children),a=Qs(this.activeUID,this.childrenMap),s=[...i,...e,...a].find(e=>e.path===n);if(s)return void this.setActiveUID(s.uid)}const i=this.pathsToFlatChildren(e);i.length?this.setActiveUID(i[i.length-1].uid):this.activeUID?this.setActiveUID(null):this.trackOpenNodes(this.nodeChangeDeps)},updateFocusIndexExternally(){this.externalFocusChange=!0,this.activeIndex>0?this.focusIndex(this.activeIndex):this.focusIndex(0)},focusNodeParent(e){const t=this.childrenMap[e.parent];if(!t)return;const n=this.nodesToRender.findIndex(e=>e.uid===t.uid);-1!==n&&this.focusIndex(n)}}},ac=ic,sc=(n("8e4d"),Object(K["a"])(ac,dl,ul,!1,null,"1543892a",null)),rc=sc.exports,oc=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("BaseNavigatorCard",e._b({on:{close:function(t){return e.$emit("close")}},scopedSlots:e._u([{key:"body",fn:function(t){var i=t.className;return[n("transition",{attrs:{name:"delay-visibility"}},[n("div",{staticClass:"loading-navigator",class:i,attrs:{"aria-hidden":"true"}},e._l(e.LOADER_ROWS,(function(e,t){return n("LoadingNavigatorItem",{key:t,attrs:{index:t,width:e.width,hideNavigatorIcon:e.hideNavigatorIcon}})})),1)])]}}])},"BaseNavigatorCard",e.$props,!1))},lc=[],cc=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("BaseNavigatorCardItem",{staticClass:"loading-navigator-item",style:"--index: "+e.index+";",attrs:{hideNavigatorIcon:e.hideNavigatorIcon},scopedSlots:e._u([{key:"navigator-icon",fn:function(e){var t=e.className;return[n("div",{class:t})]}},{key:"title-container",fn:function(){return[n("div",{staticClass:"loader",style:{width:e.width}})]},proxy:!0}])})},dc=[],uc={name:"LoadingNavigatorItem",components:{BaseNavigatorCardItem:kl},props:{...kl.props,index:{type:Number,default:0},width:{type:String,default:"50%"}}},hc=uc,pc=(n("d1b4"),Object(K["a"])(hc,cc,dc,!1,null,"0de29914",null)),gc=pc.exports;const fc=[{width:"30%",hideNavigatorIcon:!0},{width:"80%"},{width:"50%"}];var mc={name:"LoadingNavigatorCard",components:{BaseNavigatorCard:Hl,LoadingNavigatorItem:gc},props:{...Hl.props},data(){return{LOADER_ROWS:fc}}},yc=mc,vc=(n("115d"),Object(K["a"])(yc,oc,lc,!1,null,"4b6d345f",null)),bc=vc.exports,Tc={name:"Navigator",components:{NavigatorCard:rc,LoadingNavigatorCard:bc},data(){return{INDEX_ROOT_KEY:Ks}},props:{flatChildren:{type:Array,required:!0},parentTopicIdentifiers:{type:Array,required:!0},technology:{type:Object,required:!0},isFetching:{type:Boolean,default:!1},references:{type:Object,default:()=>{}},navigatorReferences:{type:Object,default:()=>{}},scrollLockID:{type:String,default:""},errorFetching:{type:Boolean,default:!1},renderFilterOnTop:{type:Boolean,default:!1},apiChanges:{type:Object,default:null},allowHiding:{type:Boolean,default:!0}},computed:{parentTopicReferences({references:e,parentTopicIdentifiers:t}){return t.reduce((t,n)=>{const i=e[n];return i?t.concat(i):(console.error(`Reference for "${n}" is missing`),t)},[])},activePath({parentTopicReferences:e,$route:{path:t}}){if(t=t.replace(/\/$/,"").toLowerCase(),!e.length)return[t];let n=1;return"technologies"===e[0].kind&&(n=2),e.slice(n).map(e=>e.url).concat(t)},type:()=>Ce["b"].module,technologyProps:({technology:e})=>({technology:e.title,technologyPath:e.path||e.url,isTechnologyBeta:e.beta})}},Sc=Tc,_c=(n("9c7e"),Object(K["a"])(Sc,ll,cl,!1,null,"159b9764",null)),Cc=_c.exports,kc=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("NavBase",{staticClass:"documentation-nav",attrs:{breakpoint:e.BreakpointName.medium,hasOverlay:!1,hasSolidBackground:"",hasNoBorder:e.hasNoBorder,isDark:e.isDark,isWideFormat:"",hasFullWidthBorder:"","aria-label":e.$t("api-reference")},scopedSlots:e._u([e.displaySidenav?{key:"pre-title",fn:function(t){var i=t.closeNav,a=t.isOpen,s=t.currentBreakpoint,r=t.className;return[n("div",{class:r},[n("transition",{attrs:{name:"sidenav-toggle"}},[n("div",{directives:[{name:"show",rawName:"v-show",value:e.sidenavHiddenOnLarge,expression:"sidenavHiddenOnLarge"}],staticClass:"sidenav-toggle-wrapper"},[n("button",{staticClass:"sidenav-toggle",attrs:{"aria-label":e.$t("navigator.open-navigator"),id:e.baseNavOpenSidenavButtonId,tabindex:a?-1:null},on:{click:function(t){return t.preventDefault(),e.handleSidenavToggle(i,s)}}},[n("span",{staticClass:"sidenav-icon-wrapper"},[n("SidenavIcon",{staticClass:"icon-inline sidenav-icon"})],1)]),n("span",{staticClass:"sidenav-toggle__separator"})])])],1)]}}:null,{key:"tray",fn:function(t){var i=t.closeNav;return[n("Hierarchy",{attrs:{currentTopicTitle:e.title,isSymbolDeprecated:e.isSymbolDeprecated,isSymbolBeta:e.isSymbolBeta,parentTopicIdentifiers:e.hierarchyItems,currentTopicTags:e.currentTopicTags,references:e.references}}),n("NavMenuItems",{staticClass:"nav-menu-settings",attrs:{previousSiblingChildren:e.breadcrumbCount}},[e.interfaceLanguage&&(e.swiftPath||e.objcPath)?n("LanguageToggle",{attrs:{interfaceLanguage:e.interfaceLanguage,objcPath:e.objcPath,swiftPath:e.swiftPath,closeNav:i}}):e._e(),e._t("menu-items")],2),e._t("tray-after",null,null,{breadcrumbCount:e.breadcrumbCount})]}}],null,!0)},[n("template",{slot:"default"},[e._t("title",(function(){return[e.rootLink?n("router-link",{staticClass:"nav-title-link",attrs:{to:e.rootLink}},[e._v(" "+e._s(e.$t("documentation.title"))+" ")]):n("span",{staticClass:"nav-title-link inactive"},[e._v(e._s(e.$t("documentation.title")))])]}),null,{rootLink:e.rootLink,linkClass:"nav-title-link",inactiveClass:"inactive"})],2),n("template",{slot:"after-content"},[e._t("after-content")],2)],2)},wc=[],Ic=n("cbcf"),xc=n("9b30"),$c=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("NavMenuItems",{staticClass:"hierarchy",class:{"has-badge":e.hasBadge},attrs:{"aria-label":e.$t("documentation.nav.breadcrumbs")}},[e.root?n("HierarchyItem",{key:e.root.title,staticClass:"root-hierarchy",attrs:{url:e.addQueryParamsToUrl(e.root.url)}},[e._v(" "+e._s(e.root.title)+" ")]):e._e(),e._l(e.collapsibleItems,(function(t){return n("HierarchyItem",{key:t.title,attrs:{isCollapsed:"",url:e.addQueryParamsToUrl(t.url)}},[e._v(" "+e._s(t.title)+" ")])})),e.collapsibleItems.length?n("HierarchyCollapsedItems",{attrs:{topics:e.collapsibleItems}}):e._e(),e._l(e.nonCollapsibleItems,(function(t){return n("HierarchyItem",{key:t.title,attrs:{url:e.addQueryParamsToUrl(t.url)}},[e._v(" "+e._s(t.title)+" ")])})),n("HierarchyItem",[e._v(" "+e._s(e.currentTopicTitle)+" "),n("template",{slot:"tags"},[e.isSymbolDeprecated?n("Badge",{attrs:{variant:"deprecated"}}):e.isSymbolBeta?n("Badge",{attrs:{variant:"beta"}}):e._e(),e._l(e.currentTopicTags,(function(t){return n("Badge",{key:t.type+"-"+t.text,attrs:{variant:t.type}},[e._v(" "+e._s(t.text)+" ")])}))],2)],2)],2)},Oc=[],Dc=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("li",{staticClass:"hierarchy-collapsed-items"},[n("span",{staticClass:"hierarchy-item-icon icon-inline"},[e._v("/")]),n("button",{ref:"btn",staticClass:"toggle",class:{focused:!e.collapsed},on:{click:e.toggleCollapsed}},[n("span",{staticClass:"indicator"},[n("EllipsisIcon",{staticClass:"icon-inline toggle-icon"})],1)]),n("ul",{ref:"dropdown",staticClass:"dropdown",class:{collapsed:e.collapsed}},e._l(e.topicsWithUrls,(function(t){return n("li",{key:t.title,staticClass:"dropdown-item"},[n("router-link",{staticClass:"nav-menu-link",attrs:{to:t.url}},[e._v(e._s(t.title))])],1)})),0)])},Pc=[],Lc=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("SVGIcon",{staticClass:"ellipsis-icon",attrs:{viewBox:"0 0 14 14",themeId:"ellipsis"}},[n("path",{attrs:{d:"m12.439 7.777v-1.554h-1.554v1.554zm-4.662 0v-1.554h-1.554v1.554zm-4.662 0v-1.554h-1.554v1.554z"}})])},Ac=[],Nc={name:"EllipsisIcon",components:{SVGIcon:fr["a"]}},Ec=Nc,jc=Object(K["a"])(Ec,Lc,Ac,!1,null,null,null),Bc=jc.exports,Rc={name:"HierarchyCollapsedItems",components:{EllipsisIcon:Bc},data:()=>({collapsed:!0}),props:{topics:{type:Array,required:!0}},watch:{collapsed(e,t){t&&!e?document.addEventListener("click",this.handleDocumentClick,!1):!t&&e&&document.removeEventListener("click",this.handleDocumentClick,!1)}},beforeDestroy(){document.removeEventListener("click",this.handleDocumentClick,!1)},computed:{topicsWithUrls:({$route:e,topics:t})=>t.map(t=>({...t,url:Object(P["b"])(t.url,e.query)}))},methods:{handleDocumentClick(e){const{target:t}=e,{collapsed:n,$refs:{btn:i,dropdown:a}}=this,s=!i.contains(t)&&!a.contains(t);!n&&s&&(this.collapsed=!0)},toggleCollapsed(){this.collapsed=!this.collapsed}}},Mc=Rc,zc=(n("2ca2"),Object(K["a"])(Mc,Dc,Pc,!1,null,"74906830",null)),Kc=zc.exports,qc=function(e,t){var n=t._c;return n(t.$options.components.NavMenuItemBase,{tag:"component",staticClass:"hierarchy-item",class:[{collapsed:t.props.isCollapsed},t.data.staticClass]},[n("span",{staticClass:"hierarchy-item-icon icon-inline"},[t._v("/")]),t.props.url?n("router-link",{staticClass:"parent item nav-menu-link",attrs:{to:t.props.url}},[t._t("default")],2):[n("span",{staticClass:"current item"},[t._t("default")],2),t._t("tags")]],2)},Fc=[],Hc=n("863d"),Vc={name:"HierarchyItem",components:{NavMenuItemBase:Hc["a"],InlineChevronRightIcon:vl["a"]},props:{isCollapsed:Boolean,url:{type:String,required:!1}}},Wc=Vc,Uc=(n("260a"),Object(K["a"])(Wc,qc,Fc,!0,null,"382bf39e",null)),Gc=Uc.exports;const Qc=3;var Xc={name:"Hierarchy",components:{Badge:ei["a"],NavMenuItems:xc["a"],HierarchyCollapsedItems:Kc,HierarchyItem:Gc},constants:{MaxVisibleLinks:Qc},inject:["store"],props:{isSymbolDeprecated:Boolean,isSymbolBeta:Boolean,references:Object,currentTopicTitle:{type:String,required:!0},parentTopicIdentifiers:{type:Array,default:()=>[]},currentTopicTags:{type:Array,default:()=>[]}},computed:{windowWidth:({store:e})=>e.state.contentWidth,parentTopics(){return this.parentTopicIdentifiers.reduce((e,t)=>{const n=this.references[t];if(n){const{title:t,url:i}=n;return e.concat({title:t,url:i})}return console.error(`Reference for "${t}" is missing`),e},[])},root:({parentTopics:e,windowWidth:t})=>t<=1e3?null:e[0],firstItemSlice:({root:e})=>e?1:0,linksAfterCollapse:({windowWidth:e,hasBadge:t})=>{const n=t?1:0;return e>1200?Qc-n:e>1e3?Qc-1-n:e>=800?Qc-2-n:0},collapsibleItems:({parentTopics:e,linksAfterCollapse:t,firstItemSlice:n})=>t?e.slice(n,-t):e.slice(n),nonCollapsibleItems:({parentTopics:e,linksAfterCollapse:t,firstItemSlice:n})=>t?e.slice(n).slice(-t):[],hasBadge:({isSymbolDeprecated:e,isSymbolBeta:t,currentTopicTags:n})=>e||t||n.length},methods:{addQueryParamsToUrl(e){return Object(P["b"])(e,this.$route.query)}}},Yc=Xc,Jc=(n("5208"),Object(K["a"])(Yc,$c,Oc,!1,null,"c2bd6086",null)),Zc=Jc.exports,ed=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("NavMenuItemBase",{staticClass:"nav-menu-setting language-container"},[n("div",{class:{"language-toggle-container":e.hasLanguages}},[n("select",{ref:"language-sizer",staticClass:"language-dropdown language-sizer",attrs:{"aria-hidden":"true",tabindex:"-1"}},[n("option",{key:e.currentLanguage.name,attrs:{selected:""}},[e._v(e._s(e.currentLanguage.name))])]),n("label",{staticClass:"nav-menu-setting-label",attrs:{for:e.hasLanguages?"language-toggle":null}},[e._v(e._s(e.$t("formats.colon",{content:e.$t("language")})))]),e.hasLanguages?n("select",{directives:[{name:"model",rawName:"v-model",value:e.languageModel,expression:"languageModel"}],staticClass:"language-dropdown nav-menu-link",style:"width: "+e.adjustedWidth+"px",attrs:{id:"language-toggle"},on:{change:[function(t){var n=Array.prototype.filter.call(t.target.options,(function(e){return e.selected})).map((function(e){var t="_value"in e?e._value:e.value;return t}));e.languageModel=t.target.multiple?n:n[0]},function(t){return e.pushRoute(e.currentLanguage.route)}]}},e._l(e.languages,(function(t){return n("option",{key:t.api,domProps:{value:t.api}},[e._v(" "+e._s(t.name)+" ")])})),0):n("span",{staticClass:"nav-menu-toggle-none current-language",attrs:{"aria-current":"page"}},[e._v(e._s(e.currentLanguage.name))]),e.hasLanguages?n("InlineChevronDownIcon",{staticClass:"toggle-icon icon-inline"}):e._e()],1),e.hasLanguages?n("div",{staticClass:"language-list-container"},[n("span",{staticClass:"nav-menu-setting-label"},[e._v(e._s(e.$t("formats.colon",{content:e.$t("language")})))]),n("ul",{staticClass:"language-list"},e._l(e.languages,(function(t){return n("li",{key:t.api,staticClass:"language-list-item"},[t.api===e.languageModel?n("span",{staticClass:"current-language",attrs:{"data-language":t.api,"aria-current":"page"}},[e._v(" "+e._s(t.name)+" ")]):n("a",{staticClass:"nav-menu-link",attrs:{href:"#"},on:{click:function(n){return n.preventDefault(),e.pushRoute(t.route)}}},[e._v(" "+e._s(t.name)+" ")])])})),0)]):e._e()])},td=[],nd=n("7948"),id={name:"LanguageToggle",components:{InlineChevronDownIcon:nd["a"],NavMenuItemBase:Hc["a"]},inject:{store:{default(){return{setPreferredLanguage(){}}}}},props:{interfaceLanguage:{type:String,required:!0},objcPath:{type:String,required:!1},swiftPath:{type:String,required:!1},closeNav:{type:Function,default:()=>{}}},data(){return{languageModel:null,adjustedWidth:0}},mounted(){const e=Ne(async()=>{await Object(Ee["b"])(3),this.calculateSelectWidth()},150);window.addEventListener("resize",e),window.addEventListener("orientationchange",e),this.$once("hook:beforeDestroy",()=>{window.removeEventListener("resize",e),window.removeEventListener("orientationchange",e)})},watch:{interfaceLanguage:{immediate:!0,handler(e){this.languageModel=e}},currentLanguage:{immediate:!0,handler:"calculateSelectWidth"}},methods:{getRoute(e){const t=e.query===O["a"].swift.key.url?void 0:e.query;return{query:{...this.$route.query,language:t},path:this.isCurrentPath(e.path)?null:Object(L["d"])(e.path)}},async pushRoute(e){await this.closeNav(),this.store.setPreferredLanguage(e.query),this.$router.push(this.getRoute(e))},isCurrentPath(e){return this.$route.path.replace(/^\//,"")===e},async calculateSelectWidth(){await this.$nextTick(),this.adjustedWidth=this.$refs["language-sizer"].clientWidth+6}},computed:{languages(){return[{name:O["a"].swift.name,api:O["a"].swift.key.api,route:{path:this.swiftPath,query:O["a"].swift.key.url}},{name:O["a"].objectiveC.name,api:O["a"].objectiveC.key.api,route:{path:this.objcPath,query:O["a"].objectiveC.key.url}}]},currentLanguage:({languages:e,languageModel:t})=>e.find(e=>e.api===t),hasLanguages:({objcPath:e,swiftPath:t})=>t&&e}},ad=id,sd=(n("b857"),Object(K["a"])(ad,ed,td,!1,null,"d12167e0",null)),rd=sd.exports,od={name:"DocumentationNav",components:{SidenavIcon:zl,NavBase:Ic["a"],NavMenuItems:xc["a"],Hierarchy:Zc,LanguageToggle:rd},props:{title:{type:String,required:!1},parentTopicIdentifiers:{type:Array,required:!1},isSymbolBeta:{type:Boolean,required:!1},isSymbolDeprecated:{type:Boolean,required:!1},isDark:{type:Boolean,default:!1},hasNoBorder:{type:Boolean,default:!1},currentTopicTags:{type:Array,required:!0},references:{type:Object,default:()=>({})},interfaceLanguage:{type:String,required:!1},objcPath:{type:String,required:!1},swiftPath:{type:String,required:!1},sidenavHiddenOnLarge:{type:Boolean,default:!1},displaySidenav:{type:Boolean,default:!1}},computed:{baseNavOpenSidenavButtonId:()=>Go["d"],BreakpointName:()=>Vo["b"],breadcrumbCount:({hierarchyItems:e})=>e.length+1,rootHierarchyReference:({parentTopicIdentifiers:e,references:t})=>t[e[0]]||{},isRootTechnologyLink:({rootHierarchyReference:{kind:e}})=>"technologies"===e,rootLink:({isRootTechnologyLink:e,rootHierarchyReference:t,$route:n})=>e?{path:t.url,query:n.query}:null,hierarchyItems:({parentTopicIdentifiers:e,isRootTechnologyLink:t})=>t?e.slice(1):e},methods:{async handleSidenavToggle(e,t){await e(),this.$emit("toggle-sidenav",t),await this.$nextTick();const n=document.getElementById(Fs);n&&n.focus()}}},ld=od,cd=(n("c61f"),Object(K["a"])(ld,kc,wc,!1,null,"138d523a",null)),dd=cd.exports,ud=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"StaticContentWidth"},[e._t("default")],2)},hd=[],pd={name:"StaticContentWidth",inject:["store"],mounted(){const e=Ne(async()=>{await this.$nextTick(),this.store.setContentWidth(this.$el.offsetWidth)},150);window.addEventListener("resize",e),window.addEventListener("orientationchange",e),this.$once("hook:beforeDestroy",()=>{window.removeEventListener("resize",e),window.removeEventListener("orientationchange",e)}),e()}},gd=pd,fd=Object(K["a"])(gd,ud,hd,!1,null,null,null),md=fd.exports,yd=n("3bdd"),vd=n("4009");const bd="symbol";var Td={watch:{topicData:{immediate:!0,handler:"extractOnThisPageSections"}},methods:{shouldRegisterContentSection(e){return e.type===_n["BlockType"].heading&&e.level<4},extractOnThisPageSections(e){if(!e)return;this.store.resetPageSections();const{metadata:{title:t},primaryContentSections:n,topicSections:i,defaultImplementationsSections:a,relationshipsSections:s,seeAlsoSections:r,kind:o}=e;this.store.addOnThisPageSection({title:t,anchor:vd["a"],level:1,isSymbol:o===bd},{i18n:!1}),n&&n.forEach(e=>{switch(e.kind){case Ke.content:At["a"].methods.forEach.call(e,e=>{this.shouldRegisterContentSection(e)&&this.store.addOnThisPageSection({title:e.text,anchor:e.anchor||Object(nn["a"])(e.text),level:e.level},{i18n:!1})});break;case Ke.properties:case Ke.restBody:case Ke.restCookies:case Ke.restEndpoint:case Ke.restHeaders:case Ke.restParameters:case Ke.restResponses:this.store.addOnThisPageSection({title:e.title,anchor:Object(nn["a"])(e.title),level:2});break;default:qt[e.kind]&&this.store.addOnThisPageSection(qt[e.kind])}}),i&&this.store.addOnThisPageSection(Kt.topics),a&&this.store.addOnThisPageSection(Kt.defaultImplementations),s&&this.store.addOnThisPageSection(Kt.relationships),r&&this.store.addOnThisPageSection(Kt.seeAlso)}}},Sd=n("9b56");const _d="0.3.0",Cd="navigator-hidden-large",{extractProps:kd}=Is.methods;var wd={name:"DocumentationTopicView",constants:{MIN_RENDER_JSON_VERSION_WITH_INDEX:_d,NAVIGATOR_HIDDEN_ON_LARGE_KEY:Cd},components:{Navigator:Cc,AdjustableSidebarWidth:ol,StaticContentWidth:md,NavigatorDataProvider:nr,Topic:Is,CodeTheme:Bs["a"],Nav:dd,QuickNavigationButton:lr,QuickNavigationModal:zo,PortalTarget:I["PortalTarget"]},mixins:[Ms["a"],zs["a"],Td],props:{enableMinimized:{type:Boolean,default:!1}},data(){return{topicDataDefault:null,topicDataObjc:null,sidenavVisibleOnMobile:!1,sidenavHiddenOnLarge:Fo["c"].get(Cd,!1),showQuickNavigationModal:!1,store:js,BreakpointName:Vo["b"]}},computed:{objcOverrides:({topicData:e})=>{const{variantOverrides:t=[]}=e||{},n=({interfaceLanguage:e})=>e===O["a"].objectiveC.key.api,i=({traits:e})=>e.some(n),a=t.find(i);return a?a.patch:null},enableQuickNavigation:({isTargetIDE:e})=>!e&&Object(Ze["c"])(["features","docs","quickNavigation","enable"],!0),topicData:{get(){return this.topicDataObjc?this.topicDataObjc:this.topicDataDefault},set(e){this.topicDataDefault=e}},topicKey:({$route:e,topicProps:t})=>[e.path,t.interfaceLanguage].join(),topicProps(){return kd(this.topicData)},parentTopicIdentifiers:({topicProps:{hierarchy:{paths:e=[]},references:t},$route:n})=>e.length?e.find(e=>{const i=e.find(e=>t[e]&&"technologies"!==t[e].kind),a=i&&t[i];return a&&n.path.toLowerCase().startsWith(a.url.toLowerCase())})||e[0]:[],technology:({$route:e,topicProps:{identifier:t,references:n,role:i,title:a},parentTopicIdentifiers:s})=>{const r={title:a,url:e.path},o=n[t];if(!s.length)return o||r;const l=n[s[0]];return l&&"technologies"!==l.kind?l:(i!==k["a"].collection||o)&&(l&&n[s[1]]||o)||r},languagePaths:({topicData:{variants:e=[]}})=>e.reduce((e,t)=>t.traits.reduce((e,n)=>n.interfaceLanguage?{...e,[n.interfaceLanguage]:(e[n.interfaceLanguage]||[]).concat(t.paths)}:e,e),{}),objcPath:({languagePaths:{[O["a"].objectiveC.key.api]:[e]=[]}={}})=>e,swiftPath:({languagePaths:{[O["a"].swift.key.api]:[e]=[]}={}})=>e,isSymbolBeta:({topicProps:{platforms:e}})=>!!(e&&e.length&&e.every(e=>e.beta)),isSymbolDeprecated:({topicProps:{platforms:e,deprecationSummary:t}})=>!!(t&&t.length>0||e&&e.length&&e.every(e=>e.deprecatedAt)),enableNavigator:({isTargetIDE:e,topicDataDefault:t})=>!e&&Object(yd["b"])(Object(yd["a"])(t.schemaVersion),_d)>=0,enableOnThisPageNav:({isTargetIDE:e})=>!Object(Ze["c"])(["features","docs","onThisPageNavigator","disable"],!1)&&!e,sidebarProps:({sidenavVisibleOnMobile:e,enableNavigator:t,sidenavHiddenOnLarge:n})=>t?{shownOnMobile:e,hiddenOnLarge:n}:{},sidebarListeners(){return this.enableNavigator?{"update:shownOnMobile":this.toggleMobileSidenav,"update:hiddenOnLarge":this.toggleLargeSidenav}:{}}},methods:{applyObjcOverrides(){this.topicDataObjc=C(Object(w["a"])(this.topicData),this.objcOverrides)},handleCodeColorsChange(e){Rs["a"].updateCodeColors(e)},handleToggleSidenav(e){e===Vo["b"].large?this.toggleLargeSidenav():this.toggleMobileSidenav()},openQuickNavigationModal(){this.sidenavVisibleOnMobile||(this.showQuickNavigationModal=!0)},toggleLargeSidenav(e=!this.sidenavHiddenOnLarge){this.sidenavHiddenOnLarge=e,Fo["c"].set(Cd,e)},toggleMobileSidenav(e=!this.sidenavVisibleOnMobile){this.sidenavVisibleOnMobile=e},onQuickNavigationKeydown(e){("/"===e.key||"o"===e.key&&e.shiftKey&&e.metaKey)&&this.enableNavigator&&"input"!==e.target.tagName.toLowerCase()&&(this.openQuickNavigationModal(),e.preventDefault())}},mounted(){this.$bridge.on("contentUpdate",this.handleContentUpdateFromBridge),this.$bridge.on("codeColors",this.handleCodeColorsChange),this.$bridge.send({type:"requestCodeColors"}),this.enableQuickNavigation&&window.addEventListener("keydown",this.onQuickNavigationKeydown)},provide(){return{store:this.store}},inject:{isTargetIDE:{default(){return!1}}},beforeDestroy(){this.$bridge.off("contentUpdate",this.handleContentUpdateFromBridge),this.$bridge.off("codeColors",this.handleCodeColorsChange),this.enableQuickNavigation&&window.removeEventListener("keydown",this.onQuickNavigationKeydown)},beforeRouteEnter(e,t,n){e.meta.skipFetchingData?n(e=>e.newContentMounted()):Object(w["c"])(e,t,n).then(t=>n(n=>{Object(Sd["c"])(e.params.locale,n),n.topicData=t,e.query.language===O["a"].objectiveC.key.url&&n.objcOverrides&&n.applyObjcOverrides()})).catch(n)},beforeRouteUpdate(e,t,n){e.path===t.path&&e.query.language===O["a"].objectiveC.key.url&&this.objcOverrides?(this.applyObjcOverrides(),n()):Object(w["e"])(e,t)?Object(w["c"])(e,t,n).then(t=>{this.topicDataObjc=null,this.topicData=t,e.query.language===O["a"].objectiveC.key.url&&this.objcOverrides&&this.applyObjcOverrides(),Object(Sd["c"])(e.params.locale,this),n()}).catch(n):n()},created(){this.store.reset()},watch:{topicData(){this.$nextTick(()=>{this.newContentMounted()})}}},Id=wd,xd=(n("857c"),Object(K["a"])(Id,i,a,!1,null,"4a89caca",null));t["default"]=xd.exports},f9c9:function(e,t,n){}}]);