{"schemaVersion": {"major": 0, "patch": 0, "minor": 3}, "identifier": {"url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/RawRepresentable-Implementations", "interfaceLanguage": "swift"}, "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle"]]}, "kind": "article", "sections": [], "variants": [{"paths": ["/documentation/jdstatusbarnotification/includedstatusbarnotificationstyle/rawrepresentable-implementations"], "traits": [{"interfaceLanguage": "swift"}]}], "topicSections": [{"identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/hashValue"], "title": "Instance Properties", "generated": true}, {"generated": true, "title": "Instance Methods", "identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/hash(into:)"]}], "metadata": {"role": "collectionGroup", "title": "RawRepresentable Implementations", "modules": [{"name": "JDStatusBarNotification"}]}, "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter": {"abstract": [{"type": "text", "text": "The NotificationPresenter let’s you present notifications below the statusBar."}, {"type": "text", "text": " "}, {"type": "text", "text": "You can customize the style (colors, fonts, etc.) and animations. It supports notch"}, {"type": "text", "text": " "}, {"type": "text", "text": "and no-notch devices, landscape & portrait layouts and Drag-to-Dismiss. It can display a"}, {"text": " ", "type": "text"}, {"type": "text", "text": "title, a subtitle, an activity indicator, an animated progress bar & custom views out of the box."}], "fragments": [{"text": "class", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "NotificationPresenter"}], "navigatorTitle": [{"text": "NotificationPresenter", "kind": "identifier"}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter", "url": "/documentation/jdstatusbarnotification/notificationpresenter", "type": "topic", "kind": "symbol", "title": "NotificationPresenter"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/hash(into:)": {"abstract": [], "type": "topic", "fragments": [{"text": "func", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "hash"}, {"kind": "text", "text": "("}, {"text": "into", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"text": "inout", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "<PERSON><PERSON>", "preciseIdentifier": "s:s6HasherV", "kind": "typeIdentifier"}, {"text": ")", "kind": "text"}], "kind": "symbol", "role": "symbol", "conformance": {"constraints": [{"code": "Self", "type": "codeVoice"}, {"text": " conforms to ", "type": "text"}, {"code": "<PERSON><PERSON><PERSON>", "type": "codeVoice"}, {"type": "text", "text": " and "}, {"type": "codeVoice", "code": "RawValue"}, {"type": "text", "text": " conforms to "}, {"type": "codeVoice", "code": "<PERSON><PERSON><PERSON>"}, {"type": "text", "text": "."}], "conformancePrefix": [{"type": "text", "text": "Conforms when"}], "availabilityPrefix": [{"type": "text", "text": "Available when"}]}, "url": "/documentation/jdstatusbarnotification/includedstatusbarnotificationstyle/hash(into:)", "title": "hash(into:)", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/hash(into:)"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/hashValue": {"url": "/documentation/jdstatusbarnotification/includedstatusbarnotificationstyle/hashvalue", "kind": "symbol", "type": "topic", "fragments": [{"text": "var", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "hashValue"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "s:<PERSON>", "kind": "typeIdentifier", "text": "Int"}], "title": "hashValue", "abstract": [], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/hashValue", "conformance": {"conformancePrefix": [{"text": "Conforms when", "type": "text"}], "availabilityPrefix": [{"type": "text", "text": "Available when"}], "constraints": [{"code": "Self", "type": "codeVoice"}, {"text": " conforms to ", "type": "text"}, {"code": "<PERSON><PERSON><PERSON>", "type": "codeVoice"}, {"text": " and ", "type": "text"}, {"code": "RawValue", "type": "codeVoice"}, {"type": "text", "text": " conforms to "}, {"type": "codeVoice", "code": "<PERSON><PERSON><PERSON>"}, {"text": ".", "type": "text"}]}, "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle": {"abstract": [{"text": "These included styles let you easily change the visual appearance of a", "type": "text"}, {"type": "text", "text": " "}, {"type": "text", "text": "notification without creating your own custom style."}], "fragments": [{"text": "enum", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "IncludedStatusBarNotificationStyle", "kind": "identifier"}], "navigatorTitle": [{"kind": "identifier", "text": "IncludedStatusBarNotificationStyle"}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle", "url": "/documentation/jdstatusbarnotification/includedstatusbarnotificationstyle", "type": "topic", "kind": "symbol", "title": "IncludedStatusBarNotificationStyle"}}}