{"abstract": [{"text": "Inherited from ", "type": "text"}, {"type": "codeVoice", "code": "RawRepresentable.hashValue"}, {"type": "text", "text": "."}], "identifier": {"url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/hashValue", "interfaceLanguage": "swift"}, "metadata": {"role": "symbol", "roleHeading": "Instance Property", "modules": [{"relatedModules": ["Swift"], "name": "JDStatusBarNotification"}], "symbolKind": "property", "title": "hashValue", "fragments": [{"text": "var", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "hashValue"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "s:<PERSON>", "text": "Int"}], "externalID": "s:SYsSHRzSH8RawValueSYRpzrlE04hashB0Sivp::SYNTHESIZED::c:@M@JDStatusBarNotification@E@JDStatusBarNotificationIncludedStyle", "extendedModule": "Swift", "conformance": {"conformancePrefix": [{"type": "text", "text": "Conforms when"}], "constraints": [{"code": "Self", "type": "codeVoice"}, {"type": "text", "text": " conforms to "}, {"type": "codeVoice", "code": "<PERSON><PERSON><PERSON>"}, {"text": " and ", "type": "text"}, {"type": "codeVoice", "code": "RawValue"}, {"text": " conforms to ", "type": "text"}, {"type": "codeVoice", "code": "<PERSON><PERSON><PERSON>"}, {"text": ".", "type": "text"}], "availabilityPrefix": [{"text": "Available when", "type": "text"}]}}, "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/RawRepresentable-Implementations"]]}, "schemaVersion": {"minor": 3, "patch": 0, "major": 0}, "kind": "symbol", "primaryContentSections": [{"kind": "declarations", "declarations": [{"tokens": [{"kind": "keyword", "text": "var"}, {"text": " ", "kind": "text"}, {"text": "hashValue", "kind": "identifier"}, {"text": ": ", "kind": "text"}, {"preciseIdentifier": "s:<PERSON>", "kind": "typeIdentifier", "text": "Int"}, {"kind": "text", "text": " { "}, {"text": "get", "kind": "keyword"}, {"kind": "text", "text": " }"}], "languages": ["swift"], "platforms": ["iOS"]}]}], "sections": [], "variants": [{"traits": [{"interfaceLanguage": "swift"}], "paths": ["/documentation/jdstatusbarnotification/includedstatusbarnotificationstyle/hashvalue"]}], "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter": {"abstract": [{"type": "text", "text": "The NotificationPresenter let’s you present notifications below the statusBar."}, {"type": "text", "text": " "}, {"type": "text", "text": "You can customize the style (colors, fonts, etc.) and animations. It supports notch"}, {"type": "text", "text": " "}, {"type": "text", "text": "and no-notch devices, landscape & portrait layouts and Drag-to-Dismiss. It can display a"}, {"text": " ", "type": "text"}, {"type": "text", "text": "title, a subtitle, an activity indicator, an animated progress bar & custom views out of the box."}], "fragments": [{"text": "class", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "NotificationPresenter"}], "navigatorTitle": [{"text": "NotificationPresenter", "kind": "identifier"}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter", "url": "/documentation/jdstatusbarnotification/notificationpresenter", "type": "topic", "kind": "symbol", "title": "NotificationPresenter"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/hashValue": {"url": "/documentation/jdstatusbarnotification/includedstatusbarnotificationstyle/hashvalue", "kind": "symbol", "type": "topic", "fragments": [{"text": "var", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "hashValue"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "s:<PERSON>", "kind": "typeIdentifier", "text": "Int"}], "title": "hashValue", "abstract": [], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/hashValue", "conformance": {"conformancePrefix": [{"text": "Conforms when", "type": "text"}], "availabilityPrefix": [{"type": "text", "text": "Available when"}], "constraints": [{"code": "Self", "type": "codeVoice"}, {"text": " conforms to ", "type": "text"}, {"code": "<PERSON><PERSON><PERSON>", "type": "codeVoice"}, {"text": " and ", "type": "text"}, {"code": "RawValue", "type": "codeVoice"}, {"type": "text", "text": " conforms to "}, {"type": "codeVoice", "code": "<PERSON><PERSON><PERSON>"}, {"text": ".", "type": "text"}]}, "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle": {"abstract": [{"text": "These included styles let you easily change the visual appearance of a", "type": "text"}, {"type": "text", "text": " "}, {"type": "text", "text": "notification without creating your own custom style."}], "fragments": [{"text": "enum", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "IncludedStatusBarNotificationStyle", "kind": "identifier"}], "navigatorTitle": [{"kind": "identifier", "text": "IncludedStatusBarNotificationStyle"}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle", "url": "/documentation/jdstatusbarnotification/includedstatusbarnotificationstyle", "type": "topic", "kind": "symbol", "title": "IncludedStatusBarNotificationStyle"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/RawRepresentable-Implementations": {"type": "topic", "url": "/documentation/jdstatusbarnotification/includedstatusbarnotificationstyle/rawrepresentable-implementations", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/RawRepresentable-Implementations", "title": "RawRepresentable Implementations", "kind": "article", "role": "collectionGroup", "abstract": []}}}