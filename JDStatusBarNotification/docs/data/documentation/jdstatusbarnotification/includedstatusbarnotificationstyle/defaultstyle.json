{"metadata": {"title": "IncludedStatusBarNotificationStyle.defaultStyle", "roleHeading": "Case", "modules": [{"name": "JDStatusBarNotification"}], "fragments": [{"kind": "keyword", "text": "case"}, {"kind": "text", "text": " "}, {"text": "defaultStyle", "kind": "identifier"}], "externalID": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationIncludedStyle@JDStatusBarNotificationIncludedStyleDefaultStyle", "role": "symbol", "symbolKind": "case"}, "seeAlsoSections": [{"title": "Default Style", "generated": true, "identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/light", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/dark"]}], "variants": [{"traits": [{"interfaceLanguage": "swift"}], "paths": ["/documentation/jdstatusbarnotification/includedstatusbarnotificationstyle/defaultstyle"]}], "kind": "symbol", "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle"]]}, "identifier": {"url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/defaultStyle", "interfaceLanguage": "swift"}, "primaryContentSections": [{"kind": "declarations", "declarations": [{"tokens": [{"kind": "keyword", "text": "case"}, {"kind": "text", "text": " "}, {"text": "defaultStyle", "kind": "identifier"}], "platforms": ["iOS"], "languages": ["swift"]}]}], "schemaVersion": {"major": 0, "minor": 3, "patch": 0}, "sections": [], "abstract": [{"type": "text", "text": "The default style. This is used when no other style was provided and the"}, {"type": "text", "text": " "}, {"type": "text", "text": "default style wasn’t replaced by the user. This is a dynamic style matching"}, {"type": "text", "text": " "}, {"type": "text", "text": "the "}, {"code": "light", "type": "codeVoice"}, {"type": "text", "text": " style in light mode and "}, {"type": "codeVoice", "code": "dark"}, {"text": " style in dark mode.", "type": "text"}], "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/light": {"abstract": [{"type": "text", "text": "A white background with a gray text."}], "fragments": [{"text": "case", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "light"}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/light", "url": "/documentation/jdstatusbarnotification/includedstatusbarnotificationstyle/light", "type": "topic", "kind": "symbol", "title": "IncludedStatusBarNotificationStyle.light"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter": {"abstract": [{"type": "text", "text": "The NotificationPresenter let’s you present notifications below the statusBar."}, {"type": "text", "text": " "}, {"type": "text", "text": "You can customize the style (colors, fonts, etc.) and animations. It supports notch"}, {"type": "text", "text": " "}, {"type": "text", "text": "and no-notch devices, landscape & portrait layouts and Drag-to-Dismiss. It can display a"}, {"text": " ", "type": "text"}, {"type": "text", "text": "title, a subtitle, an activity indicator, an animated progress bar & custom views out of the box."}], "fragments": [{"text": "class", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "NotificationPresenter"}], "navigatorTitle": [{"text": "NotificationPresenter", "kind": "identifier"}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter", "url": "/documentation/jdstatusbarnotification/notificationpresenter", "type": "topic", "kind": "symbol", "title": "NotificationPresenter"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/defaultStyle": {"abstract": [{"text": "The default style. This is used when no other style was provided and the", "type": "text"}, {"text": " ", "type": "text"}, {"text": "default style wasn’t replaced by the user. This is a dynamic style matching", "type": "text"}, {"type": "text", "text": " "}, {"type": "text", "text": "the "}, {"code": "light", "type": "codeVoice"}, {"text": " style in light mode and ", "type": "text"}, {"type": "codeVoice", "code": "dark"}, {"type": "text", "text": " style in dark mode."}], "fragments": [{"kind": "keyword", "text": "case"}, {"text": " ", "kind": "text"}, {"text": "defaultStyle", "kind": "identifier"}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/defaultStyle", "url": "/documentation/jdstatusbarnotification/includedstatusbarnotificationstyle/defaultstyle", "type": "topic", "kind": "symbol", "title": "IncludedStatusBarNotificationStyle.defaultStyle"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle": {"abstract": [{"text": "These included styles let you easily change the visual appearance of a", "type": "text"}, {"type": "text", "text": " "}, {"type": "text", "text": "notification without creating your own custom style."}], "fragments": [{"text": "enum", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "IncludedStatusBarNotificationStyle", "kind": "identifier"}], "navigatorTitle": [{"kind": "identifier", "text": "IncludedStatusBarNotificationStyle"}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle", "url": "/documentation/jdstatusbarnotification/includedstatusbarnotificationstyle", "type": "topic", "kind": "symbol", "title": "IncludedStatusBarNotificationStyle"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/dark": {"abstract": [{"type": "text", "text": "A nearly black background with a nearly white text."}], "fragments": [{"kind": "keyword", "text": "case"}, {"text": " ", "kind": "text"}, {"text": "dark", "kind": "identifier"}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/dark", "url": "/documentation/jdstatusbarnotification/includedstatusbarnotificationstyle/dark", "type": "topic", "kind": "symbol", "title": "IncludedStatusBarNotificationStyle.dark"}}}