{"kind": "article", "schemaVersion": {"patch": 0, "minor": 3, "major": 0}, "topicSections": [{"generated": true, "title": "Operators", "identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/!=(_:_:)"]}], "variants": [{"paths": ["/documentation/jdstatusbarnotification/includedstatusbarnotificationstyle/equatable-implementations"], "traits": [{"interfaceLanguage": "swift"}]}], "metadata": {"modules": [{"name": "JDStatusBarNotification"}], "title": "Equatable Implementations", "role": "collectionGroup"}, "sections": [], "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle"]]}, "identifier": {"interfaceLanguage": "swift", "url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/Equatable-Implementations"}, "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/!=(_:_:)": {"role": "symbol", "title": "!=(_:_:)", "abstract": [], "fragments": [{"kind": "keyword", "text": "static"}, {"kind": "text", "text": " "}, {"text": "func", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "!=", "kind": "identifier"}, {"kind": "text", "text": " "}, {"kind": "text", "text": "("}, {"kind": "typeIdentifier", "text": "Self"}, {"text": ", ", "kind": "text"}, {"text": "Self", "kind": "typeIdentifier"}, {"text": ") -> ", "kind": "text"}, {"text": "Bool", "preciseIdentifier": "s:Sb", "kind": "typeIdentifier"}], "kind": "symbol", "url": "/documentation/jdstatusbarnotification/includedstatusbarnotificationstyle/!=(_:_:)", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/!=(_:_:)", "type": "topic"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter": {"abstract": [{"type": "text", "text": "The NotificationPresenter let’s you present notifications below the statusBar."}, {"type": "text", "text": " "}, {"type": "text", "text": "You can customize the style (colors, fonts, etc.) and animations. It supports notch"}, {"type": "text", "text": " "}, {"type": "text", "text": "and no-notch devices, landscape & portrait layouts and Drag-to-Dismiss. It can display a"}, {"text": " ", "type": "text"}, {"type": "text", "text": "title, a subtitle, an activity indicator, an animated progress bar & custom views out of the box."}], "fragments": [{"text": "class", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "NotificationPresenter"}], "navigatorTitle": [{"text": "NotificationPresenter", "kind": "identifier"}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter", "url": "/documentation/jdstatusbarnotification/notificationpresenter", "type": "topic", "kind": "symbol", "title": "NotificationPresenter"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle": {"abstract": [{"text": "These included styles let you easily change the visual appearance of a", "type": "text"}, {"type": "text", "text": " "}, {"type": "text", "text": "notification without creating your own custom style."}], "fragments": [{"text": "enum", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "IncludedStatusBarNotificationStyle", "kind": "identifier"}], "navigatorTitle": [{"kind": "identifier", "text": "IncludedStatusBarNotificationStyle"}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle", "url": "/documentation/jdstatusbarnotification/includedstatusbarnotificationstyle", "type": "topic", "kind": "symbol", "title": "IncludedStatusBarNotificationStyle"}}}