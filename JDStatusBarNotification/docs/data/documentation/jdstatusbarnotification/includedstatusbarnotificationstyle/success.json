{"primaryContentSections": [{"kind": "declarations", "declarations": [{"platforms": ["iOS"], "tokens": [{"kind": "keyword", "text": "case"}, {"text": " ", "kind": "text"}, {"text": "success", "kind": "identifier"}], "languages": ["swift"]}]}], "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle"]]}, "abstract": [{"type": "text", "text": "A green background with a white text."}], "variants": [{"traits": [{"interfaceLanguage": "swift"}], "paths": ["/documentation/jdstatusbarnotification/includedstatusbarnotificationstyle/success"]}], "kind": "symbol", "metadata": {"fragments": [{"kind": "keyword", "text": "case"}, {"kind": "text", "text": " "}, {"text": "success", "kind": "identifier"}], "role": "symbol", "title": "IncludedStatusBarNotificationStyle.success", "roleHeading": "Case", "symbolKind": "case", "externalID": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationIncludedStyle@JDStatusBarNotificationIncludedStyleSuccess", "modules": [{"name": "JDStatusBarNotification"}]}, "schemaVersion": {"major": 0, "minor": 3, "patch": 0}, "seeAlsoSections": [{"title": "Other Included Styles", "generated": true, "identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/warning", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/error", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/matrix"]}], "sections": [], "identifier": {"url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/success", "interfaceLanguage": "swift"}, "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/error": {"fragments": [{"text": "case", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "error", "kind": "identifier"}], "title": "IncludedStatusBarNotificationStyle.error", "type": "topic", "abstract": [{"type": "text", "text": "A red background with a white text."}], "kind": "symbol", "url": "/documentation/jdstatusbarnotification/includedstatusbarnotificationstyle/error", "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/error"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/success": {"fragments": [{"text": "case", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "success", "kind": "identifier"}], "title": "IncludedStatusBarNotificationStyle.success", "type": "topic", "abstract": [{"type": "text", "text": "A green background with a white text."}], "kind": "symbol", "url": "/documentation/jdstatusbarnotification/includedstatusbarnotificationstyle/success", "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/success"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/matrix": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/matrix", "title": "IncludedStatusBarNotificationStyle.matrix", "url": "/documentation/jdstatusbarnotification/includedstatusbarnotificationstyle/matrix", "kind": "symbol", "abstract": [{"type": "text", "text": "A black background with a green bold monospace text."}], "type": "topic", "role": "symbol", "fragments": [{"text": "case", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "matrix", "kind": "identifier"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter": {"abstract": [{"type": "text", "text": "The NotificationPresenter let’s you present notifications below the statusBar."}, {"type": "text", "text": " "}, {"type": "text", "text": "You can customize the style (colors, fonts, etc.) and animations. It supports notch"}, {"type": "text", "text": " "}, {"type": "text", "text": "and no-notch devices, landscape & portrait layouts and Drag-to-Dismiss. It can display a"}, {"text": " ", "type": "text"}, {"type": "text", "text": "title, a subtitle, an activity indicator, an animated progress bar & custom views out of the box."}], "fragments": [{"text": "class", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "NotificationPresenter"}], "navigatorTitle": [{"text": "NotificationPresenter", "kind": "identifier"}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter", "url": "/documentation/jdstatusbarnotification/notificationpresenter", "type": "topic", "kind": "symbol", "title": "NotificationPresenter"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/warning": {"fragments": [{"text": "case", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "warning", "kind": "identifier"}], "title": "IncludedStatusBarNotificationStyle.warning", "type": "topic", "abstract": [{"type": "text", "text": "A yellow background with a gray text."}], "kind": "symbol", "url": "/documentation/jdstatusbarnotification/includedstatusbarnotificationstyle/warning", "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/warning"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle": {"abstract": [{"text": "These included styles let you easily change the visual appearance of a", "type": "text"}, {"type": "text", "text": " "}, {"type": "text", "text": "notification without creating your own custom style."}], "fragments": [{"text": "enum", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "IncludedStatusBarNotificationStyle", "kind": "identifier"}], "navigatorTitle": [{"kind": "identifier", "text": "IncludedStatusBarNotificationStyle"}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle", "url": "/documentation/jdstatusbarnotification/includedstatusbarnotificationstyle", "type": "topic", "kind": "symbol", "title": "IncludedStatusBarNotificationStyle"}}}