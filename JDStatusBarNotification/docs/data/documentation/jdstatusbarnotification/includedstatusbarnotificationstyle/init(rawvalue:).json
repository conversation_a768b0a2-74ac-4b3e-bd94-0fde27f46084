{"kind": "symbol", "abstract": [{"text": "Inherited from ", "type": "text"}, {"code": "RawRepresentable.init(rawValue:)", "type": "codeVoice"}, {"type": "text", "text": "."}], "metadata": {"role": "symbol", "modules": [{"name": "JDStatusBarNotification"}], "title": "init(rawValue:)", "roleHeading": "Initializer", "symbolKind": "init", "fragments": [{"kind": "identifier", "text": "init"}, {"kind": "text", "text": "?("}, {"kind": "externalParam", "text": "rawValue"}, {"text": ": ", "kind": "text"}, {"preciseIdentifier": "s:<PERSON>", "text": "Int", "kind": "typeIdentifier"}, {"kind": "text", "text": ")"}], "externalID": "s:23JDStatusBarNotification014IncludedStatusbC5StyleO8rawValueACSgSi_tcfc"}, "variants": [{"traits": [{"interfaceLanguage": "swift"}], "paths": ["/documentation/jdstatusbarnotification/includedstatusbarnotificationstyle/init(rawvalue:)"]}], "primaryContentSections": [{"declarations": [{"tokens": [{"kind": "keyword", "text": "init"}, {"text": "?(", "kind": "text"}, {"text": "rawValue", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "text": "Int", "preciseIdentifier": "s:<PERSON>"}, {"text": ")", "kind": "text"}], "languages": ["swift"], "platforms": ["iOS"]}], "kind": "declarations"}], "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle"]]}, "schemaVersion": {"minor": 3, "major": 0, "patch": 0}, "identifier": {"url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/init(rawValue:)", "interfaceLanguage": "swift"}, "sections": [], "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter": {"abstract": [{"type": "text", "text": "The NotificationPresenter let’s you present notifications below the statusBar."}, {"type": "text", "text": " "}, {"type": "text", "text": "You can customize the style (colors, fonts, etc.) and animations. It supports notch"}, {"type": "text", "text": " "}, {"type": "text", "text": "and no-notch devices, landscape & portrait layouts and Drag-to-Dismiss. It can display a"}, {"text": " ", "type": "text"}, {"type": "text", "text": "title, a subtitle, an activity indicator, an animated progress bar & custom views out of the box."}], "fragments": [{"text": "class", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "NotificationPresenter"}], "navigatorTitle": [{"text": "NotificationPresenter", "kind": "identifier"}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter", "url": "/documentation/jdstatusbarnotification/notificationpresenter", "type": "topic", "kind": "symbol", "title": "NotificationPresenter"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle": {"abstract": [{"text": "These included styles let you easily change the visual appearance of a", "type": "text"}, {"type": "text", "text": " "}, {"type": "text", "text": "notification without creating your own custom style."}], "fragments": [{"text": "enum", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "IncludedStatusBarNotificationStyle", "kind": "identifier"}], "navigatorTitle": [{"kind": "identifier", "text": "IncludedStatusBarNotificationStyle"}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle", "url": "/documentation/jdstatusbarnotification/includedstatusbarnotificationstyle", "type": "topic", "kind": "symbol", "title": "IncludedStatusBarNotificationStyle"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/init(rawValue:)": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/init(rawValue:)", "url": "/documentation/jdstatusbarnotification/includedstatusbarnotificationstyle/init(rawvalue:)", "title": "init(rawValue:)", "abstract": [], "role": "symbol", "kind": "symbol", "type": "topic", "fragments": [{"text": "init", "kind": "identifier"}, {"kind": "text", "text": "?("}, {"text": "rawValue", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"text": "Int", "preciseIdentifier": "s:<PERSON>", "kind": "typeIdentifier"}, {"kind": "text", "text": ")"}]}}}