{"sections": [], "topicSections": [{"identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/hashValue"], "generated": true, "title": "Instance Properties"}, {"title": "Instance Methods", "generated": true, "identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/hash(into:)"]}], "metadata": {"title": "RawRepresentable Implementations", "modules": [{"name": "JDStatusBarNotification"}], "role": "collectionGroup"}, "identifier": {"interfaceLanguage": "swift", "url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/RawRepresentable-Implementations"}, "schemaVersion": {"minor": 3, "patch": 0, "major": 0}, "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType"]]}, "kind": "article", "variants": [{"paths": ["/documentation/jdstatusbarnotification/statusbarnotificationbackgroundtype/rawrepresentable-implementations"], "traits": [{"interfaceLanguage": "swift"}]}], "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle", "navigatorTitle": [{"text": "StatusBarNotificationStyle", "kind": "identifier"}], "role": "symbol", "type": "topic", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "title": "StatusBarNotificationStyle", "abstract": [{"text": "A Style defines the appearance of a notification.", "type": "text"}], "fragments": [{"kind": "keyword", "text": "class"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationStyle", "kind": "identifier"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType", "fragments": [{"kind": "keyword", "text": "enum"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationBackgroundType", "kind": "identifier"}], "title": "StatusBarNotificationBackgroundType", "abstract": [{"text": "Defines the appearance of the notification background.", "type": "text"}], "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationBackgroundType"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundtype", "type": "topic", "kind": "symbol", "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/hashValue": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundtype/hashvalue", "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/hashValue", "title": "hashValue", "fragments": [{"text": "var", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "hashValue", "kind": "identifier"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "s:<PERSON>", "text": "Int"}], "abstract": [], "conformance": {"constraints": [{"type": "codeVoice", "code": "Self"}, {"type": "text", "text": " conforms to "}, {"type": "codeVoice", "code": "<PERSON><PERSON><PERSON>"}, {"type": "text", "text": " and "}, {"code": "RawValue", "type": "codeVoice"}, {"text": " conforms to ", "type": "text"}, {"code": "<PERSON><PERSON><PERSON>", "type": "codeVoice"}, {"type": "text", "text": "."}], "availabilityPrefix": [{"type": "text", "text": "Available when"}], "conformancePrefix": [{"text": "Conforms when", "type": "text"}]}, "kind": "symbol", "type": "topic"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/hash(into:)": {"role": "symbol", "type": "topic", "title": "hash(into:)", "abstract": [], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/hash(into:)", "url": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundtype/hash(into:)", "kind": "symbol", "fragments": [{"text": "func", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "hash"}, {"text": "(", "kind": "text"}, {"kind": "externalParam", "text": "into"}, {"kind": "text", "text": ": "}, {"text": "inout", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "<PERSON><PERSON>", "kind": "typeIdentifier", "preciseIdentifier": "s:s6HasherV"}, {"text": ")", "kind": "text"}], "conformance": {"conformancePrefix": [{"type": "text", "text": "Conforms when"}], "constraints": [{"code": "Self", "type": "codeVoice"}, {"type": "text", "text": " conforms to "}, {"code": "<PERSON><PERSON><PERSON>", "type": "codeVoice"}, {"text": " and ", "type": "text"}, {"code": "RawValue", "type": "codeVoice"}, {"text": " conforms to ", "type": "text"}, {"code": "<PERSON><PERSON><PERSON>", "type": "codeVoice"}, {"text": ".", "type": "text"}], "availabilityPrefix": [{"text": "Available when", "type": "text"}]}}}}