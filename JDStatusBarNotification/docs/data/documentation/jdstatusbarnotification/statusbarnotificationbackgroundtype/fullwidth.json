{"metadata": {"modules": [{"name": "JDStatusBarNotification"}], "roleHeading": "Case", "externalID": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationBackgroundType@JDStatusBarNotificationBackgroundTypeFullWidth", "role": "symbol", "title": "StatusBarNotificationBackgroundType.fullWidth", "fragments": [{"kind": "keyword", "text": "case"}, {"kind": "text", "text": " "}, {"text": "fullWidth", "kind": "identifier"}], "symbolKind": "case"}, "kind": "symbol", "identifier": {"url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/fullWidth", "interfaceLanguage": "swift"}, "variants": [{"paths": ["/documentation/jdstatusbarnotification/statusbarnotificationbackgroundtype/fullwidth"], "traits": [{"interfaceLanguage": "swift"}]}], "abstract": [{"type": "text", "text": "The background covers the full display width and the full status bar + navbar height."}], "primaryContentSections": [{"kind": "declarations", "declarations": [{"languages": ["swift"], "platforms": ["iOS"], "tokens": [{"text": "case", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "fullWidth", "kind": "identifier"}]}]}], "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType"]]}, "schemaVersion": {"major": 0, "patch": 0, "minor": 3}, "sections": [], "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle", "navigatorTitle": [{"text": "StatusBarNotificationStyle", "kind": "identifier"}], "role": "symbol", "type": "topic", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "title": "StatusBarNotificationStyle", "abstract": [{"text": "A Style defines the appearance of a notification.", "type": "text"}], "fragments": [{"kind": "keyword", "text": "class"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationStyle", "kind": "identifier"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/fullWidth": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/fullWidth", "role": "symbol", "title": "StatusBarNotificationBackgroundType.fullWidth", "type": "topic", "url": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundtype/fullwidth", "abstract": [{"type": "text", "text": "The background covers the full display width and the full status bar + navbar height."}], "kind": "symbol", "fragments": [{"text": "case", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "fullWidth", "kind": "identifier"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType", "fragments": [{"kind": "keyword", "text": "enum"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationBackgroundType", "kind": "identifier"}], "title": "StatusBarNotificationBackgroundType", "abstract": [{"text": "Defines the appearance of the notification background.", "type": "text"}], "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationBackgroundType"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundtype", "type": "topic", "kind": "symbol", "role": "symbol"}}}