{"hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/Equatable-Implementations"]]}, "abstract": [{"type": "text", "text": "Inherited from "}, {"code": "Equatable.!=(_:_:)", "type": "codeVoice"}, {"type": "text", "text": "."}], "sections": [], "metadata": {"modules": [{"relatedModules": ["Swift"], "name": "JDStatusBarNotification"}], "symbolKind": "op", "title": "!=(_:_:)", "roleHeading": "Operator", "externalID": "s:SQsE2neoiySbx_xtFZ::SYNTHESIZED::c:@M@JDStatusBarNotification@E@JDStatusBarNotificationBackgroundType", "extendedModule": "Swift", "fragments": [{"kind": "keyword", "text": "static"}, {"text": " ", "kind": "text"}, {"kind": "keyword", "text": "func"}, {"kind": "text", "text": " "}, {"text": "!=", "kind": "identifier"}, {"text": " ", "kind": "text"}, {"text": "(", "kind": "text"}, {"text": "Self", "kind": "typeIdentifier"}, {"kind": "text", "text": ", "}, {"text": "Self", "kind": "typeIdentifier"}, {"text": ") -> ", "kind": "text"}, {"text": "Bool", "preciseIdentifier": "s:Sb", "kind": "typeIdentifier"}], "role": "symbol"}, "primaryContentSections": [{"kind": "declarations", "declarations": [{"languages": ["swift"], "platforms": ["iOS"], "tokens": [{"text": "static", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "keyword", "text": "func"}, {"kind": "text", "text": " "}, {"text": "!=", "kind": "identifier"}, {"kind": "text", "text": " "}, {"text": "(", "kind": "text"}, {"text": "lhs", "kind": "internalParam"}, {"text": ": ", "kind": "text"}, {"text": "Self", "kind": "typeIdentifier"}, {"text": ", ", "kind": "text"}, {"kind": "internalParam", "text": "rhs"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "text": "Self"}, {"text": ") -> ", "kind": "text"}, {"preciseIdentifier": "s:Sb", "kind": "typeIdentifier", "text": "Bool"}]}]}], "variants": [{"paths": ["/documentation/jdstatusbarnotification/statusbarnotificationbackgroundtype/!=(_:_:)"], "traits": [{"interfaceLanguage": "swift"}]}], "schemaVersion": {"minor": 3, "major": 0, "patch": 0}, "kind": "symbol", "identifier": {"interfaceLanguage": "swift", "url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/!=(_:_:)"}, "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle", "abstract": [{"type": "text", "text": "A Style defines the appearance of a notification."}], "fragments": [{"text": "class", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "StatusBarNotificationStyle", "kind": "identifier"}], "kind": "symbol", "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationStyle"}], "type": "topic", "role": "symbol", "title": "StatusBarNotificationStyle"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType", "fragments": [{"kind": "keyword", "text": "enum"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationBackgroundType", "kind": "identifier"}], "title": "StatusBarNotificationBackgroundType", "abstract": [{"text": "Defines the appearance of the notification background.", "type": "text"}], "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationBackgroundType"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundtype", "type": "topic", "kind": "symbol", "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/Equatable-Implementations": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/Equatable-Implementations", "role": "collectionGroup", "type": "topic", "abstract": [], "kind": "article", "url": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundtype/equatable-implementations", "title": "Equatable Implementations"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/!=(_:_:)": {"fragments": [{"kind": "keyword", "text": "static"}, {"text": " ", "kind": "text"}, {"text": "func", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "!="}, {"text": " ", "kind": "text"}, {"text": "(", "kind": "text"}, {"text": "Self", "kind": "typeIdentifier"}, {"text": ", ", "kind": "text"}, {"text": "Self", "kind": "typeIdentifier"}, {"text": ") -> ", "kind": "text"}, {"kind": "typeIdentifier", "text": "Bool", "preciseIdentifier": "s:Sb"}], "kind": "symbol", "title": "!=(_:_:)", "type": "topic", "url": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundtype/!=(_:_:)", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/!=(_:_:)", "abstract": [], "role": "symbol"}}}