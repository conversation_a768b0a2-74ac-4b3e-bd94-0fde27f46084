{"variants": [{"traits": [{"interfaceLanguage": "swift"}], "paths": ["/documentation/jdstatusbarnotification/statusbarnotificationbackgroundtype/equatable-implementations"]}], "schemaVersion": {"minor": 3, "patch": 0, "major": 0}, "identifier": {"url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/Equatable-Implementations", "interfaceLanguage": "swift"}, "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType"]]}, "topicSections": [{"generated": true, "identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/!=(_:_:)"], "title": "Operators"}], "metadata": {"modules": [{"name": "JDStatusBarNotification"}], "title": "Equatable Implementations", "role": "collectionGroup"}, "sections": [], "kind": "article", "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle", "abstract": [{"type": "text", "text": "A Style defines the appearance of a notification."}], "fragments": [{"text": "class", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "StatusBarNotificationStyle", "kind": "identifier"}], "kind": "symbol", "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationStyle"}], "type": "topic", "role": "symbol", "title": "StatusBarNotificationStyle"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/!=(_:_:)": {"fragments": [{"kind": "keyword", "text": "static"}, {"text": " ", "kind": "text"}, {"text": "func", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "!="}, {"text": " ", "kind": "text"}, {"text": "(", "kind": "text"}, {"text": "Self", "kind": "typeIdentifier"}, {"text": ", ", "kind": "text"}, {"text": "Self", "kind": "typeIdentifier"}, {"text": ") -> ", "kind": "text"}, {"kind": "typeIdentifier", "text": "Bool", "preciseIdentifier": "s:Sb"}], "kind": "symbol", "title": "!=(_:_:)", "type": "topic", "url": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundtype/!=(_:_:)", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/!=(_:_:)", "abstract": [], "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType": {"kind": "symbol", "navigatorTitle": [{"text": "StatusBarNotificationBackgroundType", "kind": "identifier"}], "fragments": [{"text": "enum", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "StatusBarNotificationBackgroundType", "kind": "identifier"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType", "abstract": [{"type": "text", "text": "Defines the appearance of the notification background."}], "role": "symbol", "title": "StatusBarNotificationBackgroundType", "type": "topic", "url": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundtype"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}}}