{"sections": [], "kind": "symbol", "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType"]]}, "identifier": {"url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/init(rawValue:)", "interfaceLanguage": "swift"}, "abstract": [{"text": "Inherited from ", "type": "text"}, {"code": "RawRepresentable.init(rawValue:)", "type": "codeVoice"}, {"text": ".", "type": "text"}], "primaryContentSections": [{"kind": "declarations", "declarations": [{"languages": ["swift"], "platforms": ["iOS"], "tokens": [{"kind": "keyword", "text": "init"}, {"text": "?(", "kind": "text"}, {"text": "rawValue", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"text": "Int", "kind": "typeIdentifier", "preciseIdentifier": "s:<PERSON>"}, {"text": ")", "kind": "text"}]}]}], "variants": [{"paths": ["/documentation/jdstatusbarnotification/statusbarnotificationbackgroundtype/init(rawvalue:)"], "traits": [{"interfaceLanguage": "swift"}]}], "metadata": {"modules": [{"name": "JDStatusBarNotification"}], "title": "init(rawValue:)", "roleHeading": "Initializer", "role": "symbol", "fragments": [{"kind": "identifier", "text": "init"}, {"text": "?(", "kind": "text"}, {"text": "rawValue", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "s:<PERSON>", "kind": "typeIdentifier", "text": "Int"}, {"text": ")", "kind": "text"}], "symbolKind": "init", "externalID": "s:23JDStatusBarNotification06StatusbC14BackgroundTypeO8rawValueACSgSi_tcfc"}, "schemaVersion": {"minor": 3, "major": 0, "patch": 0}, "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle", "navigatorTitle": [{"text": "StatusBarNotificationStyle", "kind": "identifier"}], "role": "symbol", "type": "topic", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "title": "StatusBarNotificationStyle", "abstract": [{"text": "A Style defines the appearance of a notification.", "type": "text"}], "fragments": [{"kind": "keyword", "text": "class"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationStyle", "kind": "identifier"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType", "fragments": [{"kind": "keyword", "text": "enum"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationBackgroundType", "kind": "identifier"}], "title": "StatusBarNotificationBackgroundType", "abstract": [{"text": "Defines the appearance of the notification background.", "type": "text"}], "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationBackgroundType"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundtype", "type": "topic", "kind": "symbol", "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/init(rawValue:)": {"type": "topic", "role": "symbol", "title": "init(rawValue:)", "url": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundtype/init(rawvalue:)", "fragments": [{"kind": "identifier", "text": "init"}, {"text": "?(", "kind": "text"}, {"kind": "externalParam", "text": "rawValue"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "s:<PERSON>", "text": "Int", "kind": "typeIdentifier"}, {"text": ")", "kind": "text"}], "abstract": [], "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/init(rawValue:)"}}}