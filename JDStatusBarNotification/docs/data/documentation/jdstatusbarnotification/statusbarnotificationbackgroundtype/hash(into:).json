{"primaryContentSections": [{"kind": "declarations", "declarations": [{"platforms": ["iOS"], "tokens": [{"kind": "keyword", "text": "func"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "hash"}, {"text": "(", "kind": "text"}, {"kind": "externalParam", "text": "into"}, {"text": " ", "kind": "text"}, {"text": "hasher", "kind": "internalParam"}, {"kind": "text", "text": ": "}, {"kind": "keyword", "text": "inout"}, {"text": " ", "kind": "text"}, {"kind": "typeIdentifier", "text": "<PERSON><PERSON>", "preciseIdentifier": "s:s6HasherV"}, {"kind": "text", "text": ")"}], "languages": ["swift"]}]}], "variants": [{"traits": [{"interfaceLanguage": "swift"}], "paths": ["/documentation/jdstatusbarnotification/statusbarnotificationbackgroundtype/hash(into:)"]}], "sections": [], "schemaVersion": {"major": 0, "patch": 0, "minor": 3}, "identifier": {"url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/hash(into:)", "interfaceLanguage": "swift"}, "kind": "symbol", "metadata": {"roleHeading": "Instance Method", "modules": [{"name": "JDStatusBarNotification", "relatedModules": ["Swift"]}], "extendedModule": "Swift", "title": "hash(into:)", "externalID": "s:SYsSHRzSH8RawValueSYRpzrlE4hash4intoys6HasherVz_tF::SYNTHESIZED::c:@M@JDStatusBarNotification@E@JDStatusBarNotificationBackgroundType", "role": "symbol", "symbolKind": "method", "conformance": {"constraints": [{"type": "codeVoice", "code": "Self"}, {"type": "text", "text": " conforms to "}, {"code": "<PERSON><PERSON><PERSON>", "type": "codeVoice"}, {"text": " and ", "type": "text"}, {"type": "codeVoice", "code": "RawValue"}, {"type": "text", "text": " conforms to "}, {"type": "codeVoice", "code": "<PERSON><PERSON><PERSON>"}, {"type": "text", "text": "."}], "conformancePrefix": [{"type": "text", "text": "Conforms when"}], "availabilityPrefix": [{"text": "Available when", "type": "text"}]}, "fragments": [{"kind": "keyword", "text": "func"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "hash"}, {"text": "(", "kind": "text"}, {"text": "into", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"kind": "keyword", "text": "inout"}, {"text": " ", "kind": "text"}, {"text": "<PERSON><PERSON>", "kind": "typeIdentifier", "preciseIdentifier": "s:s6HasherV"}, {"kind": "text", "text": ")"}]}, "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/RawRepresentable-Implementations"]]}, "abstract": [{"type": "text", "text": "Inherited from "}, {"code": "RawRepresentable.hash(into:)", "type": "codeVoice"}, {"type": "text", "text": "."}], "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType", "fragments": [{"kind": "keyword", "text": "enum"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationBackgroundType", "kind": "identifier"}], "title": "StatusBarNotificationBackgroundType", "abstract": [{"text": "Defines the appearance of the notification background.", "type": "text"}], "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationBackgroundType"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundtype", "type": "topic", "kind": "symbol", "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/RawRepresentable-Implementations": {"role": "collectionGroup", "type": "topic", "title": "RawRepresentable Implementations", "url": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundtype/rawrepresentable-implementations", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/RawRepresentable-Implementations", "abstract": [], "kind": "article"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle", "navigatorTitle": [{"text": "StatusBarNotificationStyle", "kind": "identifier"}], "role": "symbol", "type": "topic", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "title": "StatusBarNotificationStyle", "abstract": [{"text": "A Style defines the appearance of a notification.", "type": "text"}], "fragments": [{"kind": "keyword", "text": "class"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationStyle", "kind": "identifier"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/hash(into:)": {"role": "symbol", "type": "topic", "title": "hash(into:)", "abstract": [], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/hash(into:)", "url": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundtype/hash(into:)", "kind": "symbol", "fragments": [{"text": "func", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "hash"}, {"text": "(", "kind": "text"}, {"kind": "externalParam", "text": "into"}, {"kind": "text", "text": ": "}, {"text": "inout", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "<PERSON><PERSON>", "kind": "typeIdentifier", "preciseIdentifier": "s:s6HasherV"}, {"text": ")", "kind": "text"}], "conformance": {"conformancePrefix": [{"type": "text", "text": "Conforms when"}], "constraints": [{"code": "Self", "type": "codeVoice"}, {"type": "text", "text": " conforms to "}, {"code": "<PERSON><PERSON><PERSON>", "type": "codeVoice"}, {"text": " and ", "type": "text"}, {"code": "RawValue", "type": "codeVoice"}, {"text": " conforms to ", "type": "text"}, {"code": "<PERSON><PERSON><PERSON>", "type": "codeVoice"}, {"text": ".", "type": "text"}], "availabilityPrefix": [{"text": "Available when", "type": "text"}]}}}}