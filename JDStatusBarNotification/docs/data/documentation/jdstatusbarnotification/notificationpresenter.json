{"kind": "symbol", "topicSections": [{"title": "Retrieve the presenter", "identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/shared"]}, {"identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/Completion", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/present(_:subtitle:styleName:duration:completion:)", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/present(_:subtitle:includedStyle:duration:completion:)", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle"], "title": "Present a notification"}, {"identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/presentCustomView(_:sizingController:styleName:completion:)", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/presentSwiftView(styleName:viewBuilder:completion:)", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenterCustomViewSizingController"], "title": "Present a notification (using a custom view)"}, {"identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/dismiss(animated:after:completion:)"], "title": "Dismiss a notification"}, {"title": "Customize the style (Appearance & Behavior)", "identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/PrepareStyleClosure", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/updateDefaultStyle(_:)", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/addStyle(named:usingStyle:prepare:)"]}, {"identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/displayProgressBar(at:)", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/animateProgressBar(to:duration:completion:)", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/displayActivityIndicator(_:)", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/displayLeftView(_:)"], "title": "Display supplementary views"}, {"identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/isVisible", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/updateTitle(_:)", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/updateSubtitle(_:)", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/setWindowScene(_:)"], "title": "Additional Presenter APIs"}, {"abstract": [{"type": "text", "text": "These exist for backwards compatibility with the previous objc API. In Swift use the APIs listed above instead."}], "title": "Legacy API support (for objc only)", "identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:)", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:c:)", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:d:)", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:st:c:)", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:s:)", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:s:c:)", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:d:s:)", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:st:s:c:)", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:cu:)", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:cu:c:)", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:d:cu:)", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:st:cu:c:)", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(cv:s:c:)", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zld()", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zld(a:)", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zld(c:)", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zld(d:)", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zld(d:c:)", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zld(a:d:c:)", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlas(n:p:)"]}], "variants": [{"paths": ["/documentation/jdstatusbarnotification/notificationpresenter"], "traits": [{"interfaceLanguage": "swift"}]}], "metadata": {"symbolKind": "class", "navigatorTitle": [{"text": "NotificationPresenter", "kind": "identifier"}], "role": "symbol", "title": "NotificationPresenter", "modules": [{"name": "JDStatusBarNotification"}], "externalID": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationPresenter", "roleHeading": "Class", "fragments": [{"text": "class", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "NotificationPresenter", "kind": "identifier"}]}, "identifier": {"interfaceLanguage": "swift", "url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter"}, "schemaVersion": {"minor": 3, "patch": 0, "major": 0}, "sections": [], "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification"]]}, "primaryContentSections": [{"declarations": [{"platforms": ["iOS"], "tokens": [{"text": "@objc", "kind": "attribute"}, {"kind": "text", "text": "(JDStatusBarNotificationPresenter) "}, {"text": "class", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "NotificationPresenter"}], "languages": ["swift"]}], "kind": "declarations"}, {"kind": "content", "content": [{"type": "heading", "text": "Overview", "anchor": "overview", "level": 2}, {"type": "paragraph", "inlineContent": [{"type": "text", "text": "To customize the appearance, see the "}, {"type": "emphasis", "inlineContent": [{"type": "text", "text": "Customize the style"}]}, {"type": "text", "text": " section. To see all customization"}, {"type": "text", "text": " "}, {"type": "text", "text": "options, see the "}, {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "isActive": true, "type": "reference"}, {"type": "text", "text": " documentation."}]}, {"type": "paragraph", "inlineContent": [{"text": "While a notification is displayed, a separate window is presented on top of your application", "type": "text"}, {"text": " ", "type": "text"}, {"text": "window. Upon dismissal this window, its view controller and all its views are removed from", "type": "text"}, {"text": " ", "type": "text"}, {"type": "text", "text": "memory. The presenter class itself is a singleton which will stay in memory for the lifetime of"}, {"type": "text", "text": " "}, {"type": "text", "text": "your application once it was created. The default "}, {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "isActive": true, "type": "reference"}, {"type": "text", "text": " and any styles"}, {"type": "text", "text": " "}, {"text": "added by the user also stay in memory permanently.", "type": "text"}]}]}], "relationshipsSections": [{"title": "Inherits From", "identifiers": ["doc://calimarkus.JDStatusBarNotification/objc(cs)NSObject"], "kind": "relationships", "type": "inheritsFrom"}, {"type": "conformsTo", "identifiers": ["doc://calimarkus.JDStatusBarNotification/objc(pl)NSObject", "doc://calimarkus.JDStatusBarNotification/s7CVarArgP", "doc://calimarkus.JDStatusBarNotification/s28CustomDebugStringConvertibleP", "doc://calimarkus.JDStatusBarNotification/s23CustomStringConvertibleP", "doc://calimarkus.JDStatusBarNotification/SQ", "doc://calimarkus.JDStatusBarNotification/SH"], "title": "Conforms To", "kind": "relationships"}], "abstract": [{"type": "text", "text": "The NotificationPresenter let’s you present notifications below the statusBar."}, {"type": "text", "text": " "}, {"type": "text", "text": "You can customize the style (colors, fonts, etc.) and animations. It supports notch"}, {"type": "text", "text": " "}, {"text": "and no-notch devices, landscape & portrait layouts and Drag-to-Dismiss. It can display a", "type": "text"}, {"type": "text", "text": " "}, {"text": "title, a subtitle, an activity indicator, an animated progress bar & custom views out of the box.", "type": "text"}], "references": {"doc://calimarkus.JDStatusBarNotification/s7CVarArgP": {"type": "unresolvable", "title": "Swift.CVarArg", "identifier": "doc://calimarkus.JDStatusBarNotification/s7CVarArgP"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenterCustomViewSizingController": {"abstract": [{"text": "A protocol for a custom controller, which controls the size of a presented custom view.", "type": "text"}], "role": "symbol", "type": "topic", "navigatorTitle": [{"kind": "identifier", "text": "NotificationPresenterCustomViewSizingController"}], "url": "/documentation/jdstatusbarnotification/notificationpresentercustomviewsizingcontroller", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenterCustomViewSizingController", "kind": "symbol", "fragments": [{"kind": "keyword", "text": "protocol"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "NotificationPresenterCustomViewSizingController"}], "title": "NotificationPresenterCustomViewSizingController"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:st:cu:c:)": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:st:cu:c:)", "abstract": [{"text": "Present a notification using a custom style.", "type": "text"}], "title": "zlp(t:st:cu:c:)", "type": "topic", "kind": "symbol", "fragments": [{"kind": "keyword", "text": "func"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "zlp"}, {"text": "(", "kind": "text"}, {"kind": "externalParam", "text": "t"}, {"text": ": ", "kind": "text"}, {"preciseIdentifier": "s:SS", "text": "String", "kind": "typeIdentifier"}, {"text": ", ", "kind": "text"}, {"text": "st", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "text": "String", "preciseIdentifier": "s:SS"}, {"text": "?, ", "kind": "text"}, {"kind": "externalParam", "text": "cu"}, {"kind": "text", "text": ": "}, {"text": "String", "kind": "typeIdentifier", "preciseIdentifier": "s:SS"}, {"text": "?, ", "kind": "text"}, {"text": "c", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "text": "Completion", "preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona"}, {"text": "?) -> ", "kind": "text"}, {"preciseIdentifier": "c:objc(cs)UIView", "text": "UIView", "kind": "typeIdentifier"}], "url": "/documentation/jdstatusbarnotification/notificationpresenter/zlp(t:st:cu:c:)", "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/updateTitle(_:)": {"abstract": [{"text": "Updates the title of an existing notification without animation.", "type": "text"}], "url": "/documentation/jdstatusbarnotification/notificationpresenter/updatetitle(_:)", "fragments": [{"kind": "keyword", "text": "func"}, {"text": " ", "kind": "text"}, {"text": "updateTitle", "kind": "identifier"}, {"text": "(", "kind": "text"}, {"preciseIdentifier": "s:SS", "text": "String", "kind": "typeIdentifier"}, {"text": ")", "kind": "text"}], "kind": "symbol", "title": "updateTitle(_:)", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/updateTitle(_:)", "type": "topic", "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/present(_:subtitle:includedStyle:duration:completion:)": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/present(_:subtitle:includedStyle:duration:completion:)", "url": "/documentation/jdstatusbarnotification/notificationpresenter/present(_:subtitle:includedstyle:duration:completion:)", "kind": "symbol", "title": "present(_:subtitle:includedStyle:duration:completion:)", "type": "topic", "abstract": [{"text": "Present a notification using an included style.", "type": "text"}], "role": "symbol", "fragments": [{"kind": "keyword", "text": "func"}, {"text": " ", "kind": "text"}, {"text": "present", "kind": "identifier"}, {"kind": "text", "text": "("}, {"text": "String", "kind": "typeIdentifier", "preciseIdentifier": "s:SS"}, {"kind": "text", "text": ", "}, {"text": "subtitle", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "s:SS", "kind": "typeIdentifier", "text": "String"}, {"kind": "text", "text": "?, "}, {"text": "includedStyle", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationIncludedStyle", "kind": "typeIdentifier", "text": "IncludedStatusBarNotificationStyle"}, {"text": ", ", "kind": "text"}, {"kind": "externalParam", "text": "duration"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "s:Sd", "text": "Double"}, {"text": "?, ", "kind": "text"}, {"text": "completion", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona", "text": "Completion"}, {"kind": "text", "text": "?) -> "}, {"kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIView", "text": "UIView"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/updateDefaultStyle(_:)": {"role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/updateDefaultStyle(_:)", "title": "updateDefaultStyle(_:)", "kind": "symbol", "url": "/documentation/jdstatusbarnotification/notificationpresenter/updatedefaultstyle(_:)", "abstract": [{"type": "text", "text": "Defines a new default style."}, {"text": " ", "type": "text"}, {"text": "The new style will be used in all future presentations that have no specific style specified.", "type": "text"}], "fragments": [{"text": "func", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "updateDefaultStyle", "kind": "identifier"}, {"text": "(", "kind": "text"}, {"preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC19PrepareStyleClosurea", "text": "PrepareStyleClosure", "kind": "typeIdentifier"}, {"text": ")", "kind": "text"}], "type": "topic"}, "doc://calimarkus.JDStatusBarNotification/objc(cs)NSObject": {"type": "unresolvable", "title": "ObjectiveC.NSObject", "identifier": "doc://calimarkus.JDStatusBarNotification/objc(cs)NSObject"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:s:)": {"kind": "symbol", "type": "topic", "title": "zlp(t:s:)", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:s:)", "fragments": [{"text": "func", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "zlp"}, {"kind": "text", "text": "("}, {"text": "t", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"text": "String", "preciseIdentifier": "s:SS", "kind": "typeIdentifier"}, {"text": ", ", "kind": "text"}, {"text": "s", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationIncludedStyle", "text": "IncludedStatusBarNotificationStyle"}, {"kind": "text", "text": ") -> "}, {"text": "UIView", "kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIView"}], "role": "symbol", "abstract": [{"type": "text", "text": "Present a notification using an included style."}], "url": "/documentation/jdstatusbarnotification/notificationpresenter/zlp(t:s:)"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:cu:)": {"role": "symbol", "kind": "symbol", "type": "topic", "fragments": [{"kind": "keyword", "text": "func"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "zlp"}, {"text": "(", "kind": "text"}, {"kind": "externalParam", "text": "t"}, {"text": ": ", "kind": "text"}, {"text": "String", "preciseIdentifier": "s:SS", "kind": "typeIdentifier"}, {"kind": "text", "text": ", "}, {"text": "cu", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"preciseIdentifier": "s:SS", "text": "String", "kind": "typeIdentifier"}, {"text": ") -> ", "kind": "text"}, {"preciseIdentifier": "c:objc(cs)UIView", "text": "UIView", "kind": "typeIdentifier"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:cu:)", "title": "zlp(t:cu:)", "abstract": [{"type": "text", "text": "Present a notification using a custom style."}], "url": "/documentation/jdstatusbarnotification/notificationpresenter/zlp(t:cu:)"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(cv:s:c:)": {"abstract": [{"type": "text", "text": "Present a notification using a custom subview."}], "type": "topic", "role": "symbol", "kind": "symbol", "fragments": [{"kind": "keyword", "text": "func"}, {"text": " ", "kind": "text"}, {"text": "zlp", "kind": "identifier"}, {"text": "(", "kind": "text"}, {"kind": "externalParam", "text": "cv"}, {"text": ": ", "kind": "text"}, {"text": "UIView", "kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIView"}, {"text": ", ", "kind": "text"}, {"kind": "externalParam", "text": "s"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "text": "String", "preciseIdentifier": "s:SS"}, {"kind": "text", "text": "?, "}, {"kind": "externalParam", "text": "c"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona", "text": "Completion"}, {"kind": "text", "text": "?) -> "}, {"kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIView", "text": "UIView"}], "url": "/documentation/jdstatusbarnotification/notificationpresenter/zlp(cv:s:c:)", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(cv:s:c:)", "title": "zlp(cv:s:c:)"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/animateProgressBar(to:duration:completion:)": {"type": "topic", "role": "symbol", "abstract": [{"type": "text", "text": "Displays a progress bar and animates it to the provided "}, {"code": "percentage", "type": "codeVoice"}, {"type": "text", "text": "."}], "title": "animateProgressBar(to:duration:completion:)", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/animateProgressBar(to:duration:completion:)", "url": "/documentation/jdstatusbarnotification/notificationpresenter/animateprogressbar(to:duration:completion:)", "fragments": [{"text": "func", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "animateProgressBar", "kind": "identifier"}, {"kind": "text", "text": "("}, {"text": "to", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"text": "Double", "preciseIdentifier": "s:Sd", "kind": "typeIdentifier"}, {"text": ", ", "kind": "text"}, {"text": "duration", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "s:Sd", "text": "Double", "kind": "typeIdentifier"}, {"kind": "text", "text": ", "}, {"kind": "externalParam", "text": "completion"}, {"text": ": ", "kind": "text"}, {"text": "Completion", "kind": "typeIdentifier", "preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona"}, {"kind": "text", "text": "?)"}], "kind": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle": {"abstract": [{"text": "These included styles let you easily change the visual appearance of a", "type": "text"}, {"type": "text", "text": " "}, {"type": "text", "text": "notification without creating your own custom style."}], "fragments": [{"text": "enum", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "IncludedStatusBarNotificationStyle", "kind": "identifier"}], "navigatorTitle": [{"kind": "identifier", "text": "IncludedStatusBarNotificationStyle"}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle", "url": "/documentation/jdstatusbarnotification/includedstatusbarnotificationstyle", "type": "topic", "kind": "symbol", "title": "IncludedStatusBarNotificationStyle"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/Completion": {"kind": "symbol", "abstract": [{"type": "text", "text": "Called upon animation completion."}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/Completion", "url": "/documentation/jdstatusbarnotification/notificationpresenter/completion", "fragments": [{"text": "typealias", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "Completion"}], "navigatorTitle": [{"text": "Completion", "kind": "identifier"}], "title": "NotificationPresenter.Completion", "type": "topic"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/displayActivityIndicator(_:)": {"abstract": [{"type": "text", "text": "Displays an activity indicator as the notifications left view."}], "role": "symbol", "type": "topic", "url": "/documentation/jdstatusbarnotification/notificationpresenter/displayactivityindicator(_:)", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/displayActivityIndicator(_:)", "kind": "symbol", "fragments": [{"kind": "keyword", "text": "func"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "displayActivityIndicator"}, {"kind": "text", "text": "("}, {"kind": "typeIdentifier", "preciseIdentifier": "s:Sb", "text": "Bool"}, {"kind": "text", "text": ")"}], "title": "displayActivityIndicator(_:)"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/displayLeftView(_:)": {"type": "topic", "url": "/documentation/jdstatusbarnotification/notificationpresenter/displayleftview(_:)", "abstract": [{"text": "Displays a view on the left side of the text.", "type": "text"}, {"type": "text", "text": " "}, {"type": "text", "text": "The layout is defined by the "}, {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle", "isActive": true, "type": "reference"}, {"type": "text", "text": "."}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/displayLeftView(_:)", "kind": "symbol", "fragments": [{"text": "func", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "displayLeftView"}, {"text": "(", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIView", "text": "UIView"}, {"kind": "text", "text": "?)"}], "title": "displayLeftView(_:)", "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:cu:c:)": {"url": "/documentation/jdstatusbarnotification/notificationpresenter/zlp(t:cu:c:)", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:cu:c:)", "title": "zlp(t:cu:c:)", "fragments": [{"text": "func", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "zlp", "kind": "identifier"}, {"kind": "text", "text": "("}, {"text": "t", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"text": "String", "preciseIdentifier": "s:SS", "kind": "typeIdentifier"}, {"kind": "text", "text": ", "}, {"text": "cu", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "s:SS", "text": "String", "kind": "typeIdentifier"}, {"text": "?, ", "kind": "text"}, {"text": "c", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"kind": "typeIdentifier", "text": "Completion", "preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona"}, {"text": "?) -> ", "kind": "text"}, {"text": "UIView", "preciseIdentifier": "c:objc(cs)UIView", "kind": "typeIdentifier"}], "role": "symbol", "abstract": [{"type": "text", "text": "Present a notification using a custom style."}], "type": "topic", "kind": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zld(c:)": {"type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zld(c:)", "kind": "symbol", "title": "zld(c:)", "abstract": [{"text": "Dismisses the displayed notification.", "type": "text"}], "role": "symbol", "url": "/documentation/jdstatusbarnotification/notificationpresenter/zld(c:)", "fragments": [{"text": "func", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "zld", "kind": "identifier"}, {"kind": "text", "text": "("}, {"kind": "externalParam", "text": "c"}, {"kind": "text", "text": ": "}, {"text": "Completion", "preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona", "kind": "typeIdentifier"}, {"text": "?)", "kind": "text"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/setWindowScene(_:)": {"url": "/documentation/jdstatusbarnotification/notificationpresenter/setwindowscene(_:)", "abstract": [{"text": "Lets you set an explicit ", "type": "text"}, {"type": "codeVoice", "code": "UIWindowScene"}, {"text": ", in which notifications should be presented in. In most cases you don’t need to set this.", "type": "text"}], "fragments": [{"text": "func", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "setWindowScene", "kind": "identifier"}, {"text": "(", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIWindowScene", "text": "UIWindowScene"}, {"text": "?)", "kind": "text"}], "kind": "symbol", "title": "setWindowScene(_:)", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/setWindowScene(_:)", "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/presentSwiftView(styleName:viewBuilder:completion:)": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/presentSwiftView(styleName:viewBuilder:completion:)", "type": "topic", "fragments": [{"kind": "keyword", "text": "func"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "presentSwiftView"}, {"kind": "text", "text": "("}, {"kind": "externalParam", "text": "styleName"}, {"kind": "text", "text": ": "}, {"text": "String", "preciseIdentifier": "s:SS", "kind": "typeIdentifier"}, {"text": "?, ", "kind": "text"}, {"kind": "externalParam", "text": "viewBuilder"}, {"text": ": () -> ", "kind": "text"}, {"text": "some", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"preciseIdentifier": "s:7SwiftUI4ViewP", "text": "View", "kind": "typeIdentifier"}, {"text": ", ", "kind": "text"}, {"text": "completion", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"kind": "typeIdentifier", "text": "Completion", "preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona"}, {"kind": "text", "text": "?) -> "}, {"text": "UIView", "kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIView"}], "abstract": [{"text": "Present a notification using a custom SwiftUI view.", "type": "text"}], "title": "presentSwiftView(styleName:viewBuilder:completion:)", "role": "symbol", "url": "/documentation/jdstatusbarnotification/notificationpresenter/presentswiftview(stylename:viewbuilder:completion:)", "kind": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/PrepareStyleClosure": {"title": "NotificationPresenter.PrepareStyleClosure", "navigatorTitle": [{"text": "PrepareStyleClosure", "kind": "identifier"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/PrepareStyleClosure", "type": "topic", "kind": "symbol", "abstract": [{"text": "Creates a modified copy of an existing ", "type": "text"}, {"isActive": true, "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "type": "reference"}, {"type": "text", "text": " instance."}], "url": "/documentation/jdstatusbarnotification/notificationpresenter/preparestyleclosure", "fragments": [{"text": "typealias", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "PrepareStyleClosure"}], "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/presentCustomView(_:sizingController:styleName:completion:)": {"abstract": [{"text": "Present a notification using a custom subview.", "type": "text"}], "kind": "symbol", "role": "symbol", "type": "topic", "title": "presentCustomView(_:sizingController:styleName:completion:)", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/presentCustomView(_:sizingController:styleName:completion:)", "fragments": [{"text": "func", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "presentCustom<PERSON>iew", "kind": "identifier"}, {"kind": "text", "text": "("}, {"kind": "typeIdentifier", "text": "UIView", "preciseIdentifier": "c:objc(cs)UIView"}, {"kind": "text", "text": ", "}, {"kind": "externalParam", "text": "sizingController"}, {"text": ": ", "kind": "text"}, {"preciseIdentifier": "c:@M@JDStatusBarNotification@objc(pl)JDStatusBarNotificationPresenterCustomViewSizingController", "text": "NotificationPresenterCustomViewSizingController", "kind": "typeIdentifier"}, {"kind": "text", "text": "?, "}, {"text": "styleName", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"text": "String", "kind": "typeIdentifier", "preciseIdentifier": "s:SS"}, {"text": "?, ", "kind": "text"}, {"kind": "externalParam", "text": "completion"}, {"kind": "text", "text": ": "}, {"kind": "typeIdentifier", "preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona", "text": "Completion"}, {"text": "?) -> ", "kind": "text"}, {"text": "UIView", "kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIView"}], "url": "/documentation/jdstatusbarnotification/notificationpresenter/presentcustomview(_:sizingcontroller:stylename:completion:)"}, "doc://calimarkus.JDStatusBarNotification/s28CustomDebugStringConvertibleP": {"identifier": "doc://calimarkus.JDStatusBarNotification/s28CustomDebugStringConvertibleP", "type": "unresolvable", "title": "Swift.CustomDebugStringConvertible"}, "doc://calimarkus.JDStatusBarNotification/objc(pl)NSObject": {"identifier": "doc://calimarkus.JDStatusBarNotification/objc(pl)NSObject", "type": "unresolvable", "title": "ObjectiveC.NSObjectProtocol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:d:s:)": {"role": "symbol", "type": "topic", "fragments": [{"text": "func", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "zlp"}, {"text": "(", "kind": "text"}, {"text": "t", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"text": "String", "preciseIdentifier": "s:SS", "kind": "typeIdentifier"}, {"kind": "text", "text": ", "}, {"text": "d", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "s:Sd", "kind": "typeIdentifier", "text": "Double"}, {"kind": "text", "text": ", "}, {"text": "s", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"text": "IncludedStatusBarNotificationStyle", "kind": "typeIdentifier", "preciseIdentifier": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationIncludedStyle"}, {"kind": "text", "text": ") -> "}, {"preciseIdentifier": "c:objc(cs)UIView", "kind": "typeIdentifier", "text": "UIView"}], "title": "zlp(t:d:s:)", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:d:s:)", "abstract": [{"type": "text", "text": "Present a notification using an included style."}], "kind": "symbol", "url": "/documentation/jdstatusbarnotification/notificationpresenter/zlp(t:d:s:)"}, "doc://calimarkus.JDStatusBarNotification/SQ": {"identifier": "doc://calimarkus.JDStatusBarNotification/SQ", "type": "unresolvable", "title": "Swift.Equatable"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:s:c:)": {"fragments": [{"kind": "keyword", "text": "func"}, {"kind": "text", "text": " "}, {"text": "zlp", "kind": "identifier"}, {"text": "(", "kind": "text"}, {"kind": "externalParam", "text": "t"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "s:SS", "text": "String"}, {"text": ", ", "kind": "text"}, {"text": "s", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "text": "IncludedStatusBarNotificationStyle", "preciseIdentifier": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationIncludedStyle"}, {"kind": "text", "text": ", "}, {"text": "c", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"text": "Completion", "preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona", "kind": "typeIdentifier"}, {"text": "?) -> ", "kind": "text"}, {"text": "UIView", "kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIView"}], "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:s:c:)", "url": "/documentation/jdstatusbarnotification/notificationpresenter/zlp(t:s:c:)", "abstract": [{"text": "Present a notification using an included style.", "type": "text"}], "type": "topic", "title": "zlp(t:s:c:)", "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/s23CustomStringConvertibleP": {"type": "unresolvable", "title": "Swift.CustomStringConvertible", "identifier": "doc://calimarkus.JDStatusBarNotification/s23CustomStringConvertibleP"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/shared": {"title": "shared", "url": "/documentation/jdstatusbarnotification/notificationpresenter/shared", "type": "topic", "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/shared", "fragments": [{"text": "static", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "var", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "shared"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationPresenter", "text": "NotificationPresenter", "kind": "typeIdentifier"}], "kind": "symbol", "abstract": [{"text": "Provides access to the shared presenter. This is the entry point to present, style and dismiss notifications.", "type": "text"}]}, "doc://calimarkus.JDStatusBarNotification/SH": {"type": "unresolvable", "title": "<PERSON><PERSON>", "identifier": "doc://calimarkus.JDStatusBarNotification/SH"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:d:cu:)": {"fragments": [{"text": "func", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "zlp"}, {"kind": "text", "text": "("}, {"text": "t", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "s:SS", "text": "String"}, {"text": ", ", "kind": "text"}, {"kind": "externalParam", "text": "d"}, {"kind": "text", "text": ": "}, {"text": "Double", "kind": "typeIdentifier", "preciseIdentifier": "s:Sd"}, {"kind": "text", "text": ", "}, {"kind": "externalParam", "text": "cu"}, {"text": ": ", "kind": "text"}, {"text": "String", "preciseIdentifier": "s:SS", "kind": "typeIdentifier"}, {"text": ") -> ", "kind": "text"}, {"preciseIdentifier": "c:objc(cs)UIView", "text": "UIView", "kind": "typeIdentifier"}], "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:d:cu:)", "url": "/documentation/jdstatusbarnotification/notificationpresenter/zlp(t:d:cu:)", "abstract": [{"text": "Present a notification using a custom style.", "type": "text"}], "type": "topic", "title": "zlp(t:d:cu:)", "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zld(a:d:c:)": {"abstract": [{"text": "Dismisses the displayed notification after the provided delay.", "type": "text"}], "role": "symbol", "kind": "symbol", "type": "topic", "fragments": [{"kind": "keyword", "text": "func"}, {"text": " ", "kind": "text"}, {"text": "zld", "kind": "identifier"}, {"text": "(", "kind": "text"}, {"kind": "externalParam", "text": "a"}, {"text": ": ", "kind": "text"}, {"preciseIdentifier": "s:Sb", "kind": "typeIdentifier", "text": "Bool"}, {"kind": "text", "text": ", "}, {"kind": "externalParam", "text": "d"}, {"text": ": ", "kind": "text"}, {"text": "Double", "kind": "typeIdentifier", "preciseIdentifier": "s:Sd"}, {"kind": "text", "text": ", "}, {"kind": "externalParam", "text": "c"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona", "kind": "typeIdentifier", "text": "Completion"}, {"text": "?)", "kind": "text"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zld(a:d:c:)", "title": "zld(a:d:c:)", "url": "/documentation/jdstatusbarnotification/notificationpresenter/zld(a:d:c:)"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zld(d:c:)": {"type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zld(d:c:)", "kind": "symbol", "title": "zld(d:c:)", "abstract": [{"type": "text", "text": "Dismisses the displayed notification after the provided delay."}], "role": "symbol", "url": "/documentation/jdstatusbarnotification/notificationpresenter/zld(d:c:)", "fragments": [{"kind": "keyword", "text": "func"}, {"kind": "text", "text": " "}, {"text": "zld", "kind": "identifier"}, {"kind": "text", "text": "("}, {"kind": "externalParam", "text": "d"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "text": "Double", "preciseIdentifier": "s:Sd"}, {"text": ", ", "kind": "text"}, {"kind": "externalParam", "text": "c"}, {"text": ": ", "kind": "text"}, {"preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona", "text": "Completion", "kind": "typeIdentifier"}, {"text": "?)", "kind": "text"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlas(n:p:)": {"type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlas(n:p:)", "fragments": [{"kind": "keyword", "text": "func"}, {"text": " ", "kind": "text"}, {"text": "zlas", "kind": "identifier"}, {"text": "(", "kind": "text"}, {"kind": "externalParam", "text": "n"}, {"kind": "text", "text": ": "}, {"text": "String", "preciseIdentifier": "s:SS", "kind": "typeIdentifier"}, {"kind": "text", "text": ", "}, {"text": "p", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"text": "PrepareStyleClosure", "preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC19PrepareStyleClosurea", "kind": "typeIdentifier"}, {"kind": "text", "text": ") -> "}, {"preciseIdentifier": "s:SS", "kind": "typeIdentifier", "text": "String"}], "kind": "symbol", "role": "symbol", "title": "zlas(n:p:)", "abstract": [{"text": "Adds a new named style, which can later be used by referencing it using the ", "type": "text"}, {"type": "codeVoice", "code": "styleName"}, {"type": "text", "text": "."}], "url": "/documentation/jdstatusbarnotification/notificationpresenter/zlas(n:p:)"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zld(d:)": {"type": "topic", "kind": "symbol", "fragments": [{"text": "func", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "zld"}, {"text": "(", "kind": "text"}, {"text": "d", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"preciseIdentifier": "s:Sd", "kind": "typeIdentifier", "text": "Double"}, {"kind": "text", "text": ")"}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zld(d:)", "url": "/documentation/jdstatusbarnotification/notificationpresenter/zld(d:)", "title": "zld(d:)", "abstract": [{"text": "Dismisses the displayed notification after the provided delay.", "type": "text"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:)": {"type": "topic", "kind": "symbol", "title": "zlp(t:)", "abstract": [{"text": "Present a notification", "type": "text"}], "url": "/documentation/jdstatusbarnotification/notificationpresenter/zlp(t:)", "role": "symbol", "fragments": [{"text": "func", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "zlp"}, {"kind": "text", "text": "("}, {"kind": "externalParam", "text": "t"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "text": "String", "preciseIdentifier": "s:SS"}, {"text": ") -> ", "kind": "text"}, {"preciseIdentifier": "c:objc(cs)UIView", "text": "UIView", "kind": "typeIdentifier"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:)"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/displayProgressBar(at:)": {"type": "topic", "url": "/documentation/jdstatusbarnotification/notificationpresenter/displayprogressbar(at:)", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/displayProgressBar(at:)", "kind": "symbol", "abstract": [{"text": "Displays a progress bar at the given ", "type": "text"}, {"code": "percentage", "type": "codeVoice"}, {"text": ".", "type": "text"}], "fragments": [{"kind": "keyword", "text": "func"}, {"kind": "text", "text": " "}, {"text": "displayProgressBar", "kind": "identifier"}, {"kind": "text", "text": "("}, {"kind": "externalParam", "text": "at"}, {"kind": "text", "text": ": "}, {"text": "Double", "kind": "typeIdentifier", "preciseIdentifier": "s:Sd"}, {"kind": "text", "text": ")"}], "title": "displayProgressBar(at:)", "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:d:)": {"type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:d:)", "kind": "symbol", "abstract": [{"text": "Present a notification", "type": "text"}], "title": "zlp(t:d:)", "role": "symbol", "url": "/documentation/jdstatusbarnotification/notificationpresenter/zlp(t:d:)", "fragments": [{"text": "func", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "zlp"}, {"kind": "text", "text": "("}, {"text": "t", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"text": "String", "kind": "typeIdentifier", "preciseIdentifier": "s:SS"}, {"text": ", ", "kind": "text"}, {"text": "d", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "s:Sd", "text": "Double", "kind": "typeIdentifier"}, {"kind": "text", "text": ") -> "}, {"text": "UIView", "kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIView"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/present(_:subtitle:styleName:duration:completion:)": {"kind": "symbol", "type": "topic", "url": "/documentation/jdstatusbarnotification/notificationpresenter/present(_:subtitle:stylename:duration:completion:)", "title": "present(_:subtitle:styleName:duration:completion:)", "abstract": [{"type": "text", "text": "Present a notification using the default style or a named style."}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/present(_:subtitle:styleName:duration:completion:)", "fragments": [{"text": "func", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "present", "kind": "identifier"}, {"kind": "text", "text": "("}, {"text": "String", "preciseIdentifier": "s:SS", "kind": "typeIdentifier"}, {"kind": "text", "text": ", "}, {"text": "subtitle", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"text": "String", "kind": "typeIdentifier", "preciseIdentifier": "s:SS"}, {"text": "?, ", "kind": "text"}, {"text": "styleName", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "s:SS", "text": "String", "kind": "typeIdentifier"}, {"kind": "text", "text": "?, "}, {"kind": "externalParam", "text": "duration"}, {"text": ": ", "kind": "text"}, {"text": "Double", "kind": "typeIdentifier", "preciseIdentifier": "s:Sd"}, {"kind": "text", "text": "?, "}, {"text": "completion", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona", "text": "Completion"}, {"kind": "text", "text": "?) -> "}, {"kind": "typeIdentifier", "text": "UIView", "preciseIdentifier": "c:objc(cs)UIView"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/updateSubtitle(_:)": {"url": "/documentation/jdstatusbarnotification/notificationpresenter/updatesubtitle(_:)", "abstract": [{"text": "Updates the subtitle of an existing notification without animation.", "type": "text"}], "fragments": [{"text": "func", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "updateSubtitle", "kind": "identifier"}, {"text": "(", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "s:SS", "text": "String"}, {"text": "?)", "kind": "text"}], "kind": "symbol", "title": "updateSubtitle(_:)", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/updateSubtitle(_:)", "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/addStyle(named:usingStyle:prepare:)": {"url": "/documentation/jdstatusbarnotification/notificationpresenter/addstyle(named:usingstyle:prepare:)", "abstract": [{"text": "Adds a new named style - based on an included style, if given.", "type": "text"}, {"type": "text", "text": " "}, {"type": "text", "text": "This can later be used by referencing it using the "}, {"type": "codeVoice", "code": "styleName"}, {"text": ".", "type": "text"}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/addStyle(named:usingStyle:prepare:)", "kind": "symbol", "type": "topic", "fragments": [{"kind": "keyword", "text": "func"}, {"text": " ", "kind": "text"}, {"text": "addStyle", "kind": "identifier"}, {"text": "(", "kind": "text"}, {"text": "named", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "s:SS", "text": "String"}, {"kind": "text", "text": ", "}, {"text": "usingStyle", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"kind": "typeIdentifier", "text": "IncludedStatusBarNotificationStyle", "preciseIdentifier": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationIncludedStyle"}, {"text": ", ", "kind": "text"}, {"kind": "externalParam", "text": "prepare"}, {"text": ": ", "kind": "text"}, {"text": "PrepareStyleClosure", "kind": "typeIdentifier", "preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC19PrepareStyleClosurea"}, {"kind": "text", "text": ") -> "}, {"kind": "typeIdentifier", "preciseIdentifier": "s:SS", "text": "String"}], "title": "addStyle(named:usingStyle:prepare:)"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zld(a:)": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zld(a:)", "abstract": [{"text": "Dismisses the displayed notification.", "type": "text"}], "title": "zld(a:)", "type": "topic", "kind": "symbol", "fragments": [{"text": "func", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "zld"}, {"text": "(", "kind": "text"}, {"kind": "externalParam", "text": "a"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "text": "Bool", "preciseIdentifier": "s:Sb"}, {"kind": "text", "text": ")"}], "url": "/documentation/jdstatusbarnotification/notificationpresenter/zld(a:)", "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/isVisible": {"abstract": [{"type": "text", "text": "Let’s you check if a notification is currently displayed."}], "url": "/documentation/jdstatusbarnotification/notificationpresenter/isvisible", "fragments": [{"text": "var", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "isVisible", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"text": "Bool", "preciseIdentifier": "s:Sb", "kind": "typeIdentifier"}], "kind": "symbol", "title": "isVisible", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/isVisible", "type": "topic", "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle": {"navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationLeftViewStyle"}], "type": "topic", "kind": "symbol", "title": "StatusBarNotificationLeftViewStyle", "abstract": [{"text": "Defines the appearance of a left-view, if set. It also applies to the activity indicator.", "type": "text"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewstyle", "role": "symbol", "fragments": [{"text": "class", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "StatusBarNotificationLeftViewStyle", "kind": "identifier"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/dismiss(animated:after:completion:)": {"url": "/documentation/jdstatusbarnotification/notificationpresenter/dismiss(animated:after:completion:)", "abstract": [{"type": "text", "text": "Dismisses any currently displayed notification animated - after the provided delay, if provided."}], "title": "dismiss(animated:after:completion:)", "kind": "symbol", "role": "symbol", "type": "topic", "fragments": [{"kind": "keyword", "text": "func"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "dismiss"}, {"kind": "text", "text": "("}, {"text": "animated", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "text": "Bool", "preciseIdentifier": "s:Sb"}, {"kind": "text", "text": ", "}, {"kind": "externalParam", "text": "after"}, {"text": ": ", "kind": "text"}, {"text": "Double", "preciseIdentifier": "s:Sd", "kind": "typeIdentifier"}, {"text": "?, ", "kind": "text"}, {"kind": "externalParam", "text": "completion"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona", "kind": "typeIdentifier", "text": "Completion"}, {"kind": "text", "text": "?)"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/dismiss(animated:after:completion:)"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zld()": {"abstract": [{"text": "Dismisses the displayed notification.", "type": "text"}], "url": "/documentation/jdstatusbarnotification/notificationpresenter/zld()", "title": "zld()", "kind": "symbol", "fragments": [{"kind": "keyword", "text": "func"}, {"kind": "text", "text": " "}, {"text": "zld", "kind": "identifier"}, {"text": "()", "kind": "text"}], "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zld()", "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter": {"abstract": [{"type": "text", "text": "The NotificationPresenter let’s you present notifications below the statusBar."}, {"type": "text", "text": " "}, {"type": "text", "text": "You can customize the style (colors, fonts, etc.) and animations. It supports notch"}, {"type": "text", "text": " "}, {"type": "text", "text": "and no-notch devices, landscape & portrait layouts and Drag-to-Dismiss. It can display a"}, {"text": " ", "type": "text"}, {"type": "text", "text": "title, a subtitle, an activity indicator, an animated progress bar & custom views out of the box."}], "fragments": [{"text": "class", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "NotificationPresenter"}], "navigatorTitle": [{"text": "NotificationPresenter", "kind": "identifier"}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter", "url": "/documentation/jdstatusbarnotification/notificationpresenter", "type": "topic", "kind": "symbol", "title": "NotificationPresenter"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:c:)": {"fragments": [{"text": "func", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "zlp"}, {"text": "(", "kind": "text"}, {"text": "t", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "s:SS", "text": "String"}, {"text": ", ", "kind": "text"}, {"kind": "externalParam", "text": "c"}, {"text": ": ", "kind": "text"}, {"text": "Completion", "preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona", "kind": "typeIdentifier"}, {"kind": "text", "text": "?) -> "}, {"kind": "typeIdentifier", "text": "UIView", "preciseIdentifier": "c:objc(cs)UIView"}], "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:c:)", "url": "/documentation/jdstatusbarnotification/notificationpresenter/zlp(t:c:)", "abstract": [{"text": "Present a notification", "type": "text"}], "type": "topic", "title": "zlp(t:c:)", "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:st:s:c:)": {"url": "/documentation/jdstatusbarnotification/notificationpresenter/zlp(t:st:s:c:)", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:st:s:c:)", "abstract": [{"type": "text", "text": "Present a notification using an included style."}], "fragments": [{"kind": "keyword", "text": "func"}, {"kind": "text", "text": " "}, {"text": "zlp", "kind": "identifier"}, {"kind": "text", "text": "("}, {"kind": "externalParam", "text": "t"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "s:SS", "text": "String", "kind": "typeIdentifier"}, {"kind": "text", "text": ", "}, {"text": "st", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"text": "String", "kind": "typeIdentifier", "preciseIdentifier": "s:SS"}, {"kind": "text", "text": "?, "}, {"text": "s", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"text": "IncludedStatusBarNotificationStyle", "preciseIdentifier": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationIncludedStyle", "kind": "typeIdentifier"}, {"kind": "text", "text": ", "}, {"text": "c", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"text": "Completion", "kind": "typeIdentifier", "preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona"}, {"kind": "text", "text": "?) -> "}, {"kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIView", "text": "UIView"}], "title": "zlp(t:st:s:c:)", "kind": "symbol", "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:st:c:)": {"type": "topic", "title": "zlp(t:st:c:)", "url": "/documentation/jdstatusbarnotification/notificationpresenter/zlp(t:st:c:)", "kind": "symbol", "role": "symbol", "abstract": [{"type": "text", "text": "Present a notification"}], "fragments": [{"kind": "keyword", "text": "func"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "zlp"}, {"kind": "text", "text": "("}, {"text": "t", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "text": "String", "preciseIdentifier": "s:SS"}, {"kind": "text", "text": ", "}, {"kind": "externalParam", "text": "st"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "text": "String", "preciseIdentifier": "s:SS"}, {"text": ", ", "kind": "text"}, {"text": "c", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "text": "Completion", "preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona"}, {"text": "?) -> ", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIView", "text": "UIView"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:st:c:)"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle", "navigatorTitle": [{"text": "StatusBarNotificationStyle", "kind": "identifier"}], "role": "symbol", "type": "topic", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "title": "StatusBarNotificationStyle", "abstract": [{"text": "A Style defines the appearance of a notification.", "type": "text"}], "fragments": [{"kind": "keyword", "text": "class"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationStyle", "kind": "identifier"}]}}}