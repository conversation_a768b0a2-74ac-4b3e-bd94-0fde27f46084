{"abstract": [{"text": "The color of the  label.", "type": "text"}], "metadata": {"fragments": [{"text": "var", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "textColor", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"kind": "typeIdentifier", "text": "UIColor", "preciseIdentifier": "c:objc(cs)UIColor"}, {"text": "?", "kind": "text"}], "modules": [{"name": "JDStatusBarNotification"}], "symbolKind": "property", "externalID": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationTextStyle(py)textColor", "role": "symbol", "title": "textColor", "roleHeading": "Instance Property"}, "variants": [{"paths": ["/documentation/jdstatusbarnotification/statusbarnotificationtextstyle/textcolor"], "traits": [{"interfaceLanguage": "swift"}]}], "identifier": {"interfaceLanguage": "swift", "url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle/textColor"}, "schemaVersion": {"major": 0, "minor": 3, "patch": 0}, "primaryContentSections": [{"declarations": [{"tokens": [{"text": "@objc", "kind": "attribute"}, {"text": " ", "kind": "text"}, {"text": "var", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "textColor", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"text": "UIColor", "kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIColor"}, {"text": "?", "kind": "text"}], "languages": ["swift"], "platforms": ["iOS"]}], "kind": "declarations"}], "kind": "symbol", "sections": [], "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle"]]}, "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle/textColor": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle/textColor", "role": "symbol", "type": "topic", "fragments": [{"kind": "keyword", "text": "var"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "textColor"}, {"kind": "text", "text": ": "}, {"text": "UIColor", "kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIColor"}, {"text": "?", "kind": "text"}], "abstract": [{"type": "text", "text": "The color of the  label."}], "kind": "symbol", "url": "/documentation/jdstatusbarnotification/statusbarnotificationtextstyle/textcolor", "title": "textColor"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle", "kind": "symbol", "type": "topic", "url": "/documentation/jdstatusbarnotification/statusbarnotificationtextstyle", "title": "StatusBarNotificationTextStyle", "abstract": [{"text": "Defines the appearance of a text label.", "type": "text"}], "navigatorTitle": [{"text": "StatusBarNotificationTextStyle", "kind": "identifier"}], "fragments": [{"kind": "keyword", "text": "class"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "StatusBarNotificationTextStyle"}], "role": "symbol"}}}