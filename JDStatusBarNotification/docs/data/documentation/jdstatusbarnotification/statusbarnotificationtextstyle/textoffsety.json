{"metadata": {"title": "textOffsetY", "roleHeading": "Instance Property", "symbolKind": "property", "externalID": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationTextStyle(py)textOffsetY", "modules": [{"name": "JDStatusBarNotification"}], "role": "symbol", "fragments": [{"text": "var", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "textOffsetY", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "s:Sd", "text": "Double", "kind": "typeIdentifier"}]}, "identifier": {"interfaceLanguage": "swift", "url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle/textOffsetY"}, "abstract": [{"type": "text", "text": "Offsets the text label on the y-axis. De<PERSON>ult is "}, {"code": "0.0", "type": "codeVoice"}, {"text": ".", "type": "text"}], "sections": [], "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle"]]}, "schemaVersion": {"patch": 0, "major": 0, "minor": 3}, "kind": "symbol", "primaryContentSections": [{"kind": "declarations", "declarations": [{"tokens": [{"text": "@objc", "kind": "attribute"}, {"kind": "text", "text": " "}, {"text": "var", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "textOffsetY", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"text": "Double", "kind": "typeIdentifier", "preciseIdentifier": "s:Sd"}], "platforms": ["iOS"], "languages": ["swift"]}]}], "variants": [{"paths": ["/documentation/jdstatusbarnotification/statusbarnotificationtextstyle/textoffsety"], "traits": [{"interfaceLanguage": "swift"}]}], "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle/textOffsetY": {"abstract": [{"type": "text", "text": "Offsets the text label on the y-axis. De<PERSON>ult is "}, {"code": "0.0", "type": "codeVoice"}, {"text": ".", "type": "text"}], "fragments": [{"text": "var", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "textOffsetY"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "s:Sd", "kind": "typeIdentifier", "text": "Double"}], "kind": "symbol", "type": "topic", "title": "textOffsetY", "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle/textOffsetY", "url": "/documentation/jdstatusbarnotification/statusbarnotificationtextstyle/textoffsety"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle": {"title": "StatusBarNotificationTextStyle", "fragments": [{"kind": "keyword", "text": "class"}, {"text": " ", "kind": "text"}, {"text": "StatusBarNotificationTextStyle", "kind": "identifier"}], "role": "symbol", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle", "url": "/documentation/jdstatusbarnotification/statusbarnotificationtextstyle", "type": "topic", "navigatorTitle": [{"text": "StatusBarNotificationTextStyle", "kind": "identifier"}], "abstract": [{"type": "text", "text": "Defines the appearance of a text label."}]}}}