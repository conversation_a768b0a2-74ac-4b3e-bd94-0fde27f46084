{"schemaVersion": {"patch": 0, "minor": 3, "major": 0}, "abstract": [{"type": "text", "text": "The text shadow color, the default is "}, {"code": "nil", "type": "codeVoice"}, {"text": ", meaning no shadow.", "type": "text"}], "primaryContentSections": [{"declarations": [{"tokens": [{"text": "@objc", "kind": "attribute"}, {"kind": "text", "text": " "}, {"kind": "keyword", "text": "var"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "shadowColor"}, {"text": ": ", "kind": "text"}, {"text": "UIColor", "kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIColor"}, {"text": "?", "kind": "text"}], "languages": ["swift"], "platforms": ["iOS"]}], "kind": "declarations"}], "identifier": {"url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle/shadowColor", "interfaceLanguage": "swift"}, "variants": [{"paths": ["/documentation/jdstatusbarnotification/statusbarnotificationtextstyle/shadowcolor"], "traits": [{"interfaceLanguage": "swift"}]}], "sections": [], "kind": "symbol", "metadata": {"title": "shadowColor", "modules": [{"name": "JDStatusBarNotification"}], "role": "symbol", "roleHeading": "Instance Property", "fragments": [{"kind": "keyword", "text": "var"}, {"kind": "text", "text": " "}, {"text": "shadowColor", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"text": "UIColor", "kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIColor"}, {"kind": "text", "text": "?"}], "symbolKind": "property", "externalID": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationTextStyle(py)shadowColor"}, "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle"]]}, "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle/shadowColor": {"kind": "symbol", "role": "symbol", "fragments": [{"text": "var", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "shadowColor"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "c:objc(cs)UIColor", "text": "UIColor", "kind": "typeIdentifier"}, {"text": "?", "kind": "text"}], "title": "shadowColor", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle/shadowColor", "url": "/documentation/jdstatusbarnotification/statusbarnotificationtextstyle/shadowcolor", "abstract": [{"text": "The text shadow color, the default is ", "type": "text"}, {"code": "nil", "type": "codeVoice"}, {"type": "text", "text": ", meaning no shadow."}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle": {"title": "StatusBarNotificationTextStyle", "fragments": [{"kind": "keyword", "text": "class"}, {"text": " ", "kind": "text"}, {"text": "StatusBarNotificationTextStyle", "kind": "identifier"}], "role": "symbol", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle", "url": "/documentation/jdstatusbarnotification/statusbarnotificationtextstyle", "type": "topic", "navigatorTitle": [{"text": "StatusBarNotificationTextStyle", "kind": "identifier"}], "abstract": [{"type": "text", "text": "Defines the appearance of a text label."}]}}}