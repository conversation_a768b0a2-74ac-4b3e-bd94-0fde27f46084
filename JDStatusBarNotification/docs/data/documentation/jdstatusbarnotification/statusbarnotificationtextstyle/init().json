{"schemaVersion": {"major": 0, "minor": 3, "patch": 0}, "kind": "symbol", "identifier": {"interfaceLanguage": "swift", "url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle/init()"}, "primaryContentSections": [{"kind": "declarations", "declarations": [{"languages": ["swift"], "platforms": ["iOS"], "tokens": [{"text": "override", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "init", "kind": "keyword"}, {"kind": "text", "text": "()"}]}]}], "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle"]]}, "variants": [{"traits": [{"interfaceLanguage": "swift"}], "paths": ["/documentation/jdstatusbarnotification/statusbarnotificationtextstyle/init()"]}], "sections": [], "metadata": {"externalID": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationTextStyle(im)init", "roleHeading": "Initializer", "modules": [{"name": "JDStatusBarNotification"}], "role": "symbol", "title": "init()", "symbolKind": "init", "fragments": [{"text": "init", "kind": "identifier"}, {"kind": "text", "text": "()"}]}, "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "kind": "symbol", "type": "topic", "url": "/documentation/jdstatusbarnotification", "title": "JDStatusBarNotification", "abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle", "kind": "symbol", "type": "topic", "url": "/documentation/jdstatusbarnotification/statusbarnotificationtextstyle", "title": "StatusBarNotificationTextStyle", "abstract": [{"text": "Defines the appearance of a text label.", "type": "text"}], "navigatorTitle": [{"text": "StatusBarNotificationTextStyle", "kind": "identifier"}], "fragments": [{"kind": "keyword", "text": "class"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "StatusBarNotificationTextStyle"}], "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle/init()": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle/init()", "kind": "symbol", "title": "init()", "url": "/documentation/jdstatusbarnotification/statusbarnotificationtextstyle/init()", "type": "topic", "abstract": [], "fragments": [{"kind": "identifier", "text": "init"}, {"text": "()", "kind": "text"}], "role": "symbol"}}}