{"hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle"]]}, "sections": [], "identifier": {"interfaceLanguage": "swift", "url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle/textShadowColor"}, "kind": "symbol", "abstract": [{"type": "text", "text": "The text shadow color, the default is "}, {"code": "nil", "type": "codeVoice"}, {"text": ", meaning no shadow.", "type": "text"}], "schemaVersion": {"major": 0, "minor": 3, "patch": 0}, "primaryContentSections": [{"kind": "declarations", "declarations": [{"languages": ["swift"], "tokens": [{"text": "@objc", "kind": "attribute"}, {"kind": "text", "text": " "}, {"text": "var", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "textShadowColor", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"kind": "typeIdentifier", "text": "UIColor", "preciseIdentifier": "c:objc(cs)UIColor"}, {"text": "? { ", "kind": "text"}, {"kind": "keyword", "text": "get"}, {"kind": "text", "text": " "}, {"kind": "keyword", "text": "set"}, {"kind": "text", "text": " }"}], "platforms": ["iOS"]}]}], "variants": [{"paths": ["/documentation/jdstatusbarnotification/statusbarnotificationtextstyle/textshadowcolor"], "traits": [{"interfaceLanguage": "swift"}]}], "metadata": {"fragments": [{"kind": "keyword", "text": "var"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "textShadowColor"}, {"kind": "text", "text": ": "}, {"text": "UIColor", "preciseIdentifier": "c:objc(cs)UIColor", "kind": "typeIdentifier"}, {"kind": "text", "text": "?"}], "roleHeading": "Instance Property", "role": "symbol", "symbolKind": "property", "externalID": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationTextStyle(py)textShadowColor", "title": "textShadowColor", "modules": [{"name": "JDStatusBarNotification"}], "platforms": []}, "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"kind": "symbol", "title": "JDStatusBarNotification", "role": "collection", "abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "url": "/documentation/jdstatusbarnotification", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "type": "topic"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle/textShadowColor": {"fragments": [{"kind": "keyword", "text": "var"}, {"kind": "text", "text": " "}, {"text": "textShadowColor", "kind": "identifier"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIColor", "text": "UIColor"}, {"text": "?", "kind": "text"}], "type": "topic", "deprecated": true, "title": "textShadowColor", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle/textShadowColor", "abstract": [{"text": "The text shadow color, the default is ", "type": "text"}, {"type": "codeVoice", "code": "nil"}, {"type": "text", "text": ", meaning no shadow."}], "kind": "symbol", "url": "/documentation/jdstatusbarnotification/statusbarnotificationtextstyle/textshadowcolor", "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle", "kind": "symbol", "type": "topic", "url": "/documentation/jdstatusbarnotification/statusbarnotificationtextstyle", "title": "StatusBarNotificationTextStyle", "abstract": [{"text": "Defines the appearance of a text label.", "type": "text"}], "navigatorTitle": [{"text": "StatusBarNotificationTextStyle", "kind": "identifier"}], "fragments": [{"kind": "keyword", "text": "class"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "StatusBarNotificationTextStyle"}], "role": "symbol"}}}