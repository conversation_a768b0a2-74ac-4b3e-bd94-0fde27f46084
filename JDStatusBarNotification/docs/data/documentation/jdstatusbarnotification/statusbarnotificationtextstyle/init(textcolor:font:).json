{"variants": [{"traits": [{"interfaceLanguage": "swift"}], "paths": ["/documentation/jdstatusbarnotification/statusbarnotificationtextstyle/init(textcolor:font:)"]}], "metadata": {"role": "symbol", "roleHeading": "Initializer", "modules": [{"name": "JDStatusBarNotification"}], "title": "init(textColor:font:)", "symbolKind": "init", "externalID": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationTextStyle(im)initWithTextColor:font:", "fragments": [{"text": "init", "kind": "identifier"}, {"kind": "text", "text": "("}, {"kind": "externalParam", "text": "textColor"}, {"text": ": ", "kind": "text"}, {"text": "UIColor", "preciseIdentifier": "c:objc(cs)UIColor", "kind": "typeIdentifier"}, {"kind": "text", "text": "?, "}, {"text": "font", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"text": "UIFont", "kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIFont"}, {"text": "?)", "kind": "text"}]}, "kind": "symbol", "sections": [], "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle"]]}, "identifier": {"url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle/init(textColor:font:)", "interfaceLanguage": "swift"}, "schemaVersion": {"minor": 3, "major": 0, "patch": 0}, "primaryContentSections": [{"kind": "declarations", "declarations": [{"platforms": ["iOS"], "tokens": [{"kind": "attribute", "text": "@objc"}, {"kind": "text", "text": " "}, {"kind": "keyword", "text": "init"}, {"text": "(", "kind": "text"}, {"kind": "externalParam", "text": "textColor"}, {"text": ": ", "kind": "text"}, {"text": "UIColor", "kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIColor"}, {"text": "? = nil, ", "kind": "text"}, {"kind": "externalParam", "text": "font"}, {"text": ": ", "kind": "text"}, {"preciseIdentifier": "c:objc(cs)UIFont", "kind": "typeIdentifier", "text": "UIFont"}, {"kind": "text", "text": "? = nil)"}], "languages": ["swift"]}]}], "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle": {"title": "StatusBarNotificationTextStyle", "fragments": [{"kind": "keyword", "text": "class"}, {"text": " ", "kind": "text"}, {"text": "StatusBarNotificationTextStyle", "kind": "identifier"}], "role": "symbol", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle", "url": "/documentation/jdstatusbarnotification/statusbarnotificationtextstyle", "type": "topic", "navigatorTitle": [{"text": "StatusBarNotificationTextStyle", "kind": "identifier"}], "abstract": [{"type": "text", "text": "Defines the appearance of a text label."}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle/init(textColor:font:)": {"type": "topic", "kind": "symbol", "role": "symbol", "fragments": [{"kind": "identifier", "text": "init"}, {"kind": "text", "text": "("}, {"kind": "externalParam", "text": "textColor"}, {"kind": "text", "text": ": "}, {"text": "UIColor", "kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIColor"}, {"kind": "text", "text": "?, "}, {"text": "font", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIFont", "text": "UIFont"}, {"text": "?)", "kind": "text"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle/init(textColor:font:)", "abstract": [], "url": "/documentation/jdstatusbarnotification/statusbarnotificationtextstyle/init(textcolor:font:)", "title": "init(textColor:font:)"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}}}