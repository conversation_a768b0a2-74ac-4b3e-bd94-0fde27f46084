{"metadata": {"role": "symbol", "title": "shadowOffset", "modules": [{"name": "JDStatusBarNotification"}], "externalID": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationTextStyle(py)shadowOffset", "fragments": [{"text": "var", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "shadowOffset", "kind": "identifier"}, {"text": ": ", "kind": "text"}, {"text": "CGPoint", "preciseIdentifier": "c:@S@CGPoint", "kind": "typeIdentifier"}], "roleHeading": "Instance Property", "symbolKind": "property"}, "primaryContentSections": [{"kind": "declarations", "declarations": [{"languages": ["swift"], "tokens": [{"text": "@objc", "kind": "attribute"}, {"kind": "text", "text": " "}, {"text": "var", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "shadowOffset"}, {"kind": "text", "text": ": "}, {"kind": "typeIdentifier", "preciseIdentifier": "c:@S@CGPoint", "text": "CGPoint"}], "platforms": ["iOS"]}]}], "variants": [{"traits": [{"interfaceLanguage": "swift"}], "paths": ["/documentation/jdstatusbarnotification/statusbarnotificationtextstyle/shadowoffset"]}], "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle"]]}, "sections": [], "kind": "symbol", "abstract": [{"text": "The text shadow offset of the notification label. De<PERSON>ult is ", "type": "text"}, {"code": "(1, 2)", "type": "codeVoice"}], "identifier": {"url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle/shadowOffset", "interfaceLanguage": "swift"}, "schemaVersion": {"minor": 3, "patch": 0, "major": 0}, "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle/shadowOffset": {"abstract": [{"type": "text", "text": "The text shadow offset of the notification label. De<PERSON>ult is "}, {"type": "codeVoice", "code": "(1, 2)"}], "type": "topic", "url": "/documentation/jdstatusbarnotification/statusbarnotificationtextstyle/shadowoffset", "role": "symbol", "fragments": [{"text": "var", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "shadowOffset"}, {"text": ": ", "kind": "text"}, {"text": "CGPoint", "kind": "typeIdentifier", "preciseIdentifier": "c:@S@CGPoint"}], "title": "shadowOffset", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle/shadowOffset", "kind": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle": {"title": "StatusBarNotificationTextStyle", "fragments": [{"kind": "keyword", "text": "class"}, {"text": " ", "kind": "text"}, {"text": "StatusBarNotificationTextStyle", "kind": "identifier"}], "role": "symbol", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle", "url": "/documentation/jdstatusbarnotification/statusbarnotificationtextstyle", "type": "topic", "navigatorTitle": [{"text": "StatusBarNotificationTextStyle", "kind": "identifier"}], "abstract": [{"type": "text", "text": "Defines the appearance of a text label."}]}}}