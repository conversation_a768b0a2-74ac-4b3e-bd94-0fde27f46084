{"hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle"]]}, "abstract": [{"type": "text", "text": "The font of the label."}], "schemaVersion": {"patch": 0, "major": 0, "minor": 3}, "variants": [{"traits": [{"interfaceLanguage": "swift"}], "paths": ["/documentation/jdstatusbarnotification/statusbarnotificationtextstyle/font"]}], "kind": "symbol", "sections": [], "metadata": {"externalID": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationTextStyle(py)font", "modules": [{"name": "JDStatusBarNotification"}], "role": "symbol", "title": "font", "fragments": [{"kind": "keyword", "text": "var"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "font"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "c:objc(cs)UIFont", "text": "UIFont", "kind": "typeIdentifier"}, {"text": "?", "kind": "text"}], "symbolKind": "property", "roleHeading": "Instance Property"}, "primaryContentSections": [{"declarations": [{"languages": ["swift"], "tokens": [{"text": "@objc", "kind": "attribute"}, {"text": " ", "kind": "text"}, {"kind": "keyword", "text": "var"}, {"text": " ", "kind": "text"}, {"text": "font", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "c:objc(cs)UIFont", "kind": "typeIdentifier", "text": "UIFont"}, {"text": "?", "kind": "text"}], "platforms": ["iOS"]}], "kind": "declarations"}], "identifier": {"interfaceLanguage": "swift", "url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle/font"}, "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle/font": {"type": "topic", "kind": "symbol", "role": "symbol", "fragments": [{"kind": "keyword", "text": "var"}, {"text": " ", "kind": "text"}, {"text": "font", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"text": "UIFont", "kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIFont"}, {"kind": "text", "text": "?"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle/font", "abstract": [{"type": "text", "text": "The font of the label."}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationtextstyle/font", "title": "font"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle": {"title": "StatusBarNotificationTextStyle", "fragments": [{"kind": "keyword", "text": "class"}, {"text": " ", "kind": "text"}, {"text": "StatusBarNotificationTextStyle", "kind": "identifier"}], "role": "symbol", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle", "url": "/documentation/jdstatusbarnotification/statusbarnotificationtextstyle", "type": "topic", "navigatorTitle": [{"text": "StatusBarNotificationTextStyle", "kind": "identifier"}], "abstract": [{"type": "text", "text": "Defines the appearance of a text label."}]}}}