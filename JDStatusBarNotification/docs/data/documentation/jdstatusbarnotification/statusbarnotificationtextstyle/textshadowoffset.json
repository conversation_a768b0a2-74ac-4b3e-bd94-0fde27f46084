{"variants": [{"paths": ["/documentation/jdstatusbarnotification/statusbarnotificationtextstyle/textshadowoffset"], "traits": [{"interfaceLanguage": "swift"}]}], "sections": [], "schemaVersion": {"major": 0, "minor": 3, "patch": 0}, "abstract": [{"type": "text", "text": "The text shadow offset of the notification label. De<PERSON>ult is "}, {"code": "(1, 2)", "type": "codeVoice"}], "primaryContentSections": [{"declarations": [{"tokens": [{"text": "@objc", "kind": "attribute"}, {"kind": "text", "text": " "}, {"text": "var", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "textShadowOffset", "kind": "identifier"}, {"text": ": ", "kind": "text"}, {"text": "CGSize", "kind": "typeIdentifier", "preciseIdentifier": "c:@S@CGSize"}, {"kind": "text", "text": " { "}, {"text": "get", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "keyword", "text": "set"}, {"text": " }", "kind": "text"}], "languages": ["swift"], "platforms": ["iOS"]}], "kind": "declarations"}], "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle"]]}, "kind": "symbol", "identifier": {"url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle/textShadowOffset", "interfaceLanguage": "swift"}, "metadata": {"externalID": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationTextStyle(py)textShadowOffset", "fragments": [{"text": "var", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "textShadowOffset", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "c:@S@CGSize", "text": "CGSize", "kind": "typeIdentifier"}], "platforms": [], "modules": [{"name": "JDStatusBarNotification"}], "roleHeading": "Instance Property", "role": "symbol", "symbolKind": "property", "title": "textShadowOffset"}, "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle": {"title": "StatusBarNotificationTextStyle", "fragments": [{"kind": "keyword", "text": "class"}, {"text": " ", "kind": "text"}, {"text": "StatusBarNotificationTextStyle", "kind": "identifier"}], "role": "symbol", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle", "url": "/documentation/jdstatusbarnotification/statusbarnotificationtextstyle", "type": "topic", "navigatorTitle": [{"text": "StatusBarNotificationTextStyle", "kind": "identifier"}], "abstract": [{"type": "text", "text": "Defines the appearance of a text label."}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle/textShadowOffset": {"type": "topic", "kind": "symbol", "role": "symbol", "deprecated": true, "fragments": [{"text": "var", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "textShadowOffset", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "c:@S@CGSize", "kind": "typeIdentifier", "text": "CGSize"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle/textShadowOffset", "abstract": [{"type": "text", "text": "The text shadow offset of the notification label. De<PERSON>ult is "}, {"code": "(1, 2)", "type": "codeVoice"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationtextstyle/textshadowoffset", "title": "textShadowOffset"}}}