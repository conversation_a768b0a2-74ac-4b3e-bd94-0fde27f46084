{"sections": [], "kind": "symbol", "seeAlsoSections": [{"identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/presentSwiftView(styleName:viewBuilder:completion:)", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenterCustomViewSizingController"], "generated": true, "title": "Present a notification (using a custom view)"}], "primaryContentSections": [{"declarations": [{"tokens": [{"kind": "attribute", "text": "@discardableResult"}, {"text": " ", "kind": "text"}, {"text": "@objc", "kind": "attribute"}, {"kind": "text", "text": "(presentWithCustomView:sizingController:styleName:completion:) "}, {"kind": "keyword", "text": "func"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "presentCustom<PERSON>iew"}, {"kind": "text", "text": "("}, {"kind": "externalParam", "text": "_"}, {"kind": "text", "text": " "}, {"kind": "internalParam", "text": "customView"}, {"text": ": ", "kind": "text"}, {"preciseIdentifier": "c:objc(cs)UIView", "text": "UIView", "kind": "typeIdentifier"}, {"kind": "text", "text": ", "}, {"kind": "externalParam", "text": "sizingController"}, {"kind": "text", "text": ": "}, {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenterCustomViewSizingController", "preciseIdentifier": "c:@M@JDStatusBarNotification@objc(pl)JDStatusBarNotificationPresenterCustomViewSizingController", "text": "NotificationPresenterCustomViewSizingController", "kind": "typeIdentifier"}, {"text": "? = nil, ", "kind": "text"}, {"text": "styleName", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "s:SS", "kind": "typeIdentifier", "text": "String"}, {"text": "? = nil, ", "kind": "text"}, {"text": "completion", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/Completion", "preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona", "text": "Completion", "kind": "typeIdentifier"}, {"kind": "text", "text": "? = nil) -> "}, {"text": "UIView", "kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIView"}], "platforms": ["iOS"], "languages": ["swift"]}], "kind": "declarations"}, {"parameters": [{"content": [{"type": "paragraph", "inlineContent": [{"text": "A custom UIView to display as notification content.", "type": "text"}]}], "name": "customView"}, {"name": "sizingController", "content": [{"type": "paragraph", "inlineContent": [{"text": "An optional controller conforming to ", "type": "text"}, {"isActive": true, "type": "reference", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenterCustomViewSizingController"}, {"type": "text", "text": ", which controls the size of a presented custom view."}]}]}, {"content": [{"type": "paragraph", "inlineContent": [{"type": "text", "text": "The name of the style. You can use styles previously added using e.g. "}, {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/addStyle(named:usingStyle:prepare:)", "isActive": true, "type": "reference"}, {"type": "text", "text": "."}, {"type": "text", "text": " "}, {"text": "If no style can be found for the given ", "type": "text"}, {"type": "codeVoice", "code": "styleName"}, {"text": " or it is ", "type": "text"}, {"type": "codeVoice", "code": "nil"}, {"type": "text", "text": ", the default style will be used."}]}], "name": "styleName"}, {"name": "completion", "content": [{"inlineContent": [{"text": "A ", "type": "text"}, {"type": "reference", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/Completion", "isActive": true}, {"text": " closure, which gets called once the presentation animation finishes.", "type": "text"}], "type": "paragraph"}]}], "kind": "parameters"}, {"kind": "content", "content": [{"level": 2, "text": "Return Value", "type": "heading", "anchor": "return-value"}, {"type": "paragraph", "inlineContent": [{"text": "The presented UIView for further customization", "type": "text"}]}]}, {"kind": "content", "content": [{"anchor": "discussion", "type": "heading", "text": "Discussion", "level": 2}, {"type": "paragraph", "inlineContent": [{"text": "The ", "type": "text"}, {"code": "customView", "type": "codeVoice"}, {"type": "text", "text": " will be layouted correctly according to the selected style & the current device"}, {"text": " ", "type": "text"}, {"type": "text", "text": "state (rotation, status bar visibility, etc.). The background will still be styled & layouted"}, {"text": " ", "type": "text"}, {"text": "according to the provided style. If your custom view requires custom touch handling,", "type": "text"}, {"type": "text", "text": " "}, {"text": "make sure to set ", "type": "text"}, {"type": "codeVoice", "code": "style.canTapToHold"}, {"type": "text", "text": " to "}, {"type": "codeVoice", "code": "false"}, {"type": "text", "text": ". Otherwise the "}, {"code": "customView", "type": "codeVoice"}, {"type": "text", "text": " won’t"}, {"type": "text", "text": " "}, {"text": "receive any touches, as the internal ", "type": "text"}, {"code": "gestureRecognizer", "type": "codeVoice"}, {"text": " would receive them.", "type": "text"}]}]}], "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter"]]}, "identifier": {"interfaceLanguage": "swift", "url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/presentCustomView(_:sizingController:styleName:completion:)"}, "metadata": {"modules": [{"name": "JDStatusBarNotification"}], "roleHeading": "Instance Method", "externalID": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationPresenter(im)presentWithCustomView:sizingController:styleName:completion:", "fragments": [{"text": "func", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "presentCustom<PERSON>iew"}, {"text": "(", "kind": "text"}, {"text": "UIView", "preciseIdentifier": "c:objc(cs)UIView", "kind": "typeIdentifier"}, {"kind": "text", "text": ", "}, {"text": "sizingController", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"preciseIdentifier": "c:@M@JDStatusBarNotification@objc(pl)JDStatusBarNotificationPresenterCustomViewSizingController", "kind": "typeIdentifier", "text": "NotificationPresenterCustomViewSizingController"}, {"text": "?, ", "kind": "text"}, {"text": "styleName", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"text": "String", "kind": "typeIdentifier", "preciseIdentifier": "s:SS"}, {"kind": "text", "text": "?, "}, {"kind": "externalParam", "text": "completion"}, {"text": ": ", "kind": "text"}, {"preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona", "kind": "typeIdentifier", "text": "Completion"}, {"kind": "text", "text": "?) -> "}, {"preciseIdentifier": "c:objc(cs)UIView", "text": "UIView", "kind": "typeIdentifier"}], "title": "presentCustomView(_:sizingController:styleName:completion:)", "role": "symbol", "symbolKind": "method"}, "abstract": [{"text": "Present a notification using a custom subview.", "type": "text"}], "schemaVersion": {"major": 0, "minor": 3, "patch": 0}, "variants": [{"traits": [{"interfaceLanguage": "swift"}], "paths": ["/documentation/jdstatusbarnotification/notificationpresenter/presentcustomview(_:sizingcontroller:stylename:completion:)"]}], "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/addStyle(named:usingStyle:prepare:)": {"url": "/documentation/jdstatusbarnotification/notificationpresenter/addstyle(named:usingstyle:prepare:)", "abstract": [{"text": "Adds a new named style - based on an included style, if given.", "type": "text"}, {"type": "text", "text": " "}, {"type": "text", "text": "This can later be used by referencing it using the "}, {"type": "codeVoice", "code": "styleName"}, {"text": ".", "type": "text"}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/addStyle(named:usingStyle:prepare:)", "kind": "symbol", "type": "topic", "fragments": [{"kind": "keyword", "text": "func"}, {"text": " ", "kind": "text"}, {"text": "addStyle", "kind": "identifier"}, {"text": "(", "kind": "text"}, {"text": "named", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "s:SS", "text": "String"}, {"kind": "text", "text": ", "}, {"text": "usingStyle", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"kind": "typeIdentifier", "text": "IncludedStatusBarNotificationStyle", "preciseIdentifier": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationIncludedStyle"}, {"text": ", ", "kind": "text"}, {"kind": "externalParam", "text": "prepare"}, {"text": ": ", "kind": "text"}, {"text": "PrepareStyleClosure", "kind": "typeIdentifier", "preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC19PrepareStyleClosurea"}, {"kind": "text", "text": ") -> "}, {"kind": "typeIdentifier", "preciseIdentifier": "s:SS", "text": "String"}], "title": "addStyle(named:usingStyle:prepare:)"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter": {"abstract": [{"type": "text", "text": "The NotificationPresenter let’s you present notifications below the statusBar."}, {"type": "text", "text": " "}, {"type": "text", "text": "You can customize the style (colors, fonts, etc.) and animations. It supports notch"}, {"type": "text", "text": " "}, {"type": "text", "text": "and no-notch devices, landscape & portrait layouts and Drag-to-Dismiss. It can display a"}, {"text": " ", "type": "text"}, {"type": "text", "text": "title, a subtitle, an activity indicator, an animated progress bar & custom views out of the box."}], "fragments": [{"text": "class", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "NotificationPresenter"}], "navigatorTitle": [{"text": "NotificationPresenter", "kind": "identifier"}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter", "url": "/documentation/jdstatusbarnotification/notificationpresenter", "type": "topic", "kind": "symbol", "title": "NotificationPresenter"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/presentCustomView(_:sizingController:styleName:completion:)": {"abstract": [{"text": "Present a notification using a custom subview.", "type": "text"}], "kind": "symbol", "role": "symbol", "type": "topic", "title": "presentCustomView(_:sizingController:styleName:completion:)", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/presentCustomView(_:sizingController:styleName:completion:)", "fragments": [{"text": "func", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "presentCustom<PERSON>iew", "kind": "identifier"}, {"kind": "text", "text": "("}, {"kind": "typeIdentifier", "text": "UIView", "preciseIdentifier": "c:objc(cs)UIView"}, {"kind": "text", "text": ", "}, {"kind": "externalParam", "text": "sizingController"}, {"text": ": ", "kind": "text"}, {"preciseIdentifier": "c:@M@JDStatusBarNotification@objc(pl)JDStatusBarNotificationPresenterCustomViewSizingController", "text": "NotificationPresenterCustomViewSizingController", "kind": "typeIdentifier"}, {"kind": "text", "text": "?, "}, {"text": "styleName", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"text": "String", "kind": "typeIdentifier", "preciseIdentifier": "s:SS"}, {"text": "?, ", "kind": "text"}, {"kind": "externalParam", "text": "completion"}, {"kind": "text", "text": ": "}, {"kind": "typeIdentifier", "preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona", "text": "Completion"}, {"text": "?) -> ", "kind": "text"}, {"text": "UIView", "kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIView"}], "url": "/documentation/jdstatusbarnotification/notificationpresenter/presentcustomview(_:sizingcontroller:stylename:completion:)"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/Completion": {"kind": "symbol", "abstract": [{"type": "text", "text": "Called upon animation completion."}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/Completion", "url": "/documentation/jdstatusbarnotification/notificationpresenter/completion", "fragments": [{"text": "typealias", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "Completion"}], "navigatorTitle": [{"text": "Completion", "kind": "identifier"}], "title": "NotificationPresenter.Completion", "type": "topic"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenterCustomViewSizingController": {"abstract": [{"text": "A protocol for a custom controller, which controls the size of a presented custom view.", "type": "text"}], "role": "symbol", "type": "topic", "navigatorTitle": [{"kind": "identifier", "text": "NotificationPresenterCustomViewSizingController"}], "url": "/documentation/jdstatusbarnotification/notificationpresentercustomviewsizingcontroller", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenterCustomViewSizingController", "kind": "symbol", "fragments": [{"kind": "keyword", "text": "protocol"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "NotificationPresenterCustomViewSizingController"}], "title": "NotificationPresenterCustomViewSizingController"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/presentSwiftView(styleName:viewBuilder:completion:)": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/presentSwiftView(styleName:viewBuilder:completion:)", "type": "topic", "fragments": [{"kind": "keyword", "text": "func"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "presentSwiftView"}, {"kind": "text", "text": "("}, {"kind": "externalParam", "text": "styleName"}, {"kind": "text", "text": ": "}, {"text": "String", "preciseIdentifier": "s:SS", "kind": "typeIdentifier"}, {"text": "?, ", "kind": "text"}, {"kind": "externalParam", "text": "viewBuilder"}, {"text": ": () -> ", "kind": "text"}, {"text": "some", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"preciseIdentifier": "s:7SwiftUI4ViewP", "text": "View", "kind": "typeIdentifier"}, {"text": ", ", "kind": "text"}, {"text": "completion", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"kind": "typeIdentifier", "text": "Completion", "preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona"}, {"kind": "text", "text": "?) -> "}, {"text": "UIView", "kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIView"}], "abstract": [{"text": "Present a notification using a custom SwiftUI view.", "type": "text"}], "title": "presentSwiftView(styleName:viewBuilder:completion:)", "role": "symbol", "url": "/documentation/jdstatusbarnotification/notificationpresenter/presentswiftview(stylename:viewbuilder:completion:)", "kind": "symbol"}}}