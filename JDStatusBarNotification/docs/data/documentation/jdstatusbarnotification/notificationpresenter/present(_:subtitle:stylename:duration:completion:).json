{"metadata": {"role": "symbol", "symbolKind": "method", "title": "present(_:subtitle:styleName:duration:completion:)", "externalID": "s:23JDStatusBarNotification0C9PresenterC7present_8subtitle9styleName8duration10completionSo6UIViewCSS_SSSgAKSdSgyACcSgtF", "modules": [{"name": "JDStatusBarNotification"}], "roleHeading": "Instance Method", "fragments": [{"text": "func", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "present", "kind": "identifier"}, {"kind": "text", "text": "("}, {"kind": "typeIdentifier", "preciseIdentifier": "s:SS", "text": "String"}, {"kind": "text", "text": ", "}, {"kind": "externalParam", "text": "subtitle"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "text": "String", "preciseIdentifier": "s:SS"}, {"text": "?, ", "kind": "text"}, {"text": "styleName", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"preciseIdentifier": "s:SS", "text": "String", "kind": "typeIdentifier"}, {"kind": "text", "text": "?, "}, {"text": "duration", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"preciseIdentifier": "s:Sd", "text": "Double", "kind": "typeIdentifier"}, {"text": "?, ", "kind": "text"}, {"text": "completion", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona", "text": "Completion", "kind": "typeIdentifier"}, {"text": "?) -> ", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIView", "text": "UIView"}]}, "sections": [], "abstract": [{"text": "Present a notification using the default style or a named style.", "type": "text"}], "schemaVersion": {"minor": 3, "patch": 0, "major": 0}, "primaryContentSections": [{"declarations": [{"tokens": [{"text": "@discardableResult", "kind": "attribute"}, {"kind": "text", "text": " "}, {"kind": "keyword", "text": "func"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "present"}, {"text": "(", "kind": "text"}, {"kind": "externalParam", "text": "_"}, {"text": " ", "kind": "text"}, {"kind": "internalParam", "text": "title"}, {"kind": "text", "text": ": "}, {"text": "String", "preciseIdentifier": "s:SS", "kind": "typeIdentifier"}, {"text": ", ", "kind": "text"}, {"text": "subtitle", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"text": "String", "kind": "typeIdentifier", "preciseIdentifier": "s:SS"}, {"text": "? = nil, ", "kind": "text"}, {"kind": "externalParam", "text": "styleName"}, {"kind": "text", "text": ": "}, {"kind": "typeIdentifier", "preciseIdentifier": "s:SS", "text": "String"}, {"text": "? = nil, ", "kind": "text"}, {"text": "duration", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"text": "Double", "kind": "typeIdentifier", "preciseIdentifier": "s:Sd"}, {"kind": "text", "text": "? = nil, "}, {"text": "completion", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona", "kind": "typeIdentifier", "text": "Completion", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/Completion"}, {"kind": "text", "text": "? = nil) -> "}, {"preciseIdentifier": "c:objc(cs)UIView", "kind": "typeIdentifier", "text": "UIView"}], "languages": ["swift"], "platforms": ["iOS"]}], "kind": "declarations"}, {"parameters": [{"name": "title", "content": [{"inlineContent": [{"type": "text", "text": "The text to display as title"}], "type": "paragraph"}]}, {"content": [{"type": "paragraph", "inlineContent": [{"type": "text", "text": "The text to display as subtitle"}]}], "name": "subtitle"}, {"name": "styleName", "content": [{"type": "paragraph", "inlineContent": [{"type": "text", "text": "The name of the style. You can use styles previously added using e.g. "}, {"type": "reference", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/addStyle(named:usingStyle:prepare:)", "isActive": true}, {"type": "text", "text": "."}, {"text": " ", "type": "text"}, {"type": "text", "text": "If no style can be found for the given "}, {"type": "codeVoice", "code": "styleName"}, {"type": "text", "text": " or it is "}, {"code": "nil", "type": "codeVoice"}, {"text": ", the default style will be used.", "type": "text"}]}]}, {"name": "duration", "content": [{"inlineContent": [{"type": "text", "text": "The duration defines how long the notification will be visible. If not provided the notifcation will never be dismissed.."}], "type": "paragraph"}]}, {"name": "completion", "content": [{"inlineContent": [{"text": "A ", "type": "text"}, {"type": "reference", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/Completion", "isActive": true}, {"text": " closure, which gets called once the presentation animation finishes. It won’t be called after dismissal.", "type": "text"}], "type": "paragraph"}]}], "kind": "parameters"}, {"kind": "content", "content": [{"text": "Return Value", "type": "heading", "level": 2, "anchor": "return-value"}, {"inlineContent": [{"type": "text", "text": "The presented UIView for further customization"}], "type": "paragraph"}]}], "identifier": {"url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/present(_:subtitle:styleName:duration:completion:)", "interfaceLanguage": "swift"}, "seeAlsoSections": [{"generated": true, "identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/Completion", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/present(_:subtitle:includedStyle:duration:completion:)", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle"], "title": "Present a notification"}], "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter"]]}, "kind": "symbol", "variants": [{"traits": [{"interfaceLanguage": "swift"}], "paths": ["/documentation/jdstatusbarnotification/notificationpresenter/present(_:subtitle:stylename:duration:completion:)"]}], "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/present(_:subtitle:includedStyle:duration:completion:)": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/present(_:subtitle:includedStyle:duration:completion:)", "url": "/documentation/jdstatusbarnotification/notificationpresenter/present(_:subtitle:includedstyle:duration:completion:)", "kind": "symbol", "title": "present(_:subtitle:includedStyle:duration:completion:)", "type": "topic", "abstract": [{"text": "Present a notification using an included style.", "type": "text"}], "role": "symbol", "fragments": [{"kind": "keyword", "text": "func"}, {"text": " ", "kind": "text"}, {"text": "present", "kind": "identifier"}, {"kind": "text", "text": "("}, {"text": "String", "kind": "typeIdentifier", "preciseIdentifier": "s:SS"}, {"kind": "text", "text": ", "}, {"text": "subtitle", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "s:SS", "kind": "typeIdentifier", "text": "String"}, {"kind": "text", "text": "?, "}, {"text": "includedStyle", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationIncludedStyle", "kind": "typeIdentifier", "text": "IncludedStatusBarNotificationStyle"}, {"text": ", ", "kind": "text"}, {"kind": "externalParam", "text": "duration"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "s:Sd", "text": "Double"}, {"text": "?, ", "kind": "text"}, {"text": "completion", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona", "text": "Completion"}, {"kind": "text", "text": "?) -> "}, {"kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIView", "text": "UIView"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter": {"abstract": [{"type": "text", "text": "The NotificationPresenter let’s you present notifications below the statusBar."}, {"type": "text", "text": " "}, {"type": "text", "text": "You can customize the style (colors, fonts, etc.) and animations. It supports notch"}, {"type": "text", "text": " "}, {"type": "text", "text": "and no-notch devices, landscape & portrait layouts and Drag-to-Dismiss. It can display a"}, {"text": " ", "type": "text"}, {"type": "text", "text": "title, a subtitle, an activity indicator, an animated progress bar & custom views out of the box."}], "fragments": [{"text": "class", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "NotificationPresenter"}], "navigatorTitle": [{"text": "NotificationPresenter", "kind": "identifier"}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter", "url": "/documentation/jdstatusbarnotification/notificationpresenter", "type": "topic", "kind": "symbol", "title": "NotificationPresenter"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/addStyle(named:usingStyle:prepare:)": {"url": "/documentation/jdstatusbarnotification/notificationpresenter/addstyle(named:usingstyle:prepare:)", "abstract": [{"text": "Adds a new named style - based on an included style, if given.", "type": "text"}, {"type": "text", "text": " "}, {"type": "text", "text": "This can later be used by referencing it using the "}, {"type": "codeVoice", "code": "styleName"}, {"text": ".", "type": "text"}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/addStyle(named:usingStyle:prepare:)", "kind": "symbol", "type": "topic", "fragments": [{"kind": "keyword", "text": "func"}, {"text": " ", "kind": "text"}, {"text": "addStyle", "kind": "identifier"}, {"text": "(", "kind": "text"}, {"text": "named", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "s:SS", "text": "String"}, {"kind": "text", "text": ", "}, {"text": "usingStyle", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"kind": "typeIdentifier", "text": "IncludedStatusBarNotificationStyle", "preciseIdentifier": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationIncludedStyle"}, {"text": ", ", "kind": "text"}, {"kind": "externalParam", "text": "prepare"}, {"text": ": ", "kind": "text"}, {"text": "PrepareStyleClosure", "kind": "typeIdentifier", "preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC19PrepareStyleClosurea"}, {"kind": "text", "text": ") -> "}, {"kind": "typeIdentifier", "preciseIdentifier": "s:SS", "text": "String"}], "title": "addStyle(named:usingStyle:prepare:)"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle": {"abstract": [{"text": "These included styles let you easily change the visual appearance of a", "type": "text"}, {"type": "text", "text": " "}, {"type": "text", "text": "notification without creating your own custom style."}], "fragments": [{"text": "enum", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "IncludedStatusBarNotificationStyle", "kind": "identifier"}], "navigatorTitle": [{"kind": "identifier", "text": "IncludedStatusBarNotificationStyle"}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle", "url": "/documentation/jdstatusbarnotification/includedstatusbarnotificationstyle", "type": "topic", "kind": "symbol", "title": "IncludedStatusBarNotificationStyle"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/Completion": {"kind": "symbol", "abstract": [{"type": "text", "text": "Called upon animation completion."}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/Completion", "url": "/documentation/jdstatusbarnotification/notificationpresenter/completion", "fragments": [{"text": "typealias", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "Completion"}], "navigatorTitle": [{"text": "Completion", "kind": "identifier"}], "title": "NotificationPresenter.Completion", "type": "topic"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/present(_:subtitle:styleName:duration:completion:)": {"kind": "symbol", "type": "topic", "url": "/documentation/jdstatusbarnotification/notificationpresenter/present(_:subtitle:stylename:duration:completion:)", "title": "present(_:subtitle:styleName:duration:completion:)", "abstract": [{"type": "text", "text": "Present a notification using the default style or a named style."}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/present(_:subtitle:styleName:duration:completion:)", "fragments": [{"text": "func", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "present", "kind": "identifier"}, {"kind": "text", "text": "("}, {"text": "String", "preciseIdentifier": "s:SS", "kind": "typeIdentifier"}, {"kind": "text", "text": ", "}, {"text": "subtitle", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"text": "String", "kind": "typeIdentifier", "preciseIdentifier": "s:SS"}, {"text": "?, ", "kind": "text"}, {"text": "styleName", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "s:SS", "text": "String", "kind": "typeIdentifier"}, {"kind": "text", "text": "?, "}, {"kind": "externalParam", "text": "duration"}, {"text": ": ", "kind": "text"}, {"text": "Double", "kind": "typeIdentifier", "preciseIdentifier": "s:Sd"}, {"kind": "text", "text": "?, "}, {"text": "completion", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona", "text": "Completion"}, {"kind": "text", "text": "?) -> "}, {"kind": "typeIdentifier", "text": "UIView", "preciseIdentifier": "c:objc(cs)UIView"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}}}