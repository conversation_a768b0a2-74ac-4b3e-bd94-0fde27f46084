{"seeAlsoSections": [{"identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/isVisible", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/updateTitle(_:)", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/setWindowScene(_:)"], "title": "Additional Presenter APIs", "generated": true}], "sections": [], "abstract": [{"text": "Updates the subtitle of an existing notification without animation.", "type": "text"}], "schemaVersion": {"patch": 0, "major": 0, "minor": 3}, "metadata": {"title": "updateSubtitle(_:)", "modules": [{"name": "JDStatusBarNotification"}], "symbolKind": "method", "externalID": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationPresenter(im)updateSubtitle:", "fragments": [{"kind": "keyword", "text": "func"}, {"text": " ", "kind": "text"}, {"text": "updateSubtitle", "kind": "identifier"}, {"kind": "text", "text": "("}, {"preciseIdentifier": "s:SS", "kind": "typeIdentifier", "text": "String"}, {"kind": "text", "text": "?)"}], "role": "symbol", "roleHeading": "Instance Method"}, "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter"]]}, "primaryContentSections": [{"kind": "declarations", "declarations": [{"platforms": ["iOS"], "tokens": [{"kind": "attribute", "text": "@objc"}, {"text": "(updateSubtitle:) ", "kind": "text"}, {"text": "func", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "updateSubtitle", "kind": "identifier"}, {"kind": "text", "text": "("}, {"kind": "externalParam", "text": "_"}, {"kind": "text", "text": " "}, {"kind": "internalParam", "text": "subtitle"}, {"text": ": ", "kind": "text"}, {"text": "String", "kind": "typeIdentifier", "preciseIdentifier": "s:SS"}, {"text": "?)", "kind": "text"}], "languages": ["swift"]}]}, {"kind": "parameters", "parameters": [{"content": [{"type": "paragraph", "inlineContent": [{"text": "The new subtitle to display", "type": "text"}]}], "name": "subtitle"}]}], "identifier": {"interfaceLanguage": "swift", "url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/updateSubtitle(_:)"}, "variants": [{"traits": [{"interfaceLanguage": "swift"}], "paths": ["/documentation/jdstatusbarnotification/notificationpresenter/updatesubtitle(_:)"]}], "kind": "symbol", "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter": {"abstract": [{"type": "text", "text": "The NotificationPresenter let’s you present notifications below the statusBar."}, {"type": "text", "text": " "}, {"type": "text", "text": "You can customize the style (colors, fonts, etc.) and animations. It supports notch"}, {"type": "text", "text": " "}, {"type": "text", "text": "and no-notch devices, landscape & portrait layouts and Drag-to-Dismiss. It can display a"}, {"text": " ", "type": "text"}, {"type": "text", "text": "title, a subtitle, an activity indicator, an animated progress bar & custom views out of the box."}], "fragments": [{"text": "class", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "NotificationPresenter"}], "navigatorTitle": [{"text": "NotificationPresenter", "kind": "identifier"}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter", "url": "/documentation/jdstatusbarnotification/notificationpresenter", "type": "topic", "kind": "symbol", "title": "NotificationPresenter"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/updateTitle(_:)": {"abstract": [{"text": "Updates the title of an existing notification without animation.", "type": "text"}], "url": "/documentation/jdstatusbarnotification/notificationpresenter/updatetitle(_:)", "fragments": [{"kind": "keyword", "text": "func"}, {"text": " ", "kind": "text"}, {"text": "updateTitle", "kind": "identifier"}, {"text": "(", "kind": "text"}, {"preciseIdentifier": "s:SS", "text": "String", "kind": "typeIdentifier"}, {"text": ")", "kind": "text"}], "kind": "symbol", "title": "updateTitle(_:)", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/updateTitle(_:)", "type": "topic", "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/setWindowScene(_:)": {"url": "/documentation/jdstatusbarnotification/notificationpresenter/setwindowscene(_:)", "abstract": [{"text": "Lets you set an explicit ", "type": "text"}, {"type": "codeVoice", "code": "UIWindowScene"}, {"text": ", in which notifications should be presented in. In most cases you don’t need to set this.", "type": "text"}], "fragments": [{"text": "func", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "setWindowScene", "kind": "identifier"}, {"text": "(", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIWindowScene", "text": "UIWindowScene"}, {"text": "?)", "kind": "text"}], "kind": "symbol", "title": "setWindowScene(_:)", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/setWindowScene(_:)", "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/isVisible": {"abstract": [{"type": "text", "text": "Let’s you check if a notification is currently displayed."}], "url": "/documentation/jdstatusbarnotification/notificationpresenter/isvisible", "fragments": [{"text": "var", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "isVisible", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"text": "Bool", "preciseIdentifier": "s:Sb", "kind": "typeIdentifier"}], "kind": "symbol", "title": "isVisible", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/isVisible", "type": "topic", "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/updateSubtitle(_:)": {"url": "/documentation/jdstatusbarnotification/notificationpresenter/updatesubtitle(_:)", "abstract": [{"text": "Updates the subtitle of an existing notification without animation.", "type": "text"}], "fragments": [{"text": "func", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "updateSubtitle", "kind": "identifier"}, {"text": "(", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "s:SS", "text": "String"}, {"text": "?)", "kind": "text"}], "kind": "symbol", "title": "updateSubtitle(_:)", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/updateSubtitle(_:)", "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}}}