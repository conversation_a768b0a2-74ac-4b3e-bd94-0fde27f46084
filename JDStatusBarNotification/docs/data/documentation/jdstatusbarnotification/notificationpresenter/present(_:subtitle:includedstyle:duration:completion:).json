{"kind": "symbol", "variants": [{"traits": [{"interfaceLanguage": "swift"}], "paths": ["/documentation/jdstatusbarnotification/notificationpresenter/present(_:subtitle:includedstyle:duration:completion:)"]}], "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter"]]}, "schemaVersion": {"patch": 0, "major": 0, "minor": 3}, "identifier": {"interfaceLanguage": "swift", "url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/present(_:subtitle:includedStyle:duration:completion:)"}, "sections": [], "abstract": [{"type": "text", "text": "Present a notification using an included style."}], "seeAlsoSections": [{"generated": true, "title": "Present a notification", "identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/Completion", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/present(_:subtitle:styleName:duration:completion:)", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle"]}], "primaryContentSections": [{"declarations": [{"platforms": ["iOS"], "tokens": [{"text": "@discardableResult", "kind": "attribute"}, {"text": " ", "kind": "text"}, {"text": "func", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "present", "kind": "identifier"}, {"kind": "text", "text": "("}, {"text": "_", "kind": "externalParam"}, {"kind": "text", "text": " "}, {"kind": "internalParam", "text": "title"}, {"kind": "text", "text": ": "}, {"text": "String", "kind": "typeIdentifier", "preciseIdentifier": "s:SS"}, {"text": ", ", "kind": "text"}, {"text": "subtitle", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "text": "String", "preciseIdentifier": "s:SS"}, {"text": "? = nil, ", "kind": "text"}, {"text": "includedStyle", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle", "preciseIdentifier": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationIncludedStyle", "kind": "typeIdentifier", "text": "IncludedStatusBarNotificationStyle"}, {"text": ", ", "kind": "text"}, {"kind": "externalParam", "text": "duration"}, {"kind": "text", "text": ": "}, {"text": "Double", "preciseIdentifier": "s:Sd", "kind": "typeIdentifier"}, {"kind": "text", "text": "? = nil, "}, {"text": "completion", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/Completion", "text": "Completion", "kind": "typeIdentifier", "preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona"}, {"kind": "text", "text": "? = nil) -> "}, {"preciseIdentifier": "c:objc(cs)UIView", "kind": "typeIdentifier", "text": "UIView"}], "languages": ["swift"]}], "kind": "declarations"}, {"kind": "parameters", "parameters": [{"content": [{"type": "paragraph", "inlineContent": [{"type": "text", "text": "The text to display as title"}]}], "name": "title"}, {"content": [{"inlineContent": [{"text": "The text to display as subtitle", "type": "text"}], "type": "paragraph"}], "name": "subtitle"}, {"content": [{"inlineContent": [{"text": "An existing ", "type": "text"}, {"isActive": true, "type": "reference", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle"}], "type": "paragraph"}], "name": "includedStyle"}, {"name": "duration", "content": [{"type": "paragraph", "inlineContent": [{"type": "text", "text": "The duration defines how long the notification will be visible. If not provided the notifcation will never be dismissed."}]}]}, {"content": [{"type": "paragraph", "inlineContent": [{"type": "text", "text": "A "}, {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/Completion", "type": "reference", "isActive": true}, {"type": "text", "text": " closure, which gets called once the presentation animation finishes. It won’t be called after dismissal."}]}], "name": "completion"}]}, {"content": [{"type": "heading", "anchor": "return-value", "text": "Return Value", "level": 2}, {"inlineContent": [{"type": "text", "text": "The presented UIView for further customization"}], "type": "paragraph"}], "kind": "content"}], "metadata": {"fragments": [{"text": "func", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "present", "kind": "identifier"}, {"kind": "text", "text": "("}, {"preciseIdentifier": "s:SS", "kind": "typeIdentifier", "text": "String"}, {"kind": "text", "text": ", "}, {"kind": "externalParam", "text": "subtitle"}, {"kind": "text", "text": ": "}, {"text": "String", "kind": "typeIdentifier", "preciseIdentifier": "s:SS"}, {"text": "?, ", "kind": "text"}, {"kind": "externalParam", "text": "includedStyle"}, {"text": ": ", "kind": "text"}, {"text": "IncludedStatusBarNotificationStyle", "preciseIdentifier": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationIncludedStyle", "kind": "typeIdentifier"}, {"kind": "text", "text": ", "}, {"text": "duration", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"text": "Double", "preciseIdentifier": "s:Sd", "kind": "typeIdentifier"}, {"kind": "text", "text": "?, "}, {"kind": "externalParam", "text": "completion"}, {"text": ": ", "kind": "text"}, {"preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona", "text": "Completion", "kind": "typeIdentifier"}, {"text": "?) -> ", "kind": "text"}, {"preciseIdentifier": "c:objc(cs)UIView", "kind": "typeIdentifier", "text": "UIView"}], "role": "symbol", "title": "present(_:subtitle:includedStyle:duration:completion:)", "symbolKind": "method", "roleHeading": "Instance Method", "externalID": "s:23JDStatusBarNotification0C9PresenterC7present_8subtitle13includedStyle8duration10completionSo6UIViewCSS_SSSgAA014IncludedStatusbcH0OSdSgyACcSgtF", "modules": [{"name": "JDStatusBarNotification"}]}, "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter": {"abstract": [{"type": "text", "text": "The NotificationPresenter let’s you present notifications below the statusBar."}, {"type": "text", "text": " "}, {"type": "text", "text": "You can customize the style (colors, fonts, etc.) and animations. It supports notch"}, {"type": "text", "text": " "}, {"type": "text", "text": "and no-notch devices, landscape & portrait layouts and Drag-to-Dismiss. It can display a"}, {"text": " ", "type": "text"}, {"type": "text", "text": "title, a subtitle, an activity indicator, an animated progress bar & custom views out of the box."}], "fragments": [{"text": "class", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "NotificationPresenter"}], "navigatorTitle": [{"text": "NotificationPresenter", "kind": "identifier"}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter", "url": "/documentation/jdstatusbarnotification/notificationpresenter", "type": "topic", "kind": "symbol", "title": "NotificationPresenter"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/Completion": {"kind": "symbol", "abstract": [{"type": "text", "text": "Called upon animation completion."}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/Completion", "url": "/documentation/jdstatusbarnotification/notificationpresenter/completion", "fragments": [{"text": "typealias", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "Completion"}], "navigatorTitle": [{"text": "Completion", "kind": "identifier"}], "title": "NotificationPresenter.Completion", "type": "topic"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle": {"abstract": [{"text": "These included styles let you easily change the visual appearance of a", "type": "text"}, {"type": "text", "text": " "}, {"type": "text", "text": "notification without creating your own custom style."}], "fragments": [{"text": "enum", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "IncludedStatusBarNotificationStyle", "kind": "identifier"}], "navigatorTitle": [{"kind": "identifier", "text": "IncludedStatusBarNotificationStyle"}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle", "url": "/documentation/jdstatusbarnotification/includedstatusbarnotificationstyle", "type": "topic", "kind": "symbol", "title": "IncludedStatusBarNotificationStyle"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/present(_:subtitle:styleName:duration:completion:)": {"kind": "symbol", "type": "topic", "url": "/documentation/jdstatusbarnotification/notificationpresenter/present(_:subtitle:stylename:duration:completion:)", "title": "present(_:subtitle:styleName:duration:completion:)", "abstract": [{"type": "text", "text": "Present a notification using the default style or a named style."}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/present(_:subtitle:styleName:duration:completion:)", "fragments": [{"text": "func", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "present", "kind": "identifier"}, {"kind": "text", "text": "("}, {"text": "String", "preciseIdentifier": "s:SS", "kind": "typeIdentifier"}, {"kind": "text", "text": ", "}, {"text": "subtitle", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"text": "String", "kind": "typeIdentifier", "preciseIdentifier": "s:SS"}, {"text": "?, ", "kind": "text"}, {"text": "styleName", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "s:SS", "text": "String", "kind": "typeIdentifier"}, {"kind": "text", "text": "?, "}, {"kind": "externalParam", "text": "duration"}, {"text": ": ", "kind": "text"}, {"text": "Double", "kind": "typeIdentifier", "preciseIdentifier": "s:Sd"}, {"kind": "text", "text": "?, "}, {"text": "completion", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona", "text": "Completion"}, {"kind": "text", "text": "?) -> "}, {"kind": "typeIdentifier", "text": "UIView", "preciseIdentifier": "c:objc(cs)UIView"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/present(_:subtitle:includedStyle:duration:completion:)": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/present(_:subtitle:includedStyle:duration:completion:)", "url": "/documentation/jdstatusbarnotification/notificationpresenter/present(_:subtitle:includedstyle:duration:completion:)", "kind": "symbol", "title": "present(_:subtitle:includedStyle:duration:completion:)", "type": "topic", "abstract": [{"text": "Present a notification using an included style.", "type": "text"}], "role": "symbol", "fragments": [{"kind": "keyword", "text": "func"}, {"text": " ", "kind": "text"}, {"text": "present", "kind": "identifier"}, {"kind": "text", "text": "("}, {"text": "String", "kind": "typeIdentifier", "preciseIdentifier": "s:SS"}, {"kind": "text", "text": ", "}, {"text": "subtitle", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "s:SS", "kind": "typeIdentifier", "text": "String"}, {"kind": "text", "text": "?, "}, {"text": "includedStyle", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationIncludedStyle", "kind": "typeIdentifier", "text": "IncludedStatusBarNotificationStyle"}, {"text": ", ", "kind": "text"}, {"kind": "externalParam", "text": "duration"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "s:Sd", "text": "Double"}, {"text": "?, ", "kind": "text"}, {"text": "completion", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona", "text": "Completion"}, {"kind": "text", "text": "?) -> "}, {"kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIView", "text": "UIView"}]}}}