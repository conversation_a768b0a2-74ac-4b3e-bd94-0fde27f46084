{"hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter"]]}, "metadata": {"title": "displayLeftView(_:)", "fragments": [{"kind": "keyword", "text": "func"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "displayLeftView"}, {"text": "(", "kind": "text"}, {"kind": "typeIdentifier", "text": "UIView", "preciseIdentifier": "c:objc(cs)UIView"}, {"kind": "text", "text": "?)"}], "modules": [{"name": "JDStatusBarNotification"}], "symbolKind": "method", "roleHeading": "Instance Method", "externalID": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationPresenter(im)displayLeftView:", "role": "symbol"}, "schemaVersion": {"minor": 3, "major": 0, "patch": 0}, "sections": [], "identifier": {"interfaceLanguage": "swift", "url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/displayLeftView(_:)"}, "primaryContentSections": [{"kind": "declarations", "declarations": [{"platforms": ["iOS"], "tokens": [{"kind": "attribute", "text": "@objc"}, {"kind": "text", "text": "(displayLeftView:) "}, {"kind": "keyword", "text": "func"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "displayLeftView"}, {"text": "(", "kind": "text"}, {"kind": "externalParam", "text": "_"}, {"kind": "text", "text": " "}, {"text": "leftView", "kind": "internalParam"}, {"text": ": ", "kind": "text"}, {"text": "UIView", "preciseIdentifier": "c:objc(cs)UIView", "kind": "typeIdentifier"}, {"text": "?)", "kind": "text"}], "languages": ["swift"]}]}, {"kind": "parameters", "parameters": [{"content": [{"inlineContent": [{"text": "A custom ", "type": "text"}, {"code": "UIView", "type": "codeVoice"}, {"type": "text", "text": " to display on the left side of the text. E.g. an"}, {"type": "text", "text": " "}, {"text": "icon / image / profile picture etc. A nil value removes an existing leftView.", "type": "text"}], "type": "paragraph"}], "name": "leftView"}]}], "seeAlsoSections": [{"generated": true, "title": "Display supplementary views", "identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/displayProgressBar(at:)", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/animateProgressBar(to:duration:completion:)", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/displayActivityIndicator(_:)"]}], "variants": [{"paths": ["/documentation/jdstatusbarnotification/notificationpresenter/displayleftview(_:)"], "traits": [{"interfaceLanguage": "swift"}]}], "kind": "symbol", "abstract": [{"text": "Displays a view on the left side of the text.", "type": "text"}, {"type": "text", "text": " "}, {"type": "text", "text": "The layout is defined by the "}, {"type": "reference", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle", "isActive": true}, {"type": "text", "text": "."}], "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/displayActivityIndicator(_:)": {"abstract": [{"type": "text", "text": "Displays an activity indicator as the notifications left view."}], "role": "symbol", "type": "topic", "url": "/documentation/jdstatusbarnotification/notificationpresenter/displayactivityindicator(_:)", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/displayActivityIndicator(_:)", "kind": "symbol", "fragments": [{"kind": "keyword", "text": "func"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "displayActivityIndicator"}, {"kind": "text", "text": "("}, {"kind": "typeIdentifier", "preciseIdentifier": "s:Sb", "text": "Bool"}, {"kind": "text", "text": ")"}], "title": "displayActivityIndicator(_:)"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter": {"abstract": [{"type": "text", "text": "The NotificationPresenter let’s you present notifications below the statusBar."}, {"type": "text", "text": " "}, {"type": "text", "text": "You can customize the style (colors, fonts, etc.) and animations. It supports notch"}, {"type": "text", "text": " "}, {"type": "text", "text": "and no-notch devices, landscape & portrait layouts and Drag-to-Dismiss. It can display a"}, {"text": " ", "type": "text"}, {"type": "text", "text": "title, a subtitle, an activity indicator, an animated progress bar & custom views out of the box."}], "fragments": [{"text": "class", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "NotificationPresenter"}], "navigatorTitle": [{"text": "NotificationPresenter", "kind": "identifier"}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter", "url": "/documentation/jdstatusbarnotification/notificationpresenter", "type": "topic", "kind": "symbol", "title": "NotificationPresenter"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/displayLeftView(_:)": {"type": "topic", "url": "/documentation/jdstatusbarnotification/notificationpresenter/displayleftview(_:)", "abstract": [{"text": "Displays a view on the left side of the text.", "type": "text"}, {"type": "text", "text": " "}, {"type": "text", "text": "The layout is defined by the "}, {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle", "isActive": true, "type": "reference"}, {"type": "text", "text": "."}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/displayLeftView(_:)", "kind": "symbol", "fragments": [{"text": "func", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "displayLeftView"}, {"text": "(", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIView", "text": "UIView"}, {"kind": "text", "text": "?)"}], "title": "displayLeftView(_:)", "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle": {"navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationLeftViewStyle"}], "type": "topic", "kind": "symbol", "title": "StatusBarNotificationLeftViewStyle", "abstract": [{"text": "Defines the appearance of a left-view, if set. It also applies to the activity indicator.", "type": "text"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewstyle", "role": "symbol", "fragments": [{"text": "class", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "StatusBarNotificationLeftViewStyle", "kind": "identifier"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/displayProgressBar(at:)": {"type": "topic", "url": "/documentation/jdstatusbarnotification/notificationpresenter/displayprogressbar(at:)", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/displayProgressBar(at:)", "kind": "symbol", "abstract": [{"text": "Displays a progress bar at the given ", "type": "text"}, {"code": "percentage", "type": "codeVoice"}, {"text": ".", "type": "text"}], "fragments": [{"kind": "keyword", "text": "func"}, {"kind": "text", "text": " "}, {"text": "displayProgressBar", "kind": "identifier"}, {"kind": "text", "text": "("}, {"kind": "externalParam", "text": "at"}, {"kind": "text", "text": ": "}, {"text": "Double", "kind": "typeIdentifier", "preciseIdentifier": "s:Sd"}, {"kind": "text", "text": ")"}], "title": "displayProgressBar(at:)", "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/animateProgressBar(to:duration:completion:)": {"type": "topic", "role": "symbol", "abstract": [{"type": "text", "text": "Displays a progress bar and animates it to the provided "}, {"code": "percentage", "type": "codeVoice"}, {"type": "text", "text": "."}], "title": "animateProgressBar(to:duration:completion:)", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/animateProgressBar(to:duration:completion:)", "url": "/documentation/jdstatusbarnotification/notificationpresenter/animateprogressbar(to:duration:completion:)", "fragments": [{"text": "func", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "animateProgressBar", "kind": "identifier"}, {"kind": "text", "text": "("}, {"text": "to", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"text": "Double", "preciseIdentifier": "s:Sd", "kind": "typeIdentifier"}, {"text": ", ", "kind": "text"}, {"text": "duration", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "s:Sd", "text": "Double", "kind": "typeIdentifier"}, {"kind": "text", "text": ", "}, {"kind": "externalParam", "text": "completion"}, {"text": ": ", "kind": "text"}, {"text": "Completion", "kind": "typeIdentifier", "preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona"}, {"kind": "text", "text": "?)"}], "kind": "symbol"}}}