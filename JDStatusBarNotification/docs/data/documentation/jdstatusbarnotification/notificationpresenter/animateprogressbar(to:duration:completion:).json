{"variants": [{"traits": [{"interfaceLanguage": "swift"}], "paths": ["/documentation/jdstatusbarnotification/notificationpresenter/animateprogressbar(to:duration:completion:)"]}], "identifier": {"interfaceLanguage": "swift", "url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/animateProgressBar(to:duration:completion:)"}, "kind": "symbol", "abstract": [{"type": "text", "text": "Displays a progress bar and animates it to the provided "}, {"type": "codeVoice", "code": "percentage"}, {"type": "text", "text": "."}], "metadata": {"title": "animateProgressBar(to:duration:completion:)", "modules": [{"name": "JDStatusBarNotification"}], "symbolKind": "method", "externalID": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationPresenter(im)animateProgressBarToPercentage:animationDuration:completion:", "fragments": [{"text": "func", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "animateProgressBar", "kind": "identifier"}, {"text": "(", "kind": "text"}, {"kind": "externalParam", "text": "to"}, {"text": ": ", "kind": "text"}, {"preciseIdentifier": "s:Sd", "text": "Double", "kind": "typeIdentifier"}, {"text": ", ", "kind": "text"}, {"kind": "externalParam", "text": "duration"}, {"text": ": ", "kind": "text"}, {"preciseIdentifier": "s:Sd", "kind": "typeIdentifier", "text": "Double"}, {"kind": "text", "text": ", "}, {"kind": "externalParam", "text": "completion"}, {"text": ": ", "kind": "text"}, {"preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona", "text": "Completion", "kind": "typeIdentifier"}, {"kind": "text", "text": "?)"}], "roleHeading": "Instance Method", "role": "symbol"}, "sections": [], "schemaVersion": {"major": 0, "patch": 0, "minor": 3}, "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter"]]}, "seeAlsoSections": [{"title": "Display supplementary views", "generated": true, "identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/displayProgressBar(at:)", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/displayActivityIndicator(_:)", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/displayLeftView(_:)"]}], "primaryContentSections": [{"kind": "declarations", "declarations": [{"languages": ["swift"], "tokens": [{"text": "@objc", "kind": "attribute"}, {"text": "(animateProgressBarToPercentage:animationDuration:completion:) ", "kind": "text"}, {"text": "func", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "animateProgressBar", "kind": "identifier"}, {"text": "(", "kind": "text"}, {"kind": "externalParam", "text": "to"}, {"text": " ", "kind": "text"}, {"kind": "internalParam", "text": "percentage"}, {"text": ": ", "kind": "text"}, {"text": "Double", "preciseIdentifier": "s:Sd", "kind": "typeIdentifier"}, {"text": ", ", "kind": "text"}, {"kind": "externalParam", "text": "duration"}, {"text": ": ", "kind": "text"}, {"text": "Double", "preciseIdentifier": "s:Sd", "kind": "typeIdentifier"}, {"text": ", ", "kind": "text"}, {"kind": "externalParam", "text": "completion"}, {"text": ": ", "kind": "text"}, {"preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona", "kind": "typeIdentifier", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/Completion", "text": "Completion"}, {"kind": "text", "text": "?)"}], "platforms": ["iOS"]}]}, {"kind": "parameters", "parameters": [{"name": "percentage", "content": [{"type": "paragraph", "inlineContent": [{"type": "text", "text": "Relative progress from 0.0 to 1.0"}]}]}, {"content": [{"inlineContent": [{"type": "text", "text": "The duration of the animation from the current percentage to the provided percentage."}], "type": "paragraph"}], "name": "duration"}, {"content": [{"type": "paragraph", "inlineContent": [{"text": "A ", "type": "text"}, {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/Completion", "type": "reference", "isActive": true}, {"type": "text", "text": ", which gets called once the progress bar animation finishes."}]}], "name": "completion"}]}, {"content": [{"text": "Discussion", "level": 2, "type": "heading", "anchor": "discussion"}, {"type": "paragraph", "inlineContent": [{"text": "Animates the progress bar from the currently set ", "type": "text"}, {"type": "codeVoice", "code": "percentage"}, {"type": "text", "text": " to the provided "}, {"type": "codeVoice", "code": "percentage"}, {"type": "text", "text": " using the provided "}, {"type": "codeVoice", "code": "duration"}, {"text": ".", "type": "text"}, {"text": " ", "type": "text"}, {"type": "text", "text": "The progress bar will be styled according to the current "}, {"type": "reference", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle", "isActive": true}, {"type": "text", "text": "."}]}], "kind": "content"}], "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/animateProgressBar(to:duration:completion:)": {"type": "topic", "role": "symbol", "abstract": [{"type": "text", "text": "Displays a progress bar and animates it to the provided "}, {"code": "percentage", "type": "codeVoice"}, {"type": "text", "text": "."}], "title": "animateProgressBar(to:duration:completion:)", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/animateProgressBar(to:duration:completion:)", "url": "/documentation/jdstatusbarnotification/notificationpresenter/animateprogressbar(to:duration:completion:)", "fragments": [{"text": "func", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "animateProgressBar", "kind": "identifier"}, {"kind": "text", "text": "("}, {"text": "to", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"text": "Double", "preciseIdentifier": "s:Sd", "kind": "typeIdentifier"}, {"text": ", ", "kind": "text"}, {"text": "duration", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "s:Sd", "text": "Double", "kind": "typeIdentifier"}, {"kind": "text", "text": ", "}, {"kind": "externalParam", "text": "completion"}, {"text": ": ", "kind": "text"}, {"text": "Completion", "kind": "typeIdentifier", "preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona"}, {"kind": "text", "text": "?)"}], "kind": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle": {"type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle", "kind": "symbol", "role": "symbol", "fragments": [{"text": "class", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationProgressBarStyle", "kind": "identifier"}], "title": "StatusBarNotificationProgressBarStyle", "abstract": [{"type": "text", "text": "Defines the appearance of the progress bar."}], "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationProgressBarStyle"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarstyle"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/Completion": {"kind": "symbol", "abstract": [{"type": "text", "text": "Called upon animation completion."}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/Completion", "url": "/documentation/jdstatusbarnotification/notificationpresenter/completion", "fragments": [{"text": "typealias", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "Completion"}], "navigatorTitle": [{"text": "Completion", "kind": "identifier"}], "title": "NotificationPresenter.Completion", "type": "topic"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter": {"abstract": [{"type": "text", "text": "The NotificationPresenter let’s you present notifications below the statusBar."}, {"type": "text", "text": " "}, {"type": "text", "text": "You can customize the style (colors, fonts, etc.) and animations. It supports notch"}, {"type": "text", "text": " "}, {"type": "text", "text": "and no-notch devices, landscape & portrait layouts and Drag-to-Dismiss. It can display a"}, {"text": " ", "type": "text"}, {"type": "text", "text": "title, a subtitle, an activity indicator, an animated progress bar & custom views out of the box."}], "fragments": [{"text": "class", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "NotificationPresenter"}], "navigatorTitle": [{"text": "NotificationPresenter", "kind": "identifier"}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter", "url": "/documentation/jdstatusbarnotification/notificationpresenter", "type": "topic", "kind": "symbol", "title": "NotificationPresenter"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/displayActivityIndicator(_:)": {"abstract": [{"type": "text", "text": "Displays an activity indicator as the notifications left view."}], "role": "symbol", "type": "topic", "url": "/documentation/jdstatusbarnotification/notificationpresenter/displayactivityindicator(_:)", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/displayActivityIndicator(_:)", "kind": "symbol", "fragments": [{"kind": "keyword", "text": "func"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "displayActivityIndicator"}, {"kind": "text", "text": "("}, {"kind": "typeIdentifier", "preciseIdentifier": "s:Sb", "text": "Bool"}, {"kind": "text", "text": ")"}], "title": "displayActivityIndicator(_:)"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/displayProgressBar(at:)": {"type": "topic", "url": "/documentation/jdstatusbarnotification/notificationpresenter/displayprogressbar(at:)", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/displayProgressBar(at:)", "kind": "symbol", "abstract": [{"text": "Displays a progress bar at the given ", "type": "text"}, {"code": "percentage", "type": "codeVoice"}, {"text": ".", "type": "text"}], "fragments": [{"kind": "keyword", "text": "func"}, {"kind": "text", "text": " "}, {"text": "displayProgressBar", "kind": "identifier"}, {"kind": "text", "text": "("}, {"kind": "externalParam", "text": "at"}, {"kind": "text", "text": ": "}, {"text": "Double", "kind": "typeIdentifier", "preciseIdentifier": "s:Sd"}, {"kind": "text", "text": ")"}], "title": "displayProgressBar(at:)", "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle": {"navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationLeftViewStyle"}], "type": "topic", "kind": "symbol", "title": "StatusBarNotificationLeftViewStyle", "abstract": [{"text": "Defines the appearance of a left-view, if set. It also applies to the activity indicator.", "type": "text"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewstyle", "role": "symbol", "fragments": [{"text": "class", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "StatusBarNotificationLeftViewStyle", "kind": "identifier"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/displayLeftView(_:)": {"type": "topic", "url": "/documentation/jdstatusbarnotification/notificationpresenter/displayleftview(_:)", "abstract": [{"text": "Displays a view on the left side of the text.", "type": "text"}, {"type": "text", "text": " "}, {"type": "text", "text": "The layout is defined by the "}, {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle", "isActive": true, "type": "reference"}, {"type": "text", "text": "."}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/displayLeftView(_:)", "kind": "symbol", "fragments": [{"text": "func", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "displayLeftView"}, {"text": "(", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIView", "text": "UIView"}, {"kind": "text", "text": "?)"}], "title": "displayLeftView(_:)", "role": "symbol"}}}