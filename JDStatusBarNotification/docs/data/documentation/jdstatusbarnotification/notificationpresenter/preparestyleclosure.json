{"schemaVersion": {"patch": 0, "major": 0, "minor": 3}, "sections": [], "seeAlsoSections": [{"generated": true, "title": "Customize the style (Appearance & Behavior)", "identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/updateDefaultStyle(_:)", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/addStyle(named:usingStyle:prepare:)"]}], "metadata": {"title": "NotificationPresenter.PrepareStyleClosure", "navigatorTitle": [{"text": "PrepareStyleClosure", "kind": "identifier"}], "modules": [{"name": "JDStatusBarNotification"}], "roleHeading": "Type Alias", "role": "symbol", "externalID": "s:23JDStatusBarNotification0C9PresenterC19PrepareStyleClosurea", "symbolKind": "typealias", "fragments": [{"text": "typealias", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "PrepareStyleClosure"}]}, "variants": [{"traits": [{"interfaceLanguage": "swift"}], "paths": ["/documentation/jdstatusbarnotification/notificationpresenter/preparestyleclosure"]}], "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter"]]}, "identifier": {"url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/PrepareStyleClosure", "interfaceLanguage": "swift"}, "kind": "symbol", "abstract": [{"type": "text", "text": "Creates a modified copy of an existing "}, {"type": "reference", "isActive": true, "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle"}, {"type": "text", "text": " instance."}], "primaryContentSections": [{"kind": "declarations", "declarations": [{"platforms": ["iOS"], "tokens": [{"kind": "keyword", "text": "typealias"}, {"kind": "text", "text": " "}, {"text": "PrepareStyleClosure", "kind": "identifier"}, {"text": " = (", "kind": "text"}, {"preciseIdentifier": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationStyle", "kind": "typeIdentifier", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "text": "StatusBarNotificationStyle"}, {"kind": "text", "text": ") -> "}, {"text": "StatusBarNotificationStyle", "kind": "typeIdentifier", "preciseIdentifier": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationStyle", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle"}], "languages": ["swift"]}]}, {"parameters": [{"content": [{"inlineContent": [{"text": "The current default ", "type": "text"}, {"isActive": true, "type": "reference", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle"}, {"text": " instance.", "type": "text"}], "type": "paragraph"}], "name": "style"}], "kind": "parameters"}, {"kind": "content", "content": [{"anchor": "return-value", "text": "Return Value", "type": "heading", "level": 2}, {"type": "paragraph", "inlineContent": [{"text": "The modified ", "type": "text"}, {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "type": "reference", "isActive": true}, {"type": "text", "text": " instance."}]}]}], "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle", "navigatorTitle": [{"text": "StatusBarNotificationStyle", "kind": "identifier"}], "role": "symbol", "type": "topic", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "title": "StatusBarNotificationStyle", "abstract": [{"text": "A Style defines the appearance of a notification.", "type": "text"}], "fragments": [{"kind": "keyword", "text": "class"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationStyle", "kind": "identifier"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/PrepareStyleClosure": {"title": "NotificationPresenter.PrepareStyleClosure", "navigatorTitle": [{"text": "PrepareStyleClosure", "kind": "identifier"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/PrepareStyleClosure", "type": "topic", "kind": "symbol", "abstract": [{"text": "Creates a modified copy of an existing ", "type": "text"}, {"isActive": true, "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "type": "reference"}, {"type": "text", "text": " instance."}], "url": "/documentation/jdstatusbarnotification/notificationpresenter/preparestyleclosure", "fragments": [{"text": "typealias", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "PrepareStyleClosure"}], "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/addStyle(named:usingStyle:prepare:)": {"url": "/documentation/jdstatusbarnotification/notificationpresenter/addstyle(named:usingstyle:prepare:)", "abstract": [{"text": "Adds a new named style - based on an included style, if given.", "type": "text"}, {"type": "text", "text": " "}, {"type": "text", "text": "This can later be used by referencing it using the "}, {"type": "codeVoice", "code": "styleName"}, {"text": ".", "type": "text"}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/addStyle(named:usingStyle:prepare:)", "kind": "symbol", "type": "topic", "fragments": [{"kind": "keyword", "text": "func"}, {"text": " ", "kind": "text"}, {"text": "addStyle", "kind": "identifier"}, {"text": "(", "kind": "text"}, {"text": "named", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "s:SS", "text": "String"}, {"kind": "text", "text": ", "}, {"text": "usingStyle", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"kind": "typeIdentifier", "text": "IncludedStatusBarNotificationStyle", "preciseIdentifier": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationIncludedStyle"}, {"text": ", ", "kind": "text"}, {"kind": "externalParam", "text": "prepare"}, {"text": ": ", "kind": "text"}, {"text": "PrepareStyleClosure", "kind": "typeIdentifier", "preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC19PrepareStyleClosurea"}, {"kind": "text", "text": ") -> "}, {"kind": "typeIdentifier", "preciseIdentifier": "s:SS", "text": "String"}], "title": "addStyle(named:usingStyle:prepare:)"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter": {"abstract": [{"type": "text", "text": "The NotificationPresenter let’s you present notifications below the statusBar."}, {"type": "text", "text": " "}, {"type": "text", "text": "You can customize the style (colors, fonts, etc.) and animations. It supports notch"}, {"type": "text", "text": " "}, {"type": "text", "text": "and no-notch devices, landscape & portrait layouts and Drag-to-Dismiss. It can display a"}, {"text": " ", "type": "text"}, {"type": "text", "text": "title, a subtitle, an activity indicator, an animated progress bar & custom views out of the box."}], "fragments": [{"text": "class", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "NotificationPresenter"}], "navigatorTitle": [{"text": "NotificationPresenter", "kind": "identifier"}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter", "url": "/documentation/jdstatusbarnotification/notificationpresenter", "type": "topic", "kind": "symbol", "title": "NotificationPresenter"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/updateDefaultStyle(_:)": {"role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/updateDefaultStyle(_:)", "title": "updateDefaultStyle(_:)", "kind": "symbol", "url": "/documentation/jdstatusbarnotification/notificationpresenter/updatedefaultstyle(_:)", "abstract": [{"type": "text", "text": "Defines a new default style."}, {"text": " ", "type": "text"}, {"text": "The new style will be used in all future presentations that have no specific style specified.", "type": "text"}], "fragments": [{"text": "func", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "updateDefaultStyle", "kind": "identifier"}, {"text": "(", "kind": "text"}, {"preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC19PrepareStyleClosurea", "text": "PrepareStyleClosure", "kind": "typeIdentifier"}, {"text": ")", "kind": "text"}], "type": "topic"}}}