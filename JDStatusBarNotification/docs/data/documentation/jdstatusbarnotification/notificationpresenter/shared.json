{"kind": "symbol", "schemaVersion": {"major": 0, "patch": 0, "minor": 3}, "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter"]]}, "abstract": [{"text": "Provides access to the shared presenter. This is the entry point to present, style and dismiss notifications.", "type": "text"}], "variants": [{"paths": ["/documentation/jdstatusbarnotification/notificationpresenter/shared"], "traits": [{"interfaceLanguage": "swift"}]}], "metadata": {"title": "shared", "modules": [{"name": "JDStatusBarNotification"}], "role": "symbol", "externalID": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationPresenter(cpy)sharedPresenter", "fragments": [{"kind": "keyword", "text": "static"}, {"kind": "text", "text": " "}, {"text": "var", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "shared", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"kind": "typeIdentifier", "text": "NotificationPresenter", "preciseIdentifier": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationPresenter"}], "symbolKind": "property", "roleHeading": "Type Property"}, "primaryContentSections": [{"kind": "declarations", "declarations": [{"tokens": [{"text": "@objc", "kind": "attribute"}, {"text": "(sharedPresenter) ", "kind": "text"}, {"text": "static", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "keyword", "text": "var"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "shared"}, {"text": ": ", "kind": "text"}, {"text": "NotificationPresenter", "kind": "typeIdentifier", "preciseIdentifier": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationPresenter", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter"}, {"kind": "text", "text": " { get }"}], "platforms": ["iOS"], "languages": ["swift"]}]}, {"kind": "content", "content": [{"level": 2, "type": "heading", "text": "Return Value", "anchor": "return-value"}, {"type": "paragraph", "inlineContent": [{"type": "text", "text": "An initialized "}, {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter", "type": "reference", "isActive": true}, {"type": "text", "text": " instance."}]}]}], "identifier": {"url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/shared", "interfaceLanguage": "swift"}, "sections": [], "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/shared": {"title": "shared", "url": "/documentation/jdstatusbarnotification/notificationpresenter/shared", "type": "topic", "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/shared", "fragments": [{"text": "static", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "var", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "shared"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationPresenter", "text": "NotificationPresenter", "kind": "typeIdentifier"}], "kind": "symbol", "abstract": [{"text": "Provides access to the shared presenter. This is the entry point to present, style and dismiss notifications.", "type": "text"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter": {"abstract": [{"type": "text", "text": "The NotificationPresenter let’s you present notifications below the statusBar."}, {"type": "text", "text": " "}, {"type": "text", "text": "You can customize the style (colors, fonts, etc.) and animations. It supports notch"}, {"type": "text", "text": " "}, {"type": "text", "text": "and no-notch devices, landscape & portrait layouts and Drag-to-Dismiss. It can display a"}, {"text": " ", "type": "text"}, {"type": "text", "text": "title, a subtitle, an activity indicator, an animated progress bar & custom views out of the box."}], "fragments": [{"text": "class", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "NotificationPresenter"}], "navigatorTitle": [{"text": "NotificationPresenter", "kind": "identifier"}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter", "url": "/documentation/jdstatusbarnotification/notificationpresenter", "type": "topic", "kind": "symbol", "title": "NotificationPresenter"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}}}