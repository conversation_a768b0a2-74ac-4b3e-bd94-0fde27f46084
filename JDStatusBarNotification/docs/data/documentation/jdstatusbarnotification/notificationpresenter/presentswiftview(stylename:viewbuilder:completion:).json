{"metadata": {"title": "presentSwiftView(styleName:viewBuilder:completion:)", "roleHeading": "Instance Method", "modules": [{"name": "JDStatusBarNotification"}], "fragments": [{"kind": "keyword", "text": "func"}, {"kind": "text", "text": " "}, {"text": "presentSwiftView", "kind": "identifier"}, {"text": "(", "kind": "text"}, {"kind": "externalParam", "text": "styleName"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "s:SS", "kind": "typeIdentifier", "text": "String"}, {"kind": "text", "text": "?, "}, {"kind": "externalParam", "text": "viewBuilder"}, {"text": ": () -> ", "kind": "text"}, {"kind": "keyword", "text": "some"}, {"text": " ", "kind": "text"}, {"preciseIdentifier": "s:7SwiftUI4ViewP", "text": "View", "kind": "typeIdentifier"}, {"kind": "text", "text": ", "}, {"text": "completion", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona", "kind": "typeIdentifier", "text": "Completion"}, {"kind": "text", "text": "?) -> "}, {"preciseIdentifier": "c:objc(cs)UIView", "kind": "typeIdentifier", "text": "UIView"}], "externalID": "s:23JDStatusBarNotification0C9PresenterC16presentSwiftView9styleName11viewBuilder10completionSo6UIViewCSSSg_xyXEyACcSgt0F2UI0G0RzlF", "role": "symbol", "symbolKind": "method"}, "sections": [], "schemaVersion": {"patch": 0, "minor": 3, "major": 0}, "primaryContentSections": [{"declarations": [{"tokens": [{"kind": "attribute", "text": "@discardableResult"}, {"kind": "text", "text": " "}, {"kind": "keyword", "text": "func"}, {"kind": "text", "text": " "}, {"text": "presentSwiftView", "kind": "identifier"}, {"text": "(", "kind": "text"}, {"kind": "externalParam", "text": "styleName"}, {"kind": "text", "text": ": "}, {"text": "String", "preciseIdentifier": "s:SS", "kind": "typeIdentifier"}, {"text": "? = nil, ", "kind": "text"}, {"kind": "attribute", "text": "@"}, {"preciseIdentifier": "s:7SwiftUI11ViewBuilderV", "kind": "attribute", "text": "ViewBuilder"}, {"text": " ", "kind": "text"}, {"kind": "externalParam", "text": "viewBuilder"}, {"text": ": () -> ", "kind": "text"}, {"text": "some", "kind": "keyword"}, {"kind": "text", "text": " "}, {"preciseIdentifier": "s:7SwiftUI4ViewP", "kind": "typeIdentifier", "text": "View"}, {"text": ", ", "kind": "text"}, {"text": "completion", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"text": "Completion", "preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/Completion", "kind": "typeIdentifier"}, {"kind": "text", "text": "? = nil) -> "}, {"kind": "typeIdentifier", "text": "UIView", "preciseIdentifier": "c:objc(cs)UIView"}], "platforms": ["iOS"], "languages": ["swift"]}], "kind": "declarations"}, {"parameters": [{"name": "styleName", "content": [{"type": "paragraph", "inlineContent": [{"text": "The name of the style. You can use styles previously added using e.g. ", "type": "text"}, {"isActive": true, "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/addStyle(named:usingStyle:prepare:)", "type": "reference"}, {"text": ".", "type": "text"}, {"text": " ", "type": "text"}, {"type": "text", "text": "If no style can be found for the given "}, {"type": "codeVoice", "code": "styleName"}, {"type": "text", "text": " or it is "}, {"code": "nil", "type": "codeVoice"}, {"type": "text", "text": ", the default style will be used."}]}]}, {"content": [{"type": "paragraph", "inlineContent": [{"type": "text", "text": "A ViewBuilder closure to build your custom SwiftUI view."}]}], "name": "viewBuilder"}, {"name": "completion", "content": [{"inlineContent": [{"type": "text", "text": "A "}, {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/Completion", "isActive": true, "type": "reference"}, {"text": " closure, which gets called once the presentation animation finishes.", "type": "text"}], "type": "paragraph"}]}], "kind": "parameters"}], "identifier": {"interfaceLanguage": "swift", "url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/presentSwiftView(styleName:viewBuilder:completion:)"}, "kind": "symbol", "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter"]]}, "seeAlsoSections": [{"title": "Present a notification (using a custom view)", "identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/presentCustomView(_:sizingController:styleName:completion:)", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenterCustomViewSizingController"], "generated": true}], "variants": [{"traits": [{"interfaceLanguage": "swift"}], "paths": ["/documentation/jdstatusbarnotification/notificationpresenter/presentswiftview(stylename:viewbuilder:completion:)"]}], "abstract": [{"text": "Present a notification using a custom SwiftUI view.", "type": "text"}], "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/presentSwiftView(styleName:viewBuilder:completion:)": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/presentSwiftView(styleName:viewBuilder:completion:)", "type": "topic", "fragments": [{"kind": "keyword", "text": "func"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "presentSwiftView"}, {"kind": "text", "text": "("}, {"kind": "externalParam", "text": "styleName"}, {"kind": "text", "text": ": "}, {"text": "String", "preciseIdentifier": "s:SS", "kind": "typeIdentifier"}, {"text": "?, ", "kind": "text"}, {"kind": "externalParam", "text": "viewBuilder"}, {"text": ": () -> ", "kind": "text"}, {"text": "some", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"preciseIdentifier": "s:7SwiftUI4ViewP", "text": "View", "kind": "typeIdentifier"}, {"text": ", ", "kind": "text"}, {"text": "completion", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"kind": "typeIdentifier", "text": "Completion", "preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona"}, {"kind": "text", "text": "?) -> "}, {"text": "UIView", "kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIView"}], "abstract": [{"text": "Present a notification using a custom SwiftUI view.", "type": "text"}], "title": "presentSwiftView(styleName:viewBuilder:completion:)", "role": "symbol", "url": "/documentation/jdstatusbarnotification/notificationpresenter/presentswiftview(stylename:viewbuilder:completion:)", "kind": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenterCustomViewSizingController": {"abstract": [{"text": "A protocol for a custom controller, which controls the size of a presented custom view.", "type": "text"}], "role": "symbol", "type": "topic", "navigatorTitle": [{"kind": "identifier", "text": "NotificationPresenterCustomViewSizingController"}], "url": "/documentation/jdstatusbarnotification/notificationpresentercustomviewsizingcontroller", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenterCustomViewSizingController", "kind": "symbol", "fragments": [{"kind": "keyword", "text": "protocol"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "NotificationPresenterCustomViewSizingController"}], "title": "NotificationPresenterCustomViewSizingController"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/presentCustomView(_:sizingController:styleName:completion:)": {"abstract": [{"text": "Present a notification using a custom subview.", "type": "text"}], "kind": "symbol", "role": "symbol", "type": "topic", "title": "presentCustomView(_:sizingController:styleName:completion:)", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/presentCustomView(_:sizingController:styleName:completion:)", "fragments": [{"text": "func", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "presentCustom<PERSON>iew", "kind": "identifier"}, {"kind": "text", "text": "("}, {"kind": "typeIdentifier", "text": "UIView", "preciseIdentifier": "c:objc(cs)UIView"}, {"kind": "text", "text": ", "}, {"kind": "externalParam", "text": "sizingController"}, {"text": ": ", "kind": "text"}, {"preciseIdentifier": "c:@M@JDStatusBarNotification@objc(pl)JDStatusBarNotificationPresenterCustomViewSizingController", "text": "NotificationPresenterCustomViewSizingController", "kind": "typeIdentifier"}, {"kind": "text", "text": "?, "}, {"text": "styleName", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"text": "String", "kind": "typeIdentifier", "preciseIdentifier": "s:SS"}, {"text": "?, ", "kind": "text"}, {"kind": "externalParam", "text": "completion"}, {"kind": "text", "text": ": "}, {"kind": "typeIdentifier", "preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona", "text": "Completion"}, {"text": "?) -> ", "kind": "text"}, {"text": "UIView", "kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIView"}], "url": "/documentation/jdstatusbarnotification/notificationpresenter/presentcustomview(_:sizingcontroller:stylename:completion:)"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/Completion": {"kind": "symbol", "abstract": [{"type": "text", "text": "Called upon animation completion."}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/Completion", "url": "/documentation/jdstatusbarnotification/notificationpresenter/completion", "fragments": [{"text": "typealias", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "Completion"}], "navigatorTitle": [{"text": "Completion", "kind": "identifier"}], "title": "NotificationPresenter.Completion", "type": "topic"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter": {"abstract": [{"type": "text", "text": "The NotificationPresenter let’s you present notifications below the statusBar."}, {"type": "text", "text": " "}, {"type": "text", "text": "You can customize the style (colors, fonts, etc.) and animations. It supports notch"}, {"type": "text", "text": " "}, {"type": "text", "text": "and no-notch devices, landscape & portrait layouts and Drag-to-Dismiss. It can display a"}, {"text": " ", "type": "text"}, {"type": "text", "text": "title, a subtitle, an activity indicator, an animated progress bar & custom views out of the box."}], "fragments": [{"text": "class", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "NotificationPresenter"}], "navigatorTitle": [{"text": "NotificationPresenter", "kind": "identifier"}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter", "url": "/documentation/jdstatusbarnotification/notificationpresenter", "type": "topic", "kind": "symbol", "title": "NotificationPresenter"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/addStyle(named:usingStyle:prepare:)": {"url": "/documentation/jdstatusbarnotification/notificationpresenter/addstyle(named:usingstyle:prepare:)", "abstract": [{"text": "Adds a new named style - based on an included style, if given.", "type": "text"}, {"type": "text", "text": " "}, {"type": "text", "text": "This can later be used by referencing it using the "}, {"type": "codeVoice", "code": "styleName"}, {"text": ".", "type": "text"}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/addStyle(named:usingStyle:prepare:)", "kind": "symbol", "type": "topic", "fragments": [{"kind": "keyword", "text": "func"}, {"text": " ", "kind": "text"}, {"text": "addStyle", "kind": "identifier"}, {"text": "(", "kind": "text"}, {"text": "named", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "s:SS", "text": "String"}, {"kind": "text", "text": ", "}, {"text": "usingStyle", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"kind": "typeIdentifier", "text": "IncludedStatusBarNotificationStyle", "preciseIdentifier": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationIncludedStyle"}, {"text": ", ", "kind": "text"}, {"kind": "externalParam", "text": "prepare"}, {"text": ": ", "kind": "text"}, {"text": "PrepareStyleClosure", "kind": "typeIdentifier", "preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC19PrepareStyleClosurea"}, {"kind": "text", "text": ") -> "}, {"kind": "typeIdentifier", "preciseIdentifier": "s:SS", "text": "String"}], "title": "addStyle(named:usingStyle:prepare:)"}}}