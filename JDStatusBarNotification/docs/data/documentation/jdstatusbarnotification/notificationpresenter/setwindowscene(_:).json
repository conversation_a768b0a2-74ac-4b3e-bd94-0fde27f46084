{"abstract": [{"type": "text", "text": "Lets you set an explicit "}, {"code": "UIWindowScene", "type": "codeVoice"}, {"type": "text", "text": ", in which notifications should be presented in. In most cases you don’t need to set this."}], "seeAlsoSections": [{"identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/isVisible", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/updateTitle(_:)", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/updateSubtitle(_:)"], "generated": true, "title": "Additional Presenter APIs"}], "variants": [{"traits": [{"interfaceLanguage": "swift"}], "paths": ["/documentation/jdstatusbarnotification/notificationpresenter/setwindowscene(_:)"]}], "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter"]]}, "primaryContentSections": [{"kind": "declarations", "declarations": [{"tokens": [{"text": "@objc", "kind": "attribute"}, {"kind": "text", "text": " "}, {"text": "func", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "setWindowScene", "kind": "identifier"}, {"kind": "text", "text": "("}, {"text": "_", "kind": "externalParam"}, {"kind": "text", "text": " "}, {"kind": "internalParam", "text": "windowScene"}, {"text": ": ", "kind": "text"}, {"preciseIdentifier": "c:objc(cs)UIWindowScene", "text": "UIWindowScene", "kind": "typeIdentifier"}, {"text": "?)", "kind": "text"}], "languages": ["swift"], "platforms": ["iOS"]}]}, {"kind": "parameters", "parameters": [{"content": [{"type": "paragraph", "inlineContent": [{"text": "The ", "type": "text"}, {"type": "codeVoice", "code": "UIWindowScene"}, {"type": "text", "text": " in which the notifcation should be presented."}]}], "name": "windowScene"}]}, {"content": [{"text": "Discussion", "type": "heading", "level": 2, "anchor": "discussion"}, {"type": "paragraph", "inlineContent": [{"type": "text", "text": "The "}, {"code": "UIWindowScene", "type": "codeVoice"}, {"text": " is usually inferred automatically, but if that doesn’t work for your setup, you can set it explicitly.", "type": "text"}]}], "kind": "content"}], "schemaVersion": {"minor": 3, "patch": 0, "major": 0}, "kind": "symbol", "identifier": {"interfaceLanguage": "swift", "url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/setWindowScene(_:)"}, "metadata": {"role": "symbol", "fragments": [{"kind": "keyword", "text": "func"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "setWindowScene"}, {"text": "(", "kind": "text"}, {"preciseIdentifier": "c:objc(cs)UIWindowScene", "text": "UIWindowScene", "kind": "typeIdentifier"}, {"text": "?)", "kind": "text"}], "roleHeading": "Instance Method", "modules": [{"name": "JDStatusBarNotification"}], "externalID": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationPresenter(im)setWindowScene:", "title": "setWindowScene(_:)", "symbolKind": "method"}, "sections": [], "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/isVisible": {"abstract": [{"type": "text", "text": "Let’s you check if a notification is currently displayed."}], "url": "/documentation/jdstatusbarnotification/notificationpresenter/isvisible", "fragments": [{"text": "var", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "isVisible", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"text": "Bool", "preciseIdentifier": "s:Sb", "kind": "typeIdentifier"}], "kind": "symbol", "title": "isVisible", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/isVisible", "type": "topic", "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter": {"abstract": [{"type": "text", "text": "The NotificationPresenter let’s you present notifications below the statusBar."}, {"type": "text", "text": " "}, {"type": "text", "text": "You can customize the style (colors, fonts, etc.) and animations. It supports notch"}, {"type": "text", "text": " "}, {"type": "text", "text": "and no-notch devices, landscape & portrait layouts and Drag-to-Dismiss. It can display a"}, {"text": " ", "type": "text"}, {"type": "text", "text": "title, a subtitle, an activity indicator, an animated progress bar & custom views out of the box."}], "fragments": [{"text": "class", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "NotificationPresenter"}], "navigatorTitle": [{"text": "NotificationPresenter", "kind": "identifier"}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter", "url": "/documentation/jdstatusbarnotification/notificationpresenter", "type": "topic", "kind": "symbol", "title": "NotificationPresenter"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/updateSubtitle(_:)": {"url": "/documentation/jdstatusbarnotification/notificationpresenter/updatesubtitle(_:)", "abstract": [{"text": "Updates the subtitle of an existing notification without animation.", "type": "text"}], "fragments": [{"text": "func", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "updateSubtitle", "kind": "identifier"}, {"text": "(", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "s:SS", "text": "String"}, {"text": "?)", "kind": "text"}], "kind": "symbol", "title": "updateSubtitle(_:)", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/updateSubtitle(_:)", "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/updateTitle(_:)": {"abstract": [{"text": "Updates the title of an existing notification without animation.", "type": "text"}], "url": "/documentation/jdstatusbarnotification/notificationpresenter/updatetitle(_:)", "fragments": [{"kind": "keyword", "text": "func"}, {"text": " ", "kind": "text"}, {"text": "updateTitle", "kind": "identifier"}, {"text": "(", "kind": "text"}, {"preciseIdentifier": "s:SS", "text": "String", "kind": "typeIdentifier"}, {"text": ")", "kind": "text"}], "kind": "symbol", "title": "updateTitle(_:)", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/updateTitle(_:)", "type": "topic", "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/setWindowScene(_:)": {"url": "/documentation/jdstatusbarnotification/notificationpresenter/setwindowscene(_:)", "abstract": [{"text": "Lets you set an explicit ", "type": "text"}, {"type": "codeVoice", "code": "UIWindowScene"}, {"text": ", in which notifications should be presented in. In most cases you don’t need to set this.", "type": "text"}], "fragments": [{"text": "func", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "setWindowScene", "kind": "identifier"}, {"text": "(", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIWindowScene", "text": "UIWindowScene"}, {"text": "?)", "kind": "text"}], "kind": "symbol", "title": "setWindowScene(_:)", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/setWindowScene(_:)", "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}}}