{"seeAlsoSections": [{"identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/PrepareStyleClosure", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/updateDefaultStyle(_:)"], "title": "Customize the style (Appearance & Behavior)", "generated": true}], "metadata": {"role": "symbol", "fragments": [{"kind": "keyword", "text": "func"}, {"text": " ", "kind": "text"}, {"text": "addStyle", "kind": "identifier"}, {"kind": "text", "text": "("}, {"kind": "externalParam", "text": "named"}, {"text": ": ", "kind": "text"}, {"text": "String", "kind": "typeIdentifier", "preciseIdentifier": "s:SS"}, {"kind": "text", "text": ", "}, {"text": "usingStyle", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"preciseIdentifier": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationIncludedStyle", "text": "IncludedStatusBarNotificationStyle", "kind": "typeIdentifier"}, {"kind": "text", "text": ", "}, {"kind": "externalParam", "text": "prepare"}, {"kind": "text", "text": ": "}, {"kind": "typeIdentifier", "text": "PrepareStyleClosure", "preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC19PrepareStyleClosurea"}, {"text": ") -> ", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "s:SS", "text": "String"}], "externalID": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationPresenter(im)addStyleNamed:basedOnStyle:prepare:", "symbolKind": "method", "roleHeading": "Instance Method", "modules": [{"name": "JDStatusBarNotification"}], "title": "addStyle(named:usingStyle:prepare:)"}, "kind": "symbol", "schemaVersion": {"major": 0, "minor": 3, "patch": 0}, "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter"]]}, "primaryContentSections": [{"kind": "declarations", "declarations": [{"languages": ["swift"], "platforms": ["iOS"], "tokens": [{"kind": "attribute", "text": "@discardableResult"}, {"kind": "text", "text": " "}, {"text": "@objc", "kind": "attribute"}, {"kind": "text", "text": "(addStyleNamed:basedOnStyle:prepare:) "}, {"kind": "keyword", "text": "func"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "addStyle"}, {"text": "(", "kind": "text"}, {"text": "named", "kind": "externalParam"}, {"text": " ", "kind": "text"}, {"text": "name", "kind": "internalParam"}, {"text": ": ", "kind": "text"}, {"preciseIdentifier": "s:SS", "kind": "typeIdentifier", "text": "String"}, {"text": ", ", "kind": "text"}, {"kind": "externalParam", "text": "usingStyle"}, {"kind": "text", "text": " "}, {"text": "includedStyle", "kind": "internalParam"}, {"text": ": ", "kind": "text"}, {"text": "IncludedStatusBarNotificationStyle", "kind": "typeIdentifier", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle", "preciseIdentifier": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationIncludedStyle"}, {"text": " = .defaultStyle, ", "kind": "text"}, {"kind": "externalParam", "text": "prepare"}, {"text": ": ", "kind": "text"}, {"text": "PrepareStyleClosure", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/PrepareStyleClosure", "kind": "typeIdentifier", "preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC19PrepareStyleClosurea"}, {"kind": "text", "text": ") -> "}, {"kind": "typeIdentifier", "preciseIdentifier": "s:SS", "text": "String"}]}]}, {"kind": "parameters", "parameters": [{"name": "name", "content": [{"type": "paragraph", "inlineContent": [{"text": "The styleName which will later be used to reference the added style.", "type": "text"}]}]}, {"content": [{"type": "paragraph", "inlineContent": [{"type": "text", "text": "The "}, {"type": "reference", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle", "isActive": true}, {"type": "text", "text": ", which you want to base your style on."}]}], "name": "includedStyle"}, {"name": "prepare", "content": [{"inlineContent": [{"text": "Provides an ", "type": "text"}, {"isActive": true, "type": "reference", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle"}, {"type": "text", "text": " instance based on the provided "}, {"code": "includedStyle", "type": "codeVoice"}, {"type": "text", "text": " for further customization."}], "type": "paragraph"}]}]}, {"content": [{"anchor": "return-value", "type": "heading", "text": "Return Value", "level": 2}, {"type": "paragraph", "inlineContent": [{"type": "text", "text": "Returns the "}, {"code": "styleName", "type": "codeVoice"}, {"type": "text", "text": ", so that this call can be used directly within a presentation call."}]}], "kind": "content"}, {"kind": "content", "content": [{"text": "Discussion", "anchor": "discussion", "level": 2, "type": "heading"}, {"type": "paragraph", "inlineContent": [{"text": "The added style can be used in future presentations by utilizing the same ", "type": "text"}, {"type": "codeVoice", "code": "styleName"}, {"type": "text", "text": " in e.g. "}, {"type": "reference", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/present(_:subtitle:styleName:duration:completion:)", "isActive": true}, {"type": "text", "text": "."}, {"type": "text", "text": " "}, {"type": "text", "text": "If a style with the same name already exists, it will be replaced."}]}]}], "sections": [], "identifier": {"interfaceLanguage": "swift", "url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/addStyle(named:usingStyle:prepare:)"}, "abstract": [{"text": "Adds a new named style - based on an included style, if given.", "type": "text"}, {"type": "text", "text": " "}, {"type": "text", "text": "This can later be used by referencing it using the "}, {"code": "styleName", "type": "codeVoice"}, {"type": "text", "text": "."}], "variants": [{"paths": ["/documentation/jdstatusbarnotification/notificationpresenter/addstyle(named:usingstyle:prepare:)"], "traits": [{"interfaceLanguage": "swift"}]}], "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter": {"abstract": [{"type": "text", "text": "The NotificationPresenter let’s you present notifications below the statusBar."}, {"type": "text", "text": " "}, {"type": "text", "text": "You can customize the style (colors, fonts, etc.) and animations. It supports notch"}, {"type": "text", "text": " "}, {"type": "text", "text": "and no-notch devices, landscape & portrait layouts and Drag-to-Dismiss. It can display a"}, {"text": " ", "type": "text"}, {"type": "text", "text": "title, a subtitle, an activity indicator, an animated progress bar & custom views out of the box."}], "fragments": [{"text": "class", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "NotificationPresenter"}], "navigatorTitle": [{"text": "NotificationPresenter", "kind": "identifier"}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter", "url": "/documentation/jdstatusbarnotification/notificationpresenter", "type": "topic", "kind": "symbol", "title": "NotificationPresenter"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/updateDefaultStyle(_:)": {"role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/updateDefaultStyle(_:)", "title": "updateDefaultStyle(_:)", "kind": "symbol", "url": "/documentation/jdstatusbarnotification/notificationpresenter/updatedefaultstyle(_:)", "abstract": [{"type": "text", "text": "Defines a new default style."}, {"text": " ", "type": "text"}, {"text": "The new style will be used in all future presentations that have no specific style specified.", "type": "text"}], "fragments": [{"text": "func", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "updateDefaultStyle", "kind": "identifier"}, {"text": "(", "kind": "text"}, {"preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC19PrepareStyleClosurea", "text": "PrepareStyleClosure", "kind": "typeIdentifier"}, {"text": ")", "kind": "text"}], "type": "topic"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/addStyle(named:usingStyle:prepare:)": {"url": "/documentation/jdstatusbarnotification/notificationpresenter/addstyle(named:usingstyle:prepare:)", "abstract": [{"text": "Adds a new named style - based on an included style, if given.", "type": "text"}, {"type": "text", "text": " "}, {"type": "text", "text": "This can later be used by referencing it using the "}, {"type": "codeVoice", "code": "styleName"}, {"text": ".", "type": "text"}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/addStyle(named:usingStyle:prepare:)", "kind": "symbol", "type": "topic", "fragments": [{"kind": "keyword", "text": "func"}, {"text": " ", "kind": "text"}, {"text": "addStyle", "kind": "identifier"}, {"text": "(", "kind": "text"}, {"text": "named", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "s:SS", "text": "String"}, {"kind": "text", "text": ", "}, {"text": "usingStyle", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"kind": "typeIdentifier", "text": "IncludedStatusBarNotificationStyle", "preciseIdentifier": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationIncludedStyle"}, {"text": ", ", "kind": "text"}, {"kind": "externalParam", "text": "prepare"}, {"text": ": ", "kind": "text"}, {"text": "PrepareStyleClosure", "kind": "typeIdentifier", "preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC19PrepareStyleClosurea"}, {"kind": "text", "text": ") -> "}, {"kind": "typeIdentifier", "preciseIdentifier": "s:SS", "text": "String"}], "title": "addStyle(named:usingStyle:prepare:)"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle", "navigatorTitle": [{"text": "StatusBarNotificationStyle", "kind": "identifier"}], "role": "symbol", "type": "topic", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "title": "StatusBarNotificationStyle", "abstract": [{"text": "A Style defines the appearance of a notification.", "type": "text"}], "fragments": [{"kind": "keyword", "text": "class"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationStyle", "kind": "identifier"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/PrepareStyleClosure": {"title": "NotificationPresenter.PrepareStyleClosure", "navigatorTitle": [{"text": "PrepareStyleClosure", "kind": "identifier"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/PrepareStyleClosure", "type": "topic", "kind": "symbol", "abstract": [{"text": "Creates a modified copy of an existing ", "type": "text"}, {"isActive": true, "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "type": "reference"}, {"type": "text", "text": " instance."}], "url": "/documentation/jdstatusbarnotification/notificationpresenter/preparestyleclosure", "fragments": [{"text": "typealias", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "PrepareStyleClosure"}], "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle": {"abstract": [{"text": "These included styles let you easily change the visual appearance of a", "type": "text"}, {"type": "text", "text": " "}, {"type": "text", "text": "notification without creating your own custom style."}], "fragments": [{"text": "enum", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "IncludedStatusBarNotificationStyle", "kind": "identifier"}], "navigatorTitle": [{"kind": "identifier", "text": "IncludedStatusBarNotificationStyle"}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle", "url": "/documentation/jdstatusbarnotification/includedstatusbarnotificationstyle", "type": "topic", "kind": "symbol", "title": "IncludedStatusBarNotificationStyle"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/present(_:subtitle:styleName:duration:completion:)": {"kind": "symbol", "type": "topic", "url": "/documentation/jdstatusbarnotification/notificationpresenter/present(_:subtitle:stylename:duration:completion:)", "title": "present(_:subtitle:styleName:duration:completion:)", "abstract": [{"type": "text", "text": "Present a notification using the default style or a named style."}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/present(_:subtitle:styleName:duration:completion:)", "fragments": [{"text": "func", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "present", "kind": "identifier"}, {"kind": "text", "text": "("}, {"text": "String", "preciseIdentifier": "s:SS", "kind": "typeIdentifier"}, {"kind": "text", "text": ", "}, {"text": "subtitle", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"text": "String", "kind": "typeIdentifier", "preciseIdentifier": "s:SS"}, {"text": "?, ", "kind": "text"}, {"text": "styleName", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "s:SS", "text": "String", "kind": "typeIdentifier"}, {"kind": "text", "text": "?, "}, {"kind": "externalParam", "text": "duration"}, {"text": ": ", "kind": "text"}, {"text": "Double", "kind": "typeIdentifier", "preciseIdentifier": "s:Sd"}, {"kind": "text", "text": "?, "}, {"text": "completion", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona", "text": "Completion"}, {"kind": "text", "text": "?) -> "}, {"kind": "typeIdentifier", "text": "UIView", "preciseIdentifier": "c:objc(cs)UIView"}]}}}