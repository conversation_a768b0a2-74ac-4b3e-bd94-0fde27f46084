{"hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter"]]}, "variants": [{"traits": [{"interfaceLanguage": "swift"}], "paths": ["/documentation/jdstatusbarnotification/notificationpresenter/zlp(cv:s:c:)"]}], "sections": [], "identifier": {"interfaceLanguage": "swift", "url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(cv:s:c:)"}, "kind": "symbol", "primaryContentSections": [{"declarations": [{"platforms": ["iOS"], "languages": ["swift"], "tokens": [{"text": "@discardableResult", "kind": "attribute"}, {"kind": "text", "text": " "}, {"text": "@objc", "kind": "attribute"}, {"kind": "text", "text": "(presentWithCustomView:styleName:completion:) "}, {"text": "func", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "zlp", "kind": "identifier"}, {"kind": "text", "text": "("}, {"text": "cv", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"preciseIdentifier": "c:objc(cs)UIView", "text": "UIView", "kind": "typeIdentifier"}, {"kind": "text", "text": ", "}, {"kind": "externalParam", "text": "s"}, {"kind": "text", "text": ": "}, {"text": "String", "preciseIdentifier": "s:SS", "kind": "typeIdentifier"}, {"text": "? = nil, ", "kind": "text"}, {"text": "c", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona", "text": "Completion", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/Completion", "kind": "typeIdentifier"}, {"kind": "text", "text": "? = nil) -> "}, {"text": "UIView", "preciseIdentifier": "c:objc(cs)UIView", "kind": "typeIdentifier"}]}], "kind": "declarations"}, {"parameters": [{"content": [{"inlineContent": [{"type": "text", "text": "A custom UIView to display as notification content."}], "type": "paragraph"}], "name": "cv"}, {"content": [{"type": "paragraph", "inlineContent": [{"text": "The name of the style. You can use styles previously added using e.g. ", "type": "text"}, {"isActive": true, "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/addStyle(named:usingStyle:prepare:)", "type": "reference"}, {"text": ".", "type": "text"}, {"type": "text", "text": " "}, {"type": "text", "text": "If no style can be found for the given "}, {"type": "codeVoice", "code": "styleName"}, {"text": " or it is ", "type": "text"}, {"type": "codeVoice", "code": "nil"}, {"text": ", the default style will be used.", "type": "text"}]}], "name": "s"}, {"content": [{"inlineContent": [{"text": "A ", "type": "text"}, {"isActive": true, "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/Completion", "type": "reference"}, {"type": "text", "text": " closure, which gets called once the presentation animation finishes."}], "type": "paragraph"}], "name": "c"}], "kind": "parameters"}, {"kind": "content", "content": [{"text": "Return Value", "type": "heading", "level": 2, "anchor": "return-value"}, {"type": "paragraph", "inlineContent": [{"text": "The presented UIView for further customization", "type": "text"}]}]}, {"content": [{"level": 2, "text": "Discussion", "type": "heading", "anchor": "discussion"}, {"type": "paragraph", "inlineContent": [{"type": "text", "text": "The "}, {"type": "codeVoice", "code": "customView"}, {"type": "text", "text": " will be layouted correctly according to the selected style & the current device"}, {"text": " ", "type": "text"}, {"type": "text", "text": "state (rotation, status bar visibility, etc.). The background will still be styled & layouted"}, {"text": " ", "type": "text"}, {"type": "text", "text": "according to the provided style. If your custom view requires custom touch handling,"}, {"text": " ", "type": "text"}, {"type": "text", "text": "make sure to set "}, {"code": "style.canTapToHold", "type": "codeVoice"}, {"type": "text", "text": " to "}, {"code": "false", "type": "codeVoice"}, {"type": "text", "text": ". Otherwise the "}, {"type": "codeVoice", "code": "customView"}, {"type": "text", "text": " won’t"}, {"text": " ", "type": "text"}, {"text": "receive any touches, as the internal ", "type": "text"}, {"type": "codeVoice", "code": "gestureRecognizer"}, {"text": " would receive them.", "type": "text"}]}], "kind": "content"}], "seeAlsoSections": [{"title": "Legacy API support (for objc only)", "identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:)", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:c:)", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:d:)", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:st:c:)", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:s:)", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:s:c:)", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:d:s:)", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:st:s:c:)", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:cu:)", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:cu:c:)", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:d:cu:)", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:st:cu:c:)", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zld()", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zld(a:)", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zld(c:)", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zld(d:)", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zld(d:c:)", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zld(a:d:c:)", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlas(n:p:)"], "generated": true}], "metadata": {"role": "symbol", "extendedModule": "JDStatusBarNotification", "title": "zlp(cv:s:c:)", "externalID": "c:@CM@JDStatusBarNotification@objc(cs)JDStatusBarNotificationPresenter(im)presentWithCustomView:styleName:completion:", "roleHeading": "Instance Method", "modules": [{"name": "JDStatusBarNotification"}], "symbolKind": "method", "fragments": [{"text": "func", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "zlp", "kind": "identifier"}, {"text": "(", "kind": "text"}, {"kind": "externalParam", "text": "cv"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "c:objc(cs)UIView", "text": "UIView", "kind": "typeIdentifier"}, {"text": ", ", "kind": "text"}, {"text": "s", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"text": "String", "preciseIdentifier": "s:SS", "kind": "typeIdentifier"}, {"text": "?, ", "kind": "text"}, {"kind": "externalParam", "text": "c"}, {"kind": "text", "text": ": "}, {"text": "Completion", "preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona", "kind": "typeIdentifier"}, {"text": "?) -> ", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIView", "text": "UIView"}]}, "abstract": [{"type": "text", "text": "Present a notification using a custom subview."}], "schemaVersion": {"minor": 3, "patch": 0, "major": 0}, "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter": {"abstract": [{"type": "text", "text": "The NotificationPresenter let’s you present notifications below the statusBar."}, {"type": "text", "text": " "}, {"type": "text", "text": "You can customize the style (colors, fonts, etc.) and animations. It supports notch"}, {"type": "text", "text": " "}, {"type": "text", "text": "and no-notch devices, landscape & portrait layouts and Drag-to-Dismiss. It can display a"}, {"text": " ", "type": "text"}, {"type": "text", "text": "title, a subtitle, an activity indicator, an animated progress bar & custom views out of the box."}], "fragments": [{"text": "class", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "NotificationPresenter"}], "navigatorTitle": [{"text": "NotificationPresenter", "kind": "identifier"}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter", "url": "/documentation/jdstatusbarnotification/notificationpresenter", "type": "topic", "kind": "symbol", "title": "NotificationPresenter"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:d:s:)": {"role": "symbol", "type": "topic", "fragments": [{"text": "func", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "zlp"}, {"text": "(", "kind": "text"}, {"text": "t", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"text": "String", "preciseIdentifier": "s:SS", "kind": "typeIdentifier"}, {"kind": "text", "text": ", "}, {"text": "d", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "s:Sd", "kind": "typeIdentifier", "text": "Double"}, {"kind": "text", "text": ", "}, {"text": "s", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"text": "IncludedStatusBarNotificationStyle", "kind": "typeIdentifier", "preciseIdentifier": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationIncludedStyle"}, {"kind": "text", "text": ") -> "}, {"preciseIdentifier": "c:objc(cs)UIView", "kind": "typeIdentifier", "text": "UIView"}], "title": "zlp(t:d:s:)", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:d:s:)", "abstract": [{"type": "text", "text": "Present a notification using an included style."}], "kind": "symbol", "url": "/documentation/jdstatusbarnotification/notificationpresenter/zlp(t:d:s:)"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zld(a:d:c:)": {"abstract": [{"text": "Dismisses the displayed notification after the provided delay.", "type": "text"}], "role": "symbol", "kind": "symbol", "type": "topic", "fragments": [{"kind": "keyword", "text": "func"}, {"text": " ", "kind": "text"}, {"text": "zld", "kind": "identifier"}, {"text": "(", "kind": "text"}, {"kind": "externalParam", "text": "a"}, {"text": ": ", "kind": "text"}, {"preciseIdentifier": "s:Sb", "kind": "typeIdentifier", "text": "Bool"}, {"kind": "text", "text": ", "}, {"kind": "externalParam", "text": "d"}, {"text": ": ", "kind": "text"}, {"text": "Double", "kind": "typeIdentifier", "preciseIdentifier": "s:Sd"}, {"kind": "text", "text": ", "}, {"kind": "externalParam", "text": "c"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona", "kind": "typeIdentifier", "text": "Completion"}, {"text": "?)", "kind": "text"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zld(a:d:c:)", "title": "zld(a:d:c:)", "url": "/documentation/jdstatusbarnotification/notificationpresenter/zld(a:d:c:)"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlas(n:p:)": {"type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlas(n:p:)", "fragments": [{"kind": "keyword", "text": "func"}, {"text": " ", "kind": "text"}, {"text": "zlas", "kind": "identifier"}, {"text": "(", "kind": "text"}, {"kind": "externalParam", "text": "n"}, {"kind": "text", "text": ": "}, {"text": "String", "preciseIdentifier": "s:SS", "kind": "typeIdentifier"}, {"kind": "text", "text": ", "}, {"text": "p", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"text": "PrepareStyleClosure", "preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC19PrepareStyleClosurea", "kind": "typeIdentifier"}, {"kind": "text", "text": ") -> "}, {"preciseIdentifier": "s:SS", "kind": "typeIdentifier", "text": "String"}], "kind": "symbol", "role": "symbol", "title": "zlas(n:p:)", "abstract": [{"text": "Adds a new named style, which can later be used by referencing it using the ", "type": "text"}, {"type": "codeVoice", "code": "styleName"}, {"type": "text", "text": "."}], "url": "/documentation/jdstatusbarnotification/notificationpresenter/zlas(n:p:)"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:st:cu:c:)": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:st:cu:c:)", "abstract": [{"text": "Present a notification using a custom style.", "type": "text"}], "title": "zlp(t:st:cu:c:)", "type": "topic", "kind": "symbol", "fragments": [{"kind": "keyword", "text": "func"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "zlp"}, {"text": "(", "kind": "text"}, {"kind": "externalParam", "text": "t"}, {"text": ": ", "kind": "text"}, {"preciseIdentifier": "s:SS", "text": "String", "kind": "typeIdentifier"}, {"text": ", ", "kind": "text"}, {"text": "st", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "text": "String", "preciseIdentifier": "s:SS"}, {"text": "?, ", "kind": "text"}, {"kind": "externalParam", "text": "cu"}, {"kind": "text", "text": ": "}, {"text": "String", "kind": "typeIdentifier", "preciseIdentifier": "s:SS"}, {"text": "?, ", "kind": "text"}, {"text": "c", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "text": "Completion", "preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona"}, {"text": "?) -> ", "kind": "text"}, {"preciseIdentifier": "c:objc(cs)UIView", "text": "UIView", "kind": "typeIdentifier"}], "url": "/documentation/jdstatusbarnotification/notificationpresenter/zlp(t:st:cu:c:)", "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:)": {"type": "topic", "kind": "symbol", "title": "zlp(t:)", "abstract": [{"text": "Present a notification", "type": "text"}], "url": "/documentation/jdstatusbarnotification/notificationpresenter/zlp(t:)", "role": "symbol", "fragments": [{"text": "func", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "zlp"}, {"kind": "text", "text": "("}, {"kind": "externalParam", "text": "t"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "text": "String", "preciseIdentifier": "s:SS"}, {"text": ") -> ", "kind": "text"}, {"preciseIdentifier": "c:objc(cs)UIView", "text": "UIView", "kind": "typeIdentifier"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:)"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:s:c:)": {"fragments": [{"kind": "keyword", "text": "func"}, {"kind": "text", "text": " "}, {"text": "zlp", "kind": "identifier"}, {"text": "(", "kind": "text"}, {"kind": "externalParam", "text": "t"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "s:SS", "text": "String"}, {"text": ", ", "kind": "text"}, {"text": "s", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "text": "IncludedStatusBarNotificationStyle", "preciseIdentifier": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationIncludedStyle"}, {"kind": "text", "text": ", "}, {"text": "c", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"text": "Completion", "preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona", "kind": "typeIdentifier"}, {"text": "?) -> ", "kind": "text"}, {"text": "UIView", "kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIView"}], "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:s:c:)", "url": "/documentation/jdstatusbarnotification/notificationpresenter/zlp(t:s:c:)", "abstract": [{"text": "Present a notification using an included style.", "type": "text"}], "type": "topic", "title": "zlp(t:s:c:)", "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zld()": {"abstract": [{"text": "Dismisses the displayed notification.", "type": "text"}], "url": "/documentation/jdstatusbarnotification/notificationpresenter/zld()", "title": "zld()", "kind": "symbol", "fragments": [{"kind": "keyword", "text": "func"}, {"kind": "text", "text": " "}, {"text": "zld", "kind": "identifier"}, {"text": "()", "kind": "text"}], "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zld()", "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zld(c:)": {"type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zld(c:)", "kind": "symbol", "title": "zld(c:)", "abstract": [{"text": "Dismisses the displayed notification.", "type": "text"}], "role": "symbol", "url": "/documentation/jdstatusbarnotification/notificationpresenter/zld(c:)", "fragments": [{"text": "func", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "zld", "kind": "identifier"}, {"kind": "text", "text": "("}, {"kind": "externalParam", "text": "c"}, {"kind": "text", "text": ": "}, {"text": "Completion", "preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona", "kind": "typeIdentifier"}, {"text": "?)", "kind": "text"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:d:cu:)": {"fragments": [{"text": "func", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "zlp"}, {"kind": "text", "text": "("}, {"text": "t", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "s:SS", "text": "String"}, {"text": ", ", "kind": "text"}, {"kind": "externalParam", "text": "d"}, {"kind": "text", "text": ": "}, {"text": "Double", "kind": "typeIdentifier", "preciseIdentifier": "s:Sd"}, {"kind": "text", "text": ", "}, {"kind": "externalParam", "text": "cu"}, {"text": ": ", "kind": "text"}, {"text": "String", "preciseIdentifier": "s:SS", "kind": "typeIdentifier"}, {"text": ") -> ", "kind": "text"}, {"preciseIdentifier": "c:objc(cs)UIView", "text": "UIView", "kind": "typeIdentifier"}], "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:d:cu:)", "url": "/documentation/jdstatusbarnotification/notificationpresenter/zlp(t:d:cu:)", "abstract": [{"text": "Present a notification using a custom style.", "type": "text"}], "type": "topic", "title": "zlp(t:d:cu:)", "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zld(d:)": {"type": "topic", "kind": "symbol", "fragments": [{"text": "func", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "zld"}, {"text": "(", "kind": "text"}, {"text": "d", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"preciseIdentifier": "s:Sd", "kind": "typeIdentifier", "text": "Double"}, {"kind": "text", "text": ")"}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zld(d:)", "url": "/documentation/jdstatusbarnotification/notificationpresenter/zld(d:)", "title": "zld(d:)", "abstract": [{"text": "Dismisses the displayed notification after the provided delay.", "type": "text"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/addStyle(named:usingStyle:prepare:)": {"url": "/documentation/jdstatusbarnotification/notificationpresenter/addstyle(named:usingstyle:prepare:)", "abstract": [{"text": "Adds a new named style - based on an included style, if given.", "type": "text"}, {"type": "text", "text": " "}, {"type": "text", "text": "This can later be used by referencing it using the "}, {"type": "codeVoice", "code": "styleName"}, {"text": ".", "type": "text"}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/addStyle(named:usingStyle:prepare:)", "kind": "symbol", "type": "topic", "fragments": [{"kind": "keyword", "text": "func"}, {"text": " ", "kind": "text"}, {"text": "addStyle", "kind": "identifier"}, {"text": "(", "kind": "text"}, {"text": "named", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "s:SS", "text": "String"}, {"kind": "text", "text": ", "}, {"text": "usingStyle", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"kind": "typeIdentifier", "text": "IncludedStatusBarNotificationStyle", "preciseIdentifier": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationIncludedStyle"}, {"text": ", ", "kind": "text"}, {"kind": "externalParam", "text": "prepare"}, {"text": ": ", "kind": "text"}, {"text": "PrepareStyleClosure", "kind": "typeIdentifier", "preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC19PrepareStyleClosurea"}, {"kind": "text", "text": ") -> "}, {"kind": "typeIdentifier", "preciseIdentifier": "s:SS", "text": "String"}], "title": "addStyle(named:usingStyle:prepare:)"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:s:)": {"kind": "symbol", "type": "topic", "title": "zlp(t:s:)", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:s:)", "fragments": [{"text": "func", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "zlp"}, {"kind": "text", "text": "("}, {"text": "t", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"text": "String", "preciseIdentifier": "s:SS", "kind": "typeIdentifier"}, {"text": ", ", "kind": "text"}, {"text": "s", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationIncludedStyle", "text": "IncludedStatusBarNotificationStyle"}, {"kind": "text", "text": ") -> "}, {"text": "UIView", "kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIView"}], "role": "symbol", "abstract": [{"type": "text", "text": "Present a notification using an included style."}], "url": "/documentation/jdstatusbarnotification/notificationpresenter/zlp(t:s:)"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zld(d:c:)": {"type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zld(d:c:)", "kind": "symbol", "title": "zld(d:c:)", "abstract": [{"type": "text", "text": "Dismisses the displayed notification after the provided delay."}], "role": "symbol", "url": "/documentation/jdstatusbarnotification/notificationpresenter/zld(d:c:)", "fragments": [{"kind": "keyword", "text": "func"}, {"kind": "text", "text": " "}, {"text": "zld", "kind": "identifier"}, {"kind": "text", "text": "("}, {"kind": "externalParam", "text": "d"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "text": "Double", "preciseIdentifier": "s:Sd"}, {"text": ", ", "kind": "text"}, {"kind": "externalParam", "text": "c"}, {"text": ": ", "kind": "text"}, {"preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona", "text": "Completion", "kind": "typeIdentifier"}, {"text": "?)", "kind": "text"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:cu:c:)": {"url": "/documentation/jdstatusbarnotification/notificationpresenter/zlp(t:cu:c:)", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:cu:c:)", "title": "zlp(t:cu:c:)", "fragments": [{"text": "func", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "zlp", "kind": "identifier"}, {"kind": "text", "text": "("}, {"text": "t", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"text": "String", "preciseIdentifier": "s:SS", "kind": "typeIdentifier"}, {"kind": "text", "text": ", "}, {"text": "cu", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "s:SS", "text": "String", "kind": "typeIdentifier"}, {"text": "?, ", "kind": "text"}, {"text": "c", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"kind": "typeIdentifier", "text": "Completion", "preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona"}, {"text": "?) -> ", "kind": "text"}, {"text": "UIView", "preciseIdentifier": "c:objc(cs)UIView", "kind": "typeIdentifier"}], "role": "symbol", "abstract": [{"type": "text", "text": "Present a notification using a custom style."}], "type": "topic", "kind": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:c:)": {"fragments": [{"text": "func", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "zlp"}, {"text": "(", "kind": "text"}, {"text": "t", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "s:SS", "text": "String"}, {"text": ", ", "kind": "text"}, {"kind": "externalParam", "text": "c"}, {"text": ": ", "kind": "text"}, {"text": "Completion", "preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona", "kind": "typeIdentifier"}, {"kind": "text", "text": "?) -> "}, {"kind": "typeIdentifier", "text": "UIView", "preciseIdentifier": "c:objc(cs)UIView"}], "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:c:)", "url": "/documentation/jdstatusbarnotification/notificationpresenter/zlp(t:c:)", "abstract": [{"text": "Present a notification", "type": "text"}], "type": "topic", "title": "zlp(t:c:)", "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:st:s:c:)": {"url": "/documentation/jdstatusbarnotification/notificationpresenter/zlp(t:st:s:c:)", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:st:s:c:)", "abstract": [{"type": "text", "text": "Present a notification using an included style."}], "fragments": [{"kind": "keyword", "text": "func"}, {"kind": "text", "text": " "}, {"text": "zlp", "kind": "identifier"}, {"kind": "text", "text": "("}, {"kind": "externalParam", "text": "t"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "s:SS", "text": "String", "kind": "typeIdentifier"}, {"kind": "text", "text": ", "}, {"text": "st", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"text": "String", "kind": "typeIdentifier", "preciseIdentifier": "s:SS"}, {"kind": "text", "text": "?, "}, {"text": "s", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"text": "IncludedStatusBarNotificationStyle", "preciseIdentifier": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationIncludedStyle", "kind": "typeIdentifier"}, {"kind": "text", "text": ", "}, {"text": "c", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"text": "Completion", "kind": "typeIdentifier", "preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona"}, {"kind": "text", "text": "?) -> "}, {"kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIView", "text": "UIView"}], "title": "zlp(t:st:s:c:)", "kind": "symbol", "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:d:)": {"type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:d:)", "kind": "symbol", "abstract": [{"text": "Present a notification", "type": "text"}], "title": "zlp(t:d:)", "role": "symbol", "url": "/documentation/jdstatusbarnotification/notificationpresenter/zlp(t:d:)", "fragments": [{"text": "func", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "zlp"}, {"kind": "text", "text": "("}, {"text": "t", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"text": "String", "kind": "typeIdentifier", "preciseIdentifier": "s:SS"}, {"text": ", ", "kind": "text"}, {"text": "d", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "s:Sd", "text": "Double", "kind": "typeIdentifier"}, {"kind": "text", "text": ") -> "}, {"text": "UIView", "kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIView"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/Completion": {"kind": "symbol", "abstract": [{"type": "text", "text": "Called upon animation completion."}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/Completion", "url": "/documentation/jdstatusbarnotification/notificationpresenter/completion", "fragments": [{"text": "typealias", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "Completion"}], "navigatorTitle": [{"text": "Completion", "kind": "identifier"}], "title": "NotificationPresenter.Completion", "type": "topic"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zld(a:)": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zld(a:)", "abstract": [{"text": "Dismisses the displayed notification.", "type": "text"}], "title": "zld(a:)", "type": "topic", "kind": "symbol", "fragments": [{"text": "func", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "zld"}, {"text": "(", "kind": "text"}, {"kind": "externalParam", "text": "a"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "text": "Bool", "preciseIdentifier": "s:Sb"}, {"kind": "text", "text": ")"}], "url": "/documentation/jdstatusbarnotification/notificationpresenter/zld(a:)", "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(cv:s:c:)": {"abstract": [{"type": "text", "text": "Present a notification using a custom subview."}], "type": "topic", "role": "symbol", "kind": "symbol", "fragments": [{"kind": "keyword", "text": "func"}, {"text": " ", "kind": "text"}, {"text": "zlp", "kind": "identifier"}, {"text": "(", "kind": "text"}, {"kind": "externalParam", "text": "cv"}, {"text": ": ", "kind": "text"}, {"text": "UIView", "kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIView"}, {"text": ", ", "kind": "text"}, {"kind": "externalParam", "text": "s"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "text": "String", "preciseIdentifier": "s:SS"}, {"kind": "text", "text": "?, "}, {"kind": "externalParam", "text": "c"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona", "text": "Completion"}, {"kind": "text", "text": "?) -> "}, {"kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIView", "text": "UIView"}], "url": "/documentation/jdstatusbarnotification/notificationpresenter/zlp(cv:s:c:)", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(cv:s:c:)", "title": "zlp(cv:s:c:)"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:cu:)": {"role": "symbol", "kind": "symbol", "type": "topic", "fragments": [{"kind": "keyword", "text": "func"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "zlp"}, {"text": "(", "kind": "text"}, {"kind": "externalParam", "text": "t"}, {"text": ": ", "kind": "text"}, {"text": "String", "preciseIdentifier": "s:SS", "kind": "typeIdentifier"}, {"kind": "text", "text": ", "}, {"text": "cu", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"preciseIdentifier": "s:SS", "text": "String", "kind": "typeIdentifier"}, {"text": ") -> ", "kind": "text"}, {"preciseIdentifier": "c:objc(cs)UIView", "text": "UIView", "kind": "typeIdentifier"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:cu:)", "title": "zlp(t:cu:)", "abstract": [{"type": "text", "text": "Present a notification using a custom style."}], "url": "/documentation/jdstatusbarnotification/notificationpresenter/zlp(t:cu:)"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:st:c:)": {"type": "topic", "title": "zlp(t:st:c:)", "url": "/documentation/jdstatusbarnotification/notificationpresenter/zlp(t:st:c:)", "kind": "symbol", "role": "symbol", "abstract": [{"type": "text", "text": "Present a notification"}], "fragments": [{"kind": "keyword", "text": "func"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "zlp"}, {"kind": "text", "text": "("}, {"text": "t", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "text": "String", "preciseIdentifier": "s:SS"}, {"kind": "text", "text": ", "}, {"kind": "externalParam", "text": "st"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "text": "String", "preciseIdentifier": "s:SS"}, {"text": ", ", "kind": "text"}, {"text": "c", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "text": "Completion", "preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona"}, {"text": "?) -> ", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIView", "text": "UIView"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/zlp(t:st:c:)"}}}