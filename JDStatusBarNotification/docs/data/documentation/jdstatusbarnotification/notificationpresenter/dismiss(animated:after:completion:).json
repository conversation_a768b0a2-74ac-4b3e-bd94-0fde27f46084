{"hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter"]]}, "metadata": {"externalID": "s:23JDStatusBarNotification0C9PresenterC7dismiss8animated5after10completionySb_SdSgyACcSgtF", "symbolKind": "method", "role": "symbol", "modules": [{"name": "JDStatusBarNotification"}], "fragments": [{"kind": "keyword", "text": "func"}, {"text": " ", "kind": "text"}, {"text": "dismiss", "kind": "identifier"}, {"text": "(", "kind": "text"}, {"text": "animated", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"preciseIdentifier": "s:Sb", "text": "Bool", "kind": "typeIdentifier"}, {"text": ", ", "kind": "text"}, {"kind": "externalParam", "text": "after"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "s:Sd", "text": "Double"}, {"kind": "text", "text": "?, "}, {"kind": "externalParam", "text": "completion"}, {"text": ": ", "kind": "text"}, {"text": "Completion", "kind": "typeIdentifier", "preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona"}, {"kind": "text", "text": "?)"}], "title": "dismiss(animated:after:completion:)", "roleHeading": "Instance Method"}, "identifier": {"url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/dismiss(animated:after:completion:)", "interfaceLanguage": "swift"}, "variants": [{"paths": ["/documentation/jdstatusbarnotification/notificationpresenter/dismiss(animated:after:completion:)"], "traits": [{"interfaceLanguage": "swift"}]}], "sections": [], "abstract": [{"type": "text", "text": "Dismisses any currently displayed notification animated - after the provided delay, if provided."}], "primaryContentSections": [{"kind": "declarations", "declarations": [{"languages": ["swift"], "platforms": ["iOS"], "tokens": [{"kind": "keyword", "text": "func"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "dismiss"}, {"text": "(", "kind": "text"}, {"text": "animated", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "s:Sb", "text": "Bool", "kind": "typeIdentifier"}, {"kind": "text", "text": " = true, "}, {"text": "after", "kind": "externalParam"}, {"text": " ", "kind": "text"}, {"text": "delay", "kind": "internalParam"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "s:Sd", "text": "Double"}, {"kind": "text", "text": "? = nil, "}, {"kind": "externalParam", "text": "completion"}, {"kind": "text", "text": ": "}, {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/Completion", "preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona", "text": "Completion", "kind": "typeIdentifier"}, {"text": "? = nil)", "kind": "text"}]}]}, {"kind": "parameters", "parameters": [{"name": "animated", "content": [{"inlineContent": [{"type": "text", "text": "If "}, {"code": "true", "type": "codeVoice"}, {"type": "text", "text": ", the notification will be dismissed animated according to the currently set "}, {"isActive": true, "type": "reference", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType"}, {"type": "text", "text": "."}, {"text": " ", "type": "text"}, {"type": "text", "text": "Otherwise it will be dismissed without animation."}], "type": "paragraph"}]}, {"name": "delay", "content": [{"type": "paragraph", "inlineContent": [{"text": "The delay in seconds, before the notification should be dismissed.", "type": "text"}]}]}, {"content": [{"inlineContent": [{"type": "text", "text": "A "}, {"type": "reference", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/Completion", "isActive": true}, {"type": "text", "text": " closure, which gets called once the dismiss animation finishes."}], "type": "paragraph"}], "name": "completion"}]}], "schemaVersion": {"major": 0, "patch": 0, "minor": 3}, "kind": "symbol", "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/dismiss(animated:after:completion:)": {"url": "/documentation/jdstatusbarnotification/notificationpresenter/dismiss(animated:after:completion:)", "abstract": [{"type": "text", "text": "Dismisses any currently displayed notification animated - after the provided delay, if provided."}], "title": "dismiss(animated:after:completion:)", "kind": "symbol", "role": "symbol", "type": "topic", "fragments": [{"kind": "keyword", "text": "func"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "dismiss"}, {"kind": "text", "text": "("}, {"text": "animated", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "text": "Bool", "preciseIdentifier": "s:Sb"}, {"kind": "text", "text": ", "}, {"kind": "externalParam", "text": "after"}, {"text": ": ", "kind": "text"}, {"text": "Double", "preciseIdentifier": "s:Sd", "kind": "typeIdentifier"}, {"text": "?, ", "kind": "text"}, {"kind": "externalParam", "text": "completion"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona", "kind": "typeIdentifier", "text": "Completion"}, {"kind": "text", "text": "?)"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/dismiss(animated:after:completion:)"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/Completion": {"kind": "symbol", "abstract": [{"type": "text", "text": "Called upon animation completion."}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/Completion", "url": "/documentation/jdstatusbarnotification/notificationpresenter/completion", "fragments": [{"text": "typealias", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "Completion"}], "navigatorTitle": [{"text": "Completion", "kind": "identifier"}], "title": "NotificationPresenter.Completion", "type": "topic"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType": {"abstract": [{"text": "Defines the animation used during presentation and dismissal of the notification.", "type": "text"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationanimationtype", "fragments": [{"kind": "keyword", "text": "enum"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationAnimationType", "kind": "identifier"}], "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationAnimationType"}], "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType", "title": "StatusBarNotificationAnimationType", "role": "symbol", "type": "topic"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter": {"abstract": [{"type": "text", "text": "The NotificationPresenter let’s you present notifications below the statusBar."}, {"type": "text", "text": " "}, {"type": "text", "text": "You can customize the style (colors, fonts, etc.) and animations. It supports notch"}, {"type": "text", "text": " "}, {"type": "text", "text": "and no-notch devices, landscape & portrait layouts and Drag-to-Dismiss. It can display a"}, {"text": " ", "type": "text"}, {"type": "text", "text": "title, a subtitle, an activity indicator, an animated progress bar & custom views out of the box."}], "fragments": [{"text": "class", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "NotificationPresenter"}], "navigatorTitle": [{"text": "NotificationPresenter", "kind": "identifier"}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter", "url": "/documentation/jdstatusbarnotification/notificationpresenter", "type": "topic", "kind": "symbol", "title": "NotificationPresenter"}}}