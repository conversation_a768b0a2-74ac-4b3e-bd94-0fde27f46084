{"sections": [], "variants": [{"paths": ["/documentation/jdstatusbarnotification/stylableview/style"], "traits": [{"interfaceLanguage": "swift"}]}], "identifier": {"url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StylableView/style", "interfaceLanguage": "swift"}, "kind": "symbol", "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StylableView"]]}, "metadata": {"title": "style", "symbolKind": "property", "roleHeading": "Instance Property", "required": true, "externalID": "s:23JDStatusBarNotification12StylableViewP5styleAA06StatusbC5StyleCvp", "role": "symbol", "fragments": [{"kind": "keyword", "text": "var"}, {"text": " ", "kind": "text"}, {"text": "style", "kind": "identifier"}, {"text": ": ", "kind": "text"}, {"text": "StatusBarNotificationStyle", "kind": "typeIdentifier", "preciseIdentifier": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationStyle"}], "modules": [{"name": "JDStatusBarNotification"}]}, "primaryContentSections": [{"declarations": [{"platforms": ["iOS"], "tokens": [{"kind": "keyword", "text": "var"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "style"}, {"text": ": ", "kind": "text"}, {"text": "StatusBarNotificationStyle", "preciseIdentifier": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationStyle", "kind": "typeIdentifier", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle"}, {"kind": "text", "text": " { "}, {"text": "get", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "set", "kind": "keyword"}, {"text": " }", "kind": "text"}], "languages": ["swift"]}], "kind": "declarations"}], "schemaVersion": {"major": 0, "patch": 0, "minor": 3}, "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle", "navigatorTitle": [{"text": "StatusBarNotificationStyle", "kind": "identifier"}], "role": "symbol", "type": "topic", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "title": "StatusBarNotificationStyle", "abstract": [{"text": "A Style defines the appearance of a notification.", "type": "text"}], "fragments": [{"kind": "keyword", "text": "class"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationStyle", "kind": "identifier"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StylableView/style": {"type": "topic", "role": "symbol", "fragments": [{"text": "var", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "style", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationStyle", "text": "StatusBarNotificationStyle", "kind": "typeIdentifier"}], "url": "/documentation/jdstatusbarnotification/stylableview/style", "abstract": [], "required": true, "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StylableView/style", "kind": "symbol", "title": "style"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StylableView": {"title": "St<PERSON>bleView", "role": "symbol", "fragments": [{"kind": "keyword", "text": "protocol"}, {"text": " ", "kind": "text"}, {"text": "St<PERSON>bleView", "kind": "identifier"}], "navigatorTitle": [{"text": "St<PERSON>bleView", "kind": "identifier"}], "url": "/documentation/jdstatusbarnotification/stylableview", "abstract": [{"text": "A view that can be styled by setting a StatusBarNotificationStyle.", "type": "text"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StylableView", "kind": "symbol", "type": "topic"}}}