{"primaryContentSections": [{"kind": "declarations", "declarations": [{"platforms": ["iOS"], "tokens": [{"text": "@objc", "kind": "attribute"}, {"kind": "text", "text": "(JDStatusBarNotificationLeftViewStyle) "}, {"kind": "keyword", "text": "class"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationLeftViewStyle", "kind": "identifier"}], "languages": ["swift"]}]}], "sections": [], "seeAlsoSections": [{"title": "Notification Style", "identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle"], "generated": true}], "identifier": {"interfaceLanguage": "swift", "url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle"}, "abstract": [{"text": "Defines the appearance of a left-view, if set. It also applies to the activity indicator.", "type": "text"}], "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification"]]}, "metadata": {"roleHeading": "Class", "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationLeftViewStyle"}], "role": "symbol", "fragments": [{"text": "class", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "StatusBarNotificationLeftViewStyle"}], "title": "StatusBarNotificationLeftViewStyle", "modules": [{"name": "JDStatusBarNotification"}], "externalID": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationLeftViewStyle", "symbolKind": "class"}, "kind": "symbol", "variants": [{"traits": [{"interfaceLanguage": "swift"}], "paths": ["/documentation/jdstatusbarnotification/statusbarnotificationleftviewstyle"]}], "topicSections": [{"identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle/alignment", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle/offset", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle/offsetX", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle/spacing", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle/tintColor"], "title": "Instance Properties"}], "schemaVersion": {"patch": 0, "major": 0, "minor": 3}, "relationshipsSections": [{"type": "inheritsFrom", "title": "Inherits From", "identifiers": ["doc://calimarkus.JDStatusBarNotification/objc(cs)NSObject"], "kind": "relationships"}, {"identifiers": ["doc://calimarkus.JDStatusBarNotification/objc(pl)NSObject", "doc://calimarkus.JDStatusBarNotification/s7CVarArgP", "doc://calimarkus.JDStatusBarNotification/s28CustomDebugStringConvertibleP", "doc://calimarkus.JDStatusBarNotification/s23CustomStringConvertibleP", "doc://calimarkus.JDStatusBarNotification/SQ", "doc://calimarkus.JDStatusBarNotification/SH"], "type": "conformsTo", "kind": "relationships", "title": "Conforms To"}], "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle/alignment": {"type": "topic", "kind": "symbol", "title": "alignment", "url": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewstyle/alignment", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle/alignment", "abstract": [{"type": "text", "text": "The alignment of the left-view. The default is "}, {"isActive": true, "type": "reference", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment/centerWithText"}, {"type": "text", "text": " "}, {"text": "If no title or subtitle is set, the left-view is always fully centered.", "type": "text"}], "role": "symbol", "fragments": [{"kind": "keyword", "text": "var"}, {"kind": "text", "text": " "}, {"text": "alignment", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"text": "StatusBarNotificationLeftViewAlignment", "preciseIdentifier": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationLeftViewAlignment", "kind": "typeIdentifier"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle": {"navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationLeftViewStyle"}], "type": "topic", "kind": "symbol", "title": "StatusBarNotificationLeftViewStyle", "abstract": [{"text": "Defines the appearance of a left-view, if set. It also applies to the activity indicator.", "type": "text"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewstyle", "role": "symbol", "fragments": [{"text": "class", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "StatusBarNotificationLeftViewStyle", "kind": "identifier"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle/offsetX": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewstyle/offsetx", "deprecated": true, "title": "offsetX", "type": "topic", "fragments": [{"kind": "keyword", "text": "var"}, {"text": " ", "kind": "text"}, {"text": "offsetX", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"text": "Double", "kind": "typeIdentifier", "preciseIdentifier": "s:Sd"}], "abstract": [{"text": "An optional offset to adjust the left-views position. Default 0.0.", "type": "text"}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle/offsetX", "kind": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment/centerWithText": {"abstract": [{"type": "text", "text": "Centers the left-view together with the text. The left-view will be positioned at the leading edge of the text. The text is left-aligned. This is the default."}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewalignment/centerwithtext", "fragments": [{"kind": "keyword", "text": "case"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "centerWithText"}], "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment/centerWithText", "title": "StatusBarNotificationLeftViewAlignment.centerWithText", "role": "symbol", "type": "topic"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle": {"type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle", "kind": "symbol", "role": "symbol", "fragments": [{"text": "class", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationProgressBarStyle", "kind": "identifier"}], "title": "StatusBarNotificationProgressBarStyle", "abstract": [{"type": "text", "text": "Defines the appearance of the progress bar."}], "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationProgressBarStyle"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarstyle"}, "doc://calimarkus.JDStatusBarNotification/s28CustomDebugStringConvertibleP": {"identifier": "doc://calimarkus.JDStatusBarNotification/s28CustomDebugStringConvertibleP", "title": "Swift.CustomDebugStringConvertible", "type": "unresolvable"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/pill": {"title": "StatusBarNotificationBackgroundType.pill", "url": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundtype/pill", "abstract": [{"type": "text", "text": "The background is a floating pill around the text. The pill size and appearance can be customized. This is the default."}], "kind": "symbol", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/pill", "role": "symbol", "fragments": [{"kind": "keyword", "text": "case"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "pill"}]}, "doc://calimarkus.JDStatusBarNotification/s23CustomStringConvertibleP": {"identifier": "doc://calimarkus.JDStatusBarNotification/s23CustomStringConvertibleP", "title": "Swift.CustomStringConvertible", "type": "unresolvable"}, "doc://calimarkus.JDStatusBarNotification/SQ": {"identifier": "doc://calimarkus.JDStatusBarNotification/SQ", "title": "Swift.Equatable", "type": "unresolvable"}, "doc://calimarkus.JDStatusBarNotification/SH": {"identifier": "doc://calimarkus.JDStatusBarNotification/SH", "title": "<PERSON><PERSON>", "type": "unresolvable"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle/offset": {"fragments": [{"kind": "keyword", "text": "var"}, {"text": " ", "kind": "text"}, {"text": "offset", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"text": "CGPoint", "preciseIdentifier": "c:@S@CGPoint", "kind": "typeIdentifier"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewstyle/offset", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle/offset", "type": "topic", "abstract": [{"text": "An optional offset to adjust the left-views position. Default is ", "type": "text"}, {"code": "CGPointZero", "type": "codeVoice"}, {"type": "text", "text": "."}], "title": "offset", "kind": "symbol", "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle/tintColor": {"fragments": [{"text": "var", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "tintColor", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"text": "UIColor", "kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIColor"}, {"kind": "text", "text": "?"}], "abstract": [{"text": "Sets the tint color of the left-view. <PERSON><PERSON><PERSON> is ", "type": "text"}, {"code": "nil", "type": "codeVoice"}, {"text": ".", "type": "text"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle/tintColor", "url": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewstyle/tintcolor", "role": "symbol", "type": "topic", "kind": "symbol", "title": "tintColor"}, "doc://calimarkus.JDStatusBarNotification/s7CVarArgP": {"type": "unresolvable", "identifier": "doc://calimarkus.JDStatusBarNotification/s7CVarArgP", "title": "Swift.CVarArg"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle/spacing": {"title": "spacing", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle/spacing", "kind": "symbol", "fragments": [{"kind": "keyword", "text": "var"}, {"text": " ", "kind": "text"}, {"text": "spacing", "kind": "identifier"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "text": "Double", "preciseIdentifier": "s:Sd"}], "abstract": [{"text": "The minimum distance between the left-view and the text. Defaults to 5.0.", "type": "text"}], "role": "symbol", "type": "topic", "url": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewstyle/spacing"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle": {"abstract": [{"text": "Defines the appearance of the pill, when using ", "type": "text"}, {"type": "reference", "isActive": true, "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/pill"}], "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationPillStyle"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle", "type": "topic", "kind": "symbol", "role": "symbol", "url": "/documentation/jdstatusbarnotification/statusbarnotificationpillstyle", "fragments": [{"kind": "keyword", "text": "class"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "StatusBarNotificationPillStyle"}], "title": "StatusBarNotificationPillStyle"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundStyle": {"fragments": [{"text": "class", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "StatusBarNotificationBackgroundStyle", "kind": "identifier"}], "kind": "symbol", "navigatorTitle": [{"text": "StatusBarNotificationBackgroundStyle", "kind": "identifier"}], "role": "symbol", "url": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundstyle", "title": "StatusBarNotificationBackgroundStyle", "type": "topic", "abstract": [{"type": "text", "text": "Defines the appearance of the notification background."}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundStyle"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle", "navigatorTitle": [{"text": "StatusBarNotificationStyle", "kind": "identifier"}], "role": "symbol", "type": "topic", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "title": "StatusBarNotificationStyle", "abstract": [{"text": "A Style defines the appearance of a notification.", "type": "text"}], "fragments": [{"kind": "keyword", "text": "class"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationStyle", "kind": "identifier"}]}, "doc://calimarkus.JDStatusBarNotification/objc(cs)NSObject": {"type": "unresolvable", "identifier": "doc://calimarkus.JDStatusBarNotification/objc(cs)NSObject", "title": "ObjectiveC.NSObject"}, "doc://calimarkus.JDStatusBarNotification/objc(pl)NSObject": {"type": "unresolvable", "identifier": "doc://calimarkus.JDStatusBarNotification/objc(pl)NSObject", "title": "ObjectiveC.NSObjectProtocol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle": {"title": "StatusBarNotificationTextStyle", "fragments": [{"kind": "keyword", "text": "class"}, {"text": " ", "kind": "text"}, {"text": "StatusBarNotificationTextStyle", "kind": "identifier"}], "role": "symbol", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle", "url": "/documentation/jdstatusbarnotification/statusbarnotificationtextstyle", "type": "topic", "navigatorTitle": [{"text": "StatusBarNotificationTextStyle", "kind": "identifier"}], "abstract": [{"type": "text", "text": "Defines the appearance of a text label."}]}}}