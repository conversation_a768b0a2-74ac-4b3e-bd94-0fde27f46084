{"kind": "article", "sections": [], "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType"]]}, "variants": [{"paths": ["/documentation/jdstatusbarnotification/statusbarnotificationanimationtype/equatable-implementations"], "traits": [{"interfaceLanguage": "swift"}]}], "metadata": {"modules": [{"name": "JDStatusBarNotification"}], "role": "collectionGroup", "title": "Equatable Implementations"}, "schemaVersion": {"minor": 3, "major": 0, "patch": 0}, "topicSections": [{"title": "Operators", "identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType/!=(_:_:)"], "generated": true}], "identifier": {"url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType/Equatable-Implementations", "interfaceLanguage": "swift"}, "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType/!=(_:_:)": {"title": "!=(_:_:)", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType/!=(_:_:)", "role": "symbol", "fragments": [{"kind": "keyword", "text": "static"}, {"text": " ", "kind": "text"}, {"text": "func", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "!=", "kind": "identifier"}, {"kind": "text", "text": " "}, {"text": "(", "kind": "text"}, {"kind": "typeIdentifier", "text": "Self"}, {"text": ", ", "kind": "text"}, {"text": "Self", "kind": "typeIdentifier"}, {"text": ") -> ", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "s:Sb", "text": "Bool"}], "abstract": [], "url": "/documentation/jdstatusbarnotification/statusbarnotificationanimationtype/!=(_:_:)", "kind": "symbol", "type": "topic"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle", "navigatorTitle": [{"text": "StatusBarNotificationStyle", "kind": "identifier"}], "role": "symbol", "type": "topic", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "title": "StatusBarNotificationStyle", "abstract": [{"text": "A Style defines the appearance of a notification.", "type": "text"}], "fragments": [{"kind": "keyword", "text": "class"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationStyle", "kind": "identifier"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType": {"abstract": [{"text": "Defines the animation used during presentation and dismissal of the notification.", "type": "text"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationanimationtype", "fragments": [{"kind": "keyword", "text": "enum"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationAnimationType", "kind": "identifier"}], "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationAnimationType"}], "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType", "title": "StatusBarNotificationAnimationType", "role": "symbol", "type": "topic"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}}}