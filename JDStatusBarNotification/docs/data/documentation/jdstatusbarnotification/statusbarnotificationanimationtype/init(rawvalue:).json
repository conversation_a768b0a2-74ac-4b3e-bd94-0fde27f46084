{"schemaVersion": {"patch": 0, "major": 0, "minor": 3}, "variants": [{"paths": ["/documentation/jdstatusbarnotification/statusbarnotificationanimationtype/init(rawvalue:)"], "traits": [{"interfaceLanguage": "swift"}]}], "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType"]]}, "abstract": [{"type": "text", "text": "Inherited from "}, {"code": "RawRepresentable.init(rawValue:)", "type": "codeVoice"}, {"text": ".", "type": "text"}], "primaryContentSections": [{"declarations": [{"platforms": ["iOS"], "languages": ["swift"], "tokens": [{"kind": "keyword", "text": "init"}, {"kind": "text", "text": "?("}, {"text": "rawValue", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"text": "Int", "kind": "typeIdentifier", "preciseIdentifier": "s:<PERSON>"}, {"kind": "text", "text": ")"}]}], "kind": "declarations"}], "metadata": {"modules": [{"name": "JDStatusBarNotification"}], "symbolKind": "init", "role": "symbol", "roleHeading": "Initializer", "externalID": "s:23JDStatusBarNotification06StatusbC13AnimationTypeO8rawValueACSgSi_tcfc", "fragments": [{"kind": "identifier", "text": "init"}, {"text": "?(", "kind": "text"}, {"kind": "externalParam", "text": "rawValue"}, {"text": ": ", "kind": "text"}, {"preciseIdentifier": "s:<PERSON>", "kind": "typeIdentifier", "text": "Int"}, {"kind": "text", "text": ")"}], "title": "init(rawValue:)"}, "identifier": {"interfaceLanguage": "swift", "url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType/init(rawValue:)"}, "kind": "symbol", "sections": [], "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle", "navigatorTitle": [{"text": "StatusBarNotificationStyle", "kind": "identifier"}], "role": "symbol", "type": "topic", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "title": "StatusBarNotificationStyle", "abstract": [{"text": "A Style defines the appearance of a notification.", "type": "text"}], "fragments": [{"kind": "keyword", "text": "class"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationStyle", "kind": "identifier"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType/init(rawValue:)": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType/init(rawValue:)", "abstract": [], "fragments": [{"kind": "identifier", "text": "init"}, {"kind": "text", "text": "?("}, {"text": "rawValue", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"preciseIdentifier": "s:<PERSON>", "kind": "typeIdentifier", "text": "Int"}, {"text": ")", "kind": "text"}], "role": "symbol", "type": "topic", "kind": "symbol", "url": "/documentation/jdstatusbarnotification/statusbarnotificationanimationtype/init(rawvalue:)", "title": "init(rawValue:)"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType": {"abstract": [{"text": "Defines the animation used during presentation and dismissal of the notification.", "type": "text"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationanimationtype", "fragments": [{"kind": "keyword", "text": "enum"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationAnimationType", "kind": "identifier"}], "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationAnimationType"}], "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType", "title": "StatusBarNotificationAnimationType", "role": "symbol", "type": "topic"}}}