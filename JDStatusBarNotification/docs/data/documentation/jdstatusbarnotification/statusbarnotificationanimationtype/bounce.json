{"hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType"]]}, "variants": [{"traits": [{"interfaceLanguage": "swift"}], "paths": ["/documentation/jdstatusbarnotification/statusbarnotificationanimationtype/bounce"]}], "abstract": [{"text": "Fall down from the top and bounce a little bit, before coming to a rest. Slides back out to the top.", "type": "text"}], "primaryContentSections": [{"kind": "declarations", "declarations": [{"tokens": [{"text": "case", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "bounce", "kind": "identifier"}], "platforms": ["iOS"], "languages": ["swift"]}]}], "sections": [], "metadata": {"title": "StatusBarNotificationAnimationType.bounce", "role": "symbol", "externalID": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationAnimationType@JDStatusBarNotificationAnimationTypeBounce", "symbolKind": "case", "modules": [{"name": "JDStatusBarNotification"}], "roleHeading": "Case", "fragments": [{"text": "case", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "bounce"}]}, "identifier": {"url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType/bounce", "interfaceLanguage": "swift"}, "schemaVersion": {"patch": 0, "major": 0, "minor": 3}, "kind": "symbol", "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"kind": "symbol", "title": "JDStatusBarNotification", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "type": "topic", "url": "/documentation/jdstatusbarnotification", "abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType/bounce": {"title": "StatusBarNotificationAnimationType.bounce", "type": "topic", "url": "/documentation/jdstatusbarnotification/statusbarnotificationanimationtype/bounce", "kind": "symbol", "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType/bounce", "abstract": [{"text": "Fall down from the top and bounce a little bit, before coming to a rest. Slides back out to the top.", "type": "text"}], "fragments": [{"text": "case", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "bounce"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle", "abstract": [{"type": "text", "text": "A Style defines the appearance of a notification."}], "fragments": [{"text": "class", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "StatusBarNotificationStyle", "kind": "identifier"}], "kind": "symbol", "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationStyle"}], "type": "topic", "role": "symbol", "title": "StatusBarNotificationStyle"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType": {"role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType", "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationAnimationType"}], "fragments": [{"kind": "keyword", "text": "enum"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "StatusBarNotificationAnimationType"}], "type": "topic", "url": "/documentation/jdstatusbarnotification/statusbarnotificationanimationtype", "abstract": [{"text": "Defines the animation used during presentation and dismissal of the notification.", "type": "text"}], "title": "StatusBarNotificationAnimationType", "kind": "symbol"}}}