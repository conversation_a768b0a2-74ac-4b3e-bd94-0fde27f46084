{"schemaVersion": {"major": 0, "patch": 0, "minor": 3}, "identifier": {"url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType/hash(into:)", "interfaceLanguage": "swift"}, "primaryContentSections": [{"declarations": [{"languages": ["swift"], "platforms": ["iOS"], "tokens": [{"kind": "keyword", "text": "func"}, {"kind": "text", "text": " "}, {"text": "hash", "kind": "identifier"}, {"kind": "text", "text": "("}, {"kind": "externalParam", "text": "into"}, {"kind": "text", "text": " "}, {"text": "hasher", "kind": "internalParam"}, {"kind": "text", "text": ": "}, {"kind": "keyword", "text": "inout"}, {"text": " ", "kind": "text"}, {"text": "<PERSON><PERSON>", "kind": "typeIdentifier", "preciseIdentifier": "s:s6HasherV"}, {"kind": "text", "text": ")"}]}], "kind": "declarations"}], "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType/RawRepresentable-Implementations"]]}, "variants": [{"traits": [{"interfaceLanguage": "swift"}], "paths": ["/documentation/jdstatusbarnotification/statusbarnotificationanimationtype/hash(into:)"]}], "metadata": {"role": "symbol", "extendedModule": "Swift", "roleHeading": "Instance Method", "modules": [{"relatedModules": ["Swift"], "name": "JDStatusBarNotification"}], "symbolKind": "method", "conformance": {"conformancePrefix": [{"text": "Conforms when", "type": "text"}], "availabilityPrefix": [{"type": "text", "text": "Available when"}], "constraints": [{"code": "Self", "type": "codeVoice"}, {"type": "text", "text": " conforms to "}, {"code": "<PERSON><PERSON><PERSON>", "type": "codeVoice"}, {"text": " and ", "type": "text"}, {"code": "RawValue", "type": "codeVoice"}, {"type": "text", "text": " conforms to "}, {"code": "<PERSON><PERSON><PERSON>", "type": "codeVoice"}, {"type": "text", "text": "."}]}, "fragments": [{"text": "func", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "hash", "kind": "identifier"}, {"kind": "text", "text": "("}, {"kind": "externalParam", "text": "into"}, {"kind": "text", "text": ": "}, {"text": "inout", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "s:s6HasherV", "text": "<PERSON><PERSON>"}, {"kind": "text", "text": ")"}], "externalID": "s:SYsSHRzSH8RawValueSYRpzrlE4hash4intoys6HasherVz_tF::SYNTHESIZED::c:@M@JDStatusBarNotification@E@JDStatusBarNotificationAnimationType", "title": "hash(into:)"}, "sections": [], "abstract": [{"type": "text", "text": "Inherited from "}, {"type": "codeVoice", "code": "RawRepresentable.hash(into:)"}, {"type": "text", "text": "."}], "kind": "symbol", "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType": {"abstract": [{"text": "Defines the animation used during presentation and dismissal of the notification.", "type": "text"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationanimationtype", "fragments": [{"kind": "keyword", "text": "enum"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationAnimationType", "kind": "identifier"}], "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationAnimationType"}], "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType", "title": "StatusBarNotificationAnimationType", "role": "symbol", "type": "topic"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType/RawRepresentable-Implementations": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationanimationtype/rawrepresentable-implementations", "title": "RawRepresentable Implementations", "type": "topic", "abstract": [], "role": "collectionGroup", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType/RawRepresentable-Implementations", "kind": "article"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle", "navigatorTitle": [{"text": "StatusBarNotificationStyle", "kind": "identifier"}], "role": "symbol", "type": "topic", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "title": "StatusBarNotificationStyle", "abstract": [{"text": "A Style defines the appearance of a notification.", "type": "text"}], "fragments": [{"kind": "keyword", "text": "class"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationStyle", "kind": "identifier"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType/hash(into:)": {"role": "symbol", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType/hash(into:)", "kind": "symbol", "abstract": [], "url": "/documentation/jdstatusbarnotification/statusbarnotificationanimationtype/hash(into:)", "title": "hash(into:)", "fragments": [{"text": "func", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "hash"}, {"kind": "text", "text": "("}, {"kind": "externalParam", "text": "into"}, {"kind": "text", "text": ": "}, {"text": "inout", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "typeIdentifier", "text": "<PERSON><PERSON>", "preciseIdentifier": "s:s6HasherV"}, {"text": ")", "kind": "text"}], "conformance": {"constraints": [{"code": "Self", "type": "codeVoice"}, {"text": " conforms to ", "type": "text"}, {"code": "<PERSON><PERSON><PERSON>", "type": "codeVoice"}, {"type": "text", "text": " and "}, {"type": "codeVoice", "code": "RawValue"}, {"text": " conforms to ", "type": "text"}, {"type": "codeVoice", "code": "<PERSON><PERSON><PERSON>"}, {"type": "text", "text": "."}], "availabilityPrefix": [{"text": "Available when", "type": "text"}], "conformancePrefix": [{"type": "text", "text": "Conforms when"}]}}}}