{"schemaVersion": {"patch": 0, "minor": 3, "major": 0}, "identifier": {"interfaceLanguage": "swift", "url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType/hashValue"}, "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType/RawRepresentable-Implementations"]]}, "primaryContentSections": [{"kind": "declarations", "declarations": [{"tokens": [{"text": "var", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "hashValue"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "s:<PERSON>", "kind": "typeIdentifier", "text": "Int"}, {"kind": "text", "text": " { "}, {"kind": "keyword", "text": "get"}, {"text": " }", "kind": "text"}], "platforms": ["iOS"], "languages": ["swift"]}]}], "kind": "symbol", "metadata": {"modules": [{"relatedModules": ["Swift"], "name": "JDStatusBarNotification"}], "role": "symbol", "externalID": "s:SYsSHRzSH8RawValueSYRpzrlE04hashB0Sivp::SYNTHESIZED::c:@M@JDStatusBarNotification@E@JDStatusBarNotificationAnimationType", "symbolKind": "property", "title": "hashValue", "conformance": {"availabilityPrefix": [{"type": "text", "text": "Available when"}], "constraints": [{"code": "Self", "type": "codeVoice"}, {"text": " conforms to ", "type": "text"}, {"code": "<PERSON><PERSON><PERSON>", "type": "codeVoice"}, {"type": "text", "text": " and "}, {"code": "RawValue", "type": "codeVoice"}, {"text": " conforms to ", "type": "text"}, {"code": "<PERSON><PERSON><PERSON>", "type": "codeVoice"}, {"type": "text", "text": "."}], "conformancePrefix": [{"type": "text", "text": "Conforms when"}]}, "roleHeading": "Instance Property", "extendedModule": "Swift", "fragments": [{"kind": "keyword", "text": "var"}, {"text": " ", "kind": "text"}, {"text": "hashValue", "kind": "identifier"}, {"text": ": ", "kind": "text"}, {"preciseIdentifier": "s:<PERSON>", "text": "Int", "kind": "typeIdentifier"}]}, "sections": [], "abstract": [{"type": "text", "text": "Inherited from "}, {"type": "codeVoice", "code": "RawRepresentable.hashValue"}, {"type": "text", "text": "."}], "variants": [{"paths": ["/documentation/jdstatusbarnotification/statusbarnotificationanimationtype/hashvalue"], "traits": [{"interfaceLanguage": "swift"}]}], "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType": {"abstract": [{"text": "Defines the animation used during presentation and dismissal of the notification.", "type": "text"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationanimationtype", "fragments": [{"kind": "keyword", "text": "enum"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationAnimationType", "kind": "identifier"}], "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationAnimationType"}], "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType", "title": "StatusBarNotificationAnimationType", "role": "symbol", "type": "topic"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType/hashValue": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType/hashValue", "title": "hashValue", "type": "topic", "fragments": [{"kind": "keyword", "text": "var"}, {"text": " ", "kind": "text"}, {"text": "hashValue", "kind": "identifier"}, {"text": ": ", "kind": "text"}, {"preciseIdentifier": "s:<PERSON>", "text": "Int", "kind": "typeIdentifier"}], "conformance": {"constraints": [{"type": "codeVoice", "code": "Self"}, {"text": " conforms to ", "type": "text"}, {"type": "codeVoice", "code": "<PERSON><PERSON><PERSON>"}, {"type": "text", "text": " and "}, {"code": "RawValue", "type": "codeVoice"}, {"text": " conforms to ", "type": "text"}, {"code": "<PERSON><PERSON><PERSON>", "type": "codeVoice"}, {"type": "text", "text": "."}], "availabilityPrefix": [{"text": "Available when", "type": "text"}], "conformancePrefix": [{"text": "Conforms when", "type": "text"}]}, "abstract": [], "role": "symbol", "kind": "symbol", "url": "/documentation/jdstatusbarnotification/statusbarnotificationanimationtype/hashvalue"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType/RawRepresentable-Implementations": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationanimationtype/rawrepresentable-implementations", "title": "RawRepresentable Implementations", "type": "topic", "abstract": [], "role": "collectionGroup", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType/RawRepresentable-Implementations", "kind": "article"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle", "navigatorTitle": [{"text": "StatusBarNotificationStyle", "kind": "identifier"}], "role": "symbol", "type": "topic", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "title": "StatusBarNotificationStyle", "abstract": [{"text": "A Style defines the appearance of a notification.", "type": "text"}], "fragments": [{"kind": "keyword", "text": "class"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationStyle", "kind": "identifier"}]}}}