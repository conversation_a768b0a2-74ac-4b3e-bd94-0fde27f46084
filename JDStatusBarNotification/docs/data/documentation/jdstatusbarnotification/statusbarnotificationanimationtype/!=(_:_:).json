{"sections": [], "identifier": {"interfaceLanguage": "swift", "url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType/!=(_:_:)"}, "primaryContentSections": [{"kind": "declarations", "declarations": [{"tokens": [{"text": "static", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "func", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "!=", "kind": "identifier"}, {"text": " ", "kind": "text"}, {"kind": "text", "text": "("}, {"kind": "internalParam", "text": "lhs"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "text": "Self"}, {"text": ", ", "kind": "text"}, {"kind": "internalParam", "text": "rhs"}, {"kind": "text", "text": ": "}, {"kind": "typeIdentifier", "text": "Self"}, {"text": ") -> ", "kind": "text"}, {"kind": "typeIdentifier", "text": "Bool", "preciseIdentifier": "s:Sb"}], "languages": ["swift"], "platforms": ["iOS"]}]}], "schemaVersion": {"patch": 0, "minor": 3, "major": 0}, "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType/Equatable-Implementations"]]}, "kind": "symbol", "metadata": {"roleHeading": "Operator", "extendedModule": "Swift", "role": "symbol", "fragments": [{"text": "static", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "keyword", "text": "func"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "!="}, {"text": " ", "kind": "text"}, {"kind": "text", "text": "("}, {"text": "Self", "kind": "typeIdentifier"}, {"kind": "text", "text": ", "}, {"text": "Self", "kind": "typeIdentifier"}, {"kind": "text", "text": ") -> "}, {"kind": "typeIdentifier", "preciseIdentifier": "s:Sb", "text": "Bool"}], "title": "!=(_:_:)", "modules": [{"name": "JDStatusBarNotification", "relatedModules": ["Swift"]}], "externalID": "s:SQsE2neoiySbx_xtFZ::SYNTHESIZED::c:@M@JDStatusBarNotification@E@JDStatusBarNotificationAnimationType", "symbolKind": "op"}, "abstract": [{"type": "text", "text": "Inherited from "}, {"type": "codeVoice", "code": "Equatable.!=(_:_:)"}, {"text": ".", "type": "text"}], "variants": [{"traits": [{"interfaceLanguage": "swift"}], "paths": ["/documentation/jdstatusbarnotification/statusbarnotificationanimationtype/!=(_:_:)"]}], "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType/!=(_:_:)": {"title": "!=(_:_:)", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType/!=(_:_:)", "role": "symbol", "fragments": [{"kind": "keyword", "text": "static"}, {"text": " ", "kind": "text"}, {"text": "func", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "!=", "kind": "identifier"}, {"kind": "text", "text": " "}, {"text": "(", "kind": "text"}, {"kind": "typeIdentifier", "text": "Self"}, {"text": ", ", "kind": "text"}, {"text": "Self", "kind": "typeIdentifier"}, {"text": ") -> ", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "s:Sb", "text": "Bool"}], "abstract": [], "url": "/documentation/jdstatusbarnotification/statusbarnotificationanimationtype/!=(_:_:)", "kind": "symbol", "type": "topic"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle", "navigatorTitle": [{"text": "StatusBarNotificationStyle", "kind": "identifier"}], "role": "symbol", "type": "topic", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "title": "StatusBarNotificationStyle", "abstract": [{"text": "A Style defines the appearance of a notification.", "type": "text"}], "fragments": [{"kind": "keyword", "text": "class"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationStyle", "kind": "identifier"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType": {"abstract": [{"text": "Defines the animation used during presentation and dismissal of the notification.", "type": "text"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationanimationtype", "fragments": [{"kind": "keyword", "text": "enum"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationAnimationType", "kind": "identifier"}], "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationAnimationType"}], "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType", "title": "StatusBarNotificationAnimationType", "role": "symbol", "type": "topic"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType/Equatable-Implementations": {"type": "topic", "url": "/documentation/jdstatusbarnotification/statusbarnotificationanimationtype/equatable-implementations", "kind": "article", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType/Equatable-Implementations", "title": "Equatable Implementations", "role": "collectionGroup", "abstract": []}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}}}