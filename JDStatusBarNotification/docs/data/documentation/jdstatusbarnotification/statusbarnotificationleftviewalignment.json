{"hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle"]]}, "primaryContentSections": [{"declarations": [{"platforms": ["iOS"], "tokens": [{"kind": "attribute", "text": "@objc"}, {"kind": "text", "text": "(JDStatusBarNotificationLeftViewAlignment) "}, {"text": "enum", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "StatusBarNotificationLeftViewAlignment"}], "languages": ["swift"]}], "kind": "declarations"}, {"kind": "content", "content": [{"level": 2, "text": "Overview", "type": "heading", "anchor": "overview"}, {"type": "paragraph", "inlineContent": [{"type": "text", "text": "The default is "}, {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment/centerWithText", "isActive": true, "type": "reference"}, {"text": ".", "type": "text"}, {"text": " ", "type": "text"}, {"type": "text", "text": "If no title or subtitle is set, the left-view is always fully centered."}]}, {"inlineContent": [{"type": "text", "text": "Note: This can also influence the text layout as described below."}], "type": "paragraph"}]}], "metadata": {"roleHeading": "Enumeration", "navigatorTitle": [{"text": "StatusBarNotificationLeftViewAlignment", "kind": "identifier"}], "title": "StatusBarNotificationLeftViewAlignment", "externalID": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationLeftViewAlignment", "modules": [{"name": "JDStatusBarNotification"}], "role": "symbol", "symbolKind": "enum", "fragments": [{"kind": "keyword", "text": "enum"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationLeftViewAlignment", "kind": "identifier"}]}, "schemaVersion": {"minor": 3, "patch": 0, "major": 0}, "abstract": [{"type": "text", "text": "Defines the appearance of a left-view, if set. This includes the activity indicator."}], "topicSections": [{"identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment/centerWithText", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment/left"], "title": "Enumeration Cases"}, {"title": "Initializers", "identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment/init(rawValue:)"]}, {"identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment/Equatable-Implementations", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment/RawRepresentable-Implementations"], "generated": true, "title": "Default Implementations"}], "variants": [{"traits": [{"interfaceLanguage": "swift"}], "paths": ["/documentation/jdstatusbarnotification/statusbarnotificationleftviewalignment"]}], "relationshipsSections": [{"identifiers": ["doc://calimarkus.JDStatusBarNotification/SQ", "doc://calimarkus.JDStatusBarNotification/SH", "doc://calimarkus.JDStatusBarNotification/SY"], "kind": "relationships", "type": "conformsTo", "title": "Conforms To"}], "seeAlsoSections": [{"identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition"], "generated": true, "title": "Style Enumerations"}], "identifier": {"url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment", "interfaceLanguage": "swift"}, "sections": [], "kind": "symbol", "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType": {"abstract": [{"text": "Defines the animation used during presentation and dismissal of the notification.", "type": "text"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationanimationtype", "fragments": [{"kind": "keyword", "text": "enum"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationAnimationType", "kind": "identifier"}], "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationAnimationType"}], "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType", "title": "StatusBarNotificationAnimationType", "role": "symbol", "type": "topic"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/SQ": {"identifier": "doc://calimarkus.JDStatusBarNotification/SQ", "title": "Swift.Equatable", "type": "unresolvable"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment/Equatable-Implementations": {"abstract": [], "url": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewalignment/equatable-implementations", "kind": "article", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment/Equatable-Implementations", "title": "Equatable Implementations", "role": "collectionGroup", "type": "topic"}, "doc://calimarkus.JDStatusBarNotification/SY": {"identifier": "doc://calimarkus.JDStatusBarNotification/SY", "title": "Swift.RawRepresentable", "type": "unresolvable"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle": {"abstract": [{"text": "Defines which ", "type": "text"}, {"code": "UIStatusBarStyle", "type": "codeVoice"}, {"type": "text", "text": " should be used during presentation."}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationsystembarstyle", "fragments": [{"kind": "keyword", "text": "enum"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "StatusBarNotificationSystemBarStyle"}], "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationSystemBarStyle"}], "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle", "title": "StatusBarNotificationSystemBarStyle", "role": "symbol", "type": "topic"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment/centerWithText": {"abstract": [{"type": "text", "text": "Centers the left-view together with the text. The left-view will be positioned at the leading edge of the text. The text is left-aligned. This is the default."}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewalignment/centerwithtext", "fragments": [{"kind": "keyword", "text": "case"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "centerWithText"}], "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment/centerWithText", "title": "StatusBarNotificationLeftViewAlignment.centerWithText", "role": "symbol", "type": "topic"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType", "fragments": [{"kind": "keyword", "text": "enum"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationBackgroundType", "kind": "identifier"}], "title": "StatusBarNotificationBackgroundType", "abstract": [{"text": "Defines the appearance of the notification background.", "type": "text"}], "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationBackgroundType"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundtype", "type": "topic", "kind": "symbol", "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle", "navigatorTitle": [{"text": "StatusBarNotificationStyle", "kind": "identifier"}], "role": "symbol", "type": "topic", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "title": "StatusBarNotificationStyle", "abstract": [{"text": "A Style defines the appearance of a notification.", "type": "text"}], "fragments": [{"kind": "keyword", "text": "class"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationStyle", "kind": "identifier"}]}, "doc://calimarkus.JDStatusBarNotification/SH": {"type": "unresolvable", "identifier": "doc://calimarkus.JDStatusBarNotification/SH", "title": "<PERSON><PERSON>"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment/left": {"type": "topic", "role": "symbol", "title": "StatusBarNotificationLeftViewAlignment.left", "abstract": [{"type": "text", "text": "Aligns the left-view on the left side of the notification. The text is center-aligned unless it touches the left-view."}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment/left", "fragments": [{"text": "case", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "left", "kind": "identifier"}], "kind": "symbol", "url": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewalignment/left"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment/RawRepresentable-Implementations": {"abstract": [], "title": "RawRepresentable Implementations", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment/RawRepresentable-Implementations", "url": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewalignment/rawrepresentable-implementations", "kind": "article", "role": "collectionGroup"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment", "role": "symbol", "kind": "symbol", "fragments": [{"kind": "keyword", "text": "enum"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "StatusBarNotificationLeftViewAlignment"}], "abstract": [{"text": "Defines the appearance of a left-view, if set. This includes the activity indicator.", "type": "text"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewalignment", "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationLeftViewAlignment"}], "type": "topic", "title": "StatusBarNotificationLeftViewAlignment"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition": {"abstract": [{"type": "text", "text": "Defines the position of the progress bar, when used."}], "type": "topic", "role": "symbol", "fragments": [{"kind": "keyword", "text": "enum"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "StatusBarNotificationProgressBarPosition"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarposition", "kind": "symbol", "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationProgressBarPosition"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition", "title": "StatusBarNotificationProgressBarPosition"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment/init(rawValue:)": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewalignment/init(rawvalue:)", "fragments": [{"text": "init", "kind": "identifier"}, {"text": "?(", "kind": "text"}, {"kind": "externalParam", "text": "rawValue"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "s:<PERSON>", "text": "Int"}, {"text": ")", "kind": "text"}], "role": "symbol", "abstract": [], "title": "init(rawValue:)", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment/init(rawValue:)", "kind": "symbol"}}}