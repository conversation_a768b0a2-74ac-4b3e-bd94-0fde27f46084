{"sections": [], "variants": [{"traits": [{"interfaceLanguage": "swift"}], "paths": ["/documentation/jdstatusbarnotification/statusbarnotificationprogressbarposition/init(rawvalue:)"]}], "kind": "symbol", "primaryContentSections": [{"kind": "declarations", "declarations": [{"tokens": [{"text": "init", "kind": "keyword"}, {"kind": "text", "text": "?("}, {"kind": "externalParam", "text": "rawValue"}, {"kind": "text", "text": ": "}, {"text": "Int", "kind": "typeIdentifier", "preciseIdentifier": "s:<PERSON>"}, {"text": ")", "kind": "text"}], "languages": ["swift"], "platforms": ["iOS"]}]}], "schemaVersion": {"minor": 3, "major": 0, "patch": 0}, "identifier": {"interfaceLanguage": "swift", "url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition/init(rawValue:)"}, "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition"]]}, "metadata": {"role": "symbol", "title": "init(rawValue:)", "fragments": [{"text": "init", "kind": "identifier"}, {"text": "?(", "kind": "text"}, {"kind": "externalParam", "text": "rawValue"}, {"kind": "text", "text": ": "}, {"kind": "typeIdentifier", "text": "Int", "preciseIdentifier": "s:<PERSON>"}, {"text": ")", "kind": "text"}], "modules": [{"name": "JDStatusBarNotification"}], "externalID": "s:23JDStatusBarNotification06Statusbc8ProgressB8PositionO8rawValueACSgSi_tcfc", "roleHeading": "Initializer", "symbolKind": "init"}, "abstract": [{"type": "text", "text": "Inherited from "}, {"type": "codeVoice", "code": "RawRepresentable.init(rawValue:)"}, {"text": ".", "type": "text"}], "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle", "navigatorTitle": [{"text": "StatusBarNotificationStyle", "kind": "identifier"}], "role": "symbol", "type": "topic", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "title": "StatusBarNotificationStyle", "abstract": [{"text": "A Style defines the appearance of a notification.", "type": "text"}], "fragments": [{"kind": "keyword", "text": "class"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationStyle", "kind": "identifier"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition/init(rawValue:)": {"role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition/init(rawValue:)", "abstract": [], "title": "init(rawValue:)", "url": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarposition/init(rawvalue:)", "kind": "symbol", "fragments": [{"text": "init", "kind": "identifier"}, {"text": "?(", "kind": "text"}, {"text": "rawValue", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"text": "Int", "preciseIdentifier": "s:<PERSON>", "kind": "typeIdentifier"}, {"text": ")", "kind": "text"}], "type": "topic"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition": {"abstract": [{"type": "text", "text": "Defines the position of the progress bar, when used."}], "type": "topic", "role": "symbol", "fragments": [{"kind": "keyword", "text": "enum"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "StatusBarNotificationProgressBarPosition"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarposition", "kind": "symbol", "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationProgressBarPosition"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition", "title": "StatusBarNotificationProgressBarPosition"}}}