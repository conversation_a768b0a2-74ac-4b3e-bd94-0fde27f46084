{"identifier": {"url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition/hash(into:)", "interfaceLanguage": "swift"}, "metadata": {"title": "hash(into:)", "modules": [{"relatedModules": ["Swift"], "name": "JDStatusBarNotification"}], "conformance": {"constraints": [{"type": "codeVoice", "code": "Self"}, {"text": " conforms to ", "type": "text"}, {"type": "codeVoice", "code": "<PERSON><PERSON><PERSON>"}, {"text": " and ", "type": "text"}, {"type": "codeVoice", "code": "RawValue"}, {"text": " conforms to ", "type": "text"}, {"code": "<PERSON><PERSON><PERSON>", "type": "codeVoice"}, {"type": "text", "text": "."}], "availabilityPrefix": [{"type": "text", "text": "Available when"}], "conformancePrefix": [{"text": "Conforms when", "type": "text"}]}, "fragments": [{"kind": "keyword", "text": "func"}, {"text": " ", "kind": "text"}, {"text": "hash", "kind": "identifier"}, {"text": "(", "kind": "text"}, {"kind": "externalParam", "text": "into"}, {"text": ": ", "kind": "text"}, {"kind": "keyword", "text": "inout"}, {"kind": "text", "text": " "}, {"preciseIdentifier": "s:s6HasherV", "text": "<PERSON><PERSON>", "kind": "typeIdentifier"}, {"text": ")", "kind": "text"}], "role": "symbol", "symbolKind": "method", "extendedModule": "Swift", "externalID": "s:SYsSHRzSH8RawValueSYRpzrlE4hash4intoys6HasherVz_tF::SYNTHESIZED::c:@M@JDStatusBarNotification@E@JDStatusBarNotificationProgressBarPosition", "roleHeading": "Instance Method"}, "abstract": [{"text": "Inherited from ", "type": "text"}, {"type": "codeVoice", "code": "RawRepresentable.hash(into:)"}, {"text": ".", "type": "text"}], "schemaVersion": {"major": 0, "minor": 3, "patch": 0}, "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition/RawRepresentable-Implementations"]]}, "kind": "symbol", "sections": [], "variants": [{"paths": ["/documentation/jdstatusbarnotification/statusbarnotificationprogressbarposition/hash(into:)"], "traits": [{"interfaceLanguage": "swift"}]}], "primaryContentSections": [{"declarations": [{"tokens": [{"kind": "keyword", "text": "func"}, {"kind": "text", "text": " "}, {"text": "hash", "kind": "identifier"}, {"text": "(", "kind": "text"}, {"kind": "externalParam", "text": "into"}, {"text": " ", "kind": "text"}, {"kind": "internalParam", "text": "hasher"}, {"text": ": ", "kind": "text"}, {"kind": "keyword", "text": "inout"}, {"text": " ", "kind": "text"}, {"preciseIdentifier": "s:s6HasherV", "kind": "typeIdentifier", "text": "<PERSON><PERSON>"}, {"text": ")", "kind": "text"}], "languages": ["swift"], "platforms": ["iOS"]}], "kind": "declarations"}], "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition/hash(into:)": {"role": "symbol", "conformance": {"availabilityPrefix": [{"text": "Available when", "type": "text"}], "constraints": [{"type": "codeVoice", "code": "Self"}, {"text": " conforms to ", "type": "text"}, {"type": "codeVoice", "code": "<PERSON><PERSON><PERSON>"}, {"type": "text", "text": " and "}, {"type": "codeVoice", "code": "RawValue"}, {"type": "text", "text": " conforms to "}, {"code": "<PERSON><PERSON><PERSON>", "type": "codeVoice"}, {"type": "text", "text": "."}], "conformancePrefix": [{"text": "Conforms when", "type": "text"}]}, "abstract": [], "title": "hash(into:)", "url": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarposition/hash(into:)", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition/hash(into:)", "type": "topic", "kind": "symbol", "fragments": [{"text": "func", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "hash"}, {"text": "(", "kind": "text"}, {"text": "into", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"kind": "keyword", "text": "inout"}, {"text": " ", "kind": "text"}, {"preciseIdentifier": "s:s6HasherV", "kind": "typeIdentifier", "text": "<PERSON><PERSON>"}, {"kind": "text", "text": ")"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle", "navigatorTitle": [{"text": "StatusBarNotificationStyle", "kind": "identifier"}], "role": "symbol", "type": "topic", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "title": "StatusBarNotificationStyle", "abstract": [{"text": "A Style defines the appearance of a notification.", "type": "text"}], "fragments": [{"kind": "keyword", "text": "class"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationStyle", "kind": "identifier"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition": {"abstract": [{"type": "text", "text": "Defines the position of the progress bar, when used."}], "type": "topic", "role": "symbol", "fragments": [{"kind": "keyword", "text": "enum"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "StatusBarNotificationProgressBarPosition"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarposition", "kind": "symbol", "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationProgressBarPosition"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition", "title": "StatusBarNotificationProgressBarPosition"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition/RawRepresentable-Implementations": {"abstract": [], "type": "topic", "role": "collectionGroup", "title": "RawRepresentable Implementations", "url": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarposition/rawrepresentable-implementations", "kind": "article", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition/RawRepresentable-Implementations"}}}