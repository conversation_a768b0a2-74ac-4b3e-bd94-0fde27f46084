{"schemaVersion": {"patch": 0, "minor": 3, "major": 0}, "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition/Equatable-Implementations"]]}, "kind": "symbol", "primaryContentSections": [{"declarations": [{"languages": ["swift"], "tokens": [{"kind": "keyword", "text": "static"}, {"text": " ", "kind": "text"}, {"kind": "keyword", "text": "func"}, {"kind": "text", "text": " "}, {"text": "!=", "kind": "identifier"}, {"text": " ", "kind": "text"}, {"kind": "text", "text": "("}, {"kind": "internalParam", "text": "lhs"}, {"kind": "text", "text": ": "}, {"text": "Self", "kind": "typeIdentifier"}, {"kind": "text", "text": ", "}, {"text": "rhs", "kind": "internalParam"}, {"kind": "text", "text": ": "}, {"kind": "typeIdentifier", "text": "Self"}, {"text": ") -> ", "kind": "text"}, {"preciseIdentifier": "s:Sb", "text": "Bool", "kind": "typeIdentifier"}], "platforms": ["iOS"]}], "kind": "declarations"}], "variants": [{"traits": [{"interfaceLanguage": "swift"}], "paths": ["/documentation/jdstatusbarnotification/statusbarnotificationprogressbarposition/!=(_:_:)"]}], "abstract": [{"type": "text", "text": "Inherited from "}, {"code": "Equatable.!=(_:_:)", "type": "codeVoice"}, {"text": ".", "type": "text"}], "identifier": {"url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition/!=(_:_:)", "interfaceLanguage": "swift"}, "metadata": {"role": "symbol", "roleHeading": "Operator", "symbolKind": "op", "title": "!=(_:_:)", "extendedModule": "Swift", "modules": [{"name": "JDStatusBarNotification", "relatedModules": ["Swift"]}], "externalID": "s:SQsE2neoiySbx_xtFZ::SYNTHESIZED::c:@M@JDStatusBarNotification@E@JDStatusBarNotificationProgressBarPosition", "fragments": [{"text": "static", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "func", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "!="}, {"text": " ", "kind": "text"}, {"text": "(", "kind": "text"}, {"text": "Self", "kind": "typeIdentifier"}, {"text": ", ", "kind": "text"}, {"text": "Self", "kind": "typeIdentifier"}, {"text": ") -> ", "kind": "text"}, {"text": "Bool", "preciseIdentifier": "s:Sb", "kind": "typeIdentifier"}]}, "sections": [], "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle", "navigatorTitle": [{"text": "StatusBarNotificationStyle", "kind": "identifier"}], "role": "symbol", "type": "topic", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "title": "StatusBarNotificationStyle", "abstract": [{"text": "A Style defines the appearance of a notification.", "type": "text"}], "fragments": [{"kind": "keyword", "text": "class"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationStyle", "kind": "identifier"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition": {"abstract": [{"type": "text", "text": "Defines the position of the progress bar, when used."}], "type": "topic", "role": "symbol", "fragments": [{"kind": "keyword", "text": "enum"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "StatusBarNotificationProgressBarPosition"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarposition", "kind": "symbol", "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationProgressBarPosition"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition", "title": "StatusBarNotificationProgressBarPosition"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition/Equatable-Implementations": {"kind": "article", "abstract": [], "role": "collectionGroup", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition/Equatable-Implementations", "url": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarposition/equatable-implementations", "title": "Equatable Implementations", "type": "topic"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition/!=(_:_:)": {"title": "!=(_:_:)", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition/!=(_:_:)", "url": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarposition/!=(_:_:)", "abstract": [], "type": "topic", "role": "symbol", "fragments": [{"kind": "keyword", "text": "static"}, {"text": " ", "kind": "text"}, {"text": "func", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "!=", "kind": "identifier"}, {"text": " ", "kind": "text"}, {"text": "(", "kind": "text"}, {"text": "Self", "kind": "typeIdentifier"}, {"kind": "text", "text": ", "}, {"text": "Self", "kind": "typeIdentifier"}, {"text": ") -> ", "kind": "text"}, {"text": "Bool", "preciseIdentifier": "s:Sb", "kind": "typeIdentifier"}], "kind": "symbol"}}}