{"hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition/RawRepresentable-Implementations"]]}, "primaryContentSections": [{"declarations": [{"platforms": ["iOS"], "languages": ["swift"], "tokens": [{"kind": "keyword", "text": "var"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "hashValue"}, {"text": ": ", "kind": "text"}, {"text": "Int", "preciseIdentifier": "s:<PERSON>", "kind": "typeIdentifier"}, {"kind": "text", "text": " { "}, {"kind": "keyword", "text": "get"}, {"text": " }", "kind": "text"}]}], "kind": "declarations"}], "schemaVersion": {"minor": 3, "patch": 0, "major": 0}, "variants": [{"paths": ["/documentation/jdstatusbarnotification/statusbarnotificationprogressbarposition/hashvalue"], "traits": [{"interfaceLanguage": "swift"}]}], "sections": [], "identifier": {"url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition/hashValue", "interfaceLanguage": "swift"}, "kind": "symbol", "abstract": [{"type": "text", "text": "Inherited from "}, {"code": "RawRepresentable.hashValue", "type": "codeVoice"}, {"text": ".", "type": "text"}], "metadata": {"fragments": [{"kind": "keyword", "text": "var"}, {"kind": "text", "text": " "}, {"text": "hashValue", "kind": "identifier"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "text": "Int", "preciseIdentifier": "s:<PERSON>"}], "conformance": {"availabilityPrefix": [{"type": "text", "text": "Available when"}], "constraints": [{"code": "Self", "type": "codeVoice"}, {"type": "text", "text": " conforms to "}, {"type": "codeVoice", "code": "<PERSON><PERSON><PERSON>"}, {"type": "text", "text": " and "}, {"type": "codeVoice", "code": "RawValue"}, {"text": " conforms to ", "type": "text"}, {"type": "codeVoice", "code": "<PERSON><PERSON><PERSON>"}, {"type": "text", "text": "."}], "conformancePrefix": [{"type": "text", "text": "Conforms when"}]}, "modules": [{"name": "JDStatusBarNotification", "relatedModules": ["Swift"]}], "extendedModule": "Swift", "externalID": "s:SYsSHRzSH8RawValueSYRpzrlE04hashB0Sivp::SYNTHESIZED::c:@M@JDStatusBarNotification@E@JDStatusBarNotificationProgressBarPosition", "roleHeading": "Instance Property", "role": "symbol", "title": "hashValue", "symbolKind": "property"}, "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle", "navigatorTitle": [{"text": "StatusBarNotificationStyle", "kind": "identifier"}], "role": "symbol", "type": "topic", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "title": "StatusBarNotificationStyle", "abstract": [{"text": "A Style defines the appearance of a notification.", "type": "text"}], "fragments": [{"kind": "keyword", "text": "class"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationStyle", "kind": "identifier"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition": {"abstract": [{"type": "text", "text": "Defines the position of the progress bar, when used."}], "type": "topic", "role": "symbol", "fragments": [{"kind": "keyword", "text": "enum"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "StatusBarNotificationProgressBarPosition"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarposition", "kind": "symbol", "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationProgressBarPosition"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition", "title": "StatusBarNotificationProgressBarPosition"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition/RawRepresentable-Implementations": {"abstract": [], "type": "topic", "role": "collectionGroup", "title": "RawRepresentable Implementations", "url": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarposition/rawrepresentable-implementations", "kind": "article", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition/RawRepresentable-Implementations"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition/hashValue": {"title": "hashValue", "url": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarposition/hashvalue", "kind": "symbol", "type": "topic", "role": "symbol", "conformance": {"constraints": [{"code": "Self", "type": "codeVoice"}, {"type": "text", "text": " conforms to "}, {"code": "<PERSON><PERSON><PERSON>", "type": "codeVoice"}, {"text": " and ", "type": "text"}, {"code": "RawValue", "type": "codeVoice"}, {"type": "text", "text": " conforms to "}, {"code": "<PERSON><PERSON><PERSON>", "type": "codeVoice"}, {"type": "text", "text": "."}], "conformancePrefix": [{"type": "text", "text": "Conforms when"}], "availabilityPrefix": [{"type": "text", "text": "Available when"}]}, "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition/hashValue", "abstract": [], "fragments": [{"text": "var", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "hashValue"}, {"text": ": ", "kind": "text"}, {"text": "Int", "kind": "typeIdentifier", "preciseIdentifier": "s:<PERSON>"}]}}}