{"kind": "symbol", "abstract": [{"text": "The progress bar will be at the bottom of the notification content. This is the default.", "type": "text"}], "variants": [{"paths": ["/documentation/jdstatusbarnotification/statusbarnotificationprogressbarposition/bottom"], "traits": [{"interfaceLanguage": "swift"}]}], "primaryContentSections": [{"kind": "declarations", "declarations": [{"platforms": ["iOS"], "tokens": [{"kind": "keyword", "text": "case"}, {"text": " ", "kind": "text"}, {"text": "bottom", "kind": "identifier"}], "languages": ["swift"]}]}], "sections": [], "schemaVersion": {"major": 0, "minor": 3, "patch": 0}, "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition"]]}, "identifier": {"url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition/bottom", "interfaceLanguage": "swift"}, "metadata": {"title": "StatusBarNotificationProgressBarPosition.bottom", "externalID": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationProgressBarPosition@JDStatusBarNotificationProgressBarPositionBottom", "modules": [{"name": "JDStatusBarNotification"}], "fragments": [{"kind": "keyword", "text": "case"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "bottom"}], "role": "symbol", "symbolKind": "case", "roleHeading": "Case"}, "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition/bottom": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition/bottom", "kind": "symbol", "title": "StatusBarNotificationProgressBarPosition.bottom", "url": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarposition/bottom", "type": "topic", "abstract": [{"text": "The progress bar will be at the bottom of the notification content. This is the default.", "type": "text"}], "fragments": [{"text": "case", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "bottom"}], "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition": {"abstract": [{"type": "text", "text": "Defines the position of the progress bar, when used."}], "type": "topic", "role": "symbol", "fragments": [{"kind": "keyword", "text": "enum"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "StatusBarNotificationProgressBarPosition"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarposition", "kind": "symbol", "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationProgressBarPosition"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition", "title": "StatusBarNotificationProgressBarPosition"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle", "navigatorTitle": [{"text": "StatusBarNotificationStyle", "kind": "identifier"}], "role": "symbol", "type": "topic", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "title": "StatusBarNotificationStyle", "abstract": [{"text": "A Style defines the appearance of a notification.", "type": "text"}], "fragments": [{"kind": "keyword", "text": "class"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationStyle", "kind": "identifier"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}}}