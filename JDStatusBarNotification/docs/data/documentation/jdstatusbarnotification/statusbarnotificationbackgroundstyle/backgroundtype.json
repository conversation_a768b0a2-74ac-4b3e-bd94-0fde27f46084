{"metadata": {"externalID": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationBackgroundStyle(py)backgroundType", "title": "backgroundType", "symbolKind": "property", "modules": [{"name": "JDStatusBarNotification"}], "roleHeading": "Instance Property", "fragments": [{"text": "var", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "backgroundType"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationBackgroundType", "kind": "typeIdentifier", "text": "StatusBarNotificationBackgroundType"}], "role": "symbol"}, "variants": [{"traits": [{"interfaceLanguage": "swift"}], "paths": ["/documentation/jdstatusbarnotification/statusbarnotificationbackgroundstyle/backgroundtype"]}], "schemaVersion": {"major": 0, "minor": 3, "patch": 0}, "kind": "symbol", "identifier": {"interfaceLanguage": "swift", "url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundStyle/backgroundType"}, "sections": [], "abstract": [{"text": "The background type. De<PERSON><PERSON> is ", "type": "text"}, {"isActive": true, "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/pill", "type": "reference"}], "primaryContentSections": [{"declarations": [{"platforms": ["iOS"], "tokens": [{"kind": "attribute", "text": "@objc"}, {"text": " ", "kind": "text"}, {"text": "var", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "backgroundType", "kind": "identifier"}, {"text": ": ", "kind": "text"}, {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType", "text": "StatusBarNotificationBackgroundType", "kind": "typeIdentifier", "preciseIdentifier": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationBackgroundType"}], "languages": ["swift"]}], "kind": "declarations"}], "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundStyle"]]}, "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/pill": {"title": "StatusBarNotificationBackgroundType.pill", "url": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundtype/pill", "abstract": [{"type": "text", "text": "The background is a floating pill around the text. The pill size and appearance can be customized. This is the default."}], "kind": "symbol", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/pill", "role": "symbol", "fragments": [{"kind": "keyword", "text": "case"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "pill"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundStyle/backgroundType": {"abstract": [{"text": "The background type. De<PERSON><PERSON> is ", "type": "text"}, {"isActive": true, "type": "reference", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/pill"}], "title": "backgroundType", "url": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundstyle/backgroundtype", "fragments": [{"kind": "keyword", "text": "var"}, {"text": " ", "kind": "text"}, {"text": "backgroundType", "kind": "identifier"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "text": "StatusBarNotificationBackgroundType", "preciseIdentifier": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationBackgroundType"}], "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundStyle/backgroundType", "kind": "symbol", "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundStyle": {"fragments": [{"text": "class", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "StatusBarNotificationBackgroundStyle", "kind": "identifier"}], "kind": "symbol", "navigatorTitle": [{"text": "StatusBarNotificationBackgroundStyle", "kind": "identifier"}], "role": "symbol", "url": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundstyle", "title": "StatusBarNotificationBackgroundStyle", "type": "topic", "abstract": [{"type": "text", "text": "Defines the appearance of the notification background."}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundStyle"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType", "fragments": [{"kind": "keyword", "text": "enum"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationBackgroundType", "kind": "identifier"}], "title": "StatusBarNotificationBackgroundType", "abstract": [{"text": "Defines the appearance of the notification background.", "type": "text"}], "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationBackgroundType"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundtype", "type": "topic", "kind": "symbol", "role": "symbol"}}}