{"seeAlsoSections": [{"title": "Style Enumerations", "generated": true, "identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment"]}], "topicSections": [{"identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition/bottom", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition/center", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition/top"], "title": "Enumeration Cases"}, {"title": "Initializers", "identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition/init(rawValue:)"]}, {"title": "Default Implementations", "identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition/Equatable-Implementations", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition/RawRepresentable-Implementations"], "generated": true}], "schemaVersion": {"patch": 0, "minor": 3, "major": 0}, "kind": "symbol", "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle"]]}, "abstract": [{"text": "Defines the position of the progress bar, when used.", "type": "text"}], "variants": [{"paths": ["/documentation/jdstatusbarnotification/statusbarnotificationprogressbarposition"], "traits": [{"interfaceLanguage": "swift"}]}], "sections": [], "metadata": {"modules": [{"name": "JDStatusBarNotification"}], "externalID": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationProgressBarPosition", "title": "StatusBarNotificationProgressBarPosition", "fragments": [{"kind": "keyword", "text": "enum"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "StatusBarNotificationProgressBarPosition"}], "role": "symbol", "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationProgressBarPosition"}], "roleHeading": "Enumeration", "symbolKind": "enum"}, "identifier": {"url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition", "interfaceLanguage": "swift"}, "relationshipsSections": [{"title": "Conforms To", "kind": "relationships", "type": "conformsTo", "identifiers": ["doc://calimarkus.JDStatusBarNotification/SQ", "doc://calimarkus.JDStatusBarNotification/SH", "doc://calimarkus.JDStatusBarNotification/SY"]}], "primaryContentSections": [{"kind": "declarations", "declarations": [{"platforms": ["iOS"], "tokens": [{"text": "@objc", "kind": "attribute"}, {"text": "(JDStatusBarNotificationProgressBarPosition) ", "kind": "text"}, {"text": "enum", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationProgressBarPosition", "kind": "identifier"}], "languages": ["swift"]}]}], "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition/Equatable-Implementations": {"kind": "article", "abstract": [], "role": "collectionGroup", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition/Equatable-Implementations", "url": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarposition/equatable-implementations", "title": "Equatable Implementations", "type": "topic"}, "doc://calimarkus.JDStatusBarNotification/SH": {"type": "unresolvable", "title": "<PERSON><PERSON>", "identifier": "doc://calimarkus.JDStatusBarNotification/SH"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle": {"kind": "symbol", "abstract": [{"type": "text", "text": "Defines which "}, {"code": "UIStatusBarStyle", "type": "codeVoice"}, {"text": " should be used during presentation.", "type": "text"}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle", "url": "/documentation/jdstatusbarnotification/statusbarnotificationsystembarstyle", "fragments": [{"kind": "keyword", "text": "enum"}, {"text": " ", "kind": "text"}, {"text": "StatusBarNotificationSystemBarStyle", "kind": "identifier"}], "navigatorTitle": [{"text": "StatusBarNotificationSystemBarStyle", "kind": "identifier"}], "title": "StatusBarNotificationSystemBarStyle", "type": "topic"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle", "navigatorTitle": [{"text": "StatusBarNotificationStyle", "kind": "identifier"}], "role": "symbol", "type": "topic", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "title": "StatusBarNotificationStyle", "abstract": [{"text": "A Style defines the appearance of a notification.", "type": "text"}], "fragments": [{"kind": "keyword", "text": "class"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationStyle", "kind": "identifier"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition/RawRepresentable-Implementations": {"abstract": [], "type": "topic", "role": "collectionGroup", "title": "RawRepresentable Implementations", "url": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarposition/rawrepresentable-implementations", "kind": "article", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition/RawRepresentable-Implementations"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition/bottom": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition/bottom", "kind": "symbol", "title": "StatusBarNotificationProgressBarPosition.bottom", "url": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarposition/bottom", "type": "topic", "abstract": [{"text": "The progress bar will be at the bottom of the notification content. This is the default.", "type": "text"}], "fragments": [{"text": "case", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "bottom"}], "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType": {"abstract": [{"text": "Defines the animation used during presentation and dismissal of the notification.", "type": "text"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationanimationtype", "fragments": [{"kind": "keyword", "text": "enum"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationAnimationType", "kind": "identifier"}], "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationAnimationType"}], "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType", "title": "StatusBarNotificationAnimationType", "role": "symbol", "type": "topic"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition/top": {"kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition/top", "type": "topic", "abstract": [{"type": "text", "text": "The progress bar will be at the top of the notification content."}], "role": "symbol", "fragments": [{"text": "case", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "top", "kind": "identifier"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarposition/top", "title": "StatusBarNotificationProgressBarPosition.top"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition": {"abstract": [{"type": "text", "text": "Defines the position of the progress bar, when used."}], "type": "topic", "role": "symbol", "fragments": [{"kind": "keyword", "text": "enum"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "StatusBarNotificationProgressBarPosition"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarposition", "kind": "symbol", "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationProgressBarPosition"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition", "title": "StatusBarNotificationProgressBarPosition"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition/center": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarposition/center", "abstract": [{"text": "The progress bar will be at the center of the notification content.", "type": "text"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition/center", "fragments": [{"kind": "keyword", "text": "case"}, {"text": " ", "kind": "text"}, {"text": "center", "kind": "identifier"}], "type": "topic", "title": "StatusBarNotificationProgressBarPosition.center", "role": "symbol", "kind": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType", "fragments": [{"kind": "keyword", "text": "enum"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationBackgroundType", "kind": "identifier"}], "title": "StatusBarNotificationBackgroundType", "abstract": [{"text": "Defines the appearance of the notification background.", "type": "text"}], "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationBackgroundType"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundtype", "type": "topic", "kind": "symbol", "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment", "role": "symbol", "kind": "symbol", "fragments": [{"kind": "keyword", "text": "enum"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "StatusBarNotificationLeftViewAlignment"}], "abstract": [{"text": "Defines the appearance of a left-view, if set. This includes the activity indicator.", "type": "text"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewalignment", "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationLeftViewAlignment"}], "type": "topic", "title": "StatusBarNotificationLeftViewAlignment"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition/init(rawValue:)": {"role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition/init(rawValue:)", "abstract": [], "title": "init(rawValue:)", "url": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarposition/init(rawvalue:)", "kind": "symbol", "fragments": [{"text": "init", "kind": "identifier"}, {"text": "?(", "kind": "text"}, {"text": "rawValue", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"text": "Int", "preciseIdentifier": "s:<PERSON>", "kind": "typeIdentifier"}, {"text": ")", "kind": "text"}], "type": "topic"}, "doc://calimarkus.JDStatusBarNotification/SY": {"type": "unresolvable", "identifier": "doc://calimarkus.JDStatusBarNotification/SY", "title": "Swift.RawRepresentable"}, "doc://calimarkus.JDStatusBarNotification/SQ": {"type": "unresolvable", "identifier": "doc://calimarkus.JDStatusBarNotification/SQ", "title": "Swift.Equatable"}}}