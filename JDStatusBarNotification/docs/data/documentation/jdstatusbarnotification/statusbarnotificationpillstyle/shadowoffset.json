{"abstract": [{"text": "The shadow offset for the pill shadow. The default is ", "type": "text"}, {"code": "(0, 2)", "type": "codeVoice"}, {"type": "text", "text": "."}], "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle"]]}, "schemaVersion": {"patch": 0, "major": 0, "minor": 3}, "metadata": {"externalID": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationPillStyle(py)shadowOffset", "role": "symbol", "fragments": [{"text": "var", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "shadowOffset", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"text": "CGSize", "preciseIdentifier": "c:@S@CGSize", "kind": "typeIdentifier"}], "platforms": [], "title": "shadowOffset", "symbolKind": "property", "roleHeading": "Instance Property", "modules": [{"name": "JDStatusBarNotification"}]}, "kind": "symbol", "identifier": {"url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle/shadowOffset", "interfaceLanguage": "swift"}, "variants": [{"paths": ["/documentation/jdstatusbarnotification/statusbarnotificationpillstyle/shadowoffset"], "traits": [{"interfaceLanguage": "swift"}]}], "primaryContentSections": [{"kind": "declarations", "declarations": [{"tokens": [{"kind": "attribute", "text": "@objc"}, {"kind": "text", "text": " "}, {"kind": "keyword", "text": "var"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "shadowOffset"}, {"text": ": ", "kind": "text"}, {"text": "CGSize", "preciseIdentifier": "c:@S@CGSize", "kind": "typeIdentifier"}, {"kind": "text", "text": " { "}, {"text": "get", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "set", "kind": "keyword"}, {"kind": "text", "text": " }"}], "languages": ["swift"], "platforms": ["iOS"]}]}], "sections": [], "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle": {"abstract": [{"text": "Defines the appearance of the pill, when using ", "type": "text"}, {"type": "reference", "isActive": true, "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/pill"}], "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationPillStyle"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle", "type": "topic", "kind": "symbol", "role": "symbol", "url": "/documentation/jdstatusbarnotification/statusbarnotificationpillstyle", "fragments": [{"kind": "keyword", "text": "class"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "StatusBarNotificationPillStyle"}], "title": "StatusBarNotificationPillStyle"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/pill": {"title": "StatusBarNotificationBackgroundType.pill", "url": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundtype/pill", "abstract": [{"type": "text", "text": "The background is a floating pill around the text. The pill size and appearance can be customized. This is the default."}], "kind": "symbol", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/pill", "role": "symbol", "fragments": [{"kind": "keyword", "text": "case"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "pill"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle/shadowOffset": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationpillstyle/shadowoffset", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle/shadowOffset", "title": "shadowOffset", "role": "symbol", "fragments": [{"text": "var", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "shadowOffset", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"text": "CGSize", "preciseIdentifier": "c:@S@CGSize", "kind": "typeIdentifier"}], "deprecated": true, "type": "topic", "abstract": [{"text": "The shadow offset for the pill shadow. The default is ", "type": "text"}, {"code": "(0, 2)", "type": "codeVoice"}, {"type": "text", "text": "."}], "kind": "symbol"}}}