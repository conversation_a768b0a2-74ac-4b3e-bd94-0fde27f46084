{"hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle"]]}, "kind": "symbol", "identifier": {"interfaceLanguage": "swift", "url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle/height"}, "variants": [{"traits": [{"interfaceLanguage": "swift"}], "paths": ["/documentation/jdstatusbarnotification/statusbarnotificationpillstyle/height"]}], "schemaVersion": {"patch": 0, "major": 0, "minor": 3}, "primaryContentSections": [{"kind": "declarations", "declarations": [{"platforms": ["iOS"], "tokens": [{"kind": "attribute", "text": "@objc"}, {"text": " ", "kind": "text"}, {"text": "var", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "height"}, {"text": ": ", "kind": "text"}, {"text": "Double", "kind": "typeIdentifier", "preciseIdentifier": "s:Sd"}], "languages": ["swift"]}]}], "sections": [], "metadata": {"symbolKind": "property", "role": "symbol", "title": "height", "modules": [{"name": "JDStatusBarNotification"}], "roleHeading": "Instance Property", "externalID": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationPillStyle(py)height", "fragments": [{"kind": "keyword", "text": "var"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "height"}, {"text": ": ", "kind": "text"}, {"preciseIdentifier": "s:Sd", "text": "Double", "kind": "typeIdentifier"}]}, "abstract": [{"type": "text", "text": "The height of the pill. De<PERSON><PERSON> is "}, {"code": "50.0", "type": "codeVoice"}, {"type": "text", "text": "."}], "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle": {"abstract": [{"text": "Defines the appearance of the pill, when using ", "type": "text"}, {"type": "reference", "isActive": true, "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/pill"}], "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationPillStyle"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle", "type": "topic", "kind": "symbol", "role": "symbol", "url": "/documentation/jdstatusbarnotification/statusbarnotificationpillstyle", "fragments": [{"kind": "keyword", "text": "class"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "StatusBarNotificationPillStyle"}], "title": "StatusBarNotificationPillStyle"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/pill": {"title": "StatusBarNotificationBackgroundType.pill", "url": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundtype/pill", "abstract": [{"type": "text", "text": "The background is a floating pill around the text. The pill size and appearance can be customized. This is the default."}], "kind": "symbol", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/pill", "role": "symbol", "fragments": [{"kind": "keyword", "text": "case"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "pill"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle/height": {"title": "height", "url": "/documentation/jdstatusbarnotification/statusbarnotificationpillstyle/height", "type": "topic", "abstract": [{"type": "text", "text": "The height of the pill. De<PERSON><PERSON> is "}, {"code": "50.0", "type": "codeVoice"}, {"type": "text", "text": "."}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle/height", "kind": "symbol", "fragments": [{"kind": "keyword", "text": "var"}, {"text": " ", "kind": "text"}, {"text": "height", "kind": "identifier"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "text": "Double", "preciseIdentifier": "s:Sd"}], "role": "symbol"}}}