{"variants": [{"traits": [{"interfaceLanguage": "swift"}], "paths": ["/documentation/jdstatusbarnotification/statusbarnotificationpillstyle/shadowoffsetxy"]}], "primaryContentSections": [{"kind": "declarations", "declarations": [{"tokens": [{"text": "@objc", "kind": "attribute"}, {"text": " ", "kind": "text"}, {"text": "var", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "shadowOffsetXY"}, {"kind": "text", "text": ": "}, {"kind": "typeIdentifier", "text": "CGPoint", "preciseIdentifier": "c:@S@CGPoint"}], "platforms": ["iOS"], "languages": ["swift"]}]}], "schemaVersion": {"minor": 3, "major": 0, "patch": 0}, "sections": [], "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle"]]}, "metadata": {"externalID": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationPillStyle(py)shadowOffsetXY", "roleHeading": "Instance Property", "symbolKind": "property", "modules": [{"name": "JDStatusBarNotification"}], "role": "symbol", "fragments": [{"kind": "keyword", "text": "var"}, {"text": " ", "kind": "text"}, {"text": "shadowOffsetXY", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "c:@S@CGPoint", "kind": "typeIdentifier", "text": "CGPoint"}], "title": "shadowOffsetXY"}, "identifier": {"url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle/shadowOffsetXY", "interfaceLanguage": "swift"}, "abstract": [{"type": "text", "text": "The shadow offset for the pill shadow. The default is "}, {"type": "codeVoice", "code": "(0, 2)"}, {"text": ".", "type": "text"}], "kind": "symbol", "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/pill": {"title": "StatusBarNotificationBackgroundType.pill", "url": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundtype/pill", "abstract": [{"type": "text", "text": "The background is a floating pill around the text. The pill size and appearance can be customized. This is the default."}], "kind": "symbol", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/pill", "role": "symbol", "fragments": [{"kind": "keyword", "text": "case"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "pill"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle": {"abstract": [{"text": "Defines the appearance of the pill, when using ", "type": "text"}, {"type": "reference", "isActive": true, "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/pill"}], "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationPillStyle"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle", "type": "topic", "kind": "symbol", "role": "symbol", "url": "/documentation/jdstatusbarnotification/statusbarnotificationpillstyle", "fragments": [{"kind": "keyword", "text": "class"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "StatusBarNotificationPillStyle"}], "title": "StatusBarNotificationPillStyle"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle/shadowOffsetXY": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle/shadowOffsetXY", "type": "topic", "kind": "symbol", "abstract": [{"text": "The shadow offset for the pill shadow. The default is ", "type": "text"}, {"type": "codeVoice", "code": "(0, 2)"}, {"type": "text", "text": "."}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationpillstyle/shadowoffsetxy", "role": "symbol", "fragments": [{"kind": "keyword", "text": "var"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "shadowOffsetXY"}, {"text": ": ", "kind": "text"}, {"text": "CGPoint", "kind": "typeIdentifier", "preciseIdentifier": "c:@S@CGPoint"}], "title": "shadowOffsetXY"}}}