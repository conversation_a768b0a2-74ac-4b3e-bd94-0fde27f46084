{"schemaVersion": {"major": 0, "minor": 3, "patch": 0}, "abstract": [{"text": "The minimum with of the pill. <PERSON><PERSON><PERSON> is ", "type": "text"}, {"type": "codeVoice", "code": "200.0"}, {"type": "text", "text": "."}, {"type": "text", "text": " "}, {"type": "text", "text": "If this is lower than the pill height, the pill height is used as minimum width."}], "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle"]]}, "kind": "symbol", "sections": [], "metadata": {"title": "minimumWidth", "externalID": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationPillStyle(py)minimumWidth", "roleHeading": "Instance Property", "modules": [{"name": "JDStatusBarNotification"}], "role": "symbol", "fragments": [{"text": "var", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "minimumWidth"}, {"text": ": ", "kind": "text"}, {"text": "Double", "kind": "typeIdentifier", "preciseIdentifier": "s:Sd"}], "symbolKind": "property"}, "identifier": {"interfaceLanguage": "swift", "url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle/minimumWidth"}, "primaryContentSections": [{"kind": "declarations", "declarations": [{"tokens": [{"text": "@objc", "kind": "attribute"}, {"kind": "text", "text": " "}, {"kind": "keyword", "text": "var"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "minimumWidth"}, {"text": ": ", "kind": "text"}, {"preciseIdentifier": "s:Sd", "text": "Double", "kind": "typeIdentifier"}], "languages": ["swift"], "platforms": ["iOS"]}]}], "variants": [{"traits": [{"interfaceLanguage": "swift"}], "paths": ["/documentation/jdstatusbarnotification/statusbarnotificationpillstyle/minimumwidth"]}], "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle": {"type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle", "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationPillStyle"}], "kind": "symbol", "abstract": [{"type": "text", "text": "Defines the appearance of the pill, when using "}, {"type": "reference", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/pill", "isActive": true}], "role": "symbol", "fragments": [{"kind": "keyword", "text": "class"}, {"text": " ", "kind": "text"}, {"text": "StatusBarNotificationPillStyle", "kind": "identifier"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationpillstyle", "title": "StatusBarNotificationPillStyle"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle/minimumWidth": {"abstract": [{"type": "text", "text": "The minimum with of the pill. <PERSON><PERSON><PERSON> is "}, {"type": "codeVoice", "code": "200.0"}, {"type": "text", "text": "."}, {"type": "text", "text": " "}, {"type": "text", "text": "If this is lower than the pill height, the pill height is used as minimum width."}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationpillstyle/minimumwidth", "fragments": [{"kind": "keyword", "text": "var"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "minimumWidth"}, {"kind": "text", "text": ": "}, {"text": "Double", "preciseIdentifier": "s:Sd", "kind": "typeIdentifier"}], "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle/minimumWidth", "title": "minimumWidth", "role": "symbol", "type": "topic"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/pill": {"abstract": [{"text": "The background is a floating pill around the text. The pill size and appearance can be customized. This is the default.", "type": "text"}], "role": "symbol", "type": "topic", "url": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundtype/pill", "fragments": [{"text": "case", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "pill", "kind": "identifier"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/pill", "kind": "symbol", "title": "StatusBarNotificationBackgroundType.pill"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}}}