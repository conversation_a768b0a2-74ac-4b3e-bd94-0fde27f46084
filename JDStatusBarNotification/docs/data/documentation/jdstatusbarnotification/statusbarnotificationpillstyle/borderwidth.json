{"variants": [{"traits": [{"interfaceLanguage": "swift"}], "paths": ["/documentation/jdstatusbarnotification/statusbarnotificationpillstyle/borderwidth"]}], "identifier": {"interfaceLanguage": "swift", "url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle/borderWidth"}, "sections": [], "primaryContentSections": [{"kind": "declarations", "declarations": [{"platforms": ["iOS"], "tokens": [{"text": "@objc", "kind": "attribute"}, {"text": " ", "kind": "text"}, {"kind": "keyword", "text": "var"}, {"text": " ", "kind": "text"}, {"text": "borderWidth", "kind": "identifier"}, {"text": ": ", "kind": "text"}, {"preciseIdentifier": "s:Sd", "kind": "typeIdentifier", "text": "Double"}], "languages": ["swift"]}]}], "metadata": {"symbolKind": "property", "roleHeading": "Instance Property", "externalID": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationPillStyle(py)borderWidth", "modules": [{"name": "JDStatusBarNotification"}], "role": "symbol", "title": "borderWidth", "fragments": [{"text": "var", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "borderWidth", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"text": "Double", "preciseIdentifier": "s:Sd", "kind": "typeIdentifier"}]}, "kind": "symbol", "abstract": [{"type": "text", "text": "The width of the pill border. The default is "}, {"code": "2.0", "type": "codeVoice"}, {"type": "text", "text": "."}], "schemaVersion": {"minor": 3, "patch": 0, "major": 0}, "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle"]]}, "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/pill": {"title": "StatusBarNotificationBackgroundType.pill", "url": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundtype/pill", "abstract": [{"type": "text", "text": "The background is a floating pill around the text. The pill size and appearance can be customized. This is the default."}], "kind": "symbol", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/pill", "role": "symbol", "fragments": [{"kind": "keyword", "text": "case"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "pill"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle/borderWidth": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationpillstyle/borderwidth", "abstract": [{"type": "text", "text": "The width of the pill border. The default is "}, {"code": "2.0", "type": "codeVoice"}, {"type": "text", "text": "."}], "title": "borderWidth", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle/borderWidth", "role": "symbol", "kind": "symbol", "fragments": [{"text": "var", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "borderWidth", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"text": "Double", "kind": "typeIdentifier", "preciseIdentifier": "s:Sd"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle": {"abstract": [{"text": "Defines the appearance of the pill, when using ", "type": "text"}, {"type": "reference", "isActive": true, "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/pill"}], "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationPillStyle"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle", "type": "topic", "kind": "symbol", "role": "symbol", "url": "/documentation/jdstatusbarnotification/statusbarnotificationpillstyle", "fragments": [{"kind": "keyword", "text": "class"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "StatusBarNotificationPillStyle"}], "title": "StatusBarNotificationPillStyle"}}}