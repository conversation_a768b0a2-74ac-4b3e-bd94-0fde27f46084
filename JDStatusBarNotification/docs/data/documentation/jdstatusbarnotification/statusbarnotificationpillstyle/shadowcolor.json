{"sections": [], "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle"]]}, "identifier": {"interfaceLanguage": "swift", "url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle/shadowColor"}, "abstract": [{"type": "text", "text": "The shadow color of the pill shadow. The default is "}, {"type": "codeVoice", "code": "nil"}, {"type": "text", "text": ", meaning no shadow."}], "primaryContentSections": [{"declarations": [{"platforms": ["iOS"], "languages": ["swift"], "tokens": [{"text": "@objc", "kind": "attribute"}, {"text": " ", "kind": "text"}, {"kind": "keyword", "text": "var"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "shadowColor"}, {"kind": "text", "text": ": "}, {"kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIColor", "text": "UIColor"}, {"text": "?", "kind": "text"}]}], "kind": "declarations"}], "variants": [{"paths": ["/documentation/jdstatusbarnotification/statusbarnotificationpillstyle/shadowcolor"], "traits": [{"interfaceLanguage": "swift"}]}], "metadata": {"externalID": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationPillStyle(py)shadowColor", "modules": [{"name": "JDStatusBarNotification"}], "roleHeading": "Instance Property", "title": "shadowColor", "fragments": [{"kind": "keyword", "text": "var"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "shadowColor"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIColor", "text": "UIColor"}, {"text": "?", "kind": "text"}], "role": "symbol", "symbolKind": "property"}, "kind": "symbol", "schemaVersion": {"major": 0, "minor": 3, "patch": 0}, "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/pill": {"title": "StatusBarNotificationBackgroundType.pill", "url": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundtype/pill", "abstract": [{"type": "text", "text": "The background is a floating pill around the text. The pill size and appearance can be customized. This is the default."}], "kind": "symbol", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/pill", "role": "symbol", "fragments": [{"kind": "keyword", "text": "case"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "pill"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle": {"abstract": [{"text": "Defines the appearance of the pill, when using ", "type": "text"}, {"type": "reference", "isActive": true, "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/pill"}], "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationPillStyle"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle", "type": "topic", "kind": "symbol", "role": "symbol", "url": "/documentation/jdstatusbarnotification/statusbarnotificationpillstyle", "fragments": [{"kind": "keyword", "text": "class"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "StatusBarNotificationPillStyle"}], "title": "StatusBarNotificationPillStyle"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle/shadowColor": {"abstract": [{"type": "text", "text": "The shadow color of the pill shadow. The default is "}, {"type": "codeVoice", "code": "nil"}, {"type": "text", "text": ", meaning no shadow."}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle/shadowColor", "kind": "symbol", "title": "shadowColor", "role": "symbol", "type": "topic", "url": "/documentation/jdstatusbarnotification/statusbarnotificationpillstyle/shadowcolor", "fragments": [{"text": "var", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "shadowColor", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIColor", "text": "UIColor"}, {"text": "?", "kind": "text"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}}}