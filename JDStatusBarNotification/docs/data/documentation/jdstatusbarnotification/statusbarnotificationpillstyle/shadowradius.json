{"variants": [{"traits": [{"interfaceLanguage": "swift"}], "paths": ["/documentation/jdstatusbarnotification/statusbarnotificationpillstyle/shadowradius"]}], "identifier": {"interfaceLanguage": "swift", "url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle/shadowRadius"}, "sections": [], "primaryContentSections": [{"kind": "declarations", "declarations": [{"tokens": [{"text": "@objc", "kind": "attribute"}, {"text": " ", "kind": "text"}, {"text": "var", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "shadowRadius", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"kind": "typeIdentifier", "text": "Double", "preciseIdentifier": "s:Sd"}], "platforms": ["iOS"], "languages": ["swift"]}]}], "metadata": {"fragments": [{"text": "var", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "shadowRadius"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "s:Sd", "text": "Double"}], "modules": [{"name": "JDStatusBarNotification"}], "symbolKind": "property", "roleHeading": "Instance Property", "title": "shadowRadius", "role": "symbol", "externalID": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationPillStyle(py)shadowRadius"}, "kind": "symbol", "abstract": [{"text": "The shadow radius of the pill shadow. The default is ", "type": "text"}, {"code": "4.0", "type": "codeVoice"}, {"text": ".", "type": "text"}], "schemaVersion": {"patch": 0, "major": 0, "minor": 3}, "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle"]]}, "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle": {"abstract": [{"text": "Defines the appearance of the pill, when using ", "type": "text"}, {"type": "reference", "isActive": true, "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/pill"}], "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationPillStyle"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle", "type": "topic", "kind": "symbol", "role": "symbol", "url": "/documentation/jdstatusbarnotification/statusbarnotificationpillstyle", "fragments": [{"kind": "keyword", "text": "class"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "StatusBarNotificationPillStyle"}], "title": "StatusBarNotificationPillStyle"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle/shadowRadius": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationpillstyle/shadowradius", "fragments": [{"kind": "keyword", "text": "var"}, {"kind": "text", "text": " "}, {"text": "shadowRadius", "kind": "identifier"}, {"text": ": ", "kind": "text"}, {"text": "Double", "kind": "typeIdentifier", "preciseIdentifier": "s:Sd"}], "role": "symbol", "type": "topic", "kind": "symbol", "abstract": [{"text": "The shadow radius of the pill shadow. The default is ", "type": "text"}, {"type": "codeVoice", "code": "4.0"}, {"type": "text", "text": "."}], "title": "shadowRadius", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle/shadowRadius"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/pill": {"title": "StatusBarNotificationBackgroundType.pill", "url": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundtype/pill", "abstract": [{"type": "text", "text": "The background is a floating pill around the text. The pill size and appearance can be customized. This is the default."}], "kind": "symbol", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/pill", "role": "symbol", "fragments": [{"kind": "keyword", "text": "case"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "pill"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}}}