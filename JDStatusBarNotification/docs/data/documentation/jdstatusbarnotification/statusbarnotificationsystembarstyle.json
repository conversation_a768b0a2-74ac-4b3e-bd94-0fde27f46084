{"relationshipsSections": [{"kind": "relationships", "title": "Conforms To", "identifiers": ["doc://calimarkus.JDStatusBarNotification/SQ", "doc://calimarkus.JDStatusBarNotification/SH", "doc://calimarkus.JDStatusBarNotification/SY"], "type": "conformsTo"}], "sections": [], "topicSections": [{"title": "Enumeration Cases", "identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle/darkContent", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle/defaultStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle/lightContent"]}, {"identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle/init(rawValue:)"], "title": "Initializers"}, {"title": "Default Implementations", "identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle/Equatable-Implementations", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle/RawRepresentable-Implementations"], "generated": true}], "abstract": [{"type": "text", "text": "Defines which "}, {"type": "codeVoice", "code": "UIStatusBarStyle"}, {"type": "text", "text": " should be used during presentation."}], "primaryContentSections": [{"declarations": [{"tokens": [{"kind": "attribute", "text": "@objc"}, {"kind": "text", "text": "(JDStatusBarNotificationSystemBarStyle) "}, {"kind": "keyword", "text": "enum"}, {"text": " ", "kind": "text"}, {"text": "StatusBarNotificationSystemBarStyle", "kind": "identifier"}], "platforms": ["iOS"], "languages": ["swift"]}], "kind": "declarations"}, {"kind": "content", "content": [{"level": 2, "anchor": "overview", "text": "Overview", "type": "heading"}, {"type": "paragraph", "inlineContent": [{"type": "text", "text": "Note that if you use "}, {"type": "reference", "isActive": true, "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/pill"}, {"text": ", this is ignored.", "type": "text"}, {"type": "text", "text": " "}, {"type": "text", "text": "The default is "}, {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle/defaultStyle", "type": "reference", "isActive": true}, {"text": ".", "type": "text"}]}]}], "seeAlsoSections": [{"generated": true, "identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition"], "title": "Style Enumerations"}], "identifier": {"url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle", "interfaceLanguage": "swift"}, "metadata": {"symbolKind": "enum", "title": "StatusBarNotificationSystemBarStyle", "externalID": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationSystemBarStyle", "navigatorTitle": [{"text": "StatusBarNotificationSystemBarStyle", "kind": "identifier"}], "modules": [{"name": "JDStatusBarNotification"}], "role": "symbol", "roleHeading": "Enumeration", "fragments": [{"kind": "keyword", "text": "enum"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "StatusBarNotificationSystemBarStyle"}]}, "schemaVersion": {"minor": 3, "major": 0, "patch": 0}, "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle"]]}, "variants": [{"traits": [{"interfaceLanguage": "swift"}], "paths": ["/documentation/jdstatusbarnotification/statusbarnotificationsystembarstyle"]}], "kind": "symbol", "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition": {"abstract": [{"type": "text", "text": "Defines the position of the progress bar, when used."}], "type": "topic", "role": "symbol", "fragments": [{"kind": "keyword", "text": "enum"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "StatusBarNotificationProgressBarPosition"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarposition", "kind": "symbol", "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationProgressBarPosition"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition", "title": "StatusBarNotificationProgressBarPosition"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle/defaultStyle": {"type": "topic", "abstract": [{"text": "Matches the current viewController / window.", "type": "text"}], "title": "StatusBarNotificationSystemBarStyle.defaultStyle", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle/defaultStyle", "kind": "symbol", "url": "/documentation/jdstatusbarnotification/statusbarnotificationsystembarstyle/defaultstyle", "fragments": [{"text": "case", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "defaultStyle", "kind": "identifier"}], "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/SQ": {"type": "unresolvable", "identifier": "doc://calimarkus.JDStatusBarNotification/SQ", "title": "Swift.Equatable"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment", "role": "symbol", "kind": "symbol", "fragments": [{"kind": "keyword", "text": "enum"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "StatusBarNotificationLeftViewAlignment"}], "abstract": [{"text": "Defines the appearance of a left-view, if set. This includes the activity indicator.", "type": "text"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewalignment", "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationLeftViewAlignment"}], "type": "topic", "title": "StatusBarNotificationLeftViewAlignment"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle", "navigatorTitle": [{"text": "StatusBarNotificationStyle", "kind": "identifier"}], "role": "symbol", "type": "topic", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "title": "StatusBarNotificationStyle", "abstract": [{"text": "A Style defines the appearance of a notification.", "type": "text"}], "fragments": [{"kind": "keyword", "text": "class"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationStyle", "kind": "identifier"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType": {"abstract": [{"text": "Defines the animation used during presentation and dismissal of the notification.", "type": "text"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationanimationtype", "fragments": [{"kind": "keyword", "text": "enum"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationAnimationType", "kind": "identifier"}], "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationAnimationType"}], "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType", "title": "StatusBarNotificationAnimationType", "role": "symbol", "type": "topic"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle/init(rawValue:)": {"abstract": [], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle/init(rawValue:)", "kind": "symbol", "title": "init(rawValue:)", "role": "symbol", "type": "topic", "url": "/documentation/jdstatusbarnotification/statusbarnotificationsystembarstyle/init(rawvalue:)", "fragments": [{"kind": "identifier", "text": "init"}, {"text": "?(", "kind": "text"}, {"kind": "externalParam", "text": "rawValue"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "text": "Int", "preciseIdentifier": "s:<PERSON>"}, {"kind": "text", "text": ")"}]}, "doc://calimarkus.JDStatusBarNotification/SH": {"type": "unresolvable", "identifier": "doc://calimarkus.JDStatusBarNotification/SH", "title": "<PERSON><PERSON>"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType", "fragments": [{"kind": "keyword", "text": "enum"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationBackgroundType", "kind": "identifier"}], "title": "StatusBarNotificationBackgroundType", "abstract": [{"text": "Defines the appearance of the notification background.", "type": "text"}], "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationBackgroundType"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundtype", "type": "topic", "kind": "symbol", "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle/lightContent": {"type": "topic", "title": "StatusBarNotificationSystemBarStyle.lightContent", "kind": "symbol", "role": "symbol", "abstract": [{"type": "text", "text": "Forces light status bar contents ("}, {"code": "UIStatusBarStyleLightContent", "type": "codeVoice"}, {"text": ")", "type": "text"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationsystembarstyle/lightcontent", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle/lightContent", "fragments": [{"kind": "keyword", "text": "case"}, {"kind": "text", "text": " "}, {"text": "lightContent", "kind": "identifier"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle": {"abstract": [{"text": "Defines which ", "type": "text"}, {"code": "UIStatusBarStyle", "type": "codeVoice"}, {"type": "text", "text": " should be used during presentation."}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationsystembarstyle", "fragments": [{"kind": "keyword", "text": "enum"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "StatusBarNotificationSystemBarStyle"}], "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationSystemBarStyle"}], "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle", "title": "StatusBarNotificationSystemBarStyle", "role": "symbol", "type": "topic"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle/darkContent": {"type": "topic", "title": "StatusBarNotificationSystemBarStyle.darkContent", "kind": "symbol", "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle/darkContent", "url": "/documentation/jdstatusbarnotification/statusbarnotificationsystembarstyle/darkcontent", "abstract": [{"text": "Forces dark status bar contents (", "type": "text"}, {"code": "UIStatusBarStyleDarkContent", "type": "codeVoice"}, {"type": "text", "text": ")"}], "fragments": [{"text": "case", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "darkContent"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/pill": {"title": "StatusBarNotificationBackgroundType.pill", "url": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundtype/pill", "abstract": [{"type": "text", "text": "The background is a floating pill around the text. The pill size and appearance can be customized. This is the default."}], "kind": "symbol", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/pill", "role": "symbol", "fragments": [{"kind": "keyword", "text": "case"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "pill"}]}, "doc://calimarkus.JDStatusBarNotification/SY": {"title": "Swift.RawRepresentable", "identifier": "doc://calimarkus.JDStatusBarNotification/SY", "type": "unresolvable"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle/Equatable-Implementations": {"type": "topic", "title": "Equatable Implementations", "kind": "article", "role": "collectionGroup", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle/Equatable-Implementations", "url": "/documentation/jdstatusbarnotification/statusbarnotificationsystembarstyle/equatable-implementations", "abstract": []}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle/RawRepresentable-Implementations": {"abstract": [], "role": "collectionGroup", "url": "/documentation/jdstatusbarnotification/statusbarnotificationsystembarstyle/rawrepresentable-implementations", "title": "RawRepresentable Implementations", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle/RawRepresentable-Implementations", "kind": "article"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}}}