{"variants": [{"traits": [{"interfaceLanguage": "swift"}], "paths": ["/documentation/jdstatusbarnotification/statusbarnotificationstyle"]}], "primaryContentSections": [{"kind": "declarations", "declarations": [{"languages": ["swift"], "platforms": ["iOS"], "tokens": [{"kind": "attribute", "text": "@objc"}, {"text": "(JDStatusBarNotificationStyle) ", "kind": "text"}, {"kind": "keyword", "text": "class"}, {"text": " ", "kind": "text"}, {"text": "StatusBarNotificationStyle", "kind": "identifier"}]}]}], "identifier": {"url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "interfaceLanguage": "swift"}, "relationshipsSections": [{"kind": "relationships", "identifiers": ["doc://calimarkus.JDStatusBarNotification/objc(cs)NSObject"], "title": "Inherits From", "type": "inheritsFrom"}, {"identifiers": ["doc://calimarkus.JDStatusBarNotification/objc(pl)NSObject", "doc://calimarkus.JDStatusBarNotification/s7CVarArgP", "doc://calimarkus.JDStatusBarNotification/s28CustomDebugStringConvertibleP", "doc://calimarkus.JDStatusBarNotification/s23CustomStringConvertibleP", "doc://calimarkus.JDStatusBarNotification/SQ", "doc://calimarkus.JDStatusBarNotification/SH"], "type": "conformsTo", "kind": "relationships", "title": "Conforms To"}], "schemaVersion": {"major": 0, "minor": 3, "patch": 0}, "metadata": {"role": "symbol", "fragments": [{"kind": "keyword", "text": "class"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "StatusBarNotificationStyle"}], "symbolKind": "class", "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationStyle"}], "title": "StatusBarNotificationStyle", "modules": [{"name": "JDStatusBarNotification"}], "roleHeading": "Class", "externalID": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationStyle"}, "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification"]]}, "abstract": [{"type": "text", "text": "A Style defines the appearance of a notification."}], "topicSections": [{"title": "Notification Bar Behavior", "identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/animationType", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/canDismissDuringUserInteraction", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/canSwipeToDismiss", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/canTapToHold"]}, {"identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/textStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/subtitleStyle"], "title": "Styling the text"}, {"title": "Styling the background", "identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/backgroundStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/systemStatusBarStyle"]}, {"title": "Styling supplementary views", "identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/progressBarStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/leftViewStyle"]}, {"title": "Style Enumerations", "identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition"]}], "seeAlsoSections": [{"identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle"], "generated": true, "title": "Notification Style"}], "kind": "symbol", "sections": [], "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/canSwipeToDismiss": {"type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/canSwipeToDismiss", "kind": "symbol", "role": "symbol", "fragments": [{"kind": "keyword", "text": "var"}, {"kind": "text", "text": " "}, {"text": "canSwipeToDismiss", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"text": "Bool", "kind": "typeIdentifier", "preciseIdentifier": "s:Sb"}], "title": "canSwipeToDismiss", "abstract": [{"type": "text", "text": "Defines if the bar can be dismissed by the user swiping up. Default is "}, {"code": "true", "type": "codeVoice"}, {"type": "text", "text": "."}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle/canswipetodismiss"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/animationType": {"type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/animationType", "kind": "symbol", "role": "symbol", "fragments": [{"kind": "keyword", "text": "var"}, {"kind": "text", "text": " "}, {"text": "animationType", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"text": "StatusBarNotificationAnimationType", "kind": "typeIdentifier", "preciseIdentifier": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationAnimationType"}], "title": "animationType", "abstract": [{"type": "text", "text": "Defines the animation used during presentation and dismissal of the notification."}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle/animationtype"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle": {"type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle", "kind": "symbol", "role": "symbol", "fragments": [{"text": "class", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationProgressBarStyle", "kind": "identifier"}], "title": "StatusBarNotificationProgressBarStyle", "abstract": [{"type": "text", "text": "Defines the appearance of the progress bar."}], "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationProgressBarStyle"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarstyle"}, "doc://calimarkus.JDStatusBarNotification/SH": {"type": "unresolvable", "identifier": "doc://calimarkus.JDStatusBarNotification/SH", "title": "<PERSON><PERSON>"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/canDismissDuringUserInteraction": {"abstract": [{"type": "text", "text": "Defines if the bar is allowed to be dismissed while the user touches or pans the view."}], "role": "symbol", "url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle/candismissduringuserinteraction", "fragments": [{"text": "var", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "canDismissDuringUserInteraction"}, {"text": ": ", "kind": "text"}, {"preciseIdentifier": "s:Sb", "text": "Bool", "kind": "typeIdentifier"}], "type": "topic", "title": "canDismissDuringUserInteraction", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/canDismissDuringUserInteraction", "kind": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/pill": {"type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/pill", "kind": "symbol", "role": "symbol", "fragments": [{"kind": "keyword", "text": "case"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "pill"}], "title": "StatusBarNotificationBackgroundType.pill", "abstract": [{"type": "text", "text": "The background is a floating pill around the text. The pill size and appearance can be customized. This is the default."}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundtype/pill"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/textStyle": {"type": "topic", "role": "symbol", "title": "textStyle", "abstract": [{"type": "text", "text": "Defines the appearance of the title label."}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/textStyle", "fragments": [{"kind": "keyword", "text": "var"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "textStyle"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationTextStyle", "text": "StatusBarNotificationTextStyle"}], "kind": "symbol", "url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle/textstyle"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/systemStatusBarStyle": {"abstract": [{"type": "text", "text": "Defines which "}, {"type": "codeVoice", "code": "UIStatusBarStyle"}, {"text": " should be used during presentation.", "type": "text"}], "kind": "symbol", "role": "symbol", "type": "topic", "title": "systemStatusBarStyle", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/systemStatusBarStyle", "fragments": [{"kind": "keyword", "text": "var"}, {"kind": "text", "text": " "}, {"text": "systemStatusBarStyle", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationSystemBarStyle", "text": "StatusBarNotificationSystemBarStyle", "kind": "typeIdentifier"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle/systemstatusbarstyle"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundStyle": {"fragments": [{"text": "class", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "StatusBarNotificationBackgroundStyle", "kind": "identifier"}], "kind": "symbol", "navigatorTitle": [{"text": "StatusBarNotificationBackgroundStyle", "kind": "identifier"}], "role": "symbol", "url": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundstyle", "title": "StatusBarNotificationBackgroundStyle", "type": "topic", "abstract": [{"type": "text", "text": "Defines the appearance of the notification background."}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundStyle"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/subtitleStyle": {"abstract": [{"type": "text", "text": "Defines the appearance of the subtitle label."}], "title": "subtitleStyle", "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/subtitleStyle", "url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle/subtitlestyle", "fragments": [{"text": "var", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "subtitleStyle"}, {"kind": "text", "text": ": "}, {"text": "StatusBarNotificationTextStyle", "kind": "typeIdentifier", "preciseIdentifier": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationTextStyle"}], "kind": "symbol", "type": "topic"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle": {"abstract": [{"text": "Defines which ", "type": "text"}, {"code": "UIStatusBarStyle", "type": "codeVoice"}, {"type": "text", "text": " should be used during presentation."}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationsystembarstyle", "fragments": [{"kind": "keyword", "text": "enum"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "StatusBarNotificationSystemBarStyle"}], "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationSystemBarStyle"}], "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle", "title": "StatusBarNotificationSystemBarStyle", "role": "symbol", "type": "topic"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/canTapToHold": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle/cantaptohold", "abstract": [{"type": "text", "text": "Defines if the bar can be touched to prevent a dismissal until the tap is released. De<PERSON><PERSON> is "}, {"type": "codeVoice", "code": "true"}, {"text": ".", "type": "text"}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/canTapToHold", "kind": "symbol", "type": "topic", "fragments": [{"text": "var", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "canTapToHold"}, {"kind": "text", "text": ": "}, {"text": "Bool", "kind": "typeIdentifier", "preciseIdentifier": "s:Sb"}], "title": "canTapToHold"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType": {"abstract": [{"text": "Defines the animation used during presentation and dismissal of the notification.", "type": "text"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationanimationtype", "fragments": [{"kind": "keyword", "text": "enum"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationAnimationType", "kind": "identifier"}], "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationAnimationType"}], "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType", "title": "StatusBarNotificationAnimationType", "role": "symbol", "type": "topic"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/progressBarStyle": {"type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/progressBarStyle", "kind": "symbol", "abstract": [{"text": "Defines the appearance of the progress bar.", "type": "text"}], "role": "symbol", "fragments": [{"kind": "keyword", "text": "var"}, {"kind": "text", "text": " "}, {"text": "progressBarStyle", "kind": "identifier"}, {"text": ": ", "kind": "text"}, {"preciseIdentifier": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationProgressBarStyle", "kind": "typeIdentifier", "text": "StatusBarNotificationProgressBarStyle"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle/progressbarstyle", "title": "progressBarStyle"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle": {"title": "StatusBarNotificationTextStyle", "fragments": [{"kind": "keyword", "text": "class"}, {"text": " ", "kind": "text"}, {"text": "StatusBarNotificationTextStyle", "kind": "identifier"}], "role": "symbol", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle", "url": "/documentation/jdstatusbarnotification/statusbarnotificationtextstyle", "type": "topic", "navigatorTitle": [{"text": "StatusBarNotificationTextStyle", "kind": "identifier"}], "abstract": [{"type": "text", "text": "Defines the appearance of a text label."}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/backgroundStyle": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle/backgroundstyle", "type": "topic", "title": "backgroundStyle", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/backgroundStyle", "role": "symbol", "fragments": [{"kind": "keyword", "text": "var"}, {"kind": "text", "text": " "}, {"text": "backgroundStyle", "kind": "identifier"}, {"text": ": ", "kind": "text"}, {"text": "StatusBarNotificationBackgroundStyle", "kind": "typeIdentifier", "preciseIdentifier": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationBackgroundStyle"}], "kind": "symbol", "abstract": [{"type": "text", "text": "Defines the appearance of the notification background."}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/leftViewStyle": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle/leftviewstyle", "type": "topic", "fragments": [{"text": "var", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "leftViewStyle"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationLeftViewStyle", "text": "StatusBarNotificationLeftViewStyle", "kind": "typeIdentifier"}], "kind": "symbol", "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/leftViewStyle", "title": "leftViewStyle", "abstract": [{"text": "Defines the appearance of a left-view, if set. It also applies to the activity indicator.", "type": "text"}]}, "doc://calimarkus.JDStatusBarNotification/SQ": {"title": "Swift.Equatable", "identifier": "doc://calimarkus.JDStatusBarNotification/SQ", "type": "unresolvable"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle": {"abstract": [{"text": "Defines the appearance of the pill, when using ", "type": "text"}, {"type": "reference", "isActive": true, "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/pill"}], "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationPillStyle"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle", "type": "topic", "kind": "symbol", "role": "symbol", "url": "/documentation/jdstatusbarnotification/statusbarnotificationpillstyle", "fragments": [{"kind": "keyword", "text": "class"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "StatusBarNotificationPillStyle"}], "title": "StatusBarNotificationPillStyle"}, "doc://calimarkus.JDStatusBarNotification/s7CVarArgP": {"title": "Swift.CVarArg", "identifier": "doc://calimarkus.JDStatusBarNotification/s7CVarArgP", "type": "unresolvable"}, "doc://calimarkus.JDStatusBarNotification/s23CustomStringConvertibleP": {"title": "Swift.CustomStringConvertible", "identifier": "doc://calimarkus.JDStatusBarNotification/s23CustomStringConvertibleP", "type": "unresolvable"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition": {"abstract": [{"type": "text", "text": "Defines the position of the progress bar, when used."}], "type": "topic", "role": "symbol", "fragments": [{"kind": "keyword", "text": "enum"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "StatusBarNotificationProgressBarPosition"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarposition", "kind": "symbol", "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationProgressBarPosition"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition", "title": "StatusBarNotificationProgressBarPosition"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType", "fragments": [{"kind": "keyword", "text": "enum"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationBackgroundType", "kind": "identifier"}], "title": "StatusBarNotificationBackgroundType", "abstract": [{"text": "Defines the appearance of the notification background.", "type": "text"}], "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationBackgroundType"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundtype", "type": "topic", "kind": "symbol", "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/objc(pl)NSObject": {"title": "ObjectiveC.NSObjectProtocol", "identifier": "doc://calimarkus.JDStatusBarNotification/objc(pl)NSObject", "type": "unresolvable"}, "doc://calimarkus.JDStatusBarNotification/s28CustomDebugStringConvertibleP": {"title": "Swift.CustomDebugStringConvertible", "identifier": "doc://calimarkus.JDStatusBarNotification/s28CustomDebugStringConvertibleP", "type": "unresolvable"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle", "navigatorTitle": [{"text": "StatusBarNotificationStyle", "kind": "identifier"}], "role": "symbol", "type": "topic", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "title": "StatusBarNotificationStyle", "abstract": [{"text": "A Style defines the appearance of a notification.", "type": "text"}], "fragments": [{"kind": "keyword", "text": "class"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationStyle", "kind": "identifier"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment", "role": "symbol", "kind": "symbol", "fragments": [{"kind": "keyword", "text": "enum"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "StatusBarNotificationLeftViewAlignment"}], "abstract": [{"text": "Defines the appearance of a left-view, if set. This includes the activity indicator.", "type": "text"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewalignment", "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationLeftViewAlignment"}], "type": "topic", "title": "StatusBarNotificationLeftViewAlignment"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle": {"navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationLeftViewStyle"}], "type": "topic", "kind": "symbol", "title": "StatusBarNotificationLeftViewStyle", "abstract": [{"text": "Defines the appearance of a left-view, if set. It also applies to the activity indicator.", "type": "text"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewstyle", "role": "symbol", "fragments": [{"text": "class", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "StatusBarNotificationLeftViewStyle", "kind": "identifier"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle"}, "doc://calimarkus.JDStatusBarNotification/objc(cs)NSObject": {"title": "ObjectiveC.NSObject", "identifier": "doc://calimarkus.JDStatusBarNotification/objc(cs)NSObject", "type": "unresolvable"}}}