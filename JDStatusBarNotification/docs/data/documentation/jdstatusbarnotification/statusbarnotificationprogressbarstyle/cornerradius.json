{"sections": [], "variants": [{"traits": [{"interfaceLanguage": "swift"}], "paths": ["/documentation/jdstatusbarnotification/statusbarnotificationprogressbarstyle/cornerradius"]}], "primaryContentSections": [{"declarations": [{"platforms": ["iOS"], "tokens": [{"text": "@objc", "kind": "attribute"}, {"text": " ", "kind": "text"}, {"kind": "keyword", "text": "var"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "cornerRadius"}, {"kind": "text", "text": ": "}, {"kind": "typeIdentifier", "text": "Double", "preciseIdentifier": "s:Sd"}], "languages": ["swift"]}], "kind": "declarations"}], "schemaVersion": {"minor": 3, "major": 0, "patch": 0}, "kind": "symbol", "abstract": [{"text": "The corner radius of the progress bar. De<PERSON><PERSON> is ", "type": "text"}, {"type": "codeVoice", "code": "1.0"}], "metadata": {"roleHeading": "Instance Property", "fragments": [{"text": "var", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "cornerRadius", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "s:Sd", "text": "Double", "kind": "typeIdentifier"}], "role": "symbol", "symbolKind": "property", "title": "cornerRadius", "externalID": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationProgressBarStyle(py)cornerRadius", "modules": [{"name": "JDStatusBarNotification"}]}, "identifier": {"url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle/cornerRadius", "interfaceLanguage": "swift"}, "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle"]]}, "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle/cornerRadius": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle/cornerRadius", "kind": "symbol", "url": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarstyle/cornerradius", "fragments": [{"kind": "keyword", "text": "var"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "cornerRadius"}, {"text": ": ", "kind": "text"}, {"preciseIdentifier": "s:Sd", "text": "Double", "kind": "typeIdentifier"}], "role": "symbol", "type": "topic", "title": "cornerRadius", "abstract": [{"text": "The corner radius of the progress bar. De<PERSON><PERSON> is ", "type": "text"}, {"code": "1.0", "type": "codeVoice"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle": {"type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle", "kind": "symbol", "role": "symbol", "fragments": [{"text": "class", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationProgressBarStyle", "kind": "identifier"}], "title": "StatusBarNotificationProgressBarStyle", "abstract": [{"type": "text", "text": "Defines the appearance of the progress bar."}], "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationProgressBarStyle"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarstyle"}}}