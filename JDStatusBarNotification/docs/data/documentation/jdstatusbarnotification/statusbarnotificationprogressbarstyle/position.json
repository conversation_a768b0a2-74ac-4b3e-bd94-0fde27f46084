{"abstract": [{"type": "text", "text": "The position of the progress bar. <PERSON><PERSON><PERSON> is "}, {"isActive": true, "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition/bottom", "type": "reference"}], "metadata": {"fragments": [{"text": "var", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "position", "kind": "identifier"}, {"text": ": ", "kind": "text"}, {"text": "StatusBarNotificationProgressBarPosition", "kind": "typeIdentifier", "preciseIdentifier": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationProgressBarPosition"}], "externalID": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationProgressBarStyle(py)position", "symbolKind": "property", "modules": [{"name": "JDStatusBarNotification"}], "roleHeading": "Instance Property", "role": "symbol", "title": "position"}, "identifier": {"interfaceLanguage": "swift", "url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle/position"}, "kind": "symbol", "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle"]]}, "primaryContentSections": [{"declarations": [{"platforms": ["iOS"], "languages": ["swift"], "tokens": [{"kind": "attribute", "text": "@objc"}, {"text": " ", "kind": "text"}, {"kind": "keyword", "text": "var"}, {"kind": "text", "text": " "}, {"text": "position", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition", "kind": "typeIdentifier", "text": "StatusBarNotificationProgressBarPosition", "preciseIdentifier": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationProgressBarPosition"}]}], "kind": "declarations"}], "sections": [], "variants": [{"traits": [{"interfaceLanguage": "swift"}], "paths": ["/documentation/jdstatusbarnotification/statusbarnotificationprogressbarstyle/position"]}], "schemaVersion": {"minor": 3, "patch": 0, "major": 0}, "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition": {"abstract": [{"type": "text", "text": "Defines the position of the progress bar, when used."}], "type": "topic", "role": "symbol", "fragments": [{"kind": "keyword", "text": "enum"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "StatusBarNotificationProgressBarPosition"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarposition", "kind": "symbol", "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationProgressBarPosition"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition", "title": "StatusBarNotificationProgressBarPosition"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle": {"type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle", "kind": "symbol", "role": "symbol", "fragments": [{"text": "class", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationProgressBarStyle", "kind": "identifier"}], "title": "StatusBarNotificationProgressBarStyle", "abstract": [{"type": "text", "text": "Defines the appearance of the progress bar."}], "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationProgressBarStyle"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarstyle"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition/bottom": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition/bottom", "kind": "symbol", "title": "StatusBarNotificationProgressBarPosition.bottom", "url": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarposition/bottom", "type": "topic", "abstract": [{"text": "The progress bar will be at the bottom of the notification content. This is the default.", "type": "text"}], "fragments": [{"text": "case", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "bottom"}], "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle/position": {"abstract": [{"text": "The position of the progress bar. <PERSON><PERSON><PERSON> is ", "type": "text"}, {"type": "reference", "isActive": true, "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition/bottom"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarstyle/position", "role": "symbol", "fragments": [{"kind": "keyword", "text": "var"}, {"text": " ", "kind": "text"}, {"text": "position", "kind": "identifier"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "text": "StatusBarNotificationProgressBarPosition", "preciseIdentifier": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationProgressBarPosition"}], "type": "topic", "title": "position", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle/position", "kind": "symbol"}}}