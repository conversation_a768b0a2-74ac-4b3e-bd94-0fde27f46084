{"identifier": {"interfaceLanguage": "swift", "url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle/offsetY"}, "metadata": {"symbolKind": "property", "modules": [{"name": "JDStatusBarNotification"}], "role": "symbol", "title": "offsetY", "roleHeading": "Instance Property", "externalID": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationProgressBarStyle(py)offsetY", "fragments": [{"text": "var", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "offsetY"}, {"text": ": ", "kind": "text"}, {"text": "Double", "preciseIdentifier": "s:Sd", "kind": "typeIdentifier"}]}, "abstract": [{"text": "Offsets the progress bar on the  y-axis. De<PERSON><PERSON> is ", "type": "text"}, {"type": "codeVoice", "code": "-5.0"}, {"type": "text", "text": "."}], "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle"]]}, "primaryContentSections": [{"declarations": [{"languages": ["swift"], "platforms": ["iOS"], "tokens": [{"text": "@objc", "kind": "attribute"}, {"kind": "text", "text": " "}, {"kind": "keyword", "text": "var"}, {"text": " ", "kind": "text"}, {"text": "offsetY", "kind": "identifier"}, {"text": ": ", "kind": "text"}, {"preciseIdentifier": "s:Sd", "kind": "typeIdentifier", "text": "Double"}]}], "kind": "declarations"}], "variants": [{"traits": [{"interfaceLanguage": "swift"}], "paths": ["/documentation/jdstatusbarnotification/statusbarnotificationprogressbarstyle/offsety"]}], "kind": "symbol", "schemaVersion": {"major": 0, "minor": 3, "patch": 0}, "sections": [], "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle": {"type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle", "kind": "symbol", "role": "symbol", "fragments": [{"text": "class", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationProgressBarStyle", "kind": "identifier"}], "title": "StatusBarNotificationProgressBarStyle", "abstract": [{"type": "text", "text": "Defines the appearance of the progress bar."}], "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationProgressBarStyle"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarstyle"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle/offsetY": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle/offsetY", "url": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarstyle/offsety", "kind": "symbol", "title": "offsetY", "type": "topic", "abstract": [{"type": "text", "text": "Offsets the progress bar on the  y-axis. De<PERSON><PERSON> is "}, {"type": "codeVoice", "code": "-5.0"}, {"type": "text", "text": "."}], "role": "symbol", "fragments": [{"text": "var", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "offsetY", "kind": "identifier"}, {"text": ": ", "kind": "text"}, {"preciseIdentifier": "s:Sd", "text": "Double", "kind": "typeIdentifier"}]}}}