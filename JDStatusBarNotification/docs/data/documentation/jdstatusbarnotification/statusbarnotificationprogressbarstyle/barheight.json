{"schemaVersion": {"patch": 0, "major": 0, "minor": 3}, "primaryContentSections": [{"kind": "declarations", "declarations": [{"languages": ["swift"], "platforms": ["iOS"], "tokens": [{"kind": "attribute", "text": "@objc"}, {"kind": "text", "text": " "}, {"kind": "keyword", "text": "var"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "barHeight"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "s:Sd", "text": "Double", "kind": "typeIdentifier"}]}]}], "sections": [], "variants": [{"paths": ["/documentation/jdstatusbarnotification/statusbarnotificationprogressbarstyle/barheight"], "traits": [{"interfaceLanguage": "swift"}]}], "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle"]]}, "identifier": {"interfaceLanguage": "swift", "url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle/barHeight"}, "metadata": {"modules": [{"name": "JDStatusBarNotification"}], "title": "barHeight", "symbolKind": "property", "externalID": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationProgressBarStyle(py)barHeight", "roleHeading": "Instance Property", "role": "symbol", "fragments": [{"kind": "keyword", "text": "var"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "barHeight"}, {"text": ": ", "kind": "text"}, {"text": "Double", "kind": "typeIdentifier", "preciseIdentifier": "s:Sd"}]}, "kind": "symbol", "abstract": [{"type": "text", "text": "The height of the progress bar. De<PERSON><PERSON> is "}, {"type": "codeVoice", "code": "2.0"}, {"type": "text", "text": ". The applied minimum is 0.5 and the maximum equals the full height of the notification."}], "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle": {"type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle", "kind": "symbol", "role": "symbol", "fragments": [{"text": "class", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationProgressBarStyle", "kind": "identifier"}], "title": "StatusBarNotificationProgressBarStyle", "abstract": [{"type": "text", "text": "Defines the appearance of the progress bar."}], "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationProgressBarStyle"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarstyle"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle/barHeight": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle/barHeight", "kind": "symbol", "abstract": [{"type": "text", "text": "The height of the progress bar. De<PERSON><PERSON> is "}, {"type": "codeVoice", "code": "2.0"}, {"type": "text", "text": ". The applied minimum is 0.5 and the maximum equals the full height of the notification."}], "title": "barHeight", "url": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarstyle/barheight", "role": "symbol", "fragments": [{"text": "var", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "barHeight"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "s:Sd", "kind": "typeIdentifier", "text": "Double"}], "type": "topic"}}}