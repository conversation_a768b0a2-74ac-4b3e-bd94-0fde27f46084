{"kind": "symbol", "metadata": {"externalID": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationProgressBarStyle(py)barColor", "role": "symbol", "fragments": [{"kind": "keyword", "text": "var"}, {"kind": "text", "text": " "}, {"text": "barColor", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIColor", "text": "UIColor"}, {"kind": "text", "text": "?"}], "roleHeading": "Instance Property", "symbolKind": "property", "title": "barColor", "modules": [{"name": "JDStatusBarNotification"}]}, "sections": [], "identifier": {"url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle/barColor", "interfaceLanguage": "swift"}, "schemaVersion": {"patch": 0, "major": 0, "minor": 3}, "abstract": [{"text": "The background color of the progress bar (on top of the notification bar)", "type": "text"}], "variants": [{"paths": ["/documentation/jdstatusbarnotification/statusbarnotificationprogressbarstyle/barcolor"], "traits": [{"interfaceLanguage": "swift"}]}], "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle"]]}, "primaryContentSections": [{"declarations": [{"tokens": [{"kind": "attribute", "text": "@objc"}, {"text": " ", "kind": "text"}, {"kind": "keyword", "text": "var"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "barColor"}, {"text": ": ", "kind": "text"}, {"preciseIdentifier": "c:objc(cs)UIColor", "text": "UIColor", "kind": "typeIdentifier"}, {"text": "?", "kind": "text"}], "platforms": ["iOS"], "languages": ["swift"]}], "kind": "declarations"}], "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle": {"type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle", "kind": "symbol", "role": "symbol", "fragments": [{"text": "class", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationProgressBarStyle", "kind": "identifier"}], "title": "StatusBarNotificationProgressBarStyle", "abstract": [{"type": "text", "text": "Defines the appearance of the progress bar."}], "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationProgressBarStyle"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarstyle"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle/barColor": {"abstract": [{"text": "The background color of the progress bar (on top of the notification bar)", "type": "text"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarstyle/barcolor", "role": "symbol", "fragments": [{"text": "var", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "barColor", "kind": "identifier"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "text": "UIColor", "preciseIdentifier": "c:objc(cs)UIColor"}, {"kind": "text", "text": "?"}], "type": "topic", "kind": "symbol", "title": "barColor", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle/barColor"}}}