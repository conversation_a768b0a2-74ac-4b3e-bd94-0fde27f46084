{"metadata": {"symbolKind": "method", "fragments": [{"text": "func", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "sizeThatFits"}, {"kind": "text", "text": "("}, {"kind": "externalParam", "text": "in"}, {"text": ": ", "kind": "text"}, {"preciseIdentifier": "c:@S@CGSize", "kind": "typeIdentifier", "text": "CGSize"}, {"kind": "text", "text": ") -> "}, {"kind": "typeIdentifier", "text": "CGSize", "preciseIdentifier": "c:@S@CGSize"}], "roleHeading": "Instance Method", "modules": [{"name": "JDStatusBarNotification"}], "required": true, "title": "sizeThatFits(in:)", "externalID": "c:@M@JDStatusBarNotification@objc(pl)JDStatusBarNotificationPresenterCustomViewSizingController(im)sizeThatFits:", "role": "symbol"}, "sections": [], "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenterCustomViewSizingController"]]}, "kind": "symbol", "identifier": {"interfaceLanguage": "swift", "url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenterCustomViewSizingController/sizeThatFits(in:)"}, "primaryContentSections": [{"declarations": [{"tokens": [{"kind": "attribute", "text": "@objc"}, {"text": "(sizeThatFits:) ", "kind": "text"}, {"text": "func", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "sizeThatFits"}, {"text": "(", "kind": "text"}, {"kind": "externalParam", "text": "in"}, {"text": " ", "kind": "text"}, {"kind": "internalParam", "text": "size"}, {"kind": "text", "text": ": "}, {"text": "CGSize", "preciseIdentifier": "c:@S@CGSize", "kind": "typeIdentifier"}, {"text": ") -> ", "kind": "text"}, {"text": "CGSize", "kind": "typeIdentifier", "preciseIdentifier": "c:@S@CGSize"}], "languages": ["swift"], "platforms": ["iOS"]}], "kind": "declarations"}], "variants": [{"traits": [{"interfaceLanguage": "swift"}], "paths": ["/documentation/jdstatusbarnotification/notificationpresentercustomviewsizingcontroller/sizethatfits(in:)"]}], "schemaVersion": {"patch": 0, "major": 0, "minor": 3}, "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenterCustomViewSizingController/sizeThatFits(in:)": {"type": "topic", "role": "symbol", "title": "sizeThatFits(in:)", "abstract": [], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenterCustomViewSizingController/sizeThatFits(in:)", "fragments": [{"text": "func", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "sizeThatFits", "kind": "identifier"}, {"kind": "text", "text": "("}, {"text": "in", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"text": "CGSize", "preciseIdentifier": "c:@S@CGSize", "kind": "typeIdentifier"}, {"kind": "text", "text": ") -> "}, {"text": "CGSize", "preciseIdentifier": "c:@S@CGSize", "kind": "typeIdentifier"}], "required": true, "kind": "symbol", "url": "/documentation/jdstatusbarnotification/notificationpresentercustomviewsizingcontroller/sizethatfits(in:)"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenterCustomViewSizingController": {"abstract": [{"text": "A protocol for a custom controller, which controls the size of a presented custom view.", "type": "text"}], "role": "symbol", "type": "topic", "navigatorTitle": [{"kind": "identifier", "text": "NotificationPresenterCustomViewSizingController"}], "url": "/documentation/jdstatusbarnotification/notificationpresentercustomviewsizingcontroller", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenterCustomViewSizingController", "kind": "symbol", "fragments": [{"kind": "keyword", "text": "protocol"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "NotificationPresenterCustomViewSizingController"}], "title": "NotificationPresenterCustomViewSizingController"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter": {"abstract": [{"type": "text", "text": "The NotificationPresenter let’s you present notifications below the statusBar."}, {"type": "text", "text": " "}, {"type": "text", "text": "You can customize the style (colors, fonts, etc.) and animations. It supports notch"}, {"type": "text", "text": " "}, {"type": "text", "text": "and no-notch devices, landscape & portrait layouts and Drag-to-Dismiss. It can display a"}, {"text": " ", "type": "text"}, {"type": "text", "text": "title, a subtitle, an activity indicator, an animated progress bar & custom views out of the box."}], "fragments": [{"text": "class", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "NotificationPresenter"}], "navigatorTitle": [{"text": "NotificationPresenter", "kind": "identifier"}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter", "url": "/documentation/jdstatusbarnotification/notificationpresenter", "type": "topic", "kind": "symbol", "title": "NotificationPresenter"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}}}