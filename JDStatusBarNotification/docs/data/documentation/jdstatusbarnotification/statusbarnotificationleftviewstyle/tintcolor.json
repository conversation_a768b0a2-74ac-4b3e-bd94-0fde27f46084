{"metadata": {"externalID": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationLeftViewStyle(py)tintColor", "symbolKind": "property", "roleHeading": "Instance Property", "fragments": [{"kind": "keyword", "text": "var"}, {"kind": "text", "text": " "}, {"text": "tintColor", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"kind": "typeIdentifier", "text": "UIColor", "preciseIdentifier": "c:objc(cs)UIColor"}, {"kind": "text", "text": "?"}], "role": "symbol", "modules": [{"name": "JDStatusBarNotification"}], "title": "tintColor"}, "identifier": {"url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle/tintColor", "interfaceLanguage": "swift"}, "sections": [], "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle"]]}, "kind": "symbol", "abstract": [{"type": "text", "text": "Sets the tint color of the left-view. <PERSON><PERSON><PERSON> is "}, {"code": "nil", "type": "codeVoice"}, {"text": ".", "type": "text"}], "schemaVersion": {"major": 0, "patch": 0, "minor": 3}, "variants": [{"paths": ["/documentation/jdstatusbarnotification/statusbarnotificationleftviewstyle/tintcolor"], "traits": [{"interfaceLanguage": "swift"}]}], "primaryContentSections": [{"kind": "declarations", "declarations": [{"tokens": [{"kind": "attribute", "text": "@objc"}, {"kind": "text", "text": " "}, {"text": "var", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "tintColor", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "c:objc(cs)UIColor", "kind": "typeIdentifier", "text": "UIColor"}, {"text": "?", "kind": "text"}], "languages": ["swift"], "platforms": ["iOS"]}]}, {"content": [{"text": "Discussion", "type": "heading", "anchor": "discussion", "level": 2}, {"inlineContent": [{"type": "text", "text": "This applies to the activity indicator, or a custom left-view. The activity indicator"}, {"text": " ", "type": "text"}, {"text": "defaults to the title text color, if no tintColor is specified.", "type": "text"}], "type": "paragraph"}], "kind": "content"}], "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle/tintColor": {"fragments": [{"text": "var", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "tintColor", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"text": "UIColor", "kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIColor"}, {"kind": "text", "text": "?"}], "abstract": [{"text": "Sets the tint color of the left-view. <PERSON><PERSON><PERSON> is ", "type": "text"}, {"code": "nil", "type": "codeVoice"}, {"text": ".", "type": "text"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle/tintColor", "url": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewstyle/tintcolor", "role": "symbol", "type": "topic", "kind": "symbol", "title": "tintColor"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle": {"navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationLeftViewStyle"}], "type": "topic", "kind": "symbol", "title": "StatusBarNotificationLeftViewStyle", "abstract": [{"text": "Defines the appearance of a left-view, if set. It also applies to the activity indicator.", "type": "text"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewstyle", "role": "symbol", "fragments": [{"text": "class", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "StatusBarNotificationLeftViewStyle", "kind": "identifier"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle"}}}