{"sections": [], "metadata": {"title": "alignment", "role": "symbol", "modules": [{"name": "JDStatusBarNotification"}], "roleHeading": "Instance Property", "symbolKind": "property", "externalID": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationLeftViewStyle(py)alignment", "fragments": [{"kind": "keyword", "text": "var"}, {"text": " ", "kind": "text"}, {"text": "alignment", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationLeftViewAlignment", "kind": "typeIdentifier", "text": "StatusBarNotificationLeftViewAlignment"}]}, "variants": [{"paths": ["/documentation/jdstatusbarnotification/statusbarnotificationleftviewstyle/alignment"], "traits": [{"interfaceLanguage": "swift"}]}], "kind": "symbol", "primaryContentSections": [{"declarations": [{"languages": ["swift"], "platforms": ["iOS"], "tokens": [{"text": "@objc", "kind": "attribute"}, {"kind": "text", "text": " "}, {"kind": "keyword", "text": "var"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "alignment"}, {"kind": "text", "text": ": "}, {"kind": "typeIdentifier", "text": "StatusBarNotificationLeftViewAlignment", "preciseIdentifier": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationLeftViewAlignment", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment"}]}], "kind": "declarations"}], "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle"]]}, "schemaVersion": {"major": 0, "minor": 3, "patch": 0}, "abstract": [{"type": "text", "text": "The alignment of the left-view. The default is "}, {"isActive": true, "type": "reference", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment/centerWithText"}, {"text": " ", "type": "text"}, {"text": "If no title or subtitle is set, the left-view is always fully centered.", "type": "text"}], "identifier": {"url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle/alignment", "interfaceLanguage": "swift"}, "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment/centerWithText": {"abstract": [{"type": "text", "text": "Centers the left-view together with the text. The left-view will be positioned at the leading edge of the text. The text is left-aligned. This is the default."}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewalignment/centerwithtext", "fragments": [{"kind": "keyword", "text": "case"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "centerWithText"}], "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment/centerWithText", "title": "StatusBarNotificationLeftViewAlignment.centerWithText", "role": "symbol", "type": "topic"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment", "role": "symbol", "kind": "symbol", "fragments": [{"kind": "keyword", "text": "enum"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "StatusBarNotificationLeftViewAlignment"}], "abstract": [{"text": "Defines the appearance of a left-view, if set. This includes the activity indicator.", "type": "text"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewalignment", "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationLeftViewAlignment"}], "type": "topic", "title": "StatusBarNotificationLeftViewAlignment"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle": {"navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationLeftViewStyle"}], "type": "topic", "kind": "symbol", "title": "StatusBarNotificationLeftViewStyle", "abstract": [{"text": "Defines the appearance of a left-view, if set. It also applies to the activity indicator.", "type": "text"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewstyle", "role": "symbol", "fragments": [{"text": "class", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "StatusBarNotificationLeftViewStyle", "kind": "identifier"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle/alignment": {"type": "topic", "kind": "symbol", "title": "alignment", "url": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewstyle/alignment", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle/alignment", "abstract": [{"type": "text", "text": "The alignment of the left-view. The default is "}, {"isActive": true, "type": "reference", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment/centerWithText"}, {"type": "text", "text": " "}, {"text": "If no title or subtitle is set, the left-view is always fully centered.", "type": "text"}], "role": "symbol", "fragments": [{"kind": "keyword", "text": "var"}, {"kind": "text", "text": " "}, {"text": "alignment", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"text": "StatusBarNotificationLeftViewAlignment", "preciseIdentifier": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationLeftViewAlignment", "kind": "typeIdentifier"}]}}}