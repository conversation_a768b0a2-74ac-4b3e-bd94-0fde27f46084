{"variants": [{"traits": [{"interfaceLanguage": "swift"}], "paths": ["/documentation/jdstatusbarnotification/statusbarnotificationleftviewstyle/offset"]}], "sections": [], "schemaVersion": {"minor": 3, "patch": 0, "major": 0}, "metadata": {"title": "offset", "externalID": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationLeftViewStyle(py)offset", "modules": [{"name": "JDStatusBarNotification"}], "fragments": [{"kind": "keyword", "text": "var"}, {"text": " ", "kind": "text"}, {"text": "offset", "kind": "identifier"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "c:@S@CGPoint", "text": "CGPoint"}], "symbolKind": "property", "role": "symbol", "roleHeading": "Instance Property"}, "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle"]]}, "kind": "symbol", "identifier": {"url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle/offset", "interfaceLanguage": "swift"}, "abstract": [{"text": "An optional offset to adjust the left-views position. Default is ", "type": "text"}, {"type": "codeVoice", "code": "CGPointZero"}, {"text": ".", "type": "text"}], "primaryContentSections": [{"declarations": [{"platforms": ["iOS"], "tokens": [{"text": "@objc", "kind": "attribute"}, {"kind": "text", "text": " "}, {"kind": "keyword", "text": "var"}, {"text": " ", "kind": "text"}, {"text": "offset", "kind": "identifier"}, {"text": ": ", "kind": "text"}, {"preciseIdentifier": "c:@S@CGPoint", "kind": "typeIdentifier", "text": "CGPoint"}], "languages": ["swift"]}], "kind": "declarations"}], "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle/offset": {"fragments": [{"kind": "keyword", "text": "var"}, {"text": " ", "kind": "text"}, {"text": "offset", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"text": "CGPoint", "preciseIdentifier": "c:@S@CGPoint", "kind": "typeIdentifier"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewstyle/offset", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle/offset", "type": "topic", "abstract": [{"text": "An optional offset to adjust the left-views position. Default is ", "type": "text"}, {"code": "CGPointZero", "type": "codeVoice"}, {"type": "text", "text": "."}], "title": "offset", "kind": "symbol", "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle": {"navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationLeftViewStyle"}], "type": "topic", "kind": "symbol", "title": "StatusBarNotificationLeftViewStyle", "abstract": [{"text": "Defines the appearance of a left-view, if set. It also applies to the activity indicator.", "type": "text"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewstyle", "role": "symbol", "fragments": [{"text": "class", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "StatusBarNotificationLeftViewStyle", "kind": "identifier"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle"}}}