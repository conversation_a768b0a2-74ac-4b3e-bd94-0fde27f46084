{"seeAlsoSections": [{"identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/Completion", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/present(_:subtitle:styleName:duration:completion:)", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/present(_:subtitle:includedStyle:duration:completion:)"], "generated": true, "title": "Present a notification"}], "variants": [{"traits": [{"interfaceLanguage": "swift"}], "paths": ["/documentation/jdstatusbarnotification/includedstatusbarnotificationstyle"]}], "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter"]]}, "metadata": {"fragments": [{"text": "enum", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "IncludedStatusBarNotificationStyle", "kind": "identifier"}], "externalID": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationIncludedStyle", "modules": [{"name": "JDStatusBarNotification"}], "title": "IncludedStatusBarNotificationStyle", "roleHeading": "Enumeration", "symbolKind": "enum", "navigatorTitle": [{"text": "IncludedStatusBarNotificationStyle", "kind": "identifier"}], "role": "symbol"}, "topicSections": [{"identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/defaultStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/light", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/dark"], "title": "Default Style"}, {"identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/success", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/warning", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/error", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/matrix"], "title": "Other Included Styles"}, {"title": "Initializers", "identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/init(rawValue:)"]}, {"identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/Equatable-Implementations", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/RawRepresentable-Implementations"], "title": "Default Implementations", "generated": true}], "abstract": [{"text": "These included styles let you easily change the visual appearance of a", "type": "text"}, {"text": " ", "type": "text"}, {"type": "text", "text": "notification without creating your own custom style."}], "schemaVersion": {"patch": 0, "minor": 3, "major": 0}, "primaryContentSections": [{"kind": "declarations", "declarations": [{"tokens": [{"kind": "attribute", "text": "@objc"}, {"text": "(JDStatusBarNotificationIncludedStyle) ", "kind": "text"}, {"text": "enum", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "IncludedStatusBarNotificationStyle", "kind": "identifier"}], "platforms": ["iOS"], "languages": ["swift"]}]}, {"kind": "content", "content": [{"type": "heading", "level": 2, "anchor": "overview", "text": "Overview"}, {"type": "paragraph", "inlineContent": [{"type": "text", "text": "Note that only the "}, {"type": "reference", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/defaultStyle", "isActive": true}, {"text": " is dynamic", "type": "text"}, {"text": " ", "type": "text"}, {"type": "text", "text": "and adjusts for light- & dark-mode. Other styles have a fixed appearance."}]}]}], "identifier": {"interfaceLanguage": "swift", "url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle"}, "kind": "symbol", "relationshipsSections": [{"identifiers": ["doc://calimarkus.JDStatusBarNotification/SQ", "doc://calimarkus.JDStatusBarNotification/SH", "doc://calimarkus.JDStatusBarNotification/SY"], "title": "Conforms To", "kind": "relationships", "type": "conformsTo"}], "sections": [], "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/Equatable-Implementations": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/Equatable-Implementations", "url": "/documentation/jdstatusbarnotification/includedstatusbarnotificationstyle/equatable-implementations", "type": "topic", "abstract": [], "title": "Equatable Implementations", "role": "collectionGroup", "kind": "article"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/dark": {"abstract": [{"type": "text", "text": "A nearly black background with a nearly white text."}], "fragments": [{"kind": "keyword", "text": "case"}, {"text": " ", "kind": "text"}, {"text": "dark", "kind": "identifier"}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/dark", "url": "/documentation/jdstatusbarnotification/includedstatusbarnotificationstyle/dark", "type": "topic", "kind": "symbol", "title": "IncludedStatusBarNotificationStyle.dark"}, "doc://calimarkus.JDStatusBarNotification/SH": {"identifier": "doc://calimarkus.JDStatusBarNotification/SH", "type": "unresolvable", "title": "<PERSON><PERSON>"}, "doc://calimarkus.JDStatusBarNotification/SQ": {"type": "unresolvable", "title": "Swift.Equatable", "identifier": "doc://calimarkus.JDStatusBarNotification/SQ"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle": {"abstract": [{"text": "These included styles let you easily change the visual appearance of a", "type": "text"}, {"type": "text", "text": " "}, {"type": "text", "text": "notification without creating your own custom style."}], "fragments": [{"text": "enum", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "IncludedStatusBarNotificationStyle", "kind": "identifier"}], "navigatorTitle": [{"kind": "identifier", "text": "IncludedStatusBarNotificationStyle"}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle", "url": "/documentation/jdstatusbarnotification/includedstatusbarnotificationstyle", "type": "topic", "kind": "symbol", "title": "IncludedStatusBarNotificationStyle"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/RawRepresentable-Implementations": {"type": "topic", "url": "/documentation/jdstatusbarnotification/includedstatusbarnotificationstyle/rawrepresentable-implementations", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/RawRepresentable-Implementations", "title": "RawRepresentable Implementations", "kind": "article", "role": "collectionGroup", "abstract": []}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/Completion": {"kind": "symbol", "abstract": [{"type": "text", "text": "Called upon animation completion."}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/Completion", "url": "/documentation/jdstatusbarnotification/notificationpresenter/completion", "fragments": [{"text": "typealias", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "Completion"}], "navigatorTitle": [{"text": "Completion", "kind": "identifier"}], "title": "NotificationPresenter.Completion", "type": "topic"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/matrix": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/matrix", "title": "IncludedStatusBarNotificationStyle.matrix", "url": "/documentation/jdstatusbarnotification/includedstatusbarnotificationstyle/matrix", "kind": "symbol", "abstract": [{"type": "text", "text": "A black background with a green bold monospace text."}], "type": "topic", "role": "symbol", "fragments": [{"text": "case", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "matrix", "kind": "identifier"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/success": {"fragments": [{"text": "case", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "success", "kind": "identifier"}], "title": "IncludedStatusBarNotificationStyle.success", "type": "topic", "abstract": [{"type": "text", "text": "A green background with a white text."}], "kind": "symbol", "url": "/documentation/jdstatusbarnotification/includedstatusbarnotificationstyle/success", "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/success"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/warning": {"fragments": [{"text": "case", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "warning", "kind": "identifier"}], "title": "IncludedStatusBarNotificationStyle.warning", "type": "topic", "abstract": [{"type": "text", "text": "A yellow background with a gray text."}], "kind": "symbol", "url": "/documentation/jdstatusbarnotification/includedstatusbarnotificationstyle/warning", "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/warning"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/present(_:subtitle:includedStyle:duration:completion:)": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/present(_:subtitle:includedStyle:duration:completion:)", "url": "/documentation/jdstatusbarnotification/notificationpresenter/present(_:subtitle:includedstyle:duration:completion:)", "kind": "symbol", "title": "present(_:subtitle:includedStyle:duration:completion:)", "type": "topic", "abstract": [{"text": "Present a notification using an included style.", "type": "text"}], "role": "symbol", "fragments": [{"kind": "keyword", "text": "func"}, {"text": " ", "kind": "text"}, {"text": "present", "kind": "identifier"}, {"kind": "text", "text": "("}, {"text": "String", "kind": "typeIdentifier", "preciseIdentifier": "s:SS"}, {"kind": "text", "text": ", "}, {"text": "subtitle", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "s:SS", "kind": "typeIdentifier", "text": "String"}, {"kind": "text", "text": "?, "}, {"text": "includedStyle", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationIncludedStyle", "kind": "typeIdentifier", "text": "IncludedStatusBarNotificationStyle"}, {"text": ", ", "kind": "text"}, {"kind": "externalParam", "text": "duration"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "s:Sd", "text": "Double"}, {"text": "?, ", "kind": "text"}, {"text": "completion", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona", "text": "Completion"}, {"kind": "text", "text": "?) -> "}, {"kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIView", "text": "UIView"}]}, "doc://calimarkus.JDStatusBarNotification/SY": {"type": "unresolvable", "title": "Swift.RawRepresentable", "identifier": "doc://calimarkus.JDStatusBarNotification/SY"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/light": {"abstract": [{"type": "text", "text": "A white background with a gray text."}], "fragments": [{"text": "case", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "light"}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/light", "url": "/documentation/jdstatusbarnotification/includedstatusbarnotificationstyle/light", "type": "topic", "kind": "symbol", "title": "IncludedStatusBarNotificationStyle.light"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/error": {"fragments": [{"text": "case", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "error", "kind": "identifier"}], "title": "IncludedStatusBarNotificationStyle.error", "type": "topic", "abstract": [{"type": "text", "text": "A red background with a white text."}], "kind": "symbol", "url": "/documentation/jdstatusbarnotification/includedstatusbarnotificationstyle/error", "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/error"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/present(_:subtitle:styleName:duration:completion:)": {"kind": "symbol", "type": "topic", "url": "/documentation/jdstatusbarnotification/notificationpresenter/present(_:subtitle:stylename:duration:completion:)", "title": "present(_:subtitle:styleName:duration:completion:)", "abstract": [{"type": "text", "text": "Present a notification using the default style or a named style."}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/present(_:subtitle:styleName:duration:completion:)", "fragments": [{"text": "func", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "present", "kind": "identifier"}, {"kind": "text", "text": "("}, {"text": "String", "preciseIdentifier": "s:SS", "kind": "typeIdentifier"}, {"kind": "text", "text": ", "}, {"text": "subtitle", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"text": "String", "kind": "typeIdentifier", "preciseIdentifier": "s:SS"}, {"text": "?, ", "kind": "text"}, {"text": "styleName", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "s:SS", "text": "String", "kind": "typeIdentifier"}, {"kind": "text", "text": "?, "}, {"kind": "externalParam", "text": "duration"}, {"text": ": ", "kind": "text"}, {"text": "Double", "kind": "typeIdentifier", "preciseIdentifier": "s:Sd"}, {"kind": "text", "text": "?, "}, {"text": "completion", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona", "text": "Completion"}, {"kind": "text", "text": "?) -> "}, {"kind": "typeIdentifier", "text": "UIView", "preciseIdentifier": "c:objc(cs)UIView"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter": {"abstract": [{"type": "text", "text": "The NotificationPresenter let’s you present notifications below the statusBar."}, {"type": "text", "text": " "}, {"type": "text", "text": "You can customize the style (colors, fonts, etc.) and animations. It supports notch"}, {"type": "text", "text": " "}, {"type": "text", "text": "and no-notch devices, landscape & portrait layouts and Drag-to-Dismiss. It can display a"}, {"text": " ", "type": "text"}, {"type": "text", "text": "title, a subtitle, an activity indicator, an animated progress bar & custom views out of the box."}], "fragments": [{"text": "class", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "NotificationPresenter"}], "navigatorTitle": [{"text": "NotificationPresenter", "kind": "identifier"}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter", "url": "/documentation/jdstatusbarnotification/notificationpresenter", "type": "topic", "kind": "symbol", "title": "NotificationPresenter"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/init(rawValue:)": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/init(rawValue:)", "url": "/documentation/jdstatusbarnotification/includedstatusbarnotificationstyle/init(rawvalue:)", "title": "init(rawValue:)", "abstract": [], "role": "symbol", "kind": "symbol", "type": "topic", "fragments": [{"text": "init", "kind": "identifier"}, {"kind": "text", "text": "?("}, {"text": "rawValue", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"text": "Int", "preciseIdentifier": "s:<PERSON>", "kind": "typeIdentifier"}, {"kind": "text", "text": ")"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/defaultStyle": {"abstract": [{"text": "The default style. This is used when no other style was provided and the", "type": "text"}, {"text": " ", "type": "text"}, {"text": "default style wasn’t replaced by the user. This is a dynamic style matching", "type": "text"}, {"type": "text", "text": " "}, {"type": "text", "text": "the "}, {"code": "light", "type": "codeVoice"}, {"text": " style in light mode and ", "type": "text"}, {"type": "codeVoice", "code": "dark"}, {"type": "text", "text": " style in dark mode."}], "fragments": [{"kind": "keyword", "text": "case"}, {"text": " ", "kind": "text"}, {"text": "defaultStyle", "kind": "identifier"}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/IncludedStatusBarNotificationStyle/defaultStyle", "url": "/documentation/jdstatusbarnotification/includedstatusbarnotificationstyle/defaultstyle", "type": "topic", "kind": "symbol", "title": "IncludedStatusBarNotificationStyle.defaultStyle"}}}