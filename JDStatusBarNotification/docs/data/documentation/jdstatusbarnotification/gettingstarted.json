{"metadata": {"role": "article", "roleHeading": "Article", "title": "Getting Started", "modules": [{"name": "JDStatusBarNotification"}]}, "primaryContentSections": [{"content": [{"type": "heading", "text": "Overview", "level": 2, "anchor": "overview"}, {"type": "paragraph", "inlineContent": [{"text": "Explore the full API in ", "type": "text"}, {"type": "reference", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter", "isActive": true}, {"text": ".", "type": "text"}]}, {"level": 3, "anchor": "Showing-a-text-notification", "text": "Showing a text notification", "type": "heading"}, {"inlineContent": [{"text": "See ", "type": "text"}, {"isActive": true, "type": "reference", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/present(_:subtitle:styleName:duration:completion:)"}], "type": "paragraph"}, {"type": "codeListing", "code": ["NotificationPresenter.shared.present(\"Hello World\")", "", "// with completion", "NotificationPresenter.shared.present(\"Hello World\") { presenter in", "   // ...", "}"], "syntax": "swift"}, {"text": "Showing a SwiftUI based notification", "type": "heading", "anchor": "Showing-a-SwiftUI-based-notification", "level": 3}, {"type": "paragraph", "inlineContent": [{"text": "See ", "type": "text"}, {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/presentSwiftView(styleName:viewBuilder:completion:)", "isActive": true, "type": "reference"}]}, {"syntax": "swift", "type": "codeListing", "code": ["NotificationPresenter.shared.presentSwiftView {", "    Text(\"Hi from Swift!\")", "}", "", "// with completion", "NotificationPresenter.shared.presentSwiftView {", "    Text(\"Hi from Swift!\")", "} completion: { presenter in", "   // ...", "}"]}, {"type": "heading", "text": "Dismissing a notification", "anchor": "Dismissing-a-notification", "level": 3}, {"inlineContent": [{"text": "See ", "type": "text"}, {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/dismiss(animated:after:completion:)", "type": "reference", "isActive": true}], "type": "paragraph"}, {"type": "codeListing", "syntax": "swift", "code": ["NotificationPresenter.shared.dismiss()", "", "// with completion", "NotificationPresenter.shared.dismiss(after: 0.5) { presenter in", "   // ...", "}"]}, {"anchor": "Showing-activity", "text": "Showing activity", "level": 3, "type": "heading"}, {"type": "paragraph", "inlineContent": [{"type": "text", "text": "See "}, {"isActive": true, "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/displayActivityIndicator(_:)", "type": "reference"}]}, {"type": "codeListing", "syntax": "swift", "code": ["NotificationPresenter.shared.present(\"\")", "NotificationPresenter.shared.displayActivityIndicator(true)"]}, {"type": "paragraph", "inlineContent": [{"type": "image", "identifier": "https://user-images.githubusercontent.com/807039/*********-c6255d41-4728-4bcb-bf72-fb12db01b5d5.gif"}]}, {"anchor": "Showing-a-custom-left-view", "level": 3, "type": "heading", "text": "Showing a custom left view"}, {"inlineContent": [{"type": "text", "text": "See "}, {"type": "reference", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/displayLeftView(_:)", "isActive": true}], "type": "paragraph"}, {"code": ["let image = UIImageView(image: UIImage(systemName: \"gamecontroller.fill\"))", "NotificationPresenter.shared.present(\"Player II\", subtitle: \"Connected\")", "NotificationPresenter.shared.displayLeftView(image)"], "syntax": "swift", "type": "codeListing"}, {"type": "paragraph", "inlineContent": [{"type": "image", "identifier": "https://user-images.githubusercontent.com/807039/*********-c93ffd31-a436-43d2-9eed-82d7cb23d8f6.gif"}]}, {"level": 3, "anchor": "Showing-progress", "text": "Showing progress", "type": "heading"}, {"inlineContent": [{"text": "See ", "type": "text"}, {"type": "reference", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/animateProgressBar(to:duration:completion:)", "isActive": true}], "type": "paragraph"}, {"syntax": "swift", "code": ["NotificationPresenter.shared.present(\"Animating Progress…\") { presenter in", "  presenter.animate<PERSON>rog<PERSON><PERSON><PERSON>(to: 1.0, duration: 0.75) { presenter in", "    presenter.dismiss()", "  }", "}", "", "// or set an explicit percentage manually (without animation)", "NotificationPresenter.shared.displayProgressBar(at: 0.0)"], "type": "codeListing"}, {"inlineContent": [{"type": "image", "identifier": "https://user-images.githubusercontent.com/807039/*********-e1aba466-85fa-4e32-951a-cd368c7d553d.gif"}], "type": "paragraph"}, {"type": "heading", "anchor": "Using-other-included-styles", "level": 3, "text": "Using other included styles"}, {"type": "paragraph", "inlineContent": [{"text": "There’s a few included styles you can easily use with the following API:", "type": "text"}]}, {"inlineContent": [{"text": "See ", "type": "text"}, {"type": "reference", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/present(_:subtitle:includedStyle:duration:completion:)", "isActive": true}], "type": "paragraph"}, {"code": ["NotificationPresenter.shared.present(\"Ya<PERSON>, it works!\",", "                                     includedStyle: .success)"], "type": "codeListing", "syntax": "swift"}, {"type": "paragraph", "inlineContent": [{"type": "image", "identifier": "https://user-images.githubusercontent.com/807039/*********-3beeb659-b561-4e7c-9c66-6fbc683ae152.jpg"}]}, {"type": "heading", "text": "Using a custom UIView", "level": 3, "anchor": "Using-a-custom-UIView"}, {"type": "paragraph", "inlineContent": [{"type": "text", "text": "If you want full control over the notification content and styling, you can use your own custom UIView."}]}, {"type": "paragraph", "inlineContent": [{"text": "See ", "type": "text"}, {"type": "reference", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/presentCustomView(_:sizingController:styleName:completion:)", "isActive": true}]}, {"syntax": "swift", "code": ["// present a custom view", "let button = UIButton(type: .system, primaryAction: UIAction { _ in", "  NotificationPresenter.shared.dismiss()", "})", "button.setTitle(\"Dismiss!\", for: .normal)", "NotificationPresenter.shared.presentCustomView(button)"], "type": "codeListing"}, {"rows": [[[{"inlineContent": [{"text": "Light Mode", "type": "text"}], "type": "paragraph"}], [{"type": "paragraph", "inlineContent": [{"type": "text", "text": "Dark Mode"}]}]], [[{"type": "paragraph", "inlineContent": [{"identifier": "https://user-images.githubusercontent.com/807039/*********-7a75edbe-00b1-437b-8651-2e63a1ba63c8.gif", "type": "image"}]}], [{"type": "paragraph", "inlineContent": [{"type": "image", "identifier": "https://user-images.githubusercontent.com/807039/*********-b3745101-0723-4342-9a3a-32a868ea820e.gif"}]}]]], "type": "table", "header": "row"}, {"level": 2, "text": "Customization", "type": "heading", "anchor": "Customization"}, {"inlineContent": [{"type": "text", "text": "You have the option to easily create & use fully customized styles."}], "type": "paragraph"}, {"type": "paragraph", "inlineContent": [{"text": "See ", "type": "text"}, {"isActive": true, "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/updateDefaultStyle(_:)", "type": "reference"}, {"type": "text", "text": " and "}, {"type": "reference", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/addStyle(named:usingStyle:prepare:)", "isActive": true}]}, {"inlineContent": [{"text": "The ", "type": "text"}, {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/PrepareStyleClosure", "type": "reference", "isActive": true}, {"text": " provides a copy of the default style, which can then be modified. See the ", "type": "text"}, {"type": "reference", "isActive": true, "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle"}, {"type": "text", "text": " API for all options."}], "type": "paragraph"}, {"syntax": "swift", "code": ["// update default style", "NotificationPresenter.shared.updateDefaultStyle { style in", "   style.backgroundStyle.backgroundColor = .red", "   style.textStyle.textColor = .white", "   style.textStyle.font = UIFont.preferredFont(forTextStyle: .title3)", "   // and many more options", "   return style", "}", "", "// set a named custom style", "NotificationPresenter.shared.addStyle(named: \"xxx\") { style in", "   // ...", "   return style", "}"], "type": "codeListing"}], "kind": "content"}], "identifier": {"interfaceLanguage": "swift", "url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/GettingStarted"}, "kind": "article", "abstract": [{"type": "text", "text": "Find some simple examples below to get started. Most APIs can also be called from Objective-C."}], "schemaVersion": {"minor": 3, "patch": 0, "major": 0}, "sections": [], "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification"]]}, "variants": [{"traits": [{"interfaceLanguage": "swift"}], "paths": ["/documentation/jdstatusbarnotification/gettingstarted"]}], "references": {"https://user-images.githubusercontent.com/807039/*********-c93ffd31-a436-43d2-9eed-82d7cb23d8f6.gif": {"type": "image", "variants": [{"url": "https://user-images.githubusercontent.com/807039/*********-c93ffd31-a436-43d2-9eed-82d7cb23d8f6.gif", "traits": ["1x", "light"]}], "identifier": "https://user-images.githubusercontent.com/807039/*********-c93ffd31-a436-43d2-9eed-82d7cb23d8f6.gif", "alt": "leftview"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/presentSwiftView(styleName:viewBuilder:completion:)": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/presentSwiftView(styleName:viewBuilder:completion:)", "type": "topic", "fragments": [{"kind": "keyword", "text": "func"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "presentSwiftView"}, {"kind": "text", "text": "("}, {"kind": "externalParam", "text": "styleName"}, {"kind": "text", "text": ": "}, {"text": "String", "preciseIdentifier": "s:SS", "kind": "typeIdentifier"}, {"text": "?, ", "kind": "text"}, {"kind": "externalParam", "text": "viewBuilder"}, {"text": ": () -> ", "kind": "text"}, {"text": "some", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"preciseIdentifier": "s:7SwiftUI4ViewP", "text": "View", "kind": "typeIdentifier"}, {"text": ", ", "kind": "text"}, {"text": "completion", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"kind": "typeIdentifier", "text": "Completion", "preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona"}, {"kind": "text", "text": "?) -> "}, {"text": "UIView", "kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIView"}], "abstract": [{"text": "Present a notification using a custom SwiftUI view.", "type": "text"}], "title": "presentSwiftView(styleName:viewBuilder:completion:)", "role": "symbol", "url": "/documentation/jdstatusbarnotification/notificationpresenter/presentswiftview(stylename:viewbuilder:completion:)", "kind": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/displayLeftView(_:)": {"type": "topic", "url": "/documentation/jdstatusbarnotification/notificationpresenter/displayleftview(_:)", "abstract": [{"text": "Displays a view on the left side of the text.", "type": "text"}, {"type": "text", "text": " "}, {"type": "text", "text": "The layout is defined by the "}, {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle", "isActive": true, "type": "reference"}, {"type": "text", "text": "."}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/displayLeftView(_:)", "kind": "symbol", "fragments": [{"text": "func", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "displayLeftView"}, {"text": "(", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIView", "text": "UIView"}, {"kind": "text", "text": "?)"}], "title": "displayLeftView(_:)", "role": "symbol"}, "https://user-images.githubusercontent.com/807039/*********-b3745101-0723-4342-9a3a-32a868ea820e.gif": {"identifier": "https://user-images.githubusercontent.com/807039/*********-b3745101-0723-4342-9a3a-32a868ea820e.gif", "type": "image", "alt": "customView2", "variants": [{"url": "https://user-images.githubusercontent.com/807039/*********-b3745101-0723-4342-9a3a-32a868ea820e.gif", "traits": ["1x", "light"]}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle": {"navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationLeftViewStyle"}], "type": "topic", "kind": "symbol", "title": "StatusBarNotificationLeftViewStyle", "abstract": [{"text": "Defines the appearance of a left-view, if set. It also applies to the activity indicator.", "type": "text"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewstyle", "role": "symbol", "fragments": [{"text": "class", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "StatusBarNotificationLeftViewStyle", "kind": "identifier"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle"}, "https://user-images.githubusercontent.com/807039/*********-7a75edbe-00b1-437b-8651-2e63a1ba63c8.gif": {"type": "image", "variants": [{"url": "https://user-images.githubusercontent.com/807039/*********-7a75edbe-00b1-437b-8651-2e63a1ba63c8.gif", "traits": ["1x", "light"]}], "identifier": "https://user-images.githubusercontent.com/807039/*********-7a75edbe-00b1-437b-8651-2e63a1ba63c8.gif", "alt": "customView"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/displayActivityIndicator(_:)": {"abstract": [{"type": "text", "text": "Displays an activity indicator as the notifications left view."}], "role": "symbol", "type": "topic", "url": "/documentation/jdstatusbarnotification/notificationpresenter/displayactivityindicator(_:)", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/displayActivityIndicator(_:)", "kind": "symbol", "fragments": [{"kind": "keyword", "text": "func"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "displayActivityIndicator"}, {"kind": "text", "text": "("}, {"kind": "typeIdentifier", "preciseIdentifier": "s:Sb", "text": "Bool"}, {"kind": "text", "text": ")"}], "title": "displayActivityIndicator(_:)"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/presentCustomView(_:sizingController:styleName:completion:)": {"abstract": [{"text": "Present a notification using a custom subview.", "type": "text"}], "kind": "symbol", "role": "symbol", "type": "topic", "title": "presentCustomView(_:sizingController:styleName:completion:)", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/presentCustomView(_:sizingController:styleName:completion:)", "fragments": [{"text": "func", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "presentCustom<PERSON>iew", "kind": "identifier"}, {"kind": "text", "text": "("}, {"kind": "typeIdentifier", "text": "UIView", "preciseIdentifier": "c:objc(cs)UIView"}, {"kind": "text", "text": ", "}, {"kind": "externalParam", "text": "sizingController"}, {"text": ": ", "kind": "text"}, {"preciseIdentifier": "c:@M@JDStatusBarNotification@objc(pl)JDStatusBarNotificationPresenterCustomViewSizingController", "text": "NotificationPresenterCustomViewSizingController", "kind": "typeIdentifier"}, {"kind": "text", "text": "?, "}, {"text": "styleName", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"text": "String", "kind": "typeIdentifier", "preciseIdentifier": "s:SS"}, {"text": "?, ", "kind": "text"}, {"kind": "externalParam", "text": "completion"}, {"kind": "text", "text": ": "}, {"kind": "typeIdentifier", "preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona", "text": "Completion"}, {"text": "?) -> ", "kind": "text"}, {"text": "UIView", "kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIView"}], "url": "/documentation/jdstatusbarnotification/notificationpresenter/presentcustomview(_:sizingcontroller:stylename:completion:)"}, "https://user-images.githubusercontent.com/807039/*********-e1aba466-85fa-4e32-951a-cd368c7d553d.gif": {"identifier": "https://user-images.githubusercontent.com/807039/*********-e1aba466-85fa-4e32-951a-cd368c7d553d.gif", "type": "image", "alt": "progress", "variants": [{"url": "https://user-images.githubusercontent.com/807039/*********-e1aba466-85fa-4e32-951a-cd368c7d553d.gif", "traits": ["1x", "light"]}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/PrepareStyleClosure": {"title": "NotificationPresenter.PrepareStyleClosure", "navigatorTitle": [{"text": "PrepareStyleClosure", "kind": "identifier"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/PrepareStyleClosure", "type": "topic", "kind": "symbol", "abstract": [{"text": "Creates a modified copy of an existing ", "type": "text"}, {"isActive": true, "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "type": "reference"}, {"type": "text", "text": " instance."}], "url": "/documentation/jdstatusbarnotification/notificationpresenter/preparestyleclosure", "fragments": [{"text": "typealias", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "PrepareStyleClosure"}], "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter": {"abstract": [{"type": "text", "text": "The NotificationPresenter let’s you present notifications below the statusBar."}, {"type": "text", "text": " "}, {"type": "text", "text": "You can customize the style (colors, fonts, etc.) and animations. It supports notch"}, {"type": "text", "text": " "}, {"type": "text", "text": "and no-notch devices, landscape & portrait layouts and Drag-to-Dismiss. It can display a"}, {"text": " ", "type": "text"}, {"type": "text", "text": "title, a subtitle, an activity indicator, an animated progress bar & custom views out of the box."}], "fragments": [{"text": "class", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "NotificationPresenter"}], "navigatorTitle": [{"text": "NotificationPresenter", "kind": "identifier"}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter", "url": "/documentation/jdstatusbarnotification/notificationpresenter", "type": "topic", "kind": "symbol", "title": "NotificationPresenter"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/updateDefaultStyle(_:)": {"role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/updateDefaultStyle(_:)", "title": "updateDefaultStyle(_:)", "kind": "symbol", "url": "/documentation/jdstatusbarnotification/notificationpresenter/updatedefaultstyle(_:)", "abstract": [{"type": "text", "text": "Defines a new default style."}, {"text": " ", "type": "text"}, {"text": "The new style will be used in all future presentations that have no specific style specified.", "type": "text"}], "fragments": [{"text": "func", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "updateDefaultStyle", "kind": "identifier"}, {"text": "(", "kind": "text"}, {"preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC19PrepareStyleClosurea", "text": "PrepareStyleClosure", "kind": "typeIdentifier"}, {"text": ")", "kind": "text"}], "type": "topic"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle", "navigatorTitle": [{"text": "StatusBarNotificationStyle", "kind": "identifier"}], "role": "symbol", "type": "topic", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "title": "StatusBarNotificationStyle", "abstract": [{"text": "A Style defines the appearance of a notification.", "type": "text"}], "fragments": [{"kind": "keyword", "text": "class"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationStyle", "kind": "identifier"}]}, "https://user-images.githubusercontent.com/807039/*********-c6255d41-4728-4bcb-bf72-fb12db01b5d5.gif": {"type": "image", "variants": [{"url": "https://user-images.githubusercontent.com/807039/*********-c6255d41-4728-4bcb-bf72-fb12db01b5d5.gif", "traits": ["1x", "light"]}], "identifier": "https://user-images.githubusercontent.com/807039/*********-c6255d41-4728-4bcb-bf72-fb12db01b5d5.gif", "alt": "activity"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/addStyle(named:usingStyle:prepare:)": {"url": "/documentation/jdstatusbarnotification/notificationpresenter/addstyle(named:usingstyle:prepare:)", "abstract": [{"text": "Adds a new named style - based on an included style, if given.", "type": "text"}, {"type": "text", "text": " "}, {"type": "text", "text": "This can later be used by referencing it using the "}, {"type": "codeVoice", "code": "styleName"}, {"text": ".", "type": "text"}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/addStyle(named:usingStyle:prepare:)", "kind": "symbol", "type": "topic", "fragments": [{"kind": "keyword", "text": "func"}, {"text": " ", "kind": "text"}, {"text": "addStyle", "kind": "identifier"}, {"text": "(", "kind": "text"}, {"text": "named", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "s:SS", "text": "String"}, {"kind": "text", "text": ", "}, {"text": "usingStyle", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"kind": "typeIdentifier", "text": "IncludedStatusBarNotificationStyle", "preciseIdentifier": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationIncludedStyle"}, {"text": ", ", "kind": "text"}, {"kind": "externalParam", "text": "prepare"}, {"text": ": ", "kind": "text"}, {"text": "PrepareStyleClosure", "kind": "typeIdentifier", "preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC19PrepareStyleClosurea"}, {"kind": "text", "text": ") -> "}, {"kind": "typeIdentifier", "preciseIdentifier": "s:SS", "text": "String"}], "title": "addStyle(named:usingStyle:prepare:)"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/animateProgressBar(to:duration:completion:)": {"type": "topic", "role": "symbol", "abstract": [{"type": "text", "text": "Displays a progress bar and animates it to the provided "}, {"code": "percentage", "type": "codeVoice"}, {"type": "text", "text": "."}], "title": "animateProgressBar(to:duration:completion:)", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/animateProgressBar(to:duration:completion:)", "url": "/documentation/jdstatusbarnotification/notificationpresenter/animateprogressbar(to:duration:completion:)", "fragments": [{"text": "func", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "animateProgressBar", "kind": "identifier"}, {"kind": "text", "text": "("}, {"text": "to", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"text": "Double", "preciseIdentifier": "s:Sd", "kind": "typeIdentifier"}, {"text": ", ", "kind": "text"}, {"text": "duration", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "s:Sd", "text": "Double", "kind": "typeIdentifier"}, {"kind": "text", "text": ", "}, {"kind": "externalParam", "text": "completion"}, {"text": ": ", "kind": "text"}, {"text": "Completion", "kind": "typeIdentifier", "preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona"}, {"kind": "text", "text": "?)"}], "kind": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/present(_:subtitle:includedStyle:duration:completion:)": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/present(_:subtitle:includedStyle:duration:completion:)", "url": "/documentation/jdstatusbarnotification/notificationpresenter/present(_:subtitle:includedstyle:duration:completion:)", "kind": "symbol", "title": "present(_:subtitle:includedStyle:duration:completion:)", "type": "topic", "abstract": [{"text": "Present a notification using an included style.", "type": "text"}], "role": "symbol", "fragments": [{"kind": "keyword", "text": "func"}, {"text": " ", "kind": "text"}, {"text": "present", "kind": "identifier"}, {"kind": "text", "text": "("}, {"text": "String", "kind": "typeIdentifier", "preciseIdentifier": "s:SS"}, {"kind": "text", "text": ", "}, {"text": "subtitle", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "s:SS", "kind": "typeIdentifier", "text": "String"}, {"kind": "text", "text": "?, "}, {"text": "includedStyle", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationIncludedStyle", "kind": "typeIdentifier", "text": "IncludedStatusBarNotificationStyle"}, {"text": ", ", "kind": "text"}, {"kind": "externalParam", "text": "duration"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "s:Sd", "text": "Double"}, {"text": "?, ", "kind": "text"}, {"text": "completion", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona", "text": "Completion"}, {"kind": "text", "text": "?) -> "}, {"kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIView", "text": "UIView"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/dismiss(animated:after:completion:)": {"url": "/documentation/jdstatusbarnotification/notificationpresenter/dismiss(animated:after:completion:)", "abstract": [{"type": "text", "text": "Dismisses any currently displayed notification animated - after the provided delay, if provided."}], "title": "dismiss(animated:after:completion:)", "kind": "symbol", "role": "symbol", "type": "topic", "fragments": [{"kind": "keyword", "text": "func"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "dismiss"}, {"kind": "text", "text": "("}, {"text": "animated", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "text": "Bool", "preciseIdentifier": "s:Sb"}, {"kind": "text", "text": ", "}, {"kind": "externalParam", "text": "after"}, {"text": ": ", "kind": "text"}, {"text": "Double", "preciseIdentifier": "s:Sd", "kind": "typeIdentifier"}, {"text": "?, ", "kind": "text"}, {"kind": "externalParam", "text": "completion"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona", "kind": "typeIdentifier", "text": "Completion"}, {"kind": "text", "text": "?)"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/dismiss(animated:after:completion:)"}, "https://user-images.githubusercontent.com/807039/*********-3beeb659-b561-4e7c-9c66-6fbc683ae152.jpg": {"type": "image", "variants": [{"url": "https://user-images.githubusercontent.com/807039/*********-3beeb659-b561-4e7c-9c66-6fbc683ae152.jpg", "traits": ["1x", "light"]}], "identifier": "https://user-images.githubusercontent.com/807039/*********-3beeb659-b561-4e7c-9c66-6fbc683ae152.jpg", "alt": "itworks"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/present(_:subtitle:styleName:duration:completion:)": {"kind": "symbol", "type": "topic", "url": "/documentation/jdstatusbarnotification/notificationpresenter/present(_:subtitle:stylename:duration:completion:)", "title": "present(_:subtitle:styleName:duration:completion:)", "abstract": [{"type": "text", "text": "Present a notification using the default style or a named style."}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/present(_:subtitle:styleName:duration:completion:)", "fragments": [{"text": "func", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "present", "kind": "identifier"}, {"kind": "text", "text": "("}, {"text": "String", "preciseIdentifier": "s:SS", "kind": "typeIdentifier"}, {"kind": "text", "text": ", "}, {"text": "subtitle", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"text": "String", "kind": "typeIdentifier", "preciseIdentifier": "s:SS"}, {"text": "?, ", "kind": "text"}, {"text": "styleName", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "s:SS", "text": "String", "kind": "typeIdentifier"}, {"kind": "text", "text": "?, "}, {"kind": "externalParam", "text": "duration"}, {"text": ": ", "kind": "text"}, {"text": "Double", "kind": "typeIdentifier", "preciseIdentifier": "s:Sd"}, {"kind": "text", "text": "?, "}, {"text": "completion", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona", "text": "Completion"}, {"kind": "text", "text": "?) -> "}, {"kind": "typeIdentifier", "text": "UIView", "preciseIdentifier": "c:objc(cs)UIView"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}}}