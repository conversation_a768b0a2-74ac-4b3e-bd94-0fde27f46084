{"seeAlsoSections": [{"generated": true, "title": "Notification Style", "identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle"]}], "identifier": {"interfaceLanguage": "swift", "url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundStyle"}, "sections": [], "relationshipsSections": [{"type": "inheritsFrom", "identifiers": ["doc://calimarkus.JDStatusBarNotification/objc(cs)NSObject"], "title": "Inherits From", "kind": "relationships"}, {"kind": "relationships", "type": "conformsTo", "identifiers": ["doc://calimarkus.JDStatusBarNotification/objc(pl)NSObject", "doc://calimarkus.JDStatusBarNotification/s7CVarArgP", "doc://calimarkus.JDStatusBarNotification/s28CustomDebugStringConvertibleP", "doc://calimarkus.JDStatusBarNotification/s23CustomStringConvertibleP", "doc://calimarkus.JDStatusBarNotification/SQ", "doc://calimarkus.JDStatusBarNotification/SH"], "title": "Conforms To"}], "metadata": {"externalID": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationBackgroundStyle", "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationBackgroundStyle"}], "roleHeading": "Class", "modules": [{"name": "JDStatusBarNotification"}], "fragments": [{"kind": "keyword", "text": "class"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "StatusBarNotificationBackgroundStyle"}], "role": "symbol", "title": "StatusBarNotificationBackgroundStyle", "symbolKind": "class"}, "primaryContentSections": [{"kind": "declarations", "declarations": [{"platforms": ["iOS"], "languages": ["swift"], "tokens": [{"text": "@objc", "kind": "attribute"}, {"text": "(JDStatusBarNotificationBackgroundStyle) ", "kind": "text"}, {"kind": "keyword", "text": "class"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "StatusBarNotificationBackgroundStyle"}]}]}], "kind": "symbol", "variants": [{"paths": ["/documentation/jdstatusbarnotification/statusbarnotificationbackgroundstyle"], "traits": [{"interfaceLanguage": "swift"}]}], "abstract": [{"text": "Defines the appearance of the notification background.", "type": "text"}], "topicSections": [{"title": "Instance Properties", "identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundStyle/backgroundColor", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundStyle/backgroundType", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundStyle/pillStyle"]}], "schemaVersion": {"patch": 0, "major": 0, "minor": 3}, "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification"]]}, "references": {"doc://calimarkus.JDStatusBarNotification/s7CVarArgP": {"identifier": "doc://calimarkus.JDStatusBarNotification/s7CVarArgP", "title": "Swift.CVarArg", "type": "unresolvable"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle": {"abstract": [{"text": "Defines the appearance of the pill, when using ", "type": "text"}, {"type": "reference", "isActive": true, "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/pill"}], "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationPillStyle"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle", "type": "topic", "kind": "symbol", "role": "symbol", "url": "/documentation/jdstatusbarnotification/statusbarnotificationpillstyle", "fragments": [{"kind": "keyword", "text": "class"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "StatusBarNotificationPillStyle"}], "title": "StatusBarNotificationPillStyle"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundStyle/pillStyle": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundStyle/pillStyle", "title": "pillStyle", "url": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundstyle/pillstyle", "kind": "symbol", "abstract": [{"text": "Defines the appearance of the pill, when using ", "type": "text"}, {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/pill", "isActive": true, "type": "reference"}], "type": "topic", "role": "symbol", "fragments": [{"text": "var", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "pillStyle", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"text": "StatusBarNotificationPillStyle", "kind": "typeIdentifier", "preciseIdentifier": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationPillStyle"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/pill": {"title": "StatusBarNotificationBackgroundType.pill", "url": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundtype/pill", "abstract": [{"type": "text", "text": "The background is a floating pill around the text. The pill size and appearance can be customized. This is the default."}], "kind": "symbol", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/pill", "role": "symbol", "fragments": [{"kind": "keyword", "text": "case"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "pill"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle": {"title": "StatusBarNotificationTextStyle", "fragments": [{"kind": "keyword", "text": "class"}, {"text": " ", "kind": "text"}, {"text": "StatusBarNotificationTextStyle", "kind": "identifier"}], "role": "symbol", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle", "url": "/documentation/jdstatusbarnotification/statusbarnotificationtextstyle", "type": "topic", "navigatorTitle": [{"text": "StatusBarNotificationTextStyle", "kind": "identifier"}], "abstract": [{"type": "text", "text": "Defines the appearance of a text label."}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundStyle": {"fragments": [{"text": "class", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "StatusBarNotificationBackgroundStyle", "kind": "identifier"}], "kind": "symbol", "navigatorTitle": [{"text": "StatusBarNotificationBackgroundStyle", "kind": "identifier"}], "role": "symbol", "url": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundstyle", "title": "StatusBarNotificationBackgroundStyle", "type": "topic", "abstract": [{"type": "text", "text": "Defines the appearance of the notification background."}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundStyle"}, "doc://calimarkus.JDStatusBarNotification/s28CustomDebugStringConvertibleP": {"title": "Swift.CustomDebugStringConvertible", "type": "unresolvable", "identifier": "doc://calimarkus.JDStatusBarNotification/s28CustomDebugStringConvertibleP"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundStyle/backgroundType": {"abstract": [{"text": "The background type. De<PERSON><PERSON> is ", "type": "text"}, {"isActive": true, "type": "reference", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/pill"}], "title": "backgroundType", "url": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundstyle/backgroundtype", "fragments": [{"kind": "keyword", "text": "var"}, {"text": " ", "kind": "text"}, {"text": "backgroundType", "kind": "identifier"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "text": "StatusBarNotificationBackgroundType", "preciseIdentifier": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationBackgroundType"}], "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundStyle/backgroundType", "kind": "symbol", "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/s23CustomStringConvertibleP": {"title": "Swift.CustomStringConvertible", "identifier": "doc://calimarkus.JDStatusBarNotification/s23CustomStringConvertibleP", "type": "unresolvable"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle", "navigatorTitle": [{"text": "StatusBarNotificationStyle", "kind": "identifier"}], "role": "symbol", "type": "topic", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "title": "StatusBarNotificationStyle", "abstract": [{"text": "A Style defines the appearance of a notification.", "type": "text"}], "fragments": [{"kind": "keyword", "text": "class"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationStyle", "kind": "identifier"}]}, "doc://calimarkus.JDStatusBarNotification/SH": {"title": "<PERSON><PERSON>", "identifier": "doc://calimarkus.JDStatusBarNotification/SH", "type": "unresolvable"}, "doc://calimarkus.JDStatusBarNotification/objc(pl)NSObject": {"title": "ObjectiveC.NSObjectProtocol", "identifier": "doc://calimarkus.JDStatusBarNotification/objc(pl)NSObject", "type": "unresolvable"}, "doc://calimarkus.JDStatusBarNotification/objc(cs)NSObject": {"title": "ObjectiveC.NSObject", "identifier": "doc://calimarkus.JDStatusBarNotification/objc(cs)NSObject", "type": "unresolvable"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle": {"navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationLeftViewStyle"}], "type": "topic", "kind": "symbol", "title": "StatusBarNotificationLeftViewStyle", "abstract": [{"text": "Defines the appearance of a left-view, if set. It also applies to the activity indicator.", "type": "text"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewstyle", "role": "symbol", "fragments": [{"text": "class", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "StatusBarNotificationLeftViewStyle", "kind": "identifier"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle": {"type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle", "kind": "symbol", "role": "symbol", "fragments": [{"text": "class", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationProgressBarStyle", "kind": "identifier"}], "title": "StatusBarNotificationProgressBarStyle", "abstract": [{"type": "text", "text": "Defines the appearance of the progress bar."}], "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationProgressBarStyle"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarstyle"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundStyle/backgroundColor": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundStyle/backgroundColor", "kind": "symbol", "abstract": [{"type": "text", "text": "The background color of the notification bar"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundstyle/backgroundcolor", "title": "backgroundColor", "type": "topic", "role": "symbol", "fragments": [{"kind": "keyword", "text": "var"}, {"text": " ", "kind": "text"}, {"text": "backgroundColor", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "c:objc(cs)UIColor", "text": "UIColor", "kind": "typeIdentifier"}, {"text": "?", "kind": "text"}]}, "doc://calimarkus.JDStatusBarNotification/SQ": {"identifier": "doc://calimarkus.JDStatusBarNotification/SQ", "title": "Swift.Equatable", "type": "unresolvable"}}}