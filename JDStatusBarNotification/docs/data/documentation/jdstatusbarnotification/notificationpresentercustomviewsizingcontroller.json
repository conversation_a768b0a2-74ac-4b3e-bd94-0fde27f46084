{"metadata": {"role": "symbol", "fragments": [{"text": "protocol", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "NotificationPresenterCustomViewSizingController", "kind": "identifier"}], "symbolKind": "protocol", "navigatorTitle": [{"kind": "identifier", "text": "NotificationPresenterCustomViewSizingController"}], "title": "NotificationPresenterCustomViewSizingController", "modules": [{"name": "JDStatusBarNotification"}], "roleHeading": "Protocol", "externalID": "c:@M@JDStatusBarNotification@objc(pl)JDStatusBarNotificationPresenterCustomViewSizingController"}, "variants": [{"paths": ["/documentation/jdstatusbarnotification/notificationpresentercustomviewsizingcontroller"], "traits": [{"interfaceLanguage": "swift"}]}], "schemaVersion": {"major": 0, "minor": 3, "patch": 0}, "seeAlsoSections": [{"title": "Present a notification (using a custom view)", "generated": true, "identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/presentCustomView(_:sizingController:styleName:completion:)", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/presentSwiftView(styleName:viewBuilder:completion:)"]}], "identifier": {"interfaceLanguage": "swift", "url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenterCustomViewSizingController"}, "kind": "symbol", "topicSections": [{"identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenterCustomViewSizingController/sizeThatFits(in:)"], "title": "Instance Methods"}], "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter"]]}, "sections": [], "primaryContentSections": [{"kind": "declarations", "declarations": [{"tokens": [{"kind": "attribute", "text": "@objc"}, {"text": "(JDStatusBarNotificationPresenterCustomViewSizingController) ", "kind": "text"}, {"kind": "keyword", "text": "protocol"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "NotificationPresenterCustomViewSizingController"}], "platforms": ["iOS"], "languages": ["swift"]}]}], "abstract": [{"type": "text", "text": "A protocol for a custom controller, which controls the size of a presented custom view."}], "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter": {"abstract": [{"type": "text", "text": "The NotificationPresenter let’s you present notifications below the statusBar."}, {"type": "text", "text": " "}, {"type": "text", "text": "You can customize the style (colors, fonts, etc.) and animations. It supports notch"}, {"type": "text", "text": " "}, {"type": "text", "text": "and no-notch devices, landscape & portrait layouts and Drag-to-Dismiss. It can display a"}, {"text": " ", "type": "text"}, {"type": "text", "text": "title, a subtitle, an activity indicator, an animated progress bar & custom views out of the box."}], "fragments": [{"text": "class", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "NotificationPresenter"}], "navigatorTitle": [{"text": "NotificationPresenter", "kind": "identifier"}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter", "url": "/documentation/jdstatusbarnotification/notificationpresenter", "type": "topic", "kind": "symbol", "title": "NotificationPresenter"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/presentCustomView(_:sizingController:styleName:completion:)": {"abstract": [{"text": "Present a notification using a custom subview.", "type": "text"}], "kind": "symbol", "role": "symbol", "type": "topic", "title": "presentCustomView(_:sizingController:styleName:completion:)", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/presentCustomView(_:sizingController:styleName:completion:)", "fragments": [{"text": "func", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "presentCustom<PERSON>iew", "kind": "identifier"}, {"kind": "text", "text": "("}, {"kind": "typeIdentifier", "text": "UIView", "preciseIdentifier": "c:objc(cs)UIView"}, {"kind": "text", "text": ", "}, {"kind": "externalParam", "text": "sizingController"}, {"text": ": ", "kind": "text"}, {"preciseIdentifier": "c:@M@JDStatusBarNotification@objc(pl)JDStatusBarNotificationPresenterCustomViewSizingController", "text": "NotificationPresenterCustomViewSizingController", "kind": "typeIdentifier"}, {"kind": "text", "text": "?, "}, {"text": "styleName", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"text": "String", "kind": "typeIdentifier", "preciseIdentifier": "s:SS"}, {"text": "?, ", "kind": "text"}, {"kind": "externalParam", "text": "completion"}, {"kind": "text", "text": ": "}, {"kind": "typeIdentifier", "preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona", "text": "Completion"}, {"text": "?) -> ", "kind": "text"}, {"text": "UIView", "kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIView"}], "url": "/documentation/jdstatusbarnotification/notificationpresenter/presentcustomview(_:sizingcontroller:stylename:completion:)"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenterCustomViewSizingController/sizeThatFits(in:)": {"type": "topic", "role": "symbol", "title": "sizeThatFits(in:)", "abstract": [], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenterCustomViewSizingController/sizeThatFits(in:)", "fragments": [{"text": "func", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "sizeThatFits", "kind": "identifier"}, {"kind": "text", "text": "("}, {"text": "in", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"text": "CGSize", "preciseIdentifier": "c:@S@CGSize", "kind": "typeIdentifier"}, {"kind": "text", "text": ") -> "}, {"text": "CGSize", "preciseIdentifier": "c:@S@CGSize", "kind": "typeIdentifier"}], "required": true, "kind": "symbol", "url": "/documentation/jdstatusbarnotification/notificationpresentercustomviewsizingcontroller/sizethatfits(in:)"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/presentSwiftView(styleName:viewBuilder:completion:)": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter/presentSwiftView(styleName:viewBuilder:completion:)", "type": "topic", "fragments": [{"kind": "keyword", "text": "func"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "presentSwiftView"}, {"kind": "text", "text": "("}, {"kind": "externalParam", "text": "styleName"}, {"kind": "text", "text": ": "}, {"text": "String", "preciseIdentifier": "s:SS", "kind": "typeIdentifier"}, {"text": "?, ", "kind": "text"}, {"kind": "externalParam", "text": "viewBuilder"}, {"text": ": () -> ", "kind": "text"}, {"text": "some", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"preciseIdentifier": "s:7SwiftUI4ViewP", "text": "View", "kind": "typeIdentifier"}, {"text": ", ", "kind": "text"}, {"text": "completion", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"kind": "typeIdentifier", "text": "Completion", "preciseIdentifier": "s:23JDStatusBarNotification0C9PresenterC10Completiona"}, {"kind": "text", "text": "?) -> "}, {"text": "UIView", "kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIView"}], "abstract": [{"text": "Present a notification using a custom SwiftUI view.", "type": "text"}], "title": "presentSwiftView(styleName:viewBuilder:completion:)", "role": "symbol", "url": "/documentation/jdstatusbarnotification/notificationpresenter/presentswiftview(stylename:viewbuilder:completion:)", "kind": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenterCustomViewSizingController": {"abstract": [{"text": "A protocol for a custom controller, which controls the size of a presented custom view.", "type": "text"}], "role": "symbol", "type": "topic", "navigatorTitle": [{"kind": "identifier", "text": "NotificationPresenterCustomViewSizingController"}], "url": "/documentation/jdstatusbarnotification/notificationpresentercustomviewsizingcontroller", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenterCustomViewSizingController", "kind": "symbol", "fragments": [{"kind": "keyword", "text": "protocol"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "NotificationPresenterCustomViewSizingController"}], "title": "NotificationPresenterCustomViewSizingController"}}}