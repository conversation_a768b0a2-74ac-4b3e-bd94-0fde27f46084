{"primaryContentSections": [{"kind": "declarations", "declarations": [{"languages": ["swift"], "platforms": ["iOS"], "tokens": [{"text": "@objc", "kind": "attribute"}, {"kind": "text", "text": "(JDStatusBarNotificationTextStyle) "}, {"text": "class", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "StatusBarNotificationTextStyle"}]}]}], "sections": [], "kind": "symbol", "schemaVersion": {"major": 0, "minor": 3, "patch": 0}, "variants": [{"traits": [{"interfaceLanguage": "swift"}], "paths": ["/documentation/jdstatusbarnotification/statusbarnotificationtextstyle"]}], "topicSections": [{"title": "Initializers", "identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle/init()", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle/init(textColor:font:)"]}, {"title": "Instance Properties", "identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle/font", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle/shadowColor", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle/shadowOffset", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle/textColor", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle/textOffsetY", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle/textShadowColor", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle/textShadowOffset"]}], "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification"]]}, "identifier": {"interfaceLanguage": "swift", "url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle"}, "abstract": [{"text": "Defines the appearance of a text label.", "type": "text"}], "seeAlsoSections": [{"title": "Notification Style", "generated": true, "identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle"]}], "metadata": {"fragments": [{"text": "class", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "StatusBarNotificationTextStyle", "kind": "identifier"}], "symbolKind": "class", "roleHeading": "Class", "role": "symbol", "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationTextStyle"}], "modules": [{"name": "JDStatusBarNotification"}], "externalID": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationTextStyle", "title": "StatusBarNotificationTextStyle"}, "relationshipsSections": [{"title": "Inherits From", "kind": "relationships", "type": "inheritsFrom", "identifiers": ["doc://calimarkus.JDStatusBarNotification/objc(cs)NSObject"]}, {"title": "Conforms To", "type": "conformsTo", "kind": "relationships", "identifiers": ["doc://calimarkus.JDStatusBarNotification/objc(pl)NSObject", "doc://calimarkus.JDStatusBarNotification/s7CVarArgP", "doc://calimarkus.JDStatusBarNotification/s28CustomDebugStringConvertibleP", "doc://calimarkus.JDStatusBarNotification/s23CustomStringConvertibleP", "doc://calimarkus.JDStatusBarNotification/SQ", "doc://calimarkus.JDStatusBarNotification/SH"]}], "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle/textColor": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle/textColor", "role": "symbol", "type": "topic", "fragments": [{"kind": "keyword", "text": "var"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "textColor"}, {"kind": "text", "text": ": "}, {"text": "UIColor", "kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIColor"}, {"text": "?", "kind": "text"}], "abstract": [{"type": "text", "text": "The color of the  label."}], "kind": "symbol", "url": "/documentation/jdstatusbarnotification/statusbarnotificationtextstyle/textcolor", "title": "textColor"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle/textShadowOffset": {"type": "topic", "kind": "symbol", "role": "symbol", "deprecated": true, "fragments": [{"text": "var", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "textShadowOffset", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "c:@S@CGSize", "kind": "typeIdentifier", "text": "CGSize"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle/textShadowOffset", "abstract": [{"type": "text", "text": "The text shadow offset of the notification label. De<PERSON>ult is "}, {"code": "(1, 2)", "type": "codeVoice"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationtextstyle/textshadowoffset", "title": "textShadowOffset"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle/init()": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle/init()", "kind": "symbol", "title": "init()", "url": "/documentation/jdstatusbarnotification/statusbarnotificationtextstyle/init()", "type": "topic", "abstract": [], "fragments": [{"kind": "identifier", "text": "init"}, {"text": "()", "kind": "text"}], "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle/textShadowColor": {"fragments": [{"kind": "keyword", "text": "var"}, {"kind": "text", "text": " "}, {"text": "textShadowColor", "kind": "identifier"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIColor", "text": "UIColor"}, {"text": "?", "kind": "text"}], "type": "topic", "deprecated": true, "title": "textShadowColor", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle/textShadowColor", "abstract": [{"text": "The text shadow color, the default is ", "type": "text"}, {"type": "codeVoice", "code": "nil"}, {"type": "text", "text": ", meaning no shadow."}], "kind": "symbol", "url": "/documentation/jdstatusbarnotification/statusbarnotificationtextstyle/textshadowcolor", "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle/init(textColor:font:)": {"type": "topic", "kind": "symbol", "role": "symbol", "fragments": [{"kind": "identifier", "text": "init"}, {"kind": "text", "text": "("}, {"kind": "externalParam", "text": "textColor"}, {"kind": "text", "text": ": "}, {"text": "UIColor", "kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIColor"}, {"kind": "text", "text": "?, "}, {"text": "font", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIFont", "text": "UIFont"}, {"text": "?)", "kind": "text"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle/init(textColor:font:)", "abstract": [], "url": "/documentation/jdstatusbarnotification/statusbarnotificationtextstyle/init(textcolor:font:)", "title": "init(textColor:font:)"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle/shadowOffset": {"abstract": [{"type": "text", "text": "The text shadow offset of the notification label. De<PERSON>ult is "}, {"type": "codeVoice", "code": "(1, 2)"}], "type": "topic", "url": "/documentation/jdstatusbarnotification/statusbarnotificationtextstyle/shadowoffset", "role": "symbol", "fragments": [{"text": "var", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "shadowOffset"}, {"text": ": ", "kind": "text"}, {"text": "CGPoint", "kind": "typeIdentifier", "preciseIdentifier": "c:@S@CGPoint"}], "title": "shadowOffset", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle/shadowOffset", "kind": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle": {"title": "StatusBarNotificationTextStyle", "fragments": [{"kind": "keyword", "text": "class"}, {"text": " ", "kind": "text"}, {"text": "StatusBarNotificationTextStyle", "kind": "identifier"}], "role": "symbol", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle", "url": "/documentation/jdstatusbarnotification/statusbarnotificationtextstyle", "type": "topic", "navigatorTitle": [{"text": "StatusBarNotificationTextStyle", "kind": "identifier"}], "abstract": [{"type": "text", "text": "Defines the appearance of a text label."}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle/font": {"type": "topic", "kind": "symbol", "role": "symbol", "fragments": [{"kind": "keyword", "text": "var"}, {"text": " ", "kind": "text"}, {"text": "font", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"text": "UIFont", "kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIFont"}, {"kind": "text", "text": "?"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle/font", "abstract": [{"type": "text", "text": "The font of the label."}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationtextstyle/font", "title": "font"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle": {"abstract": [{"text": "Defines the appearance of the pill, when using ", "type": "text"}, {"type": "reference", "isActive": true, "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/pill"}], "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationPillStyle"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle", "type": "topic", "kind": "symbol", "role": "symbol", "url": "/documentation/jdstatusbarnotification/statusbarnotificationpillstyle", "fragments": [{"kind": "keyword", "text": "class"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "StatusBarNotificationPillStyle"}], "title": "StatusBarNotificationPillStyle"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/pill": {"title": "StatusBarNotificationBackgroundType.pill", "url": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundtype/pill", "abstract": [{"type": "text", "text": "The background is a floating pill around the text. The pill size and appearance can be customized. This is the default."}], "kind": "symbol", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/pill", "role": "symbol", "fragments": [{"kind": "keyword", "text": "case"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "pill"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle", "navigatorTitle": [{"text": "StatusBarNotificationStyle", "kind": "identifier"}], "role": "symbol", "type": "topic", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "title": "StatusBarNotificationStyle", "abstract": [{"text": "A Style defines the appearance of a notification.", "type": "text"}], "fragments": [{"kind": "keyword", "text": "class"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationStyle", "kind": "identifier"}]}, "doc://calimarkus.JDStatusBarNotification/SH": {"identifier": "doc://calimarkus.JDStatusBarNotification/SH", "type": "unresolvable", "title": "<PERSON><PERSON>"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle": {"navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationLeftViewStyle"}], "type": "topic", "kind": "symbol", "title": "StatusBarNotificationLeftViewStyle", "abstract": [{"text": "Defines the appearance of a left-view, if set. It also applies to the activity indicator.", "type": "text"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewstyle", "role": "symbol", "fragments": [{"text": "class", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "StatusBarNotificationLeftViewStyle", "kind": "identifier"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle"}, "doc://calimarkus.JDStatusBarNotification/objc(pl)NSObject": {"identifier": "doc://calimarkus.JDStatusBarNotification/objc(pl)NSObject", "type": "unresolvable", "title": "ObjectiveC.NSObjectProtocol"}, "doc://calimarkus.JDStatusBarNotification/s7CVarArgP": {"identifier": "doc://calimarkus.JDStatusBarNotification/s7CVarArgP", "type": "unresolvable", "title": "Swift.CVarArg"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/s28CustomDebugStringConvertibleP": {"identifier": "doc://calimarkus.JDStatusBarNotification/s28CustomDebugStringConvertibleP", "type": "unresolvable", "title": "Swift.CustomDebugStringConvertible"}, "doc://calimarkus.JDStatusBarNotification/SQ": {"identifier": "doc://calimarkus.JDStatusBarNotification/SQ", "type": "unresolvable", "title": "Swift.Equatable"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle": {"type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle", "kind": "symbol", "role": "symbol", "fragments": [{"text": "class", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationProgressBarStyle", "kind": "identifier"}], "title": "StatusBarNotificationProgressBarStyle", "abstract": [{"type": "text", "text": "Defines the appearance of the progress bar."}], "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationProgressBarStyle"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarstyle"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle/shadowColor": {"kind": "symbol", "role": "symbol", "fragments": [{"text": "var", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "shadowColor"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "c:objc(cs)UIColor", "text": "UIColor", "kind": "typeIdentifier"}, {"text": "?", "kind": "text"}], "title": "shadowColor", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle/shadowColor", "url": "/documentation/jdstatusbarnotification/statusbarnotificationtextstyle/shadowcolor", "abstract": [{"text": "The text shadow color, the default is ", "type": "text"}, {"code": "nil", "type": "codeVoice"}, {"type": "text", "text": ", meaning no shadow."}]}, "doc://calimarkus.JDStatusBarNotification/objc(cs)NSObject": {"type": "unresolvable", "identifier": "doc://calimarkus.JDStatusBarNotification/objc(cs)NSObject", "title": "ObjectiveC.NSObject"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundStyle": {"fragments": [{"text": "class", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "StatusBarNotificationBackgroundStyle", "kind": "identifier"}], "kind": "symbol", "navigatorTitle": [{"text": "StatusBarNotificationBackgroundStyle", "kind": "identifier"}], "role": "symbol", "url": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundstyle", "title": "StatusBarNotificationBackgroundStyle", "type": "topic", "abstract": [{"type": "text", "text": "Defines the appearance of the notification background."}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundStyle"}, "doc://calimarkus.JDStatusBarNotification/s23CustomStringConvertibleP": {"type": "unresolvable", "identifier": "doc://calimarkus.JDStatusBarNotification/s23CustomStringConvertibleP", "title": "Swift.CustomStringConvertible"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle/textOffsetY": {"abstract": [{"type": "text", "text": "Offsets the text label on the y-axis. De<PERSON>ult is "}, {"code": "0.0", "type": "codeVoice"}, {"text": ".", "type": "text"}], "fragments": [{"text": "var", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "textOffsetY"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "s:Sd", "kind": "typeIdentifier", "text": "Double"}], "kind": "symbol", "type": "topic", "title": "textOffsetY", "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle/textOffsetY", "url": "/documentation/jdstatusbarnotification/statusbarnotificationtextstyle/textoffsety"}}}