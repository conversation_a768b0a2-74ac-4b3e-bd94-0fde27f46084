{"sections": [], "topicSections": [{"title": "Instance Properties", "identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle/borderColor", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle/borderWidth", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle/height", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle/minimumWidth", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle/shadowColor", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle/shadowOffset", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle/shadowOffsetXY", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle/shadowRadius", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle/topSpacing"]}], "metadata": {"modules": [{"name": "JDStatusBarNotification"}], "symbolKind": "class", "roleHeading": "Class", "role": "symbol", "fragments": [{"kind": "keyword", "text": "class"}, {"text": " ", "kind": "text"}, {"text": "StatusBarNotificationPillStyle", "kind": "identifier"}], "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationPillStyle"}], "externalID": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationPillStyle", "title": "StatusBarNotificationPillStyle"}, "primaryContentSections": [{"kind": "declarations", "declarations": [{"platforms": ["iOS"], "tokens": [{"text": "@objc", "kind": "attribute"}, {"text": "(JDStatusBarNotificationPillStyle) ", "kind": "text"}, {"kind": "keyword", "text": "class"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "StatusBarNotificationPillStyle"}], "languages": ["swift"]}]}], "seeAlsoSections": [{"generated": true, "identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle"], "title": "Notification Style"}], "abstract": [{"text": "Defines the appearance of the pill, when using ", "type": "text"}, {"isActive": true, "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/pill", "type": "reference"}], "variants": [{"paths": ["/documentation/jdstatusbarnotification/statusbarnotificationpillstyle"], "traits": [{"interfaceLanguage": "swift"}]}], "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification"]]}, "identifier": {"interfaceLanguage": "swift", "url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle"}, "relationshipsSections": [{"identifiers": ["doc://calimarkus.JDStatusBarNotification/objc(cs)NSObject"], "kind": "relationships", "title": "Inherits From", "type": "inheritsFrom"}, {"type": "conformsTo", "kind": "relationships", "identifiers": ["doc://calimarkus.JDStatusBarNotification/objc(pl)NSObject", "doc://calimarkus.JDStatusBarNotification/s7CVarArgP", "doc://calimarkus.JDStatusBarNotification/s28CustomDebugStringConvertibleP", "doc://calimarkus.JDStatusBarNotification/s23CustomStringConvertibleP", "doc://calimarkus.JDStatusBarNotification/SQ", "doc://calimarkus.JDStatusBarNotification/SH"], "title": "Conforms To"}], "schemaVersion": {"minor": 3, "major": 0, "patch": 0}, "kind": "symbol", "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle/borderWidth": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationpillstyle/borderwidth", "abstract": [{"type": "text", "text": "The width of the pill border. The default is "}, {"code": "2.0", "type": "codeVoice"}, {"type": "text", "text": "."}], "title": "borderWidth", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle/borderWidth", "role": "symbol", "kind": "symbol", "fragments": [{"text": "var", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "borderWidth", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"text": "Double", "kind": "typeIdentifier", "preciseIdentifier": "s:Sd"}]}, "doc://calimarkus.JDStatusBarNotification/objc(cs)NSObject": {"title": "ObjectiveC.NSObject", "identifier": "doc://calimarkus.JDStatusBarNotification/objc(cs)NSObject", "type": "unresolvable"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle/shadowOffsetXY": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle/shadowOffsetXY", "type": "topic", "kind": "symbol", "abstract": [{"text": "The shadow offset for the pill shadow. The default is ", "type": "text"}, {"type": "codeVoice", "code": "(0, 2)"}, {"type": "text", "text": "."}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationpillstyle/shadowoffsetxy", "role": "symbol", "fragments": [{"kind": "keyword", "text": "var"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "shadowOffsetXY"}, {"text": ": ", "kind": "text"}, {"text": "CGPoint", "kind": "typeIdentifier", "preciseIdentifier": "c:@S@CGPoint"}], "title": "shadowOffsetXY"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle/topSpacing": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle/topSpacing", "type": "topic", "fragments": [{"text": "var", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "topSpacing", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"kind": "typeIdentifier", "preciseIdentifier": "s:Sd", "text": "Double"}], "kind": "symbol", "role": "symbol", "abstract": [{"type": "text", "text": "The spacing between the pill and the statusbar or top of the screen.. De<PERSON><PERSON> is "}, {"type": "codeVoice", "code": "0.0"}, {"text": ".", "type": "text"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationpillstyle/topspacing", "title": "topSpacing"}, "doc://calimarkus.JDStatusBarNotification/SQ": {"title": "Swift.Equatable", "identifier": "doc://calimarkus.JDStatusBarNotification/SQ", "type": "unresolvable"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/pill": {"title": "StatusBarNotificationBackgroundType.pill", "url": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundtype/pill", "abstract": [{"type": "text", "text": "The background is a floating pill around the text. The pill size and appearance can be customized. This is the default."}], "kind": "symbol", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/pill", "role": "symbol", "fragments": [{"kind": "keyword", "text": "case"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "pill"}]}, "doc://calimarkus.JDStatusBarNotification/s28CustomDebugStringConvertibleP": {"title": "Swift.CustomDebugStringConvertible", "identifier": "doc://calimarkus.JDStatusBarNotification/s28CustomDebugStringConvertibleP", "type": "unresolvable"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle/shadowColor": {"abstract": [{"type": "text", "text": "The shadow color of the pill shadow. The default is "}, {"type": "codeVoice", "code": "nil"}, {"type": "text", "text": ", meaning no shadow."}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle/shadowColor", "kind": "symbol", "title": "shadowColor", "role": "symbol", "type": "topic", "url": "/documentation/jdstatusbarnotification/statusbarnotificationpillstyle/shadowcolor", "fragments": [{"text": "var", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "shadowColor", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"kind": "typeIdentifier", "preciseIdentifier": "c:objc(cs)UIColor", "text": "UIColor"}, {"text": "?", "kind": "text"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundStyle": {"fragments": [{"text": "class", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "StatusBarNotificationBackgroundStyle", "kind": "identifier"}], "kind": "symbol", "navigatorTitle": [{"text": "StatusBarNotificationBackgroundStyle", "kind": "identifier"}], "role": "symbol", "url": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundstyle", "title": "StatusBarNotificationBackgroundStyle", "type": "topic", "abstract": [{"type": "text", "text": "Defines the appearance of the notification background."}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundStyle"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle": {"type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle", "kind": "symbol", "role": "symbol", "fragments": [{"text": "class", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationProgressBarStyle", "kind": "identifier"}], "title": "StatusBarNotificationProgressBarStyle", "abstract": [{"type": "text", "text": "Defines the appearance of the progress bar."}], "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationProgressBarStyle"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarstyle"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle/shadowOffset": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationpillstyle/shadowoffset", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle/shadowOffset", "title": "shadowOffset", "role": "symbol", "fragments": [{"text": "var", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "shadowOffset", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"text": "CGSize", "preciseIdentifier": "c:@S@CGSize", "kind": "typeIdentifier"}], "deprecated": true, "type": "topic", "abstract": [{"text": "The shadow offset for the pill shadow. The default is ", "type": "text"}, {"code": "(0, 2)", "type": "codeVoice"}, {"type": "text", "text": "."}], "kind": "symbol"}, "doc://calimarkus.JDStatusBarNotification/SH": {"title": "<PERSON><PERSON>", "identifier": "doc://calimarkus.JDStatusBarNotification/SH", "type": "unresolvable"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle", "navigatorTitle": [{"text": "StatusBarNotificationStyle", "kind": "identifier"}], "role": "symbol", "type": "topic", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "title": "StatusBarNotificationStyle", "abstract": [{"text": "A Style defines the appearance of a notification.", "type": "text"}], "fragments": [{"kind": "keyword", "text": "class"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationStyle", "kind": "identifier"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle/minimumWidth": {"abstract": [{"type": "text", "text": "The minimum with of the pill. <PERSON><PERSON><PERSON> is "}, {"type": "codeVoice", "code": "200.0"}, {"type": "text", "text": "."}, {"type": "text", "text": " "}, {"type": "text", "text": "If this is lower than the pill height, the pill height is used as minimum width."}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationpillstyle/minimumwidth", "fragments": [{"kind": "keyword", "text": "var"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "minimumWidth"}, {"kind": "text", "text": ": "}, {"text": "Double", "preciseIdentifier": "s:Sd", "kind": "typeIdentifier"}], "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle/minimumWidth", "title": "minimumWidth", "role": "symbol", "type": "topic"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle": {"abstract": [{"text": "Defines the appearance of the pill, when using ", "type": "text"}, {"type": "reference", "isActive": true, "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/pill"}], "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationPillStyle"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle", "type": "topic", "kind": "symbol", "role": "symbol", "url": "/documentation/jdstatusbarnotification/statusbarnotificationpillstyle", "fragments": [{"kind": "keyword", "text": "class"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "StatusBarNotificationPillStyle"}], "title": "StatusBarNotificationPillStyle"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle/height": {"title": "height", "url": "/documentation/jdstatusbarnotification/statusbarnotificationpillstyle/height", "type": "topic", "abstract": [{"type": "text", "text": "The height of the pill. De<PERSON><PERSON> is "}, {"code": "50.0", "type": "codeVoice"}, {"type": "text", "text": "."}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle/height", "kind": "symbol", "fragments": [{"kind": "keyword", "text": "var"}, {"text": " ", "kind": "text"}, {"text": "height", "kind": "identifier"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "text": "Double", "preciseIdentifier": "s:Sd"}], "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle": {"title": "StatusBarNotificationTextStyle", "fragments": [{"kind": "keyword", "text": "class"}, {"text": " ", "kind": "text"}, {"text": "StatusBarNotificationTextStyle", "kind": "identifier"}], "role": "symbol", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle", "url": "/documentation/jdstatusbarnotification/statusbarnotificationtextstyle", "type": "topic", "navigatorTitle": [{"text": "StatusBarNotificationTextStyle", "kind": "identifier"}], "abstract": [{"type": "text", "text": "Defines the appearance of a text label."}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle/shadowRadius": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationpillstyle/shadowradius", "fragments": [{"kind": "keyword", "text": "var"}, {"kind": "text", "text": " "}, {"text": "shadowRadius", "kind": "identifier"}, {"text": ": ", "kind": "text"}, {"text": "Double", "kind": "typeIdentifier", "preciseIdentifier": "s:Sd"}], "role": "symbol", "type": "topic", "kind": "symbol", "abstract": [{"text": "The shadow radius of the pill shadow. The default is ", "type": "text"}, {"type": "codeVoice", "code": "4.0"}, {"type": "text", "text": "."}], "title": "shadowRadius", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle/shadowRadius"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/s7CVarArgP": {"identifier": "doc://calimarkus.JDStatusBarNotification/s7CVarArgP", "title": "Swift.CVarArg", "type": "unresolvable"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle/borderColor": {"type": "topic", "url": "/documentation/jdstatusbarnotification/statusbarnotificationpillstyle/bordercolor", "abstract": [{"type": "text", "text": "The border color of the pill. The default is "}, {"type": "codeVoice", "code": "nil"}, {"text": ", meaning no border.", "type": "text"}], "kind": "symbol", "title": "borderColor", "role": "symbol", "fragments": [{"kind": "keyword", "text": "var"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "borderColor"}, {"kind": "text", "text": ": "}, {"kind": "typeIdentifier", "text": "UIColor", "preciseIdentifier": "c:objc(cs)UIColor"}, {"text": "?", "kind": "text"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle/borderColor"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle": {"navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationLeftViewStyle"}], "type": "topic", "kind": "symbol", "title": "StatusBarNotificationLeftViewStyle", "abstract": [{"text": "Defines the appearance of a left-view, if set. It also applies to the activity indicator.", "type": "text"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewstyle", "role": "symbol", "fragments": [{"text": "class", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "StatusBarNotificationLeftViewStyle", "kind": "identifier"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle"}, "doc://calimarkus.JDStatusBarNotification/objc(pl)NSObject": {"identifier": "doc://calimarkus.JDStatusBarNotification/objc(pl)NSObject", "title": "ObjectiveC.NSObjectProtocol", "type": "unresolvable"}, "doc://calimarkus.JDStatusBarNotification/s23CustomStringConvertibleP": {"type": "unresolvable", "identifier": "doc://calimarkus.JDStatusBarNotification/s23CustomStringConvertibleP", "title": "Swift.CustomStringConvertible"}}}