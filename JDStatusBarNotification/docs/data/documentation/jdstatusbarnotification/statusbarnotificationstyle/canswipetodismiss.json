{"seeAlsoSections": [{"title": "Notification Bar Behavior", "identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/animationType", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/canDismissDuringUserInteraction", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/canTapToHold"], "generated": true}], "metadata": {"fragments": [{"kind": "keyword", "text": "var"}, {"text": " ", "kind": "text"}, {"text": "canSwipeToDismiss", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "s:Sb", "kind": "typeIdentifier", "text": "Bool"}], "modules": [{"name": "JDStatusBarNotification"}], "roleHeading": "Instance Property", "title": "canSwipeToDismiss", "role": "symbol", "symbolKind": "property", "externalID": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationStyle(py)canSwipeToDismiss"}, "primaryContentSections": [{"kind": "declarations", "declarations": [{"tokens": [{"kind": "attribute", "text": "@objc"}, {"text": " ", "kind": "text"}, {"text": "var", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "canSwipeToDismiss"}, {"text": ": ", "kind": "text"}, {"text": "Bool", "kind": "typeIdentifier", "preciseIdentifier": "s:Sb"}], "platforms": ["iOS"], "languages": ["swift"]}]}, {"kind": "content", "content": [{"level": 2, "text": "Discussion", "anchor": "discussion", "type": "heading"}, {"type": "paragraph", "inlineContent": [{"type": "text", "text": "Under the hood this enables/disables the internal "}, {"code": "PanGestureRecognizer", "type": "codeVoice"}, {"text": ".", "type": "text"}]}]}], "kind": "symbol", "schemaVersion": {"major": 0, "minor": 3, "patch": 0}, "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle"]]}, "sections": [], "variants": [{"paths": ["/documentation/jdstatusbarnotification/statusbarnotificationstyle/canswipetodismiss"], "traits": [{"interfaceLanguage": "swift"}]}], "identifier": {"url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/canSwipeToDismiss", "interfaceLanguage": "swift"}, "abstract": [{"type": "text", "text": "Defines if the bar can be dismissed by the user swiping up. Default is "}, {"code": "true", "type": "codeVoice"}, {"type": "text", "text": "."}], "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle", "navigatorTitle": [{"text": "StatusBarNotificationStyle", "kind": "identifier"}], "role": "symbol", "type": "topic", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "title": "StatusBarNotificationStyle", "abstract": [{"text": "A Style defines the appearance of a notification.", "type": "text"}], "fragments": [{"kind": "keyword", "text": "class"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationStyle", "kind": "identifier"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/canSwipeToDismiss": {"type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/canSwipeToDismiss", "kind": "symbol", "role": "symbol", "fragments": [{"kind": "keyword", "text": "var"}, {"kind": "text", "text": " "}, {"text": "canSwipeToDismiss", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"text": "Bool", "kind": "typeIdentifier", "preciseIdentifier": "s:Sb"}], "title": "canSwipeToDismiss", "abstract": [{"type": "text", "text": "Defines if the bar can be dismissed by the user swiping up. Default is "}, {"code": "true", "type": "codeVoice"}, {"type": "text", "text": "."}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle/canswipetodismiss"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/canDismissDuringUserInteraction": {"abstract": [{"type": "text", "text": "Defines if the bar is allowed to be dismissed while the user touches or pans the view."}], "role": "symbol", "url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle/candismissduringuserinteraction", "fragments": [{"text": "var", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "canDismissDuringUserInteraction"}, {"text": ": ", "kind": "text"}, {"preciseIdentifier": "s:Sb", "text": "Bool", "kind": "typeIdentifier"}], "type": "topic", "title": "canDismissDuringUserInteraction", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/canDismissDuringUserInteraction", "kind": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/animationType": {"type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/animationType", "kind": "symbol", "role": "symbol", "fragments": [{"kind": "keyword", "text": "var"}, {"kind": "text", "text": " "}, {"text": "animationType", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"text": "StatusBarNotificationAnimationType", "kind": "typeIdentifier", "preciseIdentifier": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationAnimationType"}], "title": "animationType", "abstract": [{"type": "text", "text": "Defines the animation used during presentation and dismissal of the notification."}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle/animationtype"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/canTapToHold": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle/cantaptohold", "abstract": [{"type": "text", "text": "Defines if the bar can be touched to prevent a dismissal until the tap is released. De<PERSON><PERSON> is "}, {"type": "codeVoice", "code": "true"}, {"text": ".", "type": "text"}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/canTapToHold", "kind": "symbol", "type": "topic", "fragments": [{"text": "var", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "canTapToHold"}, {"kind": "text", "text": ": "}, {"text": "Bool", "kind": "typeIdentifier", "preciseIdentifier": "s:Sb"}], "title": "canTapToHold"}}}