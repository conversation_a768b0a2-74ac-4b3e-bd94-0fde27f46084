{"metadata": {"role": "symbol", "title": "animationType", "modules": [{"name": "JDStatusBarNotification"}], "externalID": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationStyle(py)animationType", "fragments": [{"text": "var", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "animationType"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationAnimationType", "text": "StatusBarNotificationAnimationType", "kind": "typeIdentifier"}], "symbolKind": "property", "roleHeading": "Instance Property"}, "schemaVersion": {"major": 0, "minor": 3, "patch": 0}, "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle"]]}, "seeAlsoSections": [{"title": "Notification Bar Behavior", "generated": true, "identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/canDismissDuringUserInteraction", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/canSwipeToDismiss", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/canTapToHold"]}], "primaryContentSections": [{"kind": "declarations", "declarations": [{"platforms": ["iOS"], "tokens": [{"kind": "attribute", "text": "@objc"}, {"kind": "text", "text": " "}, {"text": "var", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "animationType", "kind": "identifier"}, {"text": ": ", "kind": "text"}, {"text": "StatusBarNotificationAnimationType", "kind": "typeIdentifier", "preciseIdentifier": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationAnimationType", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType"}], "languages": ["swift"]}]}, {"content": [{"type": "heading", "anchor": "discussion", "text": "Discussion", "level": 2}, {"type": "paragraph", "inlineContent": [{"text": "De<PERSON>ult is ", "type": "text"}, {"isActive": true, "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType/move", "type": "reference"}]}], "kind": "content"}], "variants": [{"traits": [{"interfaceLanguage": "swift"}], "paths": ["/documentation/jdstatusbarnotification/statusbarnotificationstyle/animationtype"]}], "sections": [], "abstract": [{"text": "Defines the animation used during presentation and dismissal of the notification.", "type": "text"}], "identifier": {"url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/animationType", "interfaceLanguage": "swift"}, "kind": "symbol", "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/canDismissDuringUserInteraction": {"abstract": [{"type": "text", "text": "Defines if the bar is allowed to be dismissed while the user touches or pans the view."}], "role": "symbol", "url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle/candismissduringuserinteraction", "fragments": [{"text": "var", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "canDismissDuringUserInteraction"}, {"text": ": ", "kind": "text"}, {"preciseIdentifier": "s:Sb", "text": "Bool", "kind": "typeIdentifier"}], "type": "topic", "title": "canDismissDuringUserInteraction", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/canDismissDuringUserInteraction", "kind": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/canSwipeToDismiss": {"type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/canSwipeToDismiss", "kind": "symbol", "role": "symbol", "fragments": [{"kind": "keyword", "text": "var"}, {"kind": "text", "text": " "}, {"text": "canSwipeToDismiss", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"text": "Bool", "kind": "typeIdentifier", "preciseIdentifier": "s:Sb"}], "title": "canSwipeToDismiss", "abstract": [{"type": "text", "text": "Defines if the bar can be dismissed by the user swiping up. Default is "}, {"code": "true", "type": "codeVoice"}, {"type": "text", "text": "."}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle/canswipetodismiss"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle", "navigatorTitle": [{"text": "StatusBarNotificationStyle", "kind": "identifier"}], "role": "symbol", "type": "topic", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "title": "StatusBarNotificationStyle", "abstract": [{"text": "A Style defines the appearance of a notification.", "type": "text"}], "fragments": [{"kind": "keyword", "text": "class"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationStyle", "kind": "identifier"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/canTapToHold": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle/cantaptohold", "abstract": [{"type": "text", "text": "Defines if the bar can be touched to prevent a dismissal until the tap is released. De<PERSON><PERSON> is "}, {"type": "codeVoice", "code": "true"}, {"text": ".", "type": "text"}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/canTapToHold", "kind": "symbol", "type": "topic", "fragments": [{"text": "var", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "canTapToHold"}, {"kind": "text", "text": ": "}, {"text": "Bool", "kind": "typeIdentifier", "preciseIdentifier": "s:Sb"}], "title": "canTapToHold"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType": {"abstract": [{"text": "Defines the animation used during presentation and dismissal of the notification.", "type": "text"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationanimationtype", "fragments": [{"kind": "keyword", "text": "enum"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationAnimationType", "kind": "identifier"}], "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationAnimationType"}], "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType", "title": "StatusBarNotificationAnimationType", "role": "symbol", "type": "topic"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType/move": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationAnimationType/move", "role": "symbol", "kind": "symbol", "url": "/documentation/jdstatusbarnotification/statusbarnotificationanimationtype/move", "abstract": [{"text": "Slide in from the top of the screen and slide back out to the top. This is the default.", "type": "text"}], "type": "topic", "fragments": [{"text": "case", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "move"}], "title": "StatusBarNotificationAnimationType.move"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/animationType": {"type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/animationType", "kind": "symbol", "role": "symbol", "fragments": [{"kind": "keyword", "text": "var"}, {"kind": "text", "text": " "}, {"text": "animationType", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"text": "StatusBarNotificationAnimationType", "kind": "typeIdentifier", "preciseIdentifier": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationAnimationType"}], "title": "animationType", "abstract": [{"type": "text", "text": "Defines the animation used during presentation and dismissal of the notification."}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle/animationtype"}}}