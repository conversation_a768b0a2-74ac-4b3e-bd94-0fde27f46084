{"metadata": {"symbolKind": "property", "externalID": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationStyle(py)canDismissDuringUserInteraction", "title": "canDismissDuringUserInteraction", "role": "symbol", "fragments": [{"text": "var", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "canDismissDuringUserInteraction"}, {"kind": "text", "text": ": "}, {"kind": "typeIdentifier", "preciseIdentifier": "s:Sb", "text": "Bool"}], "modules": [{"name": "JDStatusBarNotification"}], "roleHeading": "Instance Property"}, "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle"]]}, "primaryContentSections": [{"kind": "declarations", "declarations": [{"platforms": ["iOS"], "tokens": [{"kind": "attribute", "text": "@objc"}, {"text": " ", "kind": "text"}, {"kind": "keyword", "text": "var"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "canDismissDuringUserInteraction"}, {"kind": "text", "text": ": "}, {"kind": "typeIdentifier", "text": "Bool", "preciseIdentifier": "s:Sb"}], "languages": ["swift"]}]}, {"kind": "content", "content": [{"type": "heading", "level": 2, "text": "Discussion", "anchor": "discussion"}, {"inlineContent": [{"type": "text", "text": "The default is "}, {"code": "false", "type": "codeVoice"}, {"type": "text", "text": ", meaning that a notification stays presented as long as a touch or pan is active."}, {"type": "text", "text": " "}, {"text": "Once the touch is released, the view will be dismised (if a dismiss call was made during the interaction).", "type": "text"}, {"text": " ", "type": "text"}, {"text": "Any passed-in dismiss completion block will still be executed, once the actual dismissal happened.", "type": "text"}], "type": "paragraph"}]}], "identifier": {"url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/canDismissDuringUserInteraction", "interfaceLanguage": "swift"}, "variants": [{"traits": [{"interfaceLanguage": "swift"}], "paths": ["/documentation/jdstatusbarnotification/statusbarnotificationstyle/candismissduringuserinteraction"]}], "seeAlsoSections": [{"generated": true, "identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/animationType", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/canSwipeToDismiss", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/canTapToHold"], "title": "Notification Bar Behavior"}], "sections": [], "schemaVersion": {"minor": 3, "patch": 0, "major": 0}, "kind": "symbol", "abstract": [{"text": "Defines if the bar is allowed to be dismissed while the user touches or pans the view.", "type": "text"}], "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/animationType": {"type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/animationType", "kind": "symbol", "role": "symbol", "fragments": [{"kind": "keyword", "text": "var"}, {"kind": "text", "text": " "}, {"text": "animationType", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"text": "StatusBarNotificationAnimationType", "kind": "typeIdentifier", "preciseIdentifier": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationAnimationType"}], "title": "animationType", "abstract": [{"type": "text", "text": "Defines the animation used during presentation and dismissal of the notification."}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle/animationtype"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/canDismissDuringUserInteraction": {"abstract": [{"type": "text", "text": "Defines if the bar is allowed to be dismissed while the user touches or pans the view."}], "role": "symbol", "url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle/candismissduringuserinteraction", "fragments": [{"text": "var", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "canDismissDuringUserInteraction"}, {"text": ": ", "kind": "text"}, {"preciseIdentifier": "s:Sb", "text": "Bool", "kind": "typeIdentifier"}], "type": "topic", "title": "canDismissDuringUserInteraction", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/canDismissDuringUserInteraction", "kind": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/canTapToHold": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle/cantaptohold", "abstract": [{"type": "text", "text": "Defines if the bar can be touched to prevent a dismissal until the tap is released. De<PERSON><PERSON> is "}, {"type": "codeVoice", "code": "true"}, {"text": ".", "type": "text"}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/canTapToHold", "kind": "symbol", "type": "topic", "fragments": [{"text": "var", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "canTapToHold"}, {"kind": "text", "text": ": "}, {"text": "Bool", "kind": "typeIdentifier", "preciseIdentifier": "s:Sb"}], "title": "canTapToHold"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/canSwipeToDismiss": {"type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/canSwipeToDismiss", "kind": "symbol", "role": "symbol", "fragments": [{"kind": "keyword", "text": "var"}, {"kind": "text", "text": " "}, {"text": "canSwipeToDismiss", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"text": "Bool", "kind": "typeIdentifier", "preciseIdentifier": "s:Sb"}], "title": "canSwipeToDismiss", "abstract": [{"type": "text", "text": "Defines if the bar can be dismissed by the user swiping up. Default is "}, {"code": "true", "type": "codeVoice"}, {"type": "text", "text": "."}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle/canswipetodismiss"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle", "navigatorTitle": [{"text": "StatusBarNotificationStyle", "kind": "identifier"}], "role": "symbol", "type": "topic", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "title": "StatusBarNotificationStyle", "abstract": [{"text": "A Style defines the appearance of a notification.", "type": "text"}], "fragments": [{"kind": "keyword", "text": "class"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationStyle", "kind": "identifier"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}}}