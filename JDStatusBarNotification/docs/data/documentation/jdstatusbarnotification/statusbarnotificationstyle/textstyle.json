{"identifier": {"url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/textStyle", "interfaceLanguage": "swift"}, "primaryContentSections": [{"declarations": [{"tokens": [{"kind": "attribute", "text": "@objc"}, {"kind": "text", "text": " "}, {"kind": "keyword", "text": "var"}, {"kind": "text", "text": " "}, {"text": "textStyle", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationTextStyle", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle", "text": "StatusBarNotificationTextStyle", "kind": "typeIdentifier"}], "languages": ["swift"], "platforms": ["iOS"]}], "kind": "declarations"}, {"kind": "content", "content": [{"level": 2, "anchor": "discussion", "text": "Discussion", "type": "heading"}, {"type": "paragraph", "inlineContent": [{"text": "Defaults: ", "type": "text"}, {"type": "codeVoice", "code": "UIFontTextStyleFootnote"}, {"text": ", color: ", "type": "text"}, {"code": ".gray", "type": "codeVoice"}, {"text": " and adjusts for dark mode.", "type": "text"}, {"type": "text", "text": " "}, {"text": "The title’s ", "type": "text"}, {"type": "codeVoice", "code": "textColor"}, {"type": "text", "text": " is also used for the activity indicator, unless an explicit "}, {"code": "leftViewStyle.tintColor", "type": "codeVoice"}, {"type": "text", "text": " is set."}, {"text": " ", "type": "text"}, {"type": "text", "text": "The title’s "}, {"type": "codeVoice", "code": "textOffsetY"}, {"text": " affects both the title, the subtitle and the left-view. And also the progressBar when using ", "type": "text"}, {"type": "codeVoice", "code": ".center"}, {"type": "text", "text": " positioning."}]}]}], "sections": [], "variants": [{"traits": [{"interfaceLanguage": "swift"}], "paths": ["/documentation/jdstatusbarnotification/statusbarnotificationstyle/textstyle"]}], "abstract": [{"text": "Defines the appearance of the title label.", "type": "text"}], "metadata": {"modules": [{"name": "JDStatusBarNotification"}], "role": "symbol", "symbolKind": "property", "fragments": [{"kind": "keyword", "text": "var"}, {"text": " ", "kind": "text"}, {"text": "textStyle", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationTextStyle", "text": "StatusBarNotificationTextStyle", "kind": "typeIdentifier"}], "title": "textStyle", "roleHeading": "Instance Property", "externalID": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationStyle(py)textStyle"}, "kind": "symbol", "seeAlsoSections": [{"identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/subtitleStyle"], "title": "Styling the text", "generated": true}], "schemaVersion": {"minor": 3, "major": 0, "patch": 0}, "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle"]]}, "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle", "navigatorTitle": [{"text": "StatusBarNotificationStyle", "kind": "identifier"}], "role": "symbol", "type": "topic", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "title": "StatusBarNotificationStyle", "abstract": [{"text": "A Style defines the appearance of a notification.", "type": "text"}], "fragments": [{"kind": "keyword", "text": "class"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationStyle", "kind": "identifier"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle": {"title": "StatusBarNotificationTextStyle", "fragments": [{"kind": "keyword", "text": "class"}, {"text": " ", "kind": "text"}, {"text": "StatusBarNotificationTextStyle", "kind": "identifier"}], "role": "symbol", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle", "url": "/documentation/jdstatusbarnotification/statusbarnotificationtextstyle", "type": "topic", "navigatorTitle": [{"text": "StatusBarNotificationTextStyle", "kind": "identifier"}], "abstract": [{"type": "text", "text": "Defines the appearance of a text label."}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/textStyle": {"type": "topic", "role": "symbol", "title": "textStyle", "abstract": [{"type": "text", "text": "Defines the appearance of the title label."}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/textStyle", "fragments": [{"kind": "keyword", "text": "var"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "textStyle"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationTextStyle", "text": "StatusBarNotificationTextStyle"}], "kind": "symbol", "url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle/textstyle"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/subtitleStyle": {"abstract": [{"type": "text", "text": "Defines the appearance of the subtitle label."}], "title": "subtitleStyle", "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/subtitleStyle", "url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle/subtitlestyle", "fragments": [{"text": "var", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "subtitleStyle"}, {"kind": "text", "text": ": "}, {"text": "StatusBarNotificationTextStyle", "kind": "typeIdentifier", "preciseIdentifier": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationTextStyle"}], "kind": "symbol", "type": "topic"}}}