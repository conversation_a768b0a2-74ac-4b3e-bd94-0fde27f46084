{"abstract": [{"text": "Defines the appearance of the notification background.", "type": "text"}], "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle"]]}, "schemaVersion": {"major": 0, "minor": 3, "patch": 0}, "kind": "symbol", "metadata": {"externalID": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationStyle(py)backgroundStyle", "role": "symbol", "fragments": [{"text": "var", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "backgroundStyle", "kind": "identifier"}, {"text": ": ", "kind": "text"}, {"preciseIdentifier": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationBackgroundStyle", "text": "StatusBarNotificationBackgroundStyle", "kind": "typeIdentifier"}], "symbolKind": "property", "title": "backgroundStyle", "roleHeading": "Instance Property", "modules": [{"name": "JDStatusBarNotification"}]}, "sections": [], "primaryContentSections": [{"declarations": [{"tokens": [{"text": "@objc", "kind": "attribute"}, {"text": " ", "kind": "text"}, {"kind": "keyword", "text": "var"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "backgroundStyle"}, {"kind": "text", "text": ": "}, {"text": "StatusBarNotificationBackgroundStyle", "preciseIdentifier": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationBackgroundStyle", "kind": "typeIdentifier", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundStyle"}], "languages": ["swift"], "platforms": ["iOS"]}], "kind": "declarations"}, {"kind": "content", "content": [{"text": "Discussion", "level": 2, "type": "heading", "anchor": "discussion"}, {"inlineContent": [{"text": "That includes the ", "type": "text"}, {"isActive": true, "type": "reference", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundStyle/backgroundColor"}, {"type": "text", "text": ","}, {"type": "text", "text": " "}, {"type": "text", "text": "the "}, {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundStyle/backgroundType", "type": "reference", "isActive": true}, {"text": " ", "type": "text"}, {"type": "text", "text": "and the "}, {"type": "reference", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundStyle/pillStyle", "isActive": true}, {"type": "text", "text": " (See "}, {"type": "reference", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle", "isActive": true}, {"type": "text", "text": ")."}], "type": "paragraph"}]}], "identifier": {"interfaceLanguage": "swift", "url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/backgroundStyle"}, "seeAlsoSections": [{"identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/systemStatusBarStyle"], "title": "Styling the background", "generated": true}], "variants": [{"traits": [{"interfaceLanguage": "swift"}], "paths": ["/documentation/jdstatusbarnotification/statusbarnotificationstyle/backgroundstyle"]}], "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/pill": {"title": "StatusBarNotificationBackgroundType.pill", "url": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundtype/pill", "abstract": [{"type": "text", "text": "The background is a floating pill around the text. The pill size and appearance can be customized. This is the default."}], "kind": "symbol", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/pill", "role": "symbol", "fragments": [{"kind": "keyword", "text": "case"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "pill"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle": {"abstract": [{"text": "Defines the appearance of the pill, when using ", "type": "text"}, {"type": "reference", "isActive": true, "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/pill"}], "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationPillStyle"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle", "type": "topic", "kind": "symbol", "role": "symbol", "url": "/documentation/jdstatusbarnotification/statusbarnotificationpillstyle", "fragments": [{"kind": "keyword", "text": "class"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "StatusBarNotificationPillStyle"}], "title": "StatusBarNotificationPillStyle"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/systemStatusBarStyle": {"abstract": [{"type": "text", "text": "Defines which "}, {"type": "codeVoice", "code": "UIStatusBarStyle"}, {"text": " should be used during presentation.", "type": "text"}], "kind": "symbol", "role": "symbol", "type": "topic", "title": "systemStatusBarStyle", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/systemStatusBarStyle", "fragments": [{"kind": "keyword", "text": "var"}, {"kind": "text", "text": " "}, {"text": "systemStatusBarStyle", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationSystemBarStyle", "text": "StatusBarNotificationSystemBarStyle", "kind": "typeIdentifier"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle/systemstatusbarstyle"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/backgroundStyle": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle/backgroundstyle", "type": "topic", "title": "backgroundStyle", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/backgroundStyle", "role": "symbol", "fragments": [{"kind": "keyword", "text": "var"}, {"kind": "text", "text": " "}, {"text": "backgroundStyle", "kind": "identifier"}, {"text": ": ", "kind": "text"}, {"text": "StatusBarNotificationBackgroundStyle", "kind": "typeIdentifier", "preciseIdentifier": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationBackgroundStyle"}], "kind": "symbol", "abstract": [{"type": "text", "text": "Defines the appearance of the notification background."}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle", "navigatorTitle": [{"text": "StatusBarNotificationStyle", "kind": "identifier"}], "role": "symbol", "type": "topic", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "title": "StatusBarNotificationStyle", "abstract": [{"text": "A Style defines the appearance of a notification.", "type": "text"}], "fragments": [{"kind": "keyword", "text": "class"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationStyle", "kind": "identifier"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundStyle/backgroundType": {"abstract": [{"text": "The background type. De<PERSON><PERSON> is ", "type": "text"}, {"isActive": true, "type": "reference", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/pill"}], "title": "backgroundType", "url": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundstyle/backgroundtype", "fragments": [{"kind": "keyword", "text": "var"}, {"text": " ", "kind": "text"}, {"text": "backgroundType", "kind": "identifier"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "text": "StatusBarNotificationBackgroundType", "preciseIdentifier": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationBackgroundType"}], "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundStyle/backgroundType", "kind": "symbol", "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundStyle": {"fragments": [{"text": "class", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "StatusBarNotificationBackgroundStyle", "kind": "identifier"}], "kind": "symbol", "navigatorTitle": [{"text": "StatusBarNotificationBackgroundStyle", "kind": "identifier"}], "role": "symbol", "url": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundstyle", "title": "StatusBarNotificationBackgroundStyle", "type": "topic", "abstract": [{"type": "text", "text": "Defines the appearance of the notification background."}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundStyle"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundStyle/pillStyle": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundStyle/pillStyle", "title": "pillStyle", "url": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundstyle/pillstyle", "kind": "symbol", "abstract": [{"text": "Defines the appearance of the pill, when using ", "type": "text"}, {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/pill", "isActive": true, "type": "reference"}], "type": "topic", "role": "symbol", "fragments": [{"text": "var", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "pillStyle", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"text": "StatusBarNotificationPillStyle", "kind": "typeIdentifier", "preciseIdentifier": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationPillStyle"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundStyle/backgroundColor": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundStyle/backgroundColor", "kind": "symbol", "abstract": [{"type": "text", "text": "The background color of the notification bar"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundstyle/backgroundcolor", "title": "backgroundColor", "type": "topic", "role": "symbol", "fragments": [{"kind": "keyword", "text": "var"}, {"text": " ", "kind": "text"}, {"text": "backgroundColor", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "c:objc(cs)UIColor", "text": "UIColor", "kind": "typeIdentifier"}, {"text": "?", "kind": "text"}]}}}