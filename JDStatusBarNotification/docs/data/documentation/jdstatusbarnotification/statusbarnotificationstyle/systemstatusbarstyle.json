{"hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle"]]}, "abstract": [{"text": "Defines which ", "type": "text"}, {"code": "UIStatusBarStyle", "type": "codeVoice"}, {"text": " should be used during presentation.", "type": "text"}], "schemaVersion": {"minor": 3, "patch": 0, "major": 0}, "variants": [{"paths": ["/documentation/jdstatusbarnotification/statusbarnotificationstyle/systemstatusbarstyle"], "traits": [{"interfaceLanguage": "swift"}]}], "kind": "symbol", "sections": [], "metadata": {"symbolKind": "property", "fragments": [{"text": "var", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "systemStatusBarStyle", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationSystemBarStyle", "text": "StatusBarNotificationSystemBarStyle", "kind": "typeIdentifier"}], "role": "symbol", "title": "systemStatusBarStyle", "roleHeading": "Instance Property", "modules": [{"name": "JDStatusBarNotification"}], "externalID": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationStyle(py)systemStatusBarStyle"}, "seeAlsoSections": [{"generated": true, "title": "Styling the background", "identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/backgroundStyle"]}], "primaryContentSections": [{"kind": "declarations", "declarations": [{"tokens": [{"text": "@objc", "kind": "attribute"}, {"text": " ", "kind": "text"}, {"text": "var", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "systemStatusBarStyle", "kind": "identifier"}, {"text": ": ", "kind": "text"}, {"text": "StatusBarNotificationSystemBarStyle", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle", "preciseIdentifier": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationSystemBarStyle", "kind": "typeIdentifier"}], "platforms": ["iOS"], "languages": ["swift"]}]}, {"content": [{"anchor": "discussion", "text": "Discussion", "level": 2, "type": "heading"}, {"type": "paragraph", "inlineContent": [{"text": "If you use ", "type": "text"}, {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/pill", "type": "reference", "isActive": true}, {"text": ", this is ignored.", "type": "text"}, {"text": " ", "type": "text"}, {"type": "text", "text": "The default is "}, {"type": "reference", "isActive": true, "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle/defaultStyle"}, {"text": ".", "type": "text"}]}], "kind": "content"}], "identifier": {"url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/systemStatusBarStyle", "interfaceLanguage": "swift"}, "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/pill": {"title": "StatusBarNotificationBackgroundType.pill", "url": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundtype/pill", "abstract": [{"type": "text", "text": "The background is a floating pill around the text. The pill size and appearance can be customized. This is the default."}], "kind": "symbol", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/pill", "role": "symbol", "fragments": [{"kind": "keyword", "text": "case"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "pill"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle/defaultStyle": {"type": "topic", "abstract": [{"text": "Matches the current viewController / window.", "type": "text"}], "title": "StatusBarNotificationSystemBarStyle.defaultStyle", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle/defaultStyle", "kind": "symbol", "url": "/documentation/jdstatusbarnotification/statusbarnotificationsystembarstyle/defaultstyle", "fragments": [{"text": "case", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "defaultStyle", "kind": "identifier"}], "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle": {"abstract": [{"text": "Defines which ", "type": "text"}, {"code": "UIStatusBarStyle", "type": "codeVoice"}, {"type": "text", "text": " should be used during presentation."}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationsystembarstyle", "fragments": [{"kind": "keyword", "text": "enum"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "StatusBarNotificationSystemBarStyle"}], "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationSystemBarStyle"}], "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle", "title": "StatusBarNotificationSystemBarStyle", "role": "symbol", "type": "topic"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/systemStatusBarStyle": {"abstract": [{"type": "text", "text": "Defines which "}, {"type": "codeVoice", "code": "UIStatusBarStyle"}, {"text": " should be used during presentation.", "type": "text"}], "kind": "symbol", "role": "symbol", "type": "topic", "title": "systemStatusBarStyle", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/systemStatusBarStyle", "fragments": [{"kind": "keyword", "text": "var"}, {"kind": "text", "text": " "}, {"text": "systemStatusBarStyle", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationSystemBarStyle", "text": "StatusBarNotificationSystemBarStyle", "kind": "typeIdentifier"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle/systemstatusbarstyle"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/backgroundStyle": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle/backgroundstyle", "type": "topic", "title": "backgroundStyle", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/backgroundStyle", "role": "symbol", "fragments": [{"kind": "keyword", "text": "var"}, {"kind": "text", "text": " "}, {"text": "backgroundStyle", "kind": "identifier"}, {"text": ": ", "kind": "text"}, {"text": "StatusBarNotificationBackgroundStyle", "kind": "typeIdentifier", "preciseIdentifier": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationBackgroundStyle"}], "kind": "symbol", "abstract": [{"type": "text", "text": "Defines the appearance of the notification background."}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle", "navigatorTitle": [{"text": "StatusBarNotificationStyle", "kind": "identifier"}], "role": "symbol", "type": "topic", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "title": "StatusBarNotificationStyle", "abstract": [{"text": "A Style defines the appearance of a notification.", "type": "text"}], "fragments": [{"kind": "keyword", "text": "class"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationStyle", "kind": "identifier"}]}}}