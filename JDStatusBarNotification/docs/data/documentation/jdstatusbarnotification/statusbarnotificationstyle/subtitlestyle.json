{"variants": [{"traits": [{"interfaceLanguage": "swift"}], "paths": ["/documentation/jdstatusbarnotification/statusbarnotificationstyle/subtitlestyle"]}], "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle"]]}, "identifier": {"interfaceLanguage": "swift", "url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/subtitleStyle"}, "kind": "symbol", "schemaVersion": {"patch": 0, "major": 0, "minor": 3}, "abstract": [{"type": "text", "text": "Defines the appearance of the subtitle label."}], "seeAlsoSections": [{"identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/textStyle"], "generated": true, "title": "Styling the text"}], "sections": [], "metadata": {"role": "symbol", "title": "subtitleStyle", "symbolKind": "property", "externalID": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationStyle(py)subtitleStyle", "modules": [{"name": "JDStatusBarNotification"}], "fragments": [{"kind": "keyword", "text": "var"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "subtitleStyle"}, {"text": ": ", "kind": "text"}, {"text": "StatusBarNotificationTextStyle", "kind": "typeIdentifier", "preciseIdentifier": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationTextStyle"}], "roleHeading": "Instance Property"}, "primaryContentSections": [{"declarations": [{"platforms": ["iOS"], "languages": ["swift"], "tokens": [{"kind": "attribute", "text": "@objc"}, {"text": " ", "kind": "text"}, {"text": "var", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "subtitleStyle"}, {"text": ": ", "kind": "text"}, {"text": "StatusBarNotificationTextStyle", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle", "kind": "typeIdentifier", "preciseIdentifier": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationTextStyle"}]}], "kind": "declarations"}, {"content": [{"anchor": "discussion", "type": "heading", "level": 2, "text": "Discussion"}, {"type": "paragraph", "inlineContent": [{"type": "text", "text": "Defaults: "}, {"code": "UIFontTextStyleCaption1", "type": "codeVoice"}, {"text": ", color: The title color at 66% opacity.", "type": "text"}]}, {"inlineContent": [{"text": "The subtitle’s .textOffsetY affects only the subtitle.", "type": "text"}], "type": "paragraph"}], "kind": "content"}], "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle": {"title": "StatusBarNotificationTextStyle", "fragments": [{"kind": "keyword", "text": "class"}, {"text": " ", "kind": "text"}, {"text": "StatusBarNotificationTextStyle", "kind": "identifier"}], "role": "symbol", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle", "url": "/documentation/jdstatusbarnotification/statusbarnotificationtextstyle", "type": "topic", "navigatorTitle": [{"text": "StatusBarNotificationTextStyle", "kind": "identifier"}], "abstract": [{"type": "text", "text": "Defines the appearance of a text label."}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/textStyle": {"type": "topic", "role": "symbol", "title": "textStyle", "abstract": [{"type": "text", "text": "Defines the appearance of the title label."}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/textStyle", "fragments": [{"kind": "keyword", "text": "var"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "textStyle"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationTextStyle", "text": "StatusBarNotificationTextStyle"}], "kind": "symbol", "url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle/textstyle"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle", "navigatorTitle": [{"text": "StatusBarNotificationStyle", "kind": "identifier"}], "role": "symbol", "type": "topic", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "title": "StatusBarNotificationStyle", "abstract": [{"text": "A Style defines the appearance of a notification.", "type": "text"}], "fragments": [{"kind": "keyword", "text": "class"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationStyle", "kind": "identifier"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/subtitleStyle": {"abstract": [{"type": "text", "text": "Defines the appearance of the subtitle label."}], "title": "subtitleStyle", "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/subtitleStyle", "url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle/subtitlestyle", "fragments": [{"text": "var", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "subtitleStyle"}, {"kind": "text", "text": ": "}, {"text": "StatusBarNotificationTextStyle", "kind": "typeIdentifier", "preciseIdentifier": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationTextStyle"}], "kind": "symbol", "type": "topic"}}}