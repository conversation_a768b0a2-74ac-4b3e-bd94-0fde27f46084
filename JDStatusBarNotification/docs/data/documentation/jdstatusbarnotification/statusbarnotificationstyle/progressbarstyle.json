{"abstract": [{"type": "text", "text": "Defines the appearance of the progress bar."}], "schemaVersion": {"major": 0, "minor": 3, "patch": 0}, "kind": "symbol", "variants": [{"traits": [{"interfaceLanguage": "swift"}], "paths": ["/documentation/jdstatusbarnotification/statusbarnotificationstyle/progressbarstyle"]}], "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle"]]}, "seeAlsoSections": [{"identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/leftViewStyle"], "generated": true, "title": "Styling supplementary views"}], "primaryContentSections": [{"declarations": [{"tokens": [{"kind": "attribute", "text": "@objc"}, {"text": " ", "kind": "text"}, {"text": "var", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "progressBarStyle"}, {"text": ": ", "kind": "text"}, {"text": "StatusBarNotificationProgressBarStyle", "preciseIdentifier": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationProgressBarStyle", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle", "kind": "typeIdentifier"}], "platforms": ["iOS"], "languages": ["swift"]}], "kind": "declarations"}], "metadata": {"fragments": [{"kind": "keyword", "text": "var"}, {"kind": "text", "text": " "}, {"text": "progressBarStyle", "kind": "identifier"}, {"text": ": ", "kind": "text"}, {"preciseIdentifier": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationProgressBarStyle", "text": "StatusBarNotificationProgressBarStyle", "kind": "typeIdentifier"}], "title": "progressBarStyle", "modules": [{"name": "JDStatusBarNotification"}], "role": "symbol", "roleHeading": "Instance Property", "externalID": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationStyle(py)progressBarStyle", "symbolKind": "property"}, "sections": [], "identifier": {"url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/progressBarStyle", "interfaceLanguage": "swift"}, "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/progressBarStyle": {"type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/progressBarStyle", "kind": "symbol", "abstract": [{"text": "Defines the appearance of the progress bar.", "type": "text"}], "role": "symbol", "fragments": [{"kind": "keyword", "text": "var"}, {"kind": "text", "text": " "}, {"text": "progressBarStyle", "kind": "identifier"}, {"text": ": ", "kind": "text"}, {"preciseIdentifier": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationProgressBarStyle", "kind": "typeIdentifier", "text": "StatusBarNotificationProgressBarStyle"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle/progressbarstyle", "title": "progressBarStyle"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle": {"type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle", "kind": "symbol", "role": "symbol", "fragments": [{"text": "class", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationProgressBarStyle", "kind": "identifier"}], "title": "StatusBarNotificationProgressBarStyle", "abstract": [{"type": "text", "text": "Defines the appearance of the progress bar."}], "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationProgressBarStyle"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarstyle"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle", "navigatorTitle": [{"text": "StatusBarNotificationStyle", "kind": "identifier"}], "role": "symbol", "type": "topic", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "title": "StatusBarNotificationStyle", "abstract": [{"text": "A Style defines the appearance of a notification.", "type": "text"}], "fragments": [{"kind": "keyword", "text": "class"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationStyle", "kind": "identifier"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/leftViewStyle": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle/leftviewstyle", "type": "topic", "fragments": [{"text": "var", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "leftViewStyle"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationLeftViewStyle", "text": "StatusBarNotificationLeftViewStyle", "kind": "typeIdentifier"}], "kind": "symbol", "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/leftViewStyle", "title": "leftViewStyle", "abstract": [{"text": "Defines the appearance of a left-view, if set. It also applies to the activity indicator.", "type": "text"}]}}}