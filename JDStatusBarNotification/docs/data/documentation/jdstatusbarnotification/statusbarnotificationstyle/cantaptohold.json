{"sections": [], "variants": [{"traits": [{"interfaceLanguage": "swift"}], "paths": ["/documentation/jdstatusbarnotification/statusbarnotificationstyle/cantaptohold"]}], "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle"]]}, "seeAlsoSections": [{"generated": true, "title": "Notification Bar Behavior", "identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/animationType", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/canDismissDuringUserInteraction", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/canSwipeToDismiss"]}], "metadata": {"roleHeading": "Instance Property", "modules": [{"name": "JDStatusBarNotification"}], "symbolKind": "property", "fragments": [{"text": "var", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "canTapToHold", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"kind": "typeIdentifier", "preciseIdentifier": "s:Sb", "text": "Bool"}], "role": "symbol", "externalID": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationStyle(py)canTapToHold", "title": "canTapToHold"}, "abstract": [{"type": "text", "text": "Defines if the bar can be touched to prevent a dismissal until the tap is released. De<PERSON><PERSON> is "}, {"code": "true", "type": "codeVoice"}, {"type": "text", "text": "."}], "primaryContentSections": [{"kind": "declarations", "declarations": [{"languages": ["swift"], "tokens": [{"text": "@objc", "kind": "attribute"}, {"text": " ", "kind": "text"}, {"kind": "keyword", "text": "var"}, {"text": " ", "kind": "text"}, {"text": "canTapToHold", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"kind": "typeIdentifier", "text": "Bool", "preciseIdentifier": "s:Sb"}], "platforms": ["iOS"]}]}, {"kind": "content", "content": [{"level": 2, "text": "Discussion", "anchor": "discussion", "type": "heading"}, {"inlineContent": [{"type": "text", "text": "If "}, {"type": "reference", "isActive": true, "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/canTapToHold"}, {"text": " is ", "type": "text"}, {"type": "codeVoice", "code": "true"}, {"type": "text", "text": " "}, {"text": "and ", "type": "text"}, {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/canDismissDuringUserInteraction", "isActive": true, "type": "reference"}, {"type": "text", "text": " is "}, {"code": "false", "type": "codeVoice"}, {"text": ",", "type": "text"}, {"text": " ", "type": "text"}, {"text": "the user can tap the notification to prevent it from being dismissed until the tap is released.", "type": "text"}], "type": "paragraph"}, {"inlineContent": [{"type": "text", "text": "If you are utilizing a custom view and need custom touch handling (e.g. for a button), you should set this to "}, {"type": "codeVoice", "code": "false"}, {"text": ".", "type": "text"}, {"text": " ", "type": "text"}, {"type": "text", "text": "Under the hood this enables/disables the internal "}, {"code": "LongPressGestureRecognizer", "type": "codeVoice"}, {"text": ".", "type": "text"}], "type": "paragraph"}]}], "identifier": {"url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/canTapToHold", "interfaceLanguage": "swift"}, "schemaVersion": {"minor": 3, "patch": 0, "major": 0}, "kind": "symbol", "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/animationType": {"type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/animationType", "kind": "symbol", "role": "symbol", "fragments": [{"kind": "keyword", "text": "var"}, {"kind": "text", "text": " "}, {"text": "animationType", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"text": "StatusBarNotificationAnimationType", "kind": "typeIdentifier", "preciseIdentifier": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationAnimationType"}], "title": "animationType", "abstract": [{"type": "text", "text": "Defines the animation used during presentation and dismissal of the notification."}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle/animationtype"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/canSwipeToDismiss": {"type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/canSwipeToDismiss", "kind": "symbol", "role": "symbol", "fragments": [{"kind": "keyword", "text": "var"}, {"kind": "text", "text": " "}, {"text": "canSwipeToDismiss", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"text": "Bool", "kind": "typeIdentifier", "preciseIdentifier": "s:Sb"}], "title": "canSwipeToDismiss", "abstract": [{"type": "text", "text": "Defines if the bar can be dismissed by the user swiping up. Default is "}, {"code": "true", "type": "codeVoice"}, {"type": "text", "text": "."}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle/canswipetodismiss"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/canTapToHold": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle/cantaptohold", "abstract": [{"type": "text", "text": "Defines if the bar can be touched to prevent a dismissal until the tap is released. De<PERSON><PERSON> is "}, {"type": "codeVoice", "code": "true"}, {"text": ".", "type": "text"}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/canTapToHold", "kind": "symbol", "type": "topic", "fragments": [{"text": "var", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "canTapToHold"}, {"kind": "text", "text": ": "}, {"text": "Bool", "kind": "typeIdentifier", "preciseIdentifier": "s:Sb"}], "title": "canTapToHold"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/canDismissDuringUserInteraction": {"abstract": [{"type": "text", "text": "Defines if the bar is allowed to be dismissed while the user touches or pans the view."}], "role": "symbol", "url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle/candismissduringuserinteraction", "fragments": [{"text": "var", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "canDismissDuringUserInteraction"}, {"text": ": ", "kind": "text"}, {"preciseIdentifier": "s:Sb", "text": "Bool", "kind": "typeIdentifier"}], "type": "topic", "title": "canDismissDuringUserInteraction", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle/canDismissDuringUserInteraction", "kind": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle", "navigatorTitle": [{"text": "StatusBarNotificationStyle", "kind": "identifier"}], "role": "symbol", "type": "topic", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "title": "StatusBarNotificationStyle", "abstract": [{"text": "A Style defines the appearance of a notification.", "type": "text"}], "fragments": [{"kind": "keyword", "text": "class"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationStyle", "kind": "identifier"}]}}}