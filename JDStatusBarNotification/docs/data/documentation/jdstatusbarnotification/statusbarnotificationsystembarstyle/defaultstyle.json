{"abstract": [{"type": "text", "text": "Matches the current viewController / window."}], "schemaVersion": {"major": 0, "minor": 3, "patch": 0}, "primaryContentSections": [{"kind": "declarations", "declarations": [{"platforms": ["iOS"], "tokens": [{"kind": "keyword", "text": "case"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "defaultStyle"}], "languages": ["swift"]}]}], "metadata": {"title": "StatusBarNotificationSystemBarStyle.defaultStyle", "externalID": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationSystemBarStyle@JDStatusBarNotificationSystemBarStyleDefaultStyle", "fragments": [{"text": "case", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "defaultStyle"}], "symbolKind": "case", "roleHeading": "Case", "role": "symbol", "modules": [{"name": "JDStatusBarNotification"}]}, "sections": [], "identifier": {"interfaceLanguage": "swift", "url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle/defaultStyle"}, "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle"]]}, "variants": [{"paths": ["/documentation/jdstatusbarnotification/statusbarnotificationsystembarstyle/defaultstyle"], "traits": [{"interfaceLanguage": "swift"}]}], "kind": "symbol", "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle/defaultStyle": {"type": "topic", "abstract": [{"text": "Matches the current viewController / window.", "type": "text"}], "title": "StatusBarNotificationSystemBarStyle.defaultStyle", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle/defaultStyle", "kind": "symbol", "url": "/documentation/jdstatusbarnotification/statusbarnotificationsystembarstyle/defaultstyle", "fragments": [{"text": "case", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "defaultStyle", "kind": "identifier"}], "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle", "navigatorTitle": [{"text": "StatusBarNotificationStyle", "kind": "identifier"}], "role": "symbol", "type": "topic", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "title": "StatusBarNotificationStyle", "abstract": [{"text": "A Style defines the appearance of a notification.", "type": "text"}], "fragments": [{"kind": "keyword", "text": "class"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationStyle", "kind": "identifier"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle": {"abstract": [{"text": "Defines which ", "type": "text"}, {"code": "UIStatusBarStyle", "type": "codeVoice"}, {"type": "text", "text": " should be used during presentation."}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationsystembarstyle", "fragments": [{"kind": "keyword", "text": "enum"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "StatusBarNotificationSystemBarStyle"}], "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationSystemBarStyle"}], "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle", "title": "StatusBarNotificationSystemBarStyle", "role": "symbol", "type": "topic"}}}