{"schemaVersion": {"major": 0, "minor": 3, "patch": 0}, "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle"]]}, "kind": "article", "topicSections": [{"generated": true, "title": "Instance Properties", "identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle/hashValue"]}, {"title": "Instance Methods", "generated": true, "identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle/hash(into:)"]}], "variants": [{"traits": [{"interfaceLanguage": "swift"}], "paths": ["/documentation/jdstatusbarnotification/statusbarnotificationsystembarstyle/rawrepresentable-implementations"]}], "metadata": {"title": "RawRepresentable Implementations", "modules": [{"name": "JDStatusBarNotification"}], "role": "collectionGroup"}, "identifier": {"interfaceLanguage": "swift", "url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle/RawRepresentable-Implementations"}, "sections": [], "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle", "navigatorTitle": [{"text": "StatusBarNotificationStyle", "kind": "identifier"}], "role": "symbol", "type": "topic", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "title": "StatusBarNotificationStyle", "abstract": [{"text": "A Style defines the appearance of a notification.", "type": "text"}], "fragments": [{"kind": "keyword", "text": "class"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationStyle", "kind": "identifier"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle/hash(into:)": {"title": "hash(into:)", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle/hash(into:)", "kind": "symbol", "url": "/documentation/jdstatusbarnotification/statusbarnotificationsystembarstyle/hash(into:)", "abstract": [], "type": "topic", "fragments": [{"text": "func", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "hash", "kind": "identifier"}, {"text": "(", "kind": "text"}, {"text": "into", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"text": "inout", "kind": "keyword"}, {"kind": "text", "text": " "}, {"preciseIdentifier": "s:s6HasherV", "kind": "typeIdentifier", "text": "<PERSON><PERSON>"}, {"text": ")", "kind": "text"}], "role": "symbol", "conformance": {"availabilityPrefix": [{"text": "Available when", "type": "text"}], "constraints": [{"type": "codeVoice", "code": "Self"}, {"text": " conforms to ", "type": "text"}, {"code": "<PERSON><PERSON><PERSON>", "type": "codeVoice"}, {"type": "text", "text": " and "}, {"type": "codeVoice", "code": "RawValue"}, {"text": " conforms to ", "type": "text"}, {"type": "codeVoice", "code": "<PERSON><PERSON><PERSON>"}, {"type": "text", "text": "."}], "conformancePrefix": [{"text": "Conforms when", "type": "text"}]}}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle/hashValue": {"kind": "symbol", "abstract": [], "url": "/documentation/jdstatusbarnotification/statusbarnotificationsystembarstyle/hashvalue", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle/hashValue", "type": "topic", "conformance": {"availabilityPrefix": [{"text": "Available when", "type": "text"}], "conformancePrefix": [{"type": "text", "text": "Conforms when"}], "constraints": [{"code": "Self", "type": "codeVoice"}, {"text": " conforms to ", "type": "text"}, {"type": "codeVoice", "code": "<PERSON><PERSON><PERSON>"}, {"type": "text", "text": " and "}, {"type": "codeVoice", "code": "RawValue"}, {"text": " conforms to ", "type": "text"}, {"type": "codeVoice", "code": "<PERSON><PERSON><PERSON>"}, {"type": "text", "text": "."}]}, "title": "hashValue", "fragments": [{"text": "var", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "hashValue", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "s:<PERSON>", "text": "Int", "kind": "typeIdentifier"}], "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle": {"abstract": [{"text": "Defines which ", "type": "text"}, {"code": "UIStatusBarStyle", "type": "codeVoice"}, {"type": "text", "text": " should be used during presentation."}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationsystembarstyle", "fragments": [{"kind": "keyword", "text": "enum"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "StatusBarNotificationSystemBarStyle"}], "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationSystemBarStyle"}], "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle", "title": "StatusBarNotificationSystemBarStyle", "role": "symbol", "type": "topic"}}}