{"primaryContentSections": [{"kind": "declarations", "declarations": [{"tokens": [{"kind": "keyword", "text": "func"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "hash"}, {"text": "(", "kind": "text"}, {"kind": "externalParam", "text": "into"}, {"text": " ", "kind": "text"}, {"kind": "internalParam", "text": "hasher"}, {"text": ": ", "kind": "text"}, {"kind": "keyword", "text": "inout"}, {"text": " ", "kind": "text"}, {"preciseIdentifier": "s:s6HasherV", "text": "<PERSON><PERSON>", "kind": "typeIdentifier"}, {"kind": "text", "text": ")"}], "languages": ["swift"], "platforms": ["iOS"]}]}], "metadata": {"modules": [{"name": "JDStatusBarNotification", "relatedModules": ["Swift"]}], "conformance": {"availabilityPrefix": [{"text": "Available when", "type": "text"}], "constraints": [{"type": "codeVoice", "code": "Self"}, {"text": " conforms to ", "type": "text"}, {"type": "codeVoice", "code": "<PERSON><PERSON><PERSON>"}, {"type": "text", "text": " and "}, {"code": "RawValue", "type": "codeVoice"}, {"text": " conforms to ", "type": "text"}, {"code": "<PERSON><PERSON><PERSON>", "type": "codeVoice"}, {"text": ".", "type": "text"}], "conformancePrefix": [{"text": "Conforms when", "type": "text"}]}, "title": "hash(into:)", "role": "symbol", "extendedModule": "Swift", "fragments": [{"kind": "keyword", "text": "func"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "hash"}, {"text": "(", "kind": "text"}, {"text": "into", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"text": "inout", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "typeIdentifier", "text": "<PERSON><PERSON>", "preciseIdentifier": "s:s6HasherV"}, {"text": ")", "kind": "text"}], "externalID": "s:SYsSHRzSH8RawValueSYRpzrlE4hash4intoys6HasherVz_tF::SYNTHESIZED::c:@M@JDStatusBarNotification@E@JDStatusBarNotificationSystemBarStyle", "roleHeading": "Instance Method", "symbolKind": "method"}, "sections": [], "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle/RawRepresentable-Implementations"]]}, "identifier": {"url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle/hash(into:)", "interfaceLanguage": "swift"}, "abstract": [{"text": "Inherited from ", "type": "text"}, {"type": "codeVoice", "code": "RawRepresentable.hash(into:)"}, {"text": ".", "type": "text"}], "schemaVersion": {"major": 0, "minor": 3, "patch": 0}, "variants": [{"paths": ["/documentation/jdstatusbarnotification/statusbarnotificationsystembarstyle/hash(into:)"], "traits": [{"interfaceLanguage": "swift"}]}], "kind": "symbol", "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle": {"abstract": [{"text": "Defines which ", "type": "text"}, {"code": "UIStatusBarStyle", "type": "codeVoice"}, {"type": "text", "text": " should be used during presentation."}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationsystembarstyle", "fragments": [{"kind": "keyword", "text": "enum"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "StatusBarNotificationSystemBarStyle"}], "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationSystemBarStyle"}], "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle", "title": "StatusBarNotificationSystemBarStyle", "role": "symbol", "type": "topic"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle/RawRepresentable-Implementations": {"abstract": [], "role": "collectionGroup", "url": "/documentation/jdstatusbarnotification/statusbarnotificationsystembarstyle/rawrepresentable-implementations", "title": "RawRepresentable Implementations", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle/RawRepresentable-Implementations", "kind": "article"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle", "navigatorTitle": [{"text": "StatusBarNotificationStyle", "kind": "identifier"}], "role": "symbol", "type": "topic", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "title": "StatusBarNotificationStyle", "abstract": [{"text": "A Style defines the appearance of a notification.", "type": "text"}], "fragments": [{"kind": "keyword", "text": "class"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationStyle", "kind": "identifier"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle/hash(into:)": {"title": "hash(into:)", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle/hash(into:)", "kind": "symbol", "url": "/documentation/jdstatusbarnotification/statusbarnotificationsystembarstyle/hash(into:)", "abstract": [], "type": "topic", "fragments": [{"text": "func", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "hash", "kind": "identifier"}, {"text": "(", "kind": "text"}, {"text": "into", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"text": "inout", "kind": "keyword"}, {"kind": "text", "text": " "}, {"preciseIdentifier": "s:s6HasherV", "kind": "typeIdentifier", "text": "<PERSON><PERSON>"}, {"text": ")", "kind": "text"}], "role": "symbol", "conformance": {"availabilityPrefix": [{"text": "Available when", "type": "text"}], "constraints": [{"type": "codeVoice", "code": "Self"}, {"text": " conforms to ", "type": "text"}, {"code": "<PERSON><PERSON><PERSON>", "type": "codeVoice"}, {"type": "text", "text": " and "}, {"type": "codeVoice", "code": "RawValue"}, {"text": " conforms to ", "type": "text"}, {"type": "codeVoice", "code": "<PERSON><PERSON><PERSON>"}, {"type": "text", "text": "."}], "conformancePrefix": [{"text": "Conforms when", "type": "text"}]}}}}