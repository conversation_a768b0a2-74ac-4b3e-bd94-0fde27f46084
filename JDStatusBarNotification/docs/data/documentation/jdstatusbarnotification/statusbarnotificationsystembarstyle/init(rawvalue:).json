{"kind": "symbol", "schemaVersion": {"patch": 0, "major": 0, "minor": 3}, "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle"]]}, "sections": [], "variants": [{"paths": ["/documentation/jdstatusbarnotification/statusbarnotificationsystembarstyle/init(rawvalue:)"], "traits": [{"interfaceLanguage": "swift"}]}], "primaryContentSections": [{"kind": "declarations", "declarations": [{"platforms": ["iOS"], "tokens": [{"kind": "keyword", "text": "init"}, {"kind": "text", "text": "?("}, {"kind": "externalParam", "text": "rawValue"}, {"kind": "text", "text": ": "}, {"kind": "typeIdentifier", "text": "Int", "preciseIdentifier": "s:<PERSON>"}, {"text": ")", "kind": "text"}], "languages": ["swift"]}]}], "identifier": {"url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle/init(rawValue:)", "interfaceLanguage": "swift"}, "abstract": [{"text": "Inherited from ", "type": "text"}, {"type": "codeVoice", "code": "RawRepresentable.init(rawValue:)"}, {"type": "text", "text": "."}], "metadata": {"externalID": "s:23JDStatusBarNotification06Statusbc6SystemB5StyleO8rawValueACSgSi_tcfc", "symbolKind": "init", "modules": [{"name": "JDStatusBarNotification"}], "role": "symbol", "title": "init(rawValue:)", "roleHeading": "Initializer", "fragments": [{"kind": "identifier", "text": "init"}, {"kind": "text", "text": "?("}, {"text": "rawValue", "kind": "externalParam"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "s:<PERSON>", "text": "Int", "kind": "typeIdentifier"}, {"kind": "text", "text": ")"}]}, "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle", "navigatorTitle": [{"text": "StatusBarNotificationStyle", "kind": "identifier"}], "role": "symbol", "type": "topic", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "title": "StatusBarNotificationStyle", "abstract": [{"text": "A Style defines the appearance of a notification.", "type": "text"}], "fragments": [{"kind": "keyword", "text": "class"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationStyle", "kind": "identifier"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle": {"abstract": [{"text": "Defines which ", "type": "text"}, {"code": "UIStatusBarStyle", "type": "codeVoice"}, {"type": "text", "text": " should be used during presentation."}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationsystembarstyle", "fragments": [{"kind": "keyword", "text": "enum"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "StatusBarNotificationSystemBarStyle"}], "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationSystemBarStyle"}], "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle", "title": "StatusBarNotificationSystemBarStyle", "role": "symbol", "type": "topic"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle/init(rawValue:)": {"abstract": [], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle/init(rawValue:)", "kind": "symbol", "title": "init(rawValue:)", "role": "symbol", "type": "topic", "url": "/documentation/jdstatusbarnotification/statusbarnotificationsystembarstyle/init(rawvalue:)", "fragments": [{"kind": "identifier", "text": "init"}, {"text": "?(", "kind": "text"}, {"kind": "externalParam", "text": "rawValue"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "text": "Int", "preciseIdentifier": "s:<PERSON>"}, {"kind": "text", "text": ")"}]}}}