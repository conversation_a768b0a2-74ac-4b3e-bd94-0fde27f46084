{"metadata": {"roleHeading": "Case", "externalID": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationSystemBarStyle@JDStatusBarNotificationSystemBarStyleLightContent", "symbolKind": "case", "modules": [{"name": "JDStatusBarNotification"}], "fragments": [{"text": "case", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "lightContent"}], "role": "symbol", "title": "StatusBarNotificationSystemBarStyle.lightContent"}, "variants": [{"paths": ["/documentation/jdstatusbarnotification/statusbarnotificationsystembarstyle/lightcontent"], "traits": [{"interfaceLanguage": "swift"}]}], "sections": [], "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle"]]}, "identifier": {"interfaceLanguage": "swift", "url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle/lightContent"}, "schemaVersion": {"minor": 3, "major": 0, "patch": 0}, "abstract": [{"text": "Forces light status bar contents (", "type": "text"}, {"code": "UIStatusBarStyleLightContent", "type": "codeVoice"}, {"text": ")", "type": "text"}], "primaryContentSections": [{"kind": "declarations", "declarations": [{"platforms": ["iOS"], "tokens": [{"text": "case", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "lightContent", "kind": "identifier"}], "languages": ["swift"]}]}], "kind": "symbol", "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle/lightContent": {"type": "topic", "title": "StatusBarNotificationSystemBarStyle.lightContent", "kind": "symbol", "role": "symbol", "abstract": [{"type": "text", "text": "Forces light status bar contents ("}, {"code": "UIStatusBarStyleLightContent", "type": "codeVoice"}, {"text": ")", "type": "text"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationsystembarstyle/lightcontent", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle/lightContent", "fragments": [{"kind": "keyword", "text": "case"}, {"kind": "text", "text": " "}, {"text": "lightContent", "kind": "identifier"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle": {"abstract": [{"text": "Defines which ", "type": "text"}, {"code": "UIStatusBarStyle", "type": "codeVoice"}, {"type": "text", "text": " should be used during presentation."}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationsystembarstyle", "fragments": [{"kind": "keyword", "text": "enum"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "StatusBarNotificationSystemBarStyle"}], "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationSystemBarStyle"}], "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationSystemBarStyle", "title": "StatusBarNotificationSystemBarStyle", "role": "symbol", "type": "topic"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle", "navigatorTitle": [{"text": "StatusBarNotificationStyle", "kind": "identifier"}], "role": "symbol", "type": "topic", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "title": "StatusBarNotificationStyle", "abstract": [{"text": "A Style defines the appearance of a notification.", "type": "text"}], "fragments": [{"kind": "keyword", "text": "class"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationStyle", "kind": "identifier"}]}}}