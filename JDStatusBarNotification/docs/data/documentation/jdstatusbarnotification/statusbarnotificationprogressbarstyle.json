{"variants": [{"traits": [{"interfaceLanguage": "swift"}], "paths": ["/documentation/jdstatusbarnotification/statusbarnotificationprogressbarstyle"]}], "schemaVersion": {"minor": 3, "patch": 0, "major": 0}, "abstract": [{"text": "Defines the appearance of the progress bar.", "type": "text"}], "relationshipsSections": [{"identifiers": ["doc://calimarkus.JDStatusBarNotification/objc(cs)NSObject"], "kind": "relationships", "title": "Inherits From", "type": "inheritsFrom"}, {"identifiers": ["doc://calimarkus.JDStatusBarNotification/objc(pl)NSObject", "doc://calimarkus.JDStatusBarNotification/s7CVarArgP", "doc://calimarkus.JDStatusBarNotification/s28CustomDebugStringConvertibleP", "doc://calimarkus.JDStatusBarNotification/s23CustomStringConvertibleP", "doc://calimarkus.JDStatusBarNotification/SQ", "doc://calimarkus.JDStatusBarNotification/SH"], "title": "Conforms To", "kind": "relationships", "type": "conformsTo"}], "metadata": {"role": "symbol", "roleHeading": "Class", "modules": [{"name": "JDStatusBarNotification"}], "symbolKind": "class", "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationProgressBarStyle"}], "fragments": [{"text": "class", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "StatusBarNotificationProgressBarStyle", "kind": "identifier"}], "externalID": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationProgressBarStyle", "title": "StatusBarNotificationProgressBarStyle"}, "topicSections": [{"title": "Instance Properties", "identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle/barColor", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle/barHeight", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle/cornerRadius", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle/horizontalInsets", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle/offsetY", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle/position"]}], "kind": "symbol", "sections": [], "identifier": {"interfaceLanguage": "swift", "url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle"}, "seeAlsoSections": [{"generated": true, "title": "Notification Style", "identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle"]}], "primaryContentSections": [{"kind": "declarations", "declarations": [{"tokens": [{"kind": "attribute", "text": "@objc"}, {"text": "(JDStatusBarNotificationProgressBarStyle) ", "kind": "text"}, {"text": "class", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "StatusBarNotificationProgressBarStyle"}], "platforms": ["iOS"], "languages": ["swift"]}]}], "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification"]]}, "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle": {"title": "StatusBarNotificationTextStyle", "fragments": [{"kind": "keyword", "text": "class"}, {"text": " ", "kind": "text"}, {"text": "StatusBarNotificationTextStyle", "kind": "identifier"}], "role": "symbol", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle", "url": "/documentation/jdstatusbarnotification/statusbarnotificationtextstyle", "type": "topic", "navigatorTitle": [{"text": "StatusBarNotificationTextStyle", "kind": "identifier"}], "abstract": [{"type": "text", "text": "Defines the appearance of a text label."}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle", "navigatorTitle": [{"text": "StatusBarNotificationStyle", "kind": "identifier"}], "role": "symbol", "type": "topic", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "title": "StatusBarNotificationStyle", "abstract": [{"text": "A Style defines the appearance of a notification.", "type": "text"}], "fragments": [{"kind": "keyword", "text": "class"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationStyle", "kind": "identifier"}]}, "doc://calimarkus.JDStatusBarNotification/objc(cs)NSObject": {"title": "ObjectiveC.NSObject", "identifier": "doc://calimarkus.JDStatusBarNotification/objc(cs)NSObject", "type": "unresolvable"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundStyle": {"fragments": [{"text": "class", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "StatusBarNotificationBackgroundStyle", "kind": "identifier"}], "kind": "symbol", "navigatorTitle": [{"text": "StatusBarNotificationBackgroundStyle", "kind": "identifier"}], "role": "symbol", "url": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundstyle", "title": "StatusBarNotificationBackgroundStyle", "type": "topic", "abstract": [{"type": "text", "text": "Defines the appearance of the notification background."}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundStyle"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle/barHeight": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle/barHeight", "kind": "symbol", "abstract": [{"type": "text", "text": "The height of the progress bar. De<PERSON><PERSON> is "}, {"type": "codeVoice", "code": "2.0"}, {"type": "text", "text": ". The applied minimum is 0.5 and the maximum equals the full height of the notification."}], "title": "barHeight", "url": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarstyle/barheight", "role": "symbol", "fragments": [{"text": "var", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "barHeight"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "s:Sd", "kind": "typeIdentifier", "text": "Double"}], "type": "topic"}, "doc://calimarkus.JDStatusBarNotification/s23CustomStringConvertibleP": {"title": "Swift.CustomStringConvertible", "identifier": "doc://calimarkus.JDStatusBarNotification/s23CustomStringConvertibleP", "type": "unresolvable"}, "doc://calimarkus.JDStatusBarNotification/s7CVarArgP": {"title": "Swift.CVarArg", "identifier": "doc://calimarkus.JDStatusBarNotification/s7CVarArgP", "type": "unresolvable"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle": {"abstract": [{"text": "Defines the appearance of the pill, when using ", "type": "text"}, {"type": "reference", "isActive": true, "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/pill"}], "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationPillStyle"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle", "type": "topic", "kind": "symbol", "role": "symbol", "url": "/documentation/jdstatusbarnotification/statusbarnotificationpillstyle", "fragments": [{"kind": "keyword", "text": "class"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "StatusBarNotificationPillStyle"}], "title": "StatusBarNotificationPillStyle"}, "doc://calimarkus.JDStatusBarNotification/objc(pl)NSObject": {"title": "ObjectiveC.NSObjectProtocol", "identifier": "doc://calimarkus.JDStatusBarNotification/objc(pl)NSObject", "type": "unresolvable"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/pill": {"title": "StatusBarNotificationBackgroundType.pill", "url": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundtype/pill", "abstract": [{"type": "text", "text": "The background is a floating pill around the text. The pill size and appearance can be customized. This is the default."}], "kind": "symbol", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/pill", "role": "symbol", "fragments": [{"kind": "keyword", "text": "case"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "pill"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle/position": {"abstract": [{"text": "The position of the progress bar. <PERSON><PERSON><PERSON> is ", "type": "text"}, {"type": "reference", "isActive": true, "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition/bottom"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarstyle/position", "role": "symbol", "fragments": [{"kind": "keyword", "text": "var"}, {"text": " ", "kind": "text"}, {"text": "position", "kind": "identifier"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "text": "StatusBarNotificationProgressBarPosition", "preciseIdentifier": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationProgressBarPosition"}], "type": "topic", "title": "position", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle/position", "kind": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle/horizontalInsets": {"abstract": [{"type": "text", "text": "The insets of the progress bar. De<PERSON><PERSON> is "}, {"code": "20.0", "type": "codeVoice"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarstyle/horizontalinsets", "role": "symbol", "fragments": [{"text": "var", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "horizontalInsets"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "s:Sd", "text": "Double"}], "title": "horizontalInsets", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle/horizontalInsets", "type": "topic"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle": {"type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle", "kind": "symbol", "role": "symbol", "fragments": [{"text": "class", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationProgressBarStyle", "kind": "identifier"}], "title": "StatusBarNotificationProgressBarStyle", "abstract": [{"type": "text", "text": "Defines the appearance of the progress bar."}], "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationProgressBarStyle"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarstyle"}, "doc://calimarkus.JDStatusBarNotification/s28CustomDebugStringConvertibleP": {"title": "Swift.CustomDebugStringConvertible", "identifier": "doc://calimarkus.JDStatusBarNotification/s28CustomDebugStringConvertibleP", "type": "unresolvable"}, "doc://calimarkus.JDStatusBarNotification/SQ": {"title": "Swift.Equatable", "identifier": "doc://calimarkus.JDStatusBarNotification/SQ", "type": "unresolvable"}, "doc://calimarkus.JDStatusBarNotification/SH": {"title": "<PERSON><PERSON>", "identifier": "doc://calimarkus.JDStatusBarNotification/SH", "type": "unresolvable"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle": {"navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationLeftViewStyle"}], "type": "topic", "kind": "symbol", "title": "StatusBarNotificationLeftViewStyle", "abstract": [{"text": "Defines the appearance of a left-view, if set. It also applies to the activity indicator.", "type": "text"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewstyle", "role": "symbol", "fragments": [{"text": "class", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "StatusBarNotificationLeftViewStyle", "kind": "identifier"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition/bottom": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarPosition/bottom", "kind": "symbol", "title": "StatusBarNotificationProgressBarPosition.bottom", "url": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarposition/bottom", "type": "topic", "abstract": [{"text": "The progress bar will be at the bottom of the notification content. This is the default.", "type": "text"}], "fragments": [{"text": "case", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "bottom"}], "role": "symbol"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle/cornerRadius": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle/cornerRadius", "kind": "symbol", "url": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarstyle/cornerradius", "fragments": [{"kind": "keyword", "text": "var"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "cornerRadius"}, {"text": ": ", "kind": "text"}, {"preciseIdentifier": "s:Sd", "text": "Double", "kind": "typeIdentifier"}], "role": "symbol", "type": "topic", "title": "cornerRadius", "abstract": [{"text": "The corner radius of the progress bar. De<PERSON><PERSON> is ", "type": "text"}, {"code": "1.0", "type": "codeVoice"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle/barColor": {"abstract": [{"text": "The background color of the progress bar (on top of the notification bar)", "type": "text"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarstyle/barcolor", "role": "symbol", "fragments": [{"text": "var", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "barColor", "kind": "identifier"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "text": "UIColor", "preciseIdentifier": "c:objc(cs)UIColor"}, {"kind": "text", "text": "?"}], "type": "topic", "kind": "symbol", "title": "barColor", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle/barColor"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle/offsetY": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle/offsetY", "url": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarstyle/offsety", "kind": "symbol", "title": "offsetY", "type": "topic", "abstract": [{"type": "text", "text": "Offsets the progress bar on the  y-axis. De<PERSON><PERSON> is "}, {"type": "codeVoice", "code": "-5.0"}, {"type": "text", "text": "."}], "role": "symbol", "fragments": [{"text": "var", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "offsetY", "kind": "identifier"}, {"text": ": ", "kind": "text"}, {"preciseIdentifier": "s:Sd", "text": "Double", "kind": "typeIdentifier"}]}}}