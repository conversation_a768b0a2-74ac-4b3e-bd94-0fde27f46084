{"primaryContentSections": [{"declarations": [{"tokens": [{"kind": "keyword", "text": "static"}, {"text": " ", "kind": "text"}, {"kind": "keyword", "text": "func"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "!="}, {"text": " ", "kind": "text"}, {"kind": "text", "text": "("}, {"kind": "internalParam", "text": "lhs"}, {"kind": "text", "text": ": "}, {"text": "Self", "kind": "typeIdentifier"}, {"text": ", ", "kind": "text"}, {"text": "rhs", "kind": "internalParam"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "text": "Self"}, {"kind": "text", "text": ") -> "}, {"preciseIdentifier": "s:Sb", "text": "Bool", "kind": "typeIdentifier"}], "languages": ["swift"], "platforms": ["iOS"]}], "kind": "declarations"}], "metadata": {"roleHeading": "Operator", "modules": [{"name": "JDStatusBarNotification", "relatedModules": ["Swift"]}], "symbolKind": "op", "title": "!=(_:_:)", "externalID": "s:SQsE2neoiySbx_xtFZ::SYNTHESIZED::c:@M@JDStatusBarNotification@E@JDStatusBarNotificationLeftViewAlignment", "role": "symbol", "fragments": [{"text": "static", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "keyword", "text": "func"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "!="}, {"text": " ", "kind": "text"}, {"kind": "text", "text": "("}, {"text": "Self", "kind": "typeIdentifier"}, {"text": ", ", "kind": "text"}, {"text": "Self", "kind": "typeIdentifier"}, {"text": ") -> ", "kind": "text"}, {"text": "Bool", "kind": "typeIdentifier", "preciseIdentifier": "s:Sb"}], "extendedModule": "Swift"}, "variants": [{"traits": [{"interfaceLanguage": "swift"}], "paths": ["/documentation/jdstatusbarnotification/statusbarnotificationleftviewalignment/!=(_:_:)"]}], "kind": "symbol", "identifier": {"interfaceLanguage": "swift", "url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment/!=(_:_:)"}, "schemaVersion": {"major": 0, "patch": 0, "minor": 3}, "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment/Equatable-Implementations"]]}, "sections": [], "abstract": [{"text": "Inherited from ", "type": "text"}, {"type": "codeVoice", "code": "Equatable.!=(_:_:)"}, {"type": "text", "text": "."}], "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment", "role": "symbol", "kind": "symbol", "fragments": [{"kind": "keyword", "text": "enum"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "StatusBarNotificationLeftViewAlignment"}], "abstract": [{"text": "Defines the appearance of a left-view, if set. This includes the activity indicator.", "type": "text"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewalignment", "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationLeftViewAlignment"}], "type": "topic", "title": "StatusBarNotificationLeftViewAlignment"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment/Equatable-Implementations": {"abstract": [], "url": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewalignment/equatable-implementations", "kind": "article", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment/Equatable-Implementations", "title": "Equatable Implementations", "role": "collectionGroup", "type": "topic"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle", "navigatorTitle": [{"text": "StatusBarNotificationStyle", "kind": "identifier"}], "role": "symbol", "type": "topic", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "title": "StatusBarNotificationStyle", "abstract": [{"text": "A Style defines the appearance of a notification.", "type": "text"}], "fragments": [{"kind": "keyword", "text": "class"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationStyle", "kind": "identifier"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment/!=(_:_:)": {"type": "topic", "role": "symbol", "abstract": [], "title": "!=(_:_:)", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment/!=(_:_:)", "url": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewalignment/!=(_:_:)", "fragments": [{"text": "static", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "func", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "!=", "kind": "identifier"}, {"kind": "text", "text": " "}, {"text": "(", "kind": "text"}, {"kind": "typeIdentifier", "text": "Self"}, {"text": ", ", "kind": "text"}, {"text": "Self", "kind": "typeIdentifier"}, {"text": ") -> ", "kind": "text"}, {"kind": "typeIdentifier", "text": "Bool", "preciseIdentifier": "s:Sb"}], "kind": "symbol"}}}