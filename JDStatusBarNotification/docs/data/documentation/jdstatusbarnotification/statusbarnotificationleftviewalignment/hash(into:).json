{"primaryContentSections": [{"kind": "declarations", "declarations": [{"tokens": [{"kind": "keyword", "text": "func"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "hash"}, {"text": "(", "kind": "text"}, {"kind": "externalParam", "text": "into"}, {"kind": "text", "text": " "}, {"text": "hasher", "kind": "internalParam"}, {"kind": "text", "text": ": "}, {"kind": "keyword", "text": "inout"}, {"text": " ", "kind": "text"}, {"preciseIdentifier": "s:s6HasherV", "text": "<PERSON><PERSON>", "kind": "typeIdentifier"}, {"kind": "text", "text": ")"}], "platforms": ["iOS"], "languages": ["swift"]}]}], "variants": [{"traits": [{"interfaceLanguage": "swift"}], "paths": ["/documentation/jdstatusbarnotification/statusbarnotificationleftviewalignment/hash(into:)"]}], "sections": [], "kind": "symbol", "schemaVersion": {"major": 0, "minor": 3, "patch": 0}, "abstract": [{"type": "text", "text": "Inherited from "}, {"code": "RawRepresentable.hash(into:)", "type": "codeVoice"}, {"type": "text", "text": "."}], "identifier": {"interfaceLanguage": "swift", "url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment/hash(into:)"}, "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment/RawRepresentable-Implementations"]]}, "metadata": {"roleHeading": "Instance Method", "fragments": [{"text": "func", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "hash", "kind": "identifier"}, {"text": "(", "kind": "text"}, {"text": "into", "kind": "externalParam"}, {"text": ": ", "kind": "text"}, {"text": "inout", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "typeIdentifier", "preciseIdentifier": "s:s6HasherV", "text": "<PERSON><PERSON>"}, {"kind": "text", "text": ")"}], "externalID": "s:SYsSHRzSH8RawValueSYRpzrlE4hash4intoys6HasherVz_tF::SYNTHESIZED::c:@M@JDStatusBarNotification@E@JDStatusBarNotificationLeftViewAlignment", "role": "symbol", "modules": [{"name": "JDStatusBarNotification", "relatedModules": ["Swift"]}], "symbolKind": "method", "extendedModule": "Swift", "conformance": {"conformancePrefix": [{"type": "text", "text": "Conforms when"}], "availabilityPrefix": [{"text": "Available when", "type": "text"}], "constraints": [{"code": "Self", "type": "codeVoice"}, {"type": "text", "text": " conforms to "}, {"code": "<PERSON><PERSON><PERSON>", "type": "codeVoice"}, {"type": "text", "text": " and "}, {"type": "codeVoice", "code": "RawValue"}, {"text": " conforms to ", "type": "text"}, {"code": "<PERSON><PERSON><PERSON>", "type": "codeVoice"}, {"text": ".", "type": "text"}]}, "title": "hash(into:)"}, "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"title": "JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "role": "collection", "abstract": [{"type": "text", "text": "Highly customizable & feature rich notifications displayed below the status bar."}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "kind": "symbol", "type": "topic"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle", "navigatorTitle": [{"text": "StatusBarNotificationStyle", "kind": "identifier"}], "role": "symbol", "type": "topic", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "title": "StatusBarNotificationStyle", "abstract": [{"text": "A Style defines the appearance of a notification.", "type": "text"}], "fragments": [{"kind": "keyword", "text": "class"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationStyle", "kind": "identifier"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment/hash(into:)": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment/hash(into:)", "role": "symbol", "kind": "symbol", "fragments": [{"kind": "keyword", "text": "func"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "hash"}, {"kind": "text", "text": "("}, {"kind": "externalParam", "text": "into"}, {"text": ": ", "kind": "text"}, {"text": "inout", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "<PERSON><PERSON>", "preciseIdentifier": "s:s6HasherV", "kind": "typeIdentifier"}, {"text": ")", "kind": "text"}], "abstract": [], "conformance": {"availabilityPrefix": [{"text": "Available when", "type": "text"}], "conformancePrefix": [{"text": "Conforms when", "type": "text"}], "constraints": [{"code": "Self", "type": "codeVoice"}, {"text": " conforms to ", "type": "text"}, {"type": "codeVoice", "code": "<PERSON><PERSON><PERSON>"}, {"type": "text", "text": " and "}, {"type": "codeVoice", "code": "RawValue"}, {"text": " conforms to ", "type": "text"}, {"type": "codeVoice", "code": "<PERSON><PERSON><PERSON>"}, {"type": "text", "text": "."}]}, "url": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewalignment/hash(into:)", "type": "topic", "title": "hash(into:)"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment", "role": "symbol", "kind": "symbol", "fragments": [{"kind": "keyword", "text": "enum"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "StatusBarNotificationLeftViewAlignment"}], "abstract": [{"text": "Defines the appearance of a left-view, if set. This includes the activity indicator.", "type": "text"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewalignment", "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationLeftViewAlignment"}], "type": "topic", "title": "StatusBarNotificationLeftViewAlignment"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment/RawRepresentable-Implementations": {"abstract": [], "title": "RawRepresentable Implementations", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment/RawRepresentable-Implementations", "url": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewalignment/rawrepresentable-implementations", "kind": "article", "role": "collectionGroup"}}}