{"schemaVersion": {"minor": 3, "major": 0, "patch": 0}, "primaryContentSections": [{"kind": "declarations", "declarations": [{"tokens": [{"text": "var", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "hashValue", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"kind": "typeIdentifier", "text": "Int", "preciseIdentifier": "s:<PERSON>"}, {"text": " { ", "kind": "text"}, {"kind": "keyword", "text": "get"}, {"text": " }", "kind": "text"}], "platforms": ["iOS"], "languages": ["swift"]}]}], "sections": [], "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment/RawRepresentable-Implementations"]]}, "identifier": {"url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment/hashValue", "interfaceLanguage": "swift"}, "abstract": [{"type": "text", "text": "Inherited from "}, {"code": "RawRepresentable.hashValue", "type": "codeVoice"}, {"text": ".", "type": "text"}], "variants": [{"paths": ["/documentation/jdstatusbarnotification/statusbarnotificationleftviewalignment/hashvalue"], "traits": [{"interfaceLanguage": "swift"}]}], "kind": "symbol", "metadata": {"role": "symbol", "extendedModule": "Swift", "modules": [{"name": "JDStatusBarNotification", "relatedModules": ["Swift"]}], "fragments": [{"kind": "keyword", "text": "var"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "hashValue"}, {"text": ": ", "kind": "text"}, {"kind": "typeIdentifier", "text": "Int", "preciseIdentifier": "s:<PERSON>"}], "roleHeading": "Instance Property", "title": "hashValue", "conformance": {"availabilityPrefix": [{"type": "text", "text": "Available when"}], "conformancePrefix": [{"text": "Conforms when", "type": "text"}], "constraints": [{"type": "codeVoice", "code": "Self"}, {"type": "text", "text": " conforms to "}, {"code": "<PERSON><PERSON><PERSON>", "type": "codeVoice"}, {"type": "text", "text": " and "}, {"code": "RawValue", "type": "codeVoice"}, {"text": " conforms to ", "type": "text"}, {"type": "codeVoice", "code": "<PERSON><PERSON><PERSON>"}, {"text": ".", "type": "text"}]}, "symbolKind": "property", "externalID": "s:SYsSHRzSH8RawValueSYRpzrlE04hashB0Sivp::SYNTHESIZED::c:@M@JDStatusBarNotification@E@JDStatusBarNotificationLeftViewAlignment"}, "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment", "role": "symbol", "kind": "symbol", "fragments": [{"kind": "keyword", "text": "enum"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "StatusBarNotificationLeftViewAlignment"}], "abstract": [{"text": "Defines the appearance of a left-view, if set. This includes the activity indicator.", "type": "text"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewalignment", "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationLeftViewAlignment"}], "type": "topic", "title": "StatusBarNotificationLeftViewAlignment"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment/RawRepresentable-Implementations": {"abstract": [], "title": "RawRepresentable Implementations", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment/RawRepresentable-Implementations", "url": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewalignment/rawrepresentable-implementations", "kind": "article", "role": "collectionGroup"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle", "navigatorTitle": [{"text": "StatusBarNotificationStyle", "kind": "identifier"}], "role": "symbol", "type": "topic", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "title": "StatusBarNotificationStyle", "abstract": [{"text": "A Style defines the appearance of a notification.", "type": "text"}], "fragments": [{"kind": "keyword", "text": "class"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationStyle", "kind": "identifier"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment/hashValue": {"role": "symbol", "url": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewalignment/hashvalue", "abstract": [], "type": "topic", "kind": "symbol", "fragments": [{"text": "var", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "hashValue"}, {"kind": "text", "text": ": "}, {"kind": "typeIdentifier", "text": "Int", "preciseIdentifier": "s:<PERSON>"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment/hashValue", "title": "hashValue", "conformance": {"conformancePrefix": [{"text": "Conforms when", "type": "text"}], "availabilityPrefix": [{"type": "text", "text": "Available when"}], "constraints": [{"type": "codeVoice", "code": "Self"}, {"text": " conforms to ", "type": "text"}, {"type": "codeVoice", "code": "<PERSON><PERSON><PERSON>"}, {"type": "text", "text": " and "}, {"code": "RawValue", "type": "codeVoice"}, {"type": "text", "text": " conforms to "}, {"type": "codeVoice", "code": "<PERSON><PERSON><PERSON>"}, {"type": "text", "text": "."}]}}}}