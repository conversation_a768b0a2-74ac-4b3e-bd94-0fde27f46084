{"kind": "symbol", "schemaVersion": {"major": 0, "minor": 3, "patch": 0}, "variants": [{"traits": [{"interfaceLanguage": "swift"}], "paths": ["/documentation/jdstatusbarnotification/statusbarnotificationleftviewalignment/left"]}], "metadata": {"title": "StatusBarNotificationLeftViewAlignment.left", "modules": [{"name": "JDStatusBarNotification"}], "role": "symbol", "fragments": [{"text": "case", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "left"}], "symbolKind": "case", "roleHeading": "Case", "externalID": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationLeftViewAlignment@JDStatusBarNotificationLeftViewAlignmentLeft"}, "sections": [], "identifier": {"url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment/left", "interfaceLanguage": "swift"}, "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment"]]}, "abstract": [{"type": "text", "text": "Aligns the left-view on the left side of the notification. The text is center-aligned unless it touches the left-view."}], "primaryContentSections": [{"kind": "declarations", "declarations": [{"platforms": ["iOS"], "tokens": [{"text": "case", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "left", "kind": "identifier"}], "languages": ["swift"]}]}, {"kind": "content", "content": [{"text": "Discussion", "type": "heading", "anchor": "discussion", "level": 2}, {"inlineContent": [{"text": "If the text does touch the left-view, the text will also be left-aligned.", "type": "text"}, {"text": " ", "type": "text"}, {"type": "text", "text": "If no title or subtitle is set, the left-view is always fully centered."}], "type": "paragraph"}]}], "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment/left": {"type": "topic", "role": "symbol", "title": "StatusBarNotificationLeftViewAlignment.left", "abstract": [{"type": "text", "text": "Aligns the left-view on the left side of the notification. The text is center-aligned unless it touches the left-view."}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment/left", "fragments": [{"text": "case", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "left", "kind": "identifier"}], "kind": "symbol", "url": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewalignment/left"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle", "navigatorTitle": [{"text": "StatusBarNotificationStyle", "kind": "identifier"}], "role": "symbol", "type": "topic", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "title": "StatusBarNotificationStyle", "abstract": [{"text": "A Style defines the appearance of a notification.", "type": "text"}], "fragments": [{"kind": "keyword", "text": "class"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationStyle", "kind": "identifier"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment", "role": "symbol", "kind": "symbol", "fragments": [{"kind": "keyword", "text": "enum"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "StatusBarNotificationLeftViewAlignment"}], "abstract": [{"text": "Defines the appearance of a left-view, if set. This includes the activity indicator.", "type": "text"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewalignment", "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationLeftViewAlignment"}], "type": "topic", "title": "StatusBarNotificationLeftViewAlignment"}}}