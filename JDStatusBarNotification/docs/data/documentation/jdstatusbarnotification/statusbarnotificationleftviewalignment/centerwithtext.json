{"variants": [{"traits": [{"interfaceLanguage": "swift"}], "paths": ["/documentation/jdstatusbarnotification/statusbarnotificationleftviewalignment/centerwithtext"]}], "abstract": [{"type": "text", "text": "Centers the left-view together with the text. The left-view will be positioned at the leading edge of the text. The text is left-aligned. This is the default."}], "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment"]]}, "kind": "symbol", "schemaVersion": {"patch": 0, "major": 0, "minor": 3}, "primaryContentSections": [{"kind": "declarations", "declarations": [{"tokens": [{"text": "case", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "centerWithText", "kind": "identifier"}], "platforms": ["iOS"], "languages": ["swift"]}]}, {"kind": "content", "content": [{"type": "heading", "level": 2, "anchor": "discussion", "text": "Discussion"}, {"inlineContent": [{"type": "text", "text": "If no title or subtitle is set, the left-view is always fully centered."}], "type": "paragraph"}]}], "identifier": {"url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment/centerWithText", "interfaceLanguage": "swift"}, "metadata": {"title": "StatusBarNotificationLeftViewAlignment.centerWithText", "modules": [{"name": "JDStatusBarNotification"}], "roleHeading": "Case", "externalID": "c:@M@JDStatusBarNotification@E@JDStatusBarNotificationLeftViewAlignment@JDStatusBarNotificationLeftViewAlignmentCenterWithText", "symbolKind": "case", "fragments": [{"text": "case", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "centerWithText"}], "role": "symbol"}, "sections": [], "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle", "navigatorTitle": [{"text": "StatusBarNotificationStyle", "kind": "identifier"}], "role": "symbol", "type": "topic", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "title": "StatusBarNotificationStyle", "abstract": [{"text": "A Style defines the appearance of a notification.", "type": "text"}], "fragments": [{"kind": "keyword", "text": "class"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationStyle", "kind": "identifier"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment/centerWithText": {"abstract": [{"type": "text", "text": "Centers the left-view together with the text. The left-view will be positioned at the leading edge of the text. The text is left-aligned. This is the default."}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewalignment/centerwithtext", "fragments": [{"kind": "keyword", "text": "case"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "centerWithText"}], "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment/centerWithText", "title": "StatusBarNotificationLeftViewAlignment.centerWithText", "role": "symbol", "type": "topic"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment": {"identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewAlignment", "role": "symbol", "kind": "symbol", "fragments": [{"kind": "keyword", "text": "enum"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "StatusBarNotificationLeftViewAlignment"}], "abstract": [{"text": "Defines the appearance of a left-view, if set. This includes the activity indicator.", "type": "text"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewalignment", "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationLeftViewAlignment"}], "type": "topic", "title": "StatusBarNotificationLeftViewAlignment"}}}