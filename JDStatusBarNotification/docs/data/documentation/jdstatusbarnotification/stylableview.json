{"topicSections": [{"title": "Instance Properties", "identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StylableView/style"]}], "primaryContentSections": [{"kind": "declarations", "declarations": [{"languages": ["swift"], "platforms": ["iOS"], "tokens": [{"text": "protocol", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "St<PERSON>bleView"}, {"kind": "text", "text": " : "}, {"preciseIdentifier": "c:objc(cs)UIView", "text": "UIView", "kind": "typeIdentifier"}]}]}], "schemaVersion": {"minor": 3, "patch": 0, "major": 0}, "identifier": {"url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StylableView", "interfaceLanguage": "swift"}, "metadata": {"role": "symbol", "fragments": [{"kind": "keyword", "text": "protocol"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "St<PERSON>bleView"}], "modules": [{"name": "JDStatusBarNotification"}], "navigatorTitle": [{"text": "St<PERSON>bleView", "kind": "identifier"}], "roleHeading": "Protocol", "externalID": "s:23JDStatusBarNotification12StylableViewP", "symbolKind": "protocol", "title": "St<PERSON>bleView"}, "kind": "symbol", "abstract": [{"text": "A view that can be styled by setting a StatusBarNotificationStyle.", "type": "text"}], "relationshipsSections": [{"title": "Inherits From", "identifiers": ["doc://calimarkus.JDStatusBarNotification/objc(cs)UIView"], "kind": "relationships", "type": "inheritsFrom"}], "sections": [], "hierarchy": {"paths": [["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification"]]}, "variants": [{"paths": ["/documentation/jdstatusbarnotification/stylableview"], "traits": [{"interfaceLanguage": "swift"}]}], "references": {"doc://calimarkus.JDStatusBarNotification/objc(cs)UIView": {"type": "unresolvable", "identifier": "doc://calimarkus.JDStatusBarNotification/objc(cs)UIView", "title": "UIKit.UIView"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StylableView/style": {"type": "topic", "role": "symbol", "fragments": [{"text": "var", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "style", "kind": "identifier"}, {"kind": "text", "text": ": "}, {"preciseIdentifier": "c:@M@JDStatusBarNotification@objc(cs)JDStatusBarNotificationStyle", "text": "StatusBarNotificationStyle", "kind": "typeIdentifier"}], "url": "/documentation/jdstatusbarnotification/stylableview/style", "abstract": [], "required": true, "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StylableView/style", "kind": "symbol", "title": "style"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StylableView": {"title": "St<PERSON>bleView", "role": "symbol", "fragments": [{"kind": "keyword", "text": "protocol"}, {"text": " ", "kind": "text"}, {"text": "St<PERSON>bleView", "kind": "identifier"}], "navigatorTitle": [{"text": "St<PERSON>bleView", "kind": "identifier"}], "url": "/documentation/jdstatusbarnotification/stylableview", "abstract": [{"text": "A view that can be styled by setting a StatusBarNotificationStyle.", "type": "text"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StylableView", "kind": "symbol", "type": "topic"}}}