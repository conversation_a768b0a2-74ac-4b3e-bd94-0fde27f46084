{"identifier": {"interfaceLanguage": "swift", "url": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification"}, "variants": [{"paths": ["/documentation/jdstatusbarnotification"], "traits": [{"interfaceLanguage": "swift"}]}], "schemaVersion": {"minor": 3, "major": 0, "patch": 0}, "kind": "symbol", "metadata": {"roleHeading": "Framework", "externalID": "JDStatusBarNotification", "role": "collection", "title": "JDStatusBarNotification", "symbolKind": "module", "modules": [{"name": "JDStatusBarNotification"}]}, "abstract": [{"type": "text", "text": "Highly customizable & feature rich notifications displayed below the status bar."}], "primaryContentSections": [{"kind": "content", "content": [{"type": "heading", "level": 2, "anchor": "Overview", "text": "Overview"}, {"type": "paragraph", "inlineContent": [{"identifier": "https://user-images.githubusercontent.com/807039/173831886-d7c8cca9-9274-429d-b924-78f21a4f6092.jpg", "type": "image"}]}]}], "sections": [], "topicSections": [{"title": "<PERSON><PERSON>s", "identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/GettingStarted"]}, {"identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter"], "title": "Notification Presenter"}, {"identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle", "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle"], "title": "Notification Style"}, {"identifiers": ["doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StylableView"], "title": "Protocols"}], "hierarchy": {"paths": [[]]}, "references": {"doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle": {"title": "StatusBarNotificationTextStyle", "fragments": [{"kind": "keyword", "text": "class"}, {"text": " ", "kind": "text"}, {"text": "StatusBarNotificationTextStyle", "kind": "identifier"}], "role": "symbol", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationTextStyle", "url": "/documentation/jdstatusbarnotification/statusbarnotificationtextstyle", "type": "topic", "navigatorTitle": [{"text": "StatusBarNotificationTextStyle", "kind": "identifier"}], "abstract": [{"type": "text", "text": "Defines the appearance of a text label."}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundStyle": {"fragments": [{"text": "class", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "StatusBarNotificationBackgroundStyle", "kind": "identifier"}], "kind": "symbol", "navigatorTitle": [{"text": "StatusBarNotificationBackgroundStyle", "kind": "identifier"}], "role": "symbol", "url": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundstyle", "title": "StatusBarNotificationBackgroundStyle", "type": "topic", "abstract": [{"type": "text", "text": "Defines the appearance of the notification background."}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundStyle"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StylableView": {"title": "St<PERSON>bleView", "role": "symbol", "fragments": [{"kind": "keyword", "text": "protocol"}, {"text": " ", "kind": "text"}, {"text": "St<PERSON>bleView", "kind": "identifier"}], "navigatorTitle": [{"text": "St<PERSON>bleView", "kind": "identifier"}], "url": "/documentation/jdstatusbarnotification/stylableview", "abstract": [{"text": "A view that can be styled by setting a StatusBarNotificationStyle.", "type": "text"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StylableView", "kind": "symbol", "type": "topic"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/GettingStarted": {"type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/GettingStarted", "kind": "article", "title": "Getting Started", "abstract": [{"type": "text", "text": "Find some simple examples below to get started. Most APIs can also be called from Objective-C."}], "role": "article", "url": "/documentation/jdstatusbarnotification/gettingstarted"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/pill": {"title": "StatusBarNotificationBackgroundType.pill", "url": "/documentation/jdstatusbarnotification/statusbarnotificationbackgroundtype/pill", "abstract": [{"type": "text", "text": "The background is a floating pill around the text. The pill size and appearance can be customized. This is the default."}], "kind": "symbol", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/pill", "role": "symbol", "fragments": [{"kind": "keyword", "text": "case"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "pill"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle": {"navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationLeftViewStyle"}], "type": "topic", "kind": "symbol", "title": "StatusBarNotificationLeftViewStyle", "abstract": [{"text": "Defines the appearance of a left-view, if set. It also applies to the activity indicator.", "type": "text"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationleftviewstyle", "role": "symbol", "fragments": [{"text": "class", "kind": "keyword"}, {"text": " ", "kind": "text"}, {"text": "StatusBarNotificationLeftViewStyle", "kind": "identifier"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationLeftViewStyle"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification": {"abstract": [{"text": "Highly customizable & feature rich notifications displayed below the status bar.", "type": "text"}], "role": "collection", "type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification", "url": "/documentation/jdstatusbarnotification", "kind": "symbol", "title": "JDStatusBarNotification"}, "https://user-images.githubusercontent.com/807039/173831886-d7c8cca9-9274-429d-b924-78f21a4f6092.jpg": {"variants": [{"url": "https://user-images.githubusercontent.com/807039/173831886-d7c8cca9-9274-429d-b924-78f21a4f6092.jpg", "traits": ["1x", "light"]}], "identifier": "https://user-images.githubusercontent.com/807039/173831886-d7c8cca9-9274-429d-b924-78f21a4f6092.jpg", "type": "image", "alt": "Example notifications in multiple styles."}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle": {"url": "/documentation/jdstatusbarnotification/statusbarnotificationstyle", "navigatorTitle": [{"text": "StatusBarNotificationStyle", "kind": "identifier"}], "role": "symbol", "type": "topic", "kind": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationStyle", "title": "StatusBarNotificationStyle", "abstract": [{"text": "A Style defines the appearance of a notification.", "type": "text"}], "fragments": [{"kind": "keyword", "text": "class"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationStyle", "kind": "identifier"}]}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle": {"type": "topic", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationProgressBarStyle", "kind": "symbol", "role": "symbol", "fragments": [{"text": "class", "kind": "keyword"}, {"kind": "text", "text": " "}, {"text": "StatusBarNotificationProgressBarStyle", "kind": "identifier"}], "title": "StatusBarNotificationProgressBarStyle", "abstract": [{"type": "text", "text": "Defines the appearance of the progress bar."}], "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationProgressBarStyle"}], "url": "/documentation/jdstatusbarnotification/statusbarnotificationprogressbarstyle"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle": {"abstract": [{"text": "Defines the appearance of the pill, when using ", "type": "text"}, {"type": "reference", "isActive": true, "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationBackgroundType/pill"}], "navigatorTitle": [{"kind": "identifier", "text": "StatusBarNotificationPillStyle"}], "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/StatusBarNotificationPillStyle", "type": "topic", "kind": "symbol", "role": "symbol", "url": "/documentation/jdstatusbarnotification/statusbarnotificationpillstyle", "fragments": [{"kind": "keyword", "text": "class"}, {"text": " ", "kind": "text"}, {"kind": "identifier", "text": "StatusBarNotificationPillStyle"}], "title": "StatusBarNotificationPillStyle"}, "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter": {"abstract": [{"type": "text", "text": "The NotificationPresenter let’s you present notifications below the statusBar."}, {"type": "text", "text": " "}, {"type": "text", "text": "You can customize the style (colors, fonts, etc.) and animations. It supports notch"}, {"type": "text", "text": " "}, {"type": "text", "text": "and no-notch devices, landscape & portrait layouts and Drag-to-Dismiss. It can display a"}, {"text": " ", "type": "text"}, {"type": "text", "text": "title, a subtitle, an activity indicator, an animated progress bar & custom views out of the box."}], "fragments": [{"text": "class", "kind": "keyword"}, {"kind": "text", "text": " "}, {"kind": "identifier", "text": "NotificationPresenter"}], "navigatorTitle": [{"text": "NotificationPresenter", "kind": "identifier"}], "role": "symbol", "identifier": "doc://calimarkus.JDStatusBarNotification/documentation/JDStatusBarNotification/NotificationPresenter", "url": "/documentation/jdstatusbarnotification/notificationpresenter", "type": "topic", "kind": "symbol", "title": "NotificationPresenter"}}}