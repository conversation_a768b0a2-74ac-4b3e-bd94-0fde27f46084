/*!
 * This source file is part of the Swift.org open source project
 * 
 * Copyright (c) 2021 Apple Inc. and the Swift project authors
 * Licensed under Apache License v2.0 with Runtime Library Exception
 * 
 * See https://swift.org/LICENSE.txt for license information
 * See https://swift.org/CONTRIBUTORS.txt for Swift project authors
 */.color-scheme-toggle[data-v-02a6f6ec]{--toggle-color-fill:var(--color-button-background);--toggle-color-text:var(--color-fill-blue);font-size:.70588rem;line-height:1.33333;font-weight:400;font-family:var(--typography-html-font,"Helvetica Neue","Helvetica","Arial",sans-serif);border:1px solid var(--toggle-color-fill);border-radius:var(--toggle-border-radius-outer,var(--border-radius,4px));display:inline-flex;padding:1px}@media screen{[data-color-scheme=dark] .color-scheme-toggle[data-v-02a6f6ec]{--toggle-color-text:var(--color-figure-blue)}}@media screen and (prefers-color-scheme:dark){[data-color-scheme=auto] .color-scheme-toggle[data-v-02a6f6ec]{--toggle-color-text:var(--color-figure-blue)}}@media print{.color-scheme-toggle[data-v-02a6f6ec]{display:none}}input[data-v-02a6f6ec]{position:absolute;clip:rect(1px,1px,1px,1px);-webkit-clip-path:inset(0 0 99.9% 99.9%);clip-path:inset(0 0 99.9% 99.9%);overflow:hidden;height:1px;width:1px;padding:0;border:0;-webkit-appearance:none;-moz-appearance:none;appearance:none}.fromkeyboard label[data-v-02a6f6ec]:focus-within{outline:4px solid var(--color-focus-color);outline-offset:1px}.text[data-v-02a6f6ec]{border:1px solid transparent;border-radius:var(--toggle-border-radius-inner,2px);color:var(--toggle-color-text);display:inline-block;text-align:center;padding:1px 6px;min-width:42px;box-sizing:border-box}.text[data-v-02a6f6ec]:hover{cursor:pointer}input:checked+.text[data-v-02a6f6ec]{--toggle-color-text:var(--color-button-text);background:var(--toggle-color-fill);border-color:var(--toggle-color-fill)}.footer[data-v-4e049dbd]{border-top:1px solid var(--color-grid)}.row[data-v-4e049dbd]{margin-left:auto;margin-right:auto;width:980px;display:flex;flex-direction:row-reverse;margin:20px auto}@media only screen and (max-width:1250px){.row[data-v-4e049dbd]{width:692px}}@media only screen and (max-width:735px){.row[data-v-4e049dbd]{width:87.5%}}@media only screen and (max-width:320px){.row[data-v-4e049dbd]{width:215px}}@media only screen and (max-width:735px){.row[data-v-4e049dbd]{width:100%;padding:0 .94118rem;box-sizing:border-box}}.InitialLoadingPlaceholder[data-v-35c356b6]{background:var(--colors-loading-placeholder-background,var(--color-loading-placeholder-background));height:100vh;width:100%}.svg-icon[data-v-33d3200a]{fill:var(--colors-svg-icon-fill-light,var(--color-svg-icon));transform:scale(1);-webkit-transform:scale(1);overflow:visible}.theme-dark .svg-icon[data-v-33d3200a]{fill:var(--colors-svg-icon-fill-dark,var(--color-svg-icon))}.svg-icon.icon-inline[data-v-33d3200a]{display:inline-block;vertical-align:middle;fill:currentColor}.svg-icon.icon-inline[data-v-33d3200a] .svg-icon-stroke{stroke:currentColor}[data-v-33d3200a] .svg-icon-stroke{stroke:var(--colors-svg-icon-fill-light,var(--color-svg-icon))}.theme-dark[data-v-33d3200a] .svg-icon-stroke{stroke:var(--colors-svg-icon-fill-dark,var(--color-svg-icon))}.suggest-lang[data-v-ad72c62e]{background:#000;color:#fff;display:flex;justify-content:center;border-bottom:1px solid var(--color-grid)}.suggest-lang__wrapper[data-v-ad72c62e]{display:flex;align-items:center;width:100%;max-width:var(--wrapper-max-width,1920px);margin:0 .94118rem;position:relative;height:52px}.suggest-lang__link[data-v-ad72c62e]{font-size:.82353rem;line-height:1.42857;font-weight:400;font-family:var(--typography-html-font,"Helvetica Neue","Helvetica","Arial",sans-serif);margin:0 auto;color:#09f}.suggest-lang__close-icon-wrapper[data-v-ad72c62e]{position:absolute;right:-.23529rem;top:0;height:100%;box-sizing:border-box;display:flex;align-items:center;z-index:1}.suggest-lang__close-icon-button[data-v-ad72c62e]{padding:.23529rem}.suggest-lang__close-icon-button .close-icon[data-v-ad72c62e]{width:8px;display:block}.suggest-lang .inline-chevron-right-icon[data-v-ad72c62e]{padding-left:.23529rem;width:8px}select[data-v-7e4d9b69]{font-size:.70588rem;line-height:1.33333;font-weight:600;font-family:var(--typography-html-font,"Helvetica Neue","Helvetica","Arial",sans-serif);color:var(--color-fill-blue);padding-right:15px;-webkit-appearance:none;-moz-appearance:none;appearance:none;background:transparent;border:none;cursor:pointer}select[data-v-7e4d9b69]:hover{text-decoration:underline}.locale-selector[data-v-7e4d9b69]{position:relative}.svg-icon.icon-inline[data-v-7e4d9b69]{position:absolute;fill:var(--color-fill-blue);right:2px;bottom:7px;height:5px}html{-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%;background-color:var(--colors-text-background,var(--color-text-background));height:100%}abbr,blockquote,body,button,dd,dl,dt,fieldset,figure,form,h1,h2,h3,h4,h5,h6,hgroup,input,legend,li,ol,p,pre,ul{margin:0;padding:0}address,caption,code,figcaption,pre,th{font-size:1em;font-weight:400;font-style:normal}fieldset,iframe,img{border:0}caption,th{text-align:left}table{border-collapse:collapse;border-spacing:0}article,aside,details,figcaption,figure,footer,header,hgroup,main,nav,section,summary{display:block}audio,canvas,progress,video{display:inline-block;vertical-align:baseline}button{background:none;border:0;box-sizing:content-box;color:inherit;cursor:pointer;font:inherit;line-height:inherit;overflow:visible;vertical-align:inherit}button:disabled{cursor:default}:focus{outline:4px solid var(--color-focus-color);outline-offset:1px}::-moz-focus-inner{border:0;padding:0}@media print{#content,#main,body{color:#000}a,a:link,a:visited{color:#000;text-decoration:none}.hide,.noprint{display:none}}body{height:100%;min-width:320px}html{font:var(--typography-html-font,17px "Helvetica Neue","Helvetica","Arial",sans-serif);quotes:"“" "”"}html:lang(ja-JP){quotes:"「" "」"}body{font-size:1rem;line-height:1.47059;font-weight:400;font-family:var(--typography-html-font,"Helvetica Neue","Helvetica","Arial",sans-serif);background-color:var(--color-text-background);color:var(--colors-text,var(--color-text));font-style:normal;word-wrap:break-word;--spacing-stacked-margin-small:0.4em;--spacing-stacked-margin-large:0.8em;--spacing-stacked-margin-xlarge:calc(var(--spacing-stacked-margin-large)*2);--spacing-param:1.64706rem;--declaration-code-listing-margin:30px 0 0 0;--code-block-style-elements-padding:8px 14px}body,button,input,select,textarea{font-synthesis:none;-moz-font-feature-settings:"kern";-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;direction:ltr;text-align:left}h1,h2,h3,h4,h5,h6{color:var(--colors-header-text,var(--color-header-text))}h1+*,h2+*,h3+*,h4+*,h5+*,h6+*{margin-top:var(--spacing-stacked-margin-large)}ol+h1,ol+h2,ol+h3,ol+h4,ol+h5,ol+h6,p+h1,p+h2,p+h3,p+h4,p+h5,p+h6,ul+h1,ul+h2,ul+h3,ul+h4,ul+h5,ul+h6{margin-top:1.6em}ol+*,p+*,ul+*{margin-top:var(--spacing-stacked-margin-large)}ol,ul{margin-left:1.17647em}ol ol,ol ul,ul ol,ul ul{margin-top:0;margin-bottom:0}nav ol,nav ul{margin:0;list-style:none}li li{font-size:1em}a{color:var(--colors-link,var(--color-link))}a:link,a:visited{text-decoration:none}a:hover{text-decoration:underline}a:active{text-decoration:none}p+a{display:inline-block}b,strong{font-weight:600}cite,dfn,em,i{font-style:italic}sup{font-size:.6em;vertical-align:top;position:relative;bottom:-.2em}h1 sup,h2 sup,h3 sup{font-size:.4em}sup a{vertical-align:inherit;color:inherit}sup a:hover{color:var(--figure-blue);text-decoration:none}sub{line-height:1}abbr{border:0}pre{overflow:auto;-webkit-overflow-scrolling:auto;white-space:pre;word-wrap:normal}code{font-family:var(--typography-html-font-mono,Menlo,monospace);font-weight:inherit;letter-spacing:0}.syntax-addition{color:var(--syntax-addition,var(--color-syntax-addition))}.syntax-comment{color:var(--syntax-comment,var(--color-syntax-comments))}.syntax-quote{color:var(--syntax-quote,var(--color-syntax-comments))}.syntax-deletion{color:var(--syntax-deletion,var(--color-syntax-deletion))}.syntax-keyword{color:var(--syntax-keyword,var(--color-syntax-keywords))}.syntax-literal{color:var(--syntax-literal,var(--color-syntax-keywords))}.syntax-selector-tag{color:var(--syntax-selector-tag,var(--color-syntax-keywords))}.syntax-string{color:var(--syntax-string,var(--color-syntax-strings))}.syntax-bullet{color:var(--syntax-bullet,var(--color-syntax-characters))}.syntax-meta{color:var(--syntax-meta,var(--color-syntax-characters))}.syntax-number{color:var(--syntax-number,var(--color-syntax-characters))}.syntax-symbol{color:var(--syntax-symbol,var(--color-syntax-characters))}.syntax-tag{color:var(--syntax-tag,var(--color-syntax-characters))}.syntax-attr{color:var(--syntax-attr,var(--color-syntax-other-type-names))}.syntax-built_in{color:var(--syntax-built_in,var(--color-syntax-other-type-names))}.syntax-builtin-name{color:var(--syntax-builtin-name,var(--color-syntax-other-type-names))}.syntax-class{color:var(--syntax-class,var(--color-syntax-other-type-names))}.syntax-params{color:var(--syntax-params,var(--color-syntax-other-type-names))}.syntax-section{color:var(--syntax-section,var(--color-syntax-other-type-names))}.syntax-title{color:var(--syntax-title,var(--color-syntax-other-type-names))}.syntax-type{color:var(--syntax-type,var(--color-syntax-other-type-names))}.syntax-attribute{color:var(--syntax-attribute,var(--color-syntax-plain-text))}.syntax-identifier{color:var(--syntax-identifier,var(--color-syntax-plain-text))}.syntax-subst{color:var(--syntax-subst,var(--color-syntax-plain-text))}.syntax-doctag,.syntax-strong{font-weight:700}.syntax-emphasis,.syntax-link{font-style:italic}[data-syntax=swift] .syntax-meta{color:var(--syntax-meta,var(--color-syntax-keywords))}[data-syntax=swift] .syntax-class,[data-syntax=swift] .syntax-keyword+.syntax-params,[data-syntax=swift] .syntax-params+.syntax-params{color:unset}[data-syntax=json] .syntax-attr{color:var(--syntax-attr,var(--color-syntax-strings))}#skip-nav{position:absolute;clip:rect(1px,1px,1px,1px);-webkit-clip-path:inset(0 0 99.9% 99.9%);clip-path:inset(0 0 99.9% 99.9%);overflow:hidden;height:1px;width:1px;padding:0;border:0}#skip-nav:active,#skip-nav:focus{position:relative;float:left;width:-webkit-fit-content;width:-moz-fit-content;width:fit-content;color:var(--color-figure-blue);font-size:1em;padding:0 10px;z-index:100000;top:0;left:0;height:44px;line-height:44px;-webkit-clip-path:unset;clip-path:unset}.nav--in-breakpoint-range #skip-nav{display:none}.visuallyhidden{position:absolute;clip:rect(1px,1px,1px,1px);-webkit-clip-path:inset(0 0 99.9% 99.9%);clip-path:inset(0 0 99.9% 99.9%);overflow:hidden;height:1px;width:1px;padding:0;border:0}@-webkit-keyframes pulse{0%{opacity:0}33%{opacity:1}66%{opacity:1}to{opacity:0}}@keyframes pulse{0%{opacity:0}33%{opacity:1}66%{opacity:1}to{opacity:0}}.changed{border:1px solid var(--color-changes-modified);border-radius:var(--border-radius,4px);position:relative}.changed.displays-multiple-lines,.displays-multiple-lines .changed{border-radius:var(--border-radius,4px)}.changed:after{left:8px;background-image:url(../img/modified-icon.f496e73d.svg);background-repeat:no-repeat;bottom:0;content:" ";margin:auto;margin-right:8px;position:absolute;top:0;width:1.17647rem;height:1.17647rem;margin-top:.61765rem;z-index:2}@media screen{[data-color-scheme=dark] .changed:after{background-image:url(../img/modified-icon.f496e73d.svg)}}@media screen and (prefers-color-scheme:dark){[data-color-scheme=auto] .changed:after{background-image:url(../img/modified-icon.f496e73d.svg)}}.changed-added{border-color:var(--color-changes-added)}.changed-added:after{background-image:url(../img/added-icon.d6f7e47d.svg)}@media screen{[data-color-scheme=dark] .changed-added:after{background-image:url(../img/added-icon.d6f7e47d.svg)}}@media screen and (prefers-color-scheme:dark){[data-color-scheme=auto] .changed-added:after{background-image:url(../img/added-icon.d6f7e47d.svg)}}.changed-deprecated{border-color:var(--color-changes-deprecated)}.changed-deprecated:after{background-image:url(../img/deprecated-icon.015b4f17.svg)}@media screen{[data-color-scheme=dark] .changed-deprecated:after{background-image:url(../img/deprecated-icon.015b4f17.svg)}}@media screen and (prefers-color-scheme:dark){[data-color-scheme=auto] .changed-deprecated:after{background-image:url(../img/deprecated-icon.015b4f17.svg)}}.changed.link-block:after,.changed.relationships-item:after,.link-block .changed:after{margin-top:10px}.change-added,.change-removed{padding:2px 0}.change-removed{background-color:var(--color-highlight-red)}.change-added{background-color:var(--color-highlight-green)}body{color-scheme:light dark}body[data-color-scheme=light]{color-scheme:light}body[data-color-scheme=dark]{color-scheme:dark}body{--color-fill:#fff;--color-fill-secondary:#f7f7f7;--color-fill-tertiary:#f0f0f0;--color-fill-quaternary:#282828;--color-fill-blue:#00f;--color-fill-light-blue-secondary:#d1d1ff;--color-fill-gray:#ccc;--color-fill-gray-secondary:#f5f5f5;--color-fill-gray-tertiary:#f0f0f0;--color-fill-gray-quaternary:#f0f0f0;--color-fill-green-secondary:#f0fff0;--color-fill-orange-secondary:#fffaf6;--color-fill-red-secondary:#fff0f5;--color-figure-blue:#36f;--color-figure-gray:#000;--color-figure-gray-secondary:#666;--color-figure-gray-secondary-alt:#666;--color-figure-gray-tertiary:#666;--color-figure-green:green;--color-figure-light-gray:#666;--color-figure-orange:#c30;--color-figure-red:red;--color-tutorials-teal:#000;--color-article-background:var(--color-fill-tertiary);--color-article-body-background:var(--color-fill);--color-aside-deprecated:var(--color-figure-gray);--color-aside-deprecated-background:var(--color-fill-orange-secondary);--color-aside-deprecated-border:var(--color-figure-orange);--color-aside-experiment:var(--color-figure-gray);--color-aside-experiment-background:var(--color-fill-gray-secondary);--color-aside-experiment-border:var(--color-figure-light-gray);--color-aside-important:var(--color-figure-gray);--color-aside-important-background:var(--color-fill-gray-secondary);--color-aside-important-border:var(--color-figure-light-gray);--color-aside-note:var(--color-figure-gray);--color-aside-note-background:var(--color-fill-gray-secondary);--color-aside-note-border:var(--color-figure-light-gray);--color-aside-tip:var(--color-figure-gray);--color-aside-tip-background:var(--color-fill-gray-secondary);--color-aside-tip-border:var(--color-figure-light-gray);--color-aside-warning:var(--color-figure-gray);--color-aside-warning-background:var(--color-fill-red-secondary);--color-aside-warning-border:var(--color-figure-red);--color-badge-default:var(--color-figure-light-gray);--color-badge-beta:var(--color-figure-gray-tertiary);--color-badge-deprecated:var(--color-figure-orange);--color-badge-dark-default:#fff;--color-badge-dark-beta:#b0b0b0;--color-badge-dark-deprecated:#f60;--color-button-background:var(--color-fill-blue);--color-button-background-active:#36f;--color-button-background-hover:var(--color-figure-blue);--color-button-text:#fff;--color-call-to-action-background:var(--color-fill-secondary);--color-changes-added:var(--color-figure-light-gray);--color-changes-added-hover:var(--color-figure-light-gray);--color-changes-deprecated:var(--color-figure-light-gray);--color-changes-deprecated-hover:var(--color-figure-light-gray);--color-changes-modified:var(--color-figure-light-gray);--color-changes-modified-hover:var(--color-figure-light-gray);--color-changes-modified-previous-background:var(--color-fill);--color-code-background:var(--color-fill-secondary);--color-code-collapsible-background:var(--color-fill-tertiary);--color-code-collapsible-text:var(--color-figure-gray-secondary-alt);--color-code-line-highlight:rgba(51,102,255,0.08);--color-code-line-highlight-border:var(--color-figure-blue);--color-code-plain:var(--color-figure-gray);--color-dropdown-background:hsla(0,0%,100%,0.8);--color-dropdown-border:#ccc;--color-dropdown-option-text:#666;--color-dropdown-text:#000;--color-dropdown-dark-background:hsla(0,0%,100%,0.1);--color-dropdown-dark-border:hsla(0,0%,94.1%,0.2);--color-dropdown-dark-option-text:#ccc;--color-dropdown-dark-text:#fff;--color-eyebrow:var(--color-figure-gray-secondary);--color-focus-border-color:var(--color-fill-blue);--color-focus-color:rgba(0,125,250,0.6);--color-form-error:var(--color-figure-red);--color-form-error-background:var(--color-fill-red-secondary);--color-form-valid:var(--color-figure-green);--color-form-valid-background:var(--color-fill-green-secondary);--color-generic-modal-background:var(--color-fill);--color-grid:var(--color-fill-gray);--color-header-text:var(--color-figure-gray);--color-hero-eyebrow:#ccc;--color-link:var(--color-figure-blue);--color-loading-placeholder-background:var(--color-fill);--color-nav-color:#666;--color-nav-current-link:rgba(0,0,0,0.6);--color-nav-expanded:#fff;--color-nav-hierarchy-collapse-background:#f0f0f0;--color-nav-hierarchy-collapse-borders:#ccc;--color-nav-hierarchy-item-borders:#ccc;--color-nav-keyline:rgba(0,0,0,0.2);--color-nav-link-color:#000;--color-nav-link-color-hover:#36f;--color-nav-outlines:#ccc;--color-nav-rule:hsla(0,0%,94.1%,0.5);--color-nav-solid-background:#fff;--color-nav-sticking-expanded-keyline:rgba(0,0,0,0.1);--color-nav-stuck:hsla(0,0%,100%,0.9);--color-nav-uiblur-expanded:hsla(0,0%,100%,0.9);--color-nav-uiblur-stuck:hsla(0,0%,100%,0.7);--color-nav-root-subhead:var(--color-tutorials-teal);--color-nav-dark-border-top-color:hsla(0,0%,100%,0.4);--color-nav-dark-color:#b0b0b0;--color-nav-dark-current-link:hsla(0,0%,100%,0.6);--color-nav-dark-expanded:#2a2a2a;--color-nav-dark-hierarchy-collapse-background:#424242;--color-nav-dark-hierarchy-collapse-borders:#666;--color-nav-dark-hierarchy-item-borders:#424242;--color-nav-dark-keyline:rgba(66,66,66,0.95);--color-nav-dark-link-color:#fff;--color-nav-dark-link-color-hover:#09f;--color-nav-dark-outlines:#575757;--color-nav-dark-rule:#575757;--color-nav-dark-solid-background:#000;--color-nav-dark-sticking-expanded-keyline:rgba(66,66,66,0.7);--color-nav-dark-stuck:rgba(42,42,42,0.9);--color-nav-dark-uiblur-expanded:rgba(42,42,42,0.9);--color-nav-dark-uiblur-stuck:rgba(42,42,42,0.7);--color-nav-dark-root-subhead:#fff;--color-runtime-preview-background:var(--color-fill-tertiary);--color-runtime-preview-disabled-text:hsla(0,0%,40%,0.6);--color-runtime-preview-text:var(--color-figure-gray-secondary);--color-secondary-label:var(--color-figure-gray-secondary);--color-step-background:var(--color-fill-secondary);--color-step-caption:var(--color-figure-gray-secondary);--color-step-focused:var(--color-figure-light-gray);--color-step-text:var(--color-figure-gray-secondary);--color-svg-icon:#666;--color-syntax-addition:var(--color-figure-green);--color-syntax-attributes:#947100;--color-syntax-characters:#272ad8;--color-syntax-comments:#707f8c;--color-syntax-deletion:var(--color-figure-red);--color-syntax-documentation-markup:#506375;--color-syntax-documentation-markup-keywords:#506375;--color-syntax-heading:#ba2da2;--color-syntax-keywords:#ad3da4;--color-syntax-marks:#000;--color-syntax-numbers:#272ad8;--color-syntax-other-class-names:#703daa;--color-syntax-other-constants:#4b21b0;--color-syntax-other-declarations:#047cb0;--color-syntax-other-function-and-method-names:#4b21b0;--color-syntax-other-instance-variables-and-globals:#703daa;--color-syntax-other-preprocessor-macros:#78492a;--color-syntax-other-type-names:#703daa;--color-syntax-param-internal-name:#404040;--color-syntax-plain-text:#000;--color-syntax-preprocessor-statements:#78492a;--color-syntax-project-class-names:#3e8087;--color-syntax-project-constants:#2d6469;--color-syntax-project-function-and-method-names:#2d6469;--color-syntax-project-instance-variables-and-globals:#3e8087;--color-syntax-project-preprocessor-macros:#78492a;--color-syntax-project-type-names:#3e8087;--color-syntax-strings:#d12f1b;--color-syntax-type-declarations:#03638c;--color-syntax-urls:#1337ff;--color-tabnav-item-border-color:var(--color-fill-gray);--color-text:var(--color-figure-gray);--color-text-background:var(--color-fill);--color-tutorial-assessments-background:var(--color-fill-secondary);--color-tutorial-background:var(--color-fill);--color-tutorial-navbar-dropdown-background:var(--color-fill);--color-tutorial-navbar-dropdown-border:var(--color-fill-gray);--color-tutorial-quiz-border-active:var(--color-figure-blue);--color-tutorials-overview-background:#161616;--color-tutorials-overview-content:#fff;--color-tutorials-overview-content-alt:#fff;--color-tutorials-overview-eyebrow:#ccc;--color-tutorials-overview-icon:#b0b0b0;--color-tutorials-overview-link:#09f;--color-tutorials-overview-navigation-link:#ccc;--color-tutorials-overview-navigation-link-active:#fff;--color-tutorials-overview-navigation-link-hover:#fff;--color-tutorial-hero-text:#fff;--color-tutorial-hero-background:#000;--color-navigator-item-hover:rgba(0,0,255,0.05);--color-card-background:var(--color-fill);--color-card-content-text:var(--color-figure-gray);--color-card-eyebrow:var(--color-figure-gray-secondary-alt);--color-card-shadow:rgba(0,0,0,0.04);--color-link-block-card-border:rgba(0,0,0,0.04);--color-standard-red:#8b0000;--color-standard-orange:#8b4000;--color-standard-yellow:#8f7200;--color-standard-blue:#002d75;--color-standard-green:#023b2d;--color-standard-purple:#512b55;--color-standard-gray:#2a2a2a}@media screen{body[data-color-scheme=dark]{--color-fill:#000;--color-fill-secondary:#161616;--color-fill-tertiary:#2a2a2a;--color-fill-blue:#06f;--color-fill-light-blue-secondary:#004ec4;--color-fill-gray:#575757;--color-fill-gray-secondary:#222;--color-fill-gray-tertiary:#424242;--color-fill-gray-quaternary:#424242;--color-fill-green-secondary:#030;--color-fill-orange-secondary:#472400;--color-fill-red-secondary:#300;--color-figure-blue:#09f;--color-figure-gray:#fff;--color-figure-gray-secondary:#ccc;--color-figure-gray-secondary-alt:#b0b0b0;--color-figure-gray-tertiary:#b0b0b0;--color-figure-green:#090;--color-figure-light-gray:#b0b0b0;--color-figure-orange:#f60;--color-figure-red:#f33;--color-tutorials-teal:#fff;--color-article-body-background:#111;--color-badge-default:var(--color-badge-dark-default);--color-button-background-active:#06f;--color-code-line-highlight:rgba(0,153,255,0.08);--color-dropdown-background:var(--color-dropdown-dark-background);--color-dropdown-border:var(--color-dropdown-dark-border);--color-dropdown-option-text:var(--color-dropdown-dark-option-text);--color-dropdown-text:var(--color-dropdown-dark-text);--color-nav-color:var(--color-nav-dark-color);--color-nav-current-link:var(--color-nav-dark-current-link);--color-nav-expanded:var(--color-nav-dark-expanded);--color-nav-hierarchy-collapse-background:var(--color-nav-dark-hierarchy-collapse-background);--color-nav-hierarchy-collapse-borders:var(--color-nav-dark-hierarchy-collapse-borders);--color-nav-hierarchy-item-borders:var(--color-nav-dark-hierarchy-item-borders);--color-nav-keyline:var(--color-nav-dark-keyline);--color-nav-link-color:var(--color-nav-dark-link-color);--color-nav-link-color-hover:var(--color-nav-dark-link-color-hover);--color-nav-outlines:var(--color-nav-dark-outlines);--color-nav-rule:var(--color-nav-dark-rule);--color-nav-solid-background:var(--color-nav-dark-solid-background);--color-nav-sticking-expanded-keyline:var(--color-nav-dark-sticking-expanded-keyline);--color-nav-stuck:var(--color-nav-dark-stuck);--color-nav-uiblur-expanded:var(--color-nav-dark-uiblur-expanded);--color-nav-uiblur-stuck:var(--color-nav-dark-uiblur-stuck);--color-runtime-preview-disabled-text:hsla(0,0%,80%,0.6);--color-syntax-attributes:#cc9768;--color-syntax-characters:#d9c97c;--color-syntax-comments:#7f8c98;--color-syntax-documentation-markup:#7f8c98;--color-syntax-documentation-markup-keywords:#a3b1bf;--color-syntax-keywords:#ff7ab2;--color-syntax-marks:#fff;--color-syntax-numbers:#d9c97c;--color-syntax-other-class-names:#dabaff;--color-syntax-other-constants:#a7ebdd;--color-syntax-other-declarations:#4eb0cc;--color-syntax-other-function-and-method-names:#b281eb;--color-syntax-other-instance-variables-and-globals:#b281eb;--color-syntax-other-preprocessor-macros:#ffa14f;--color-syntax-other-type-names:#dabaff;--color-syntax-param-internal-name:#bfbfbf;--color-syntax-plain-text:#fff;--color-syntax-preprocessor-statements:#ffa14f;--color-syntax-project-class-names:#acf2e4;--color-syntax-project-constants:#78c2b3;--color-syntax-project-function-and-method-names:#78c2b3;--color-syntax-project-instance-variables-and-globals:#78c2b3;--color-syntax-project-preprocessor-macros:#ffa14f;--color-syntax-project-type-names:#acf2e4;--color-syntax-strings:#ff8170;--color-syntax-type-declarations:#6bdfff;--color-syntax-urls:#69f;--color-tutorial-background:var(--color-fill-tertiary);--color-navigator-item-hover:rgba(0,102,255,0.5);--color-card-shadow:hsla(0,0%,100%,0.04);--color-link-block-card-border:hsla(0,0%,100%,0.25)}}@media screen and (prefers-color-scheme:dark){body[data-color-scheme=auto]{--color-fill:#000;--color-fill-secondary:#161616;--color-fill-tertiary:#2a2a2a;--color-fill-blue:#06f;--color-fill-light-blue-secondary:#004ec4;--color-fill-gray:#575757;--color-fill-gray-secondary:#222;--color-fill-gray-tertiary:#424242;--color-fill-gray-quaternary:#424242;--color-fill-green-secondary:#030;--color-fill-orange-secondary:#472400;--color-fill-red-secondary:#300;--color-figure-blue:#09f;--color-figure-gray:#fff;--color-figure-gray-secondary:#ccc;--color-figure-gray-secondary-alt:#b0b0b0;--color-figure-gray-tertiary:#b0b0b0;--color-figure-green:#090;--color-figure-light-gray:#b0b0b0;--color-figure-orange:#f60;--color-figure-red:#f33;--color-tutorials-teal:#fff;--color-article-body-background:#111;--color-badge-default:var(--color-badge-dark-default);--color-button-background-active:#06f;--color-code-line-highlight:rgba(0,153,255,0.08);--color-dropdown-background:var(--color-dropdown-dark-background);--color-dropdown-border:var(--color-dropdown-dark-border);--color-dropdown-option-text:var(--color-dropdown-dark-option-text);--color-dropdown-text:var(--color-dropdown-dark-text);--color-nav-color:var(--color-nav-dark-color);--color-nav-current-link:var(--color-nav-dark-current-link);--color-nav-expanded:var(--color-nav-dark-expanded);--color-nav-hierarchy-collapse-background:var(--color-nav-dark-hierarchy-collapse-background);--color-nav-hierarchy-collapse-borders:var(--color-nav-dark-hierarchy-collapse-borders);--color-nav-hierarchy-item-borders:var(--color-nav-dark-hierarchy-item-borders);--color-nav-keyline:var(--color-nav-dark-keyline);--color-nav-link-color:var(--color-nav-dark-link-color);--color-nav-link-color-hover:var(--color-nav-dark-link-color-hover);--color-nav-outlines:var(--color-nav-dark-outlines);--color-nav-rule:var(--color-nav-dark-rule);--color-nav-solid-background:var(--color-nav-dark-solid-background);--color-nav-sticking-expanded-keyline:var(--color-nav-dark-sticking-expanded-keyline);--color-nav-stuck:var(--color-nav-dark-stuck);--color-nav-uiblur-expanded:var(--color-nav-dark-uiblur-expanded);--color-nav-uiblur-stuck:var(--color-nav-dark-uiblur-stuck);--color-runtime-preview-disabled-text:hsla(0,0%,80%,0.6);--color-syntax-attributes:#cc9768;--color-syntax-characters:#d9c97c;--color-syntax-comments:#7f8c98;--color-syntax-documentation-markup:#7f8c98;--color-syntax-documentation-markup-keywords:#a3b1bf;--color-syntax-keywords:#ff7ab2;--color-syntax-marks:#fff;--color-syntax-numbers:#d9c97c;--color-syntax-other-class-names:#dabaff;--color-syntax-other-constants:#a7ebdd;--color-syntax-other-declarations:#4eb0cc;--color-syntax-other-function-and-method-names:#b281eb;--color-syntax-other-instance-variables-and-globals:#b281eb;--color-syntax-other-preprocessor-macros:#ffa14f;--color-syntax-other-type-names:#dabaff;--color-syntax-param-internal-name:#bfbfbf;--color-syntax-plain-text:#fff;--color-syntax-preprocessor-statements:#ffa14f;--color-syntax-project-class-names:#acf2e4;--color-syntax-project-constants:#78c2b3;--color-syntax-project-function-and-method-names:#78c2b3;--color-syntax-project-instance-variables-and-globals:#78c2b3;--color-syntax-project-preprocessor-macros:#ffa14f;--color-syntax-project-type-names:#acf2e4;--color-syntax-strings:#ff8170;--color-syntax-type-declarations:#6bdfff;--color-syntax-urls:#69f;--color-tutorial-background:var(--color-fill-tertiary);--color-navigator-item-hover:rgba(0,102,255,0.5);--color-card-shadow:hsla(0,0%,100%,0.04);--color-link-block-card-border:hsla(0,0%,100%,0.25)}}#main{outline-style:none}:root{--app-height:100vh}[data-v-7d594ed9] :focus:not(input):not(textarea):not(select){outline:none}.fromkeyboard[data-v-7d594ed9] :focus:not(input):not(textarea):not(select){outline:4px solid var(--color-focus-color);outline-offset:1px}#app[data-v-7d594ed9]{display:flex;flex-flow:column;min-height:100%}#app[data-v-7d594ed9]>*{min-width:0}#app .router-content[data-v-7d594ed9]{flex:1}.container[data-v-1f05d9ec]{margin-left:auto;margin-right:auto;width:980px;outline-style:none;margin-top:92px;margin-bottom:140px}@media only screen and (max-width:1250px){.container[data-v-1f05d9ec]{width:692px}}@media only screen and (max-width:735px){.container[data-v-1f05d9ec]{width:87.5%}}@media only screen and (max-width:320px){.container[data-v-1f05d9ec]{width:215px}}.error-content[data-v-1f05d9ec]{box-sizing:border-box;width:502px;margin-left:auto;margin-right:auto;margin-bottom:54px}@media only screen and (max-width:1250px){.error-content[data-v-1f05d9ec]{width:420px;margin-bottom:45px}}@media only screen and (max-width:735px){.error-content[data-v-1f05d9ec]{max-width:330px;width:auto;margin-bottom:35px}}.title[data-v-1f05d9ec]{text-align:center;font-size:2.82353rem;line-height:1.08333;font-weight:400;font-family:var(--typography-html-font,"Helvetica Neue","Helvetica","Arial",sans-serif)}@media only screen and (max-width:1250px){.title[data-v-1f05d9ec]{font-size:2.35294rem;line-height:1.1;font-weight:400;font-family:var(--typography-html-font,"Helvetica Neue","Helvetica","Arial",sans-serif)}}@media only screen and (max-width:735px){.title[data-v-1f05d9ec]{font-size:1.88235rem;line-height:1.125;font-weight:400;font-family:var(--typography-html-font,"Helvetica Neue","Helvetica","Arial",sans-serif)}}