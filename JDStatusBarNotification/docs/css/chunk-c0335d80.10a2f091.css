/*!
 * This source file is part of the Swift.org open source project
 * 
 * Copyright (c) 2021 Apple Inc. and the Swift project authors
 * Licensed under Apache License v2.0 with Runtime Library Exception
 * 
 * See https://swift.org/LICENSE.txt for license information
 * See https://swift.org/CONTRIBUTORS.txt for Swift project authors
 */[data-v-20945666] .code-listing{background:var(--background,var(--color-code-background));color:var(--text,var(--color-code-plain));border-color:var(--colors-grid,var(--color-grid));border-width:var(--code-border-width,1px);border-style:var(--code-border-style,solid)}[data-v-20945666] .code-listing pre{padding:var(--code-block-style-elements-padding)}[data-v-20945666] .code-listing pre>code{font-size:.88235rem;line-height:1.66667;font-weight:400;font-family:var(--typography-html-font-mono,Menlo,monospace)}[data-v-20945666] *+.code-listing,[data-v-20945666] *+.endpoint-example,[data-v-20945666] *+.inline-image-container,[data-v-20945666] *+aside,[data-v-20945666] *+figure,[data-v-20945666] .code-listing+*,[data-v-20945666] .endpoint-example+*,[data-v-20945666] .inline-image-container+*,[data-v-20945666] aside+*,[data-v-20945666] figure+*{margin-top:var(--spacing-stacked-margin-xlarge)}[data-v-20945666] *+dl,[data-v-20945666] dl+*{margin-top:var(--spacing-stacked-margin-large)}[data-v-20945666] img{display:block;margin:auto;max-width:100%}[data-v-20945666] ol,[data-v-20945666] ol li:not(:first-child),[data-v-20945666] ul,[data-v-20945666] ul li:not(:first-child){margin-top:var(--spacing-stacked-margin-large)}@media only screen and (max-width:735px){[data-v-20945666] ol,[data-v-20945666] ul{margin-left:1.25rem}}[data-v-20945666] dt:not(:first-child){margin-top:var(--spacing-stacked-margin-large)}[data-v-20945666] dd{margin-left:2em}.badge[data-v-8d6893ae]{--badge-color:var(--color-badge-default);--badge-dark-color:var(--color-badge-dark-default);font-size:.70588rem;line-height:1.33333;font-weight:400;font-family:var(--typography-html-font,"Helvetica Neue","Helvetica","Arial",sans-serif);display:inline-block;padding:2px 10px;white-space:nowrap;background:none;border-radius:var(--badge-border-radius,calc(var(--border-radius, 4px) - 1px));border-style:var(--badge-border-style,solid);border-width:var(--badge-border-width,1px);margin-left:10px;color:var(--badge-color)}.theme-dark .badge[data-v-8d6893ae]{--badge-color:var(--badge-dark-color)}.badge-deprecated[data-v-8d6893ae]{--badge-color:var(--color-badge-deprecated);--badge-dark-color:var(--color-badge-dark-deprecated)}.badge-beta[data-v-8d6893ae]{--badge-color:var(--color-badge-beta);--badge-dark-color:var(--color-badge-dark-beta)}.topic-icon-wrapper[data-v-03cf3183]{display:flex;align-items:center;justify-content:center;height:1.47059rem;flex:0 0 1.294rem;width:1.294rem;margin-right:1rem}.topic-icon[data-v-03cf3183]{height:.88235rem;transform:scale(1);-webkit-transform:scale(1);overflow:visible}.topic-icon[data-v-03cf3183] img{margin:0;display:block;width:100%;height:100%;-o-object-fit:contain;object-fit:contain}.topic-icon.curly-brackets-icon[data-v-03cf3183]{height:1rem}.token-method[data-v-3fd63d6c]{font-weight:700}.token-keyword[data-v-3fd63d6c]{color:var(--syntax-keyword,var(--color-syntax-keywords))}.token-number[data-v-3fd63d6c]{color:var(--syntax-number,var(--color-syntax-numbers))}.token-string[data-v-3fd63d6c]{color:var(--syntax-string,var(--color-syntax-strings))}.attribute-link[data-v-3fd63d6c],.token-attribute[data-v-3fd63d6c]{color:var(--syntax-attribute,var(--color-syntax-keywords))}.token-internalParam[data-v-3fd63d6c]{color:var(--color-syntax-param-internal-name)}.type-identifier-link[data-v-3fd63d6c]{color:var(--syntax-type,var(--color-syntax-other-type-names))}.token-removed[data-v-3fd63d6c]{background-color:var(--color-highlight-red)}.token-added[data-v-3fd63d6c]{background-color:var(--color-highlight-green)}.decorator[data-v-06ec7395],.label[data-v-06ec7395]{color:var(--colors-secondary-label,var(--color-secondary-label))}.label[data-v-06ec7395]{font-size:1rem;line-height:1.47059;font-weight:400;font-family:var(--typography-html-font,"Helvetica Neue","Helvetica","Arial",sans-serif)}.empty-token[data-v-06ec7395]{font-size:0}.empty-token[data-v-06ec7395]:after{content:"\00a0";font-size:1rem}.conditional-constraints[data-v-1548fd90] code{color:var(--colors-secondary-label,var(--color-secondary-label))}.abstract[data-v-52205924],.link-block[data-v-52205924] .badge{margin-left:2.294rem}.link-block .badge+.badge[data-v-52205924]{margin-left:1rem}.link[data-v-52205924]{display:flex}.link-block .badge[data-v-52205924]{margin-top:.5rem}.link-block.has-inline-element[data-v-52205924]{display:flex;align-items:flex-start;flex-flow:row wrap}.link-block.has-inline-element .badge[data-v-52205924]{margin-left:1rem;margin-top:0}.link-block .has-adjacent-elements[data-v-52205924]{padding-top:5px;padding-bottom:5px;display:inline-flex}.link-block[data-v-52205924],.link[data-v-52205924]{box-sizing:inherit}.link-block.changed[data-v-52205924],.link.changed[data-v-52205924]{padding-right:1rem;padding-left:2.17647rem;padding-top:8px;padding-bottom:8px;display:inline-flex;width:100%;box-sizing:border-box}.link-block.changed.changed[data-v-52205924],.link.changed.changed[data-v-52205924]{padding-right:1rem}@media only screen and (max-width:735px){.link-block.changed[data-v-52205924],.link.changed[data-v-52205924]{padding-left:0;padding-right:0}.link-block.changed.changed[data-v-52205924],.link.changed.changed[data-v-52205924]{padding-right:17px;padding-left:2.17647rem}}@media only screen and (max-width:735px){.link-block.changed[data-v-52205924],.link.changed[data-v-52205924]{padding-left:0;padding-right:0}}.abstract .topic-required[data-v-52205924]:not(:first-child){margin-top:4px}.topic-required[data-v-52205924]{font-size:.8em}.deprecated[data-v-52205924]{text-decoration:line-through}.conditional-constraints[data-v-52205924]{font-size:.82353rem;margin-top:4px}