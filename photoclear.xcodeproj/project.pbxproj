// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 70;
	objects = {

/* Begin PBXBuildFile section */
		0977B9892E8682B300DE3421 /* WidgetViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0977B9882E8682B300DE3421 /* WidgetViewController.m */; };
		0977B98B2E8682C300DE3421 /* WidgetAlbumSelectViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0977B98D2E8682C300DE3421 /* WidgetAlbumSelectViewController.m */; };
		097E20832E7D29CD00C090DC /* NotificationManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 097E20822E7D29CD00C090DC /* NotificationManager.m */; };
		097E20852E7D3D9300C090DC /* photoclear_icon.icon in Resources */ = {isa = PBXBuildFile; fileRef = 097E20842E7D3D9300C090DC /* photoclear_icon.icon */; };
		097E20932E8179A800C090DC /* AlbumTableViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 097E20922E8179A800C090DC /* AlbumTableViewCell.m */; };
		097E20DB2E825EF000C090DC /* SettingViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 097E20DA2E825EF000C090DC /* SettingViewController.m */; };
		09A8C8C82E87A89800EC3891 /* UIView+Shadow.m in Sources */ = {isa = PBXBuildFile; fileRef = 09A8C8C72E87A89800EC3891 /* UIView+Shadow.m */; };
		09A8C91B2E88E63B00EC3891 /* PHAsset+Identifier.m in Sources */ = {isa = PBXBuildFile; fileRef = 09A8C91A2E88E63B00EC3891 /* PHAsset+Identifier.m */; };
		09A8C9202E89270A00EC3891 /* SwiftyStoreKit in Frameworks */ = {isa = PBXBuildFile; productRef = 09A8C91F2E89270A00EC3891 /* SwiftyStoreKit */; };
		09A8C9232E89272B00EC3891 /* SwiftyStoreKit in Frameworks */ = {isa = PBXBuildFile; productRef = 09A8C9222E89272B00EC3891 /* SwiftyStoreKit */; };
		09A8C9252E89276600EC3891 /* VipGetTool.swift in Sources */ = {isa = PBXBuildFile; fileRef = 09A8C9242E89276600EC3891 /* VipGetTool.swift */; };
		09A8C9272E89277D00EC3891 /* AlertManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 09A8C9262E89277D00EC3891 /* AlertManager.swift */; };
		09A8C92A2E89279F00EC3891 /* JDStatusBarNotification in Frameworks */ = {isa = PBXBuildFile; productRef = 09A8C9292E89279F00EC3891 /* JDStatusBarNotification */; };
		09A8C92D2E892C1800EC3891 /* MembershipPurchaseViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 09A8C92C2E892C1800EC3891 /* MembershipPurchaseViewController.m */; };
		09DF41FE2E7BB1740006D3D5 /* WidgetKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 09DF41FD2E7BB1740006D3D5 /* WidgetKit.framework */; };
		09DF42002E7BB1740006D3D5 /* SwiftUI.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 09DF41FF2E7BB1740006D3D5 /* SwiftUI.framework */; };
		09DF420B2E7BB1750006D3D5 /* MyWidgetExtensionExtension.appex in Embed Foundation Extensions */ = {isa = PBXBuildFile; fileRef = 09DF41FB2E7BB1740006D3D5 /* MyWidgetExtensionExtension.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		09DF42162E7BBB880006D3D5 /* WidgetKitHelper.swift in Sources */ = {isa = PBXBuildFile; fileRef = 09DF42152E7BBB880006D3D5 /* WidgetKitHelper.swift */; };
		375C890983B54BDEA95EF945 /* WaterfallFlowLayout.m in Sources */ = {isa = PBXBuildFile; fileRef = 10CDD74C48DC4534AAEFF980 /* WaterfallFlowLayout.m */; };
		4CEEBF6A135849979CBF79BE /* PhotoSettingsViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 8497666512A249F9AA8C80B7 /* PhotoSettingsViewController.m */; };
		670D9CD0268B696D0025C7B7 /* PhotoTools.m in Sources */ = {isa = PBXBuildFile; fileRef = 670D9CCF268B696D0025C7B7 /* PhotoTools.m */; };
		670D9CD4268CC4BF0025C7B7 /* UICollectionViewLineFlowLayout.m in Sources */ = {isa = PBXBuildFile; fileRef = 670D9CD3268CC4BF0025C7B7 /* UICollectionViewLineFlowLayout.m */; };
		677E247C2678EE2F007CBDDE /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 677E247B2678EE2F007CBDDE /* AppDelegate.m */; };
		677E247F2678EE2F007CBDDE /* SceneDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 677E247E2678EE2F007CBDDE /* SceneDelegate.m */; };
		677E24822678EE2F007CBDDE /* BaseViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 677E24812678EE2F007CBDDE /* BaseViewController.m */; };
		677E24852678EE2F007CBDDE /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 677E24832678EE2F007CBDDE /* Main.storyboard */; };
		677E24872678EE36007CBDDE /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 677E24862678EE36007CBDDE /* Assets.xcassets */; };
		677E248A2678EE36007CBDDE /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 677E24882678EE36007CBDDE /* LaunchScreen.storyboard */; };
		677E248D2678EE36007CBDDE /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 677E248C2678EE36007CBDDE /* main.m */; };
		677E24AA2678EE53007CBDDE /* BaseRadiusView.m in Sources */ = {isa = PBXBuildFile; fileRef = 677E24952678EE53007CBDDE /* BaseRadiusView.m */; };
		677E24AB2678EE53007CBDDE /* UIButton+CenterImageAndTitle.m in Sources */ = {isa = PBXBuildFile; fileRef = 677E24962678EE53007CBDDE /* UIButton+CenterImageAndTitle.m */; };
		677E24AC2678EE53007CBDDE /* PhotoCollectionViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 677E24982678EE53007CBDDE /* PhotoCollectionViewCell.m */; };
		677E24AD2678EE53007CBDDE /* FQ_CollectionViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 677E249F2678EE53007CBDDE /* FQ_CollectionViewCell.m */; };
		677E24AE2678EE53007CBDDE /* PAPreferences.m in Sources */ = {isa = PBXBuildFile; fileRef = 677E24A22678EE53007CBDDE /* PAPreferences.m */; };
		677E24AF2678EE53007CBDDE /* PAPropertyDescriptor.m in Sources */ = {isa = PBXBuildFile; fileRef = 677E24A52678EE53007CBDDE /* PAPropertyDescriptor.m */; };
		677E24B02678EE53007CBDDE /* UIView+Extension.m in Sources */ = {isa = PBXBuildFile; fileRef = 677E24A62678EE53007CBDDE /* UIView+Extension.m */; };
		677E24B12678EE53007CBDDE /* BaseRadiusButton.m in Sources */ = {isa = PBXBuildFile; fileRef = 677E24A72678EE53007CBDDE /* BaseRadiusButton.m */; };
		677E24B22678EE53007CBDDE /* BaseCycleLabel.m in Sources */ = {isa = PBXBuildFile; fileRef = 677E24A82678EE53007CBDDE /* BaseCycleLabel.m */; };
		677E24B32678EE53007CBDDE /* TimeHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = 677E24A92678EE53007CBDDE /* TimeHelper.m */; };
		677E24BD2678EE6A007CBDDE /* AlbumSelectViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 677E24B72678EE69007CBDDE /* AlbumSelectViewController.m */; };
		677E24BE2678EE6A007CBDDE /* Preferences.m in Sources */ = {isa = PBXBuildFile; fileRef = 677E24B82678EE6A007CBDDE /* Preferences.m */; };
		677E24BF2678EE6A007CBDDE /* ScrollViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 677E24BA2678EE6A007CBDDE /* ScrollViewController.m */; };
		677E24C02678EE6A007CBDDE /* PhotoSelectViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 677E24BB2678EE6A007CBDDE /* PhotoSelectViewController.m */; };
		965DF60FDFB848F088ECAA15 /* DeletePhotoCollectionViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 6E97780DC03340FFA05C2C14 /* DeletePhotoCollectionViewCell.m */; };
		C506702A4F4E48BE820AB7BB /* DeleteListViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 845DC734E03342118CF94867 /* DeleteListViewController.m */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		09DF42092E7BB1750006D3D5 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 677E246F2678EE2F007CBDDE /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 09DF41FA2E7BB1740006D3D5;
			remoteInfo = MyWidgetExtensionExtension;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		09DF420C2E7BB1750006D3D5 /* Embed Foundation Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 13;
			files = (
				09DF420B2E7BB1750006D3D5 /* MyWidgetExtensionExtension.appex in Embed Foundation Extensions */,
			);
			name = "Embed Foundation Extensions";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		0977B9872E8682B300DE3421 /* WidgetViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = WidgetViewController.h; sourceTree = "<group>"; };
		0977B9882E8682B300DE3421 /* WidgetViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = WidgetViewController.m; sourceTree = "<group>"; };
		0977B98C2E8682C300DE3421 /* WidgetAlbumSelectViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = WidgetAlbumSelectViewController.h; sourceTree = "<group>"; };
		0977B98D2E8682C300DE3421 /* WidgetAlbumSelectViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = WidgetAlbumSelectViewController.m; sourceTree = "<group>"; };
		097E20812E7D29CD00C090DC /* NotificationManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = NotificationManager.h; sourceTree = "<group>"; };
		097E20822E7D29CD00C090DC /* NotificationManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = NotificationManager.m; sourceTree = "<group>"; };
		097E20842E7D3D9300C090DC /* photoclear_icon.icon */ = {isa = PBXFileReference; lastKnownFileType = folder.iconcomposer.icon; path = photoclear_icon.icon; sourceTree = "<group>"; };
		097E20912E8179A800C090DC /* AlbumTableViewCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AlbumTableViewCell.h; sourceTree = "<group>"; };
		097E20922E8179A800C090DC /* AlbumTableViewCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AlbumTableViewCell.m; sourceTree = "<group>"; };
		097E20D92E825EF000C090DC /* SettingViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SettingViewController.h; sourceTree = "<group>"; };
		097E20DA2E825EF000C090DC /* SettingViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SettingViewController.m; sourceTree = "<group>"; };
		09A8C8C62E87A89800EC3891 /* UIView+Shadow.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIView+Shadow.h"; sourceTree = "<group>"; };
		09A8C8C72E87A89800EC3891 /* UIView+Shadow.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIView+Shadow.m"; sourceTree = "<group>"; };
		09A8C9192E88E63B00EC3891 /* PHAsset+Identifier.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "PHAsset+Identifier.h"; sourceTree = "<group>"; };
		09A8C91A2E88E63B00EC3891 /* PHAsset+Identifier.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "PHAsset+Identifier.m"; sourceTree = "<group>"; };
		09A8C91C2E88E70E00EC3891 /* PrefixHeader.pch */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PrefixHeader.pch; sourceTree = "<group>"; };
		09A8C9242E89276600EC3891 /* VipGetTool.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VipGetTool.swift; sourceTree = "<group>"; };
		09A8C9262E89277D00EC3891 /* AlertManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AlertManager.swift; sourceTree = "<group>"; };
		09A8C92B2E892C1800EC3891 /* MembershipPurchaseViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MembershipPurchaseViewController.h; sourceTree = "<group>"; };
		09A8C92C2E892C1800EC3891 /* MembershipPurchaseViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MembershipPurchaseViewController.m; sourceTree = "<group>"; };
		09DF41FB2E7BB1740006D3D5 /* MyWidgetExtensionExtension.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = MyWidgetExtensionExtension.appex; sourceTree = BUILT_PRODUCTS_DIR; };
		09DF41FD2E7BB1740006D3D5 /* WidgetKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = WidgetKit.framework; path = System/Library/Frameworks/WidgetKit.framework; sourceTree = SDKROOT; };
		09DF41FF2E7BB1740006D3D5 /* SwiftUI.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SwiftUI.framework; path = System/Library/Frameworks/SwiftUI.framework; sourceTree = SDKROOT; };
		09DF42132E7BB7BA0006D3D5 /* photoclear.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = photoclear.entitlements; sourceTree = "<group>"; };
		09DF42142E7BB8490006D3D5 /* MyWidgetExtensionExtension.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = MyWidgetExtensionExtension.entitlements; sourceTree = "<group>"; };
		09DF42152E7BBB880006D3D5 /* WidgetKitHelper.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WidgetKitHelper.swift; sourceTree = "<group>"; };
		09DF42172E7BBB890006D3D5 /* photoclear-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "photoclear-Bridging-Header.h"; sourceTree = "<group>"; };
		10CDD74C48DC4534AAEFF980 /* WaterfallFlowLayout.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = WaterfallFlowLayout.m; sourceTree = "<group>"; };
		43794212CF9A4A2A9251A3DC /* DeleteListViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DeleteListViewController.h; sourceTree = "<group>"; };
		670D9CCE268B696D0025C7B7 /* PhotoTools.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PhotoTools.h; sourceTree = "<group>"; };
		670D9CCF268B696D0025C7B7 /* PhotoTools.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PhotoTools.m; sourceTree = "<group>"; };
		670D9CD2268CC4BF0025C7B7 /* UICollectionViewLineFlowLayout.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UICollectionViewLineFlowLayout.h; sourceTree = "<group>"; };
		670D9CD3268CC4BF0025C7B7 /* UICollectionViewLineFlowLayout.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UICollectionViewLineFlowLayout.m; sourceTree = "<group>"; };
		677E24772678EE2F007CBDDE /* photoclear.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = photoclear.app; sourceTree = BUILT_PRODUCTS_DIR; };
		677E247A2678EE2F007CBDDE /* AppDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AppDelegate.h; sourceTree = "<group>"; };
		677E247B2678EE2F007CBDDE /* AppDelegate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AppDelegate.m; sourceTree = "<group>"; };
		677E247D2678EE2F007CBDDE /* SceneDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SceneDelegate.h; sourceTree = "<group>"; };
		677E247E2678EE2F007CBDDE /* SceneDelegate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SceneDelegate.m; sourceTree = "<group>"; };
		677E24802678EE2F007CBDDE /* BaseViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BaseViewController.h; sourceTree = "<group>"; };
		677E24812678EE2F007CBDDE /* BaseViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BaseViewController.m; sourceTree = "<group>"; };
		677E24842678EE2F007CBDDE /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		677E24862678EE36007CBDDE /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		677E24892678EE36007CBDDE /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		677E248B2678EE36007CBDDE /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		677E248C2678EE36007CBDDE /* main.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
		677E24952678EE53007CBDDE /* BaseRadiusView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BaseRadiusView.m; sourceTree = "<group>"; };
		677E24962678EE53007CBDDE /* UIButton+CenterImageAndTitle.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIButton+CenterImageAndTitle.m"; sourceTree = "<group>"; };
		677E24972678EE53007CBDDE /* UIView+Extension.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIView+Extension.h"; sourceTree = "<group>"; };
		677E24982678EE53007CBDDE /* PhotoCollectionViewCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PhotoCollectionViewCell.m; sourceTree = "<group>"; };
		677E24992678EE53007CBDDE /* FQ_CollectionViewCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FQ_CollectionViewCell.h; sourceTree = "<group>"; };
		677E249A2678EE53007CBDDE /* TimeHelper.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TimeHelper.h; sourceTree = "<group>"; };
		677E249B2678EE53007CBDDE /* BaseCycleLabel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BaseCycleLabel.h; sourceTree = "<group>"; };
		677E249C2678EE53007CBDDE /* BaseRadiusButton.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BaseRadiusButton.h; sourceTree = "<group>"; };
		677E249D2678EE53007CBDDE /* UIButton+CenterImageAndTitle.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIButton+CenterImageAndTitle.h"; sourceTree = "<group>"; };
		677E249E2678EE53007CBDDE /* BaseRadiusView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BaseRadiusView.h; sourceTree = "<group>"; };
		677E249F2678EE53007CBDDE /* FQ_CollectionViewCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FQ_CollectionViewCell.m; sourceTree = "<group>"; };
		677E24A02678EE53007CBDDE /* PhotoCollectionViewCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PhotoCollectionViewCell.h; sourceTree = "<group>"; };
		677E24A22678EE53007CBDDE /* PAPreferences.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PAPreferences.m; sourceTree = "<group>"; };
		677E24A32678EE53007CBDDE /* PAPropertyDescriptor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PAPropertyDescriptor.h; sourceTree = "<group>"; };
		677E24A42678EE53007CBDDE /* PAPreferences.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PAPreferences.h; sourceTree = "<group>"; };
		677E24A52678EE53007CBDDE /* PAPropertyDescriptor.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PAPropertyDescriptor.m; sourceTree = "<group>"; };
		677E24A62678EE53007CBDDE /* UIView+Extension.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIView+Extension.m"; sourceTree = "<group>"; };
		677E24A72678EE53007CBDDE /* BaseRadiusButton.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BaseRadiusButton.m; sourceTree = "<group>"; };
		677E24A82678EE53007CBDDE /* BaseCycleLabel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BaseCycleLabel.m; sourceTree = "<group>"; };
		677E24A92678EE53007CBDDE /* TimeHelper.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TimeHelper.m; sourceTree = "<group>"; };
		677E24B52678EE69007CBDDE /* Preferences.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Preferences.h; sourceTree = "<group>"; };
		677E24B62678EE69007CBDDE /* PhotoSelectViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PhotoSelectViewController.h; sourceTree = "<group>"; };
		677E24B72678EE69007CBDDE /* AlbumSelectViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AlbumSelectViewController.m; sourceTree = "<group>"; };
		677E24B82678EE6A007CBDDE /* Preferences.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = Preferences.m; sourceTree = "<group>"; };
		677E24B92678EE6A007CBDDE /* ScrollViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ScrollViewController.h; sourceTree = "<group>"; };
		677E24BA2678EE6A007CBDDE /* ScrollViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ScrollViewController.m; sourceTree = "<group>"; };
		677E24BB2678EE6A007CBDDE /* PhotoSelectViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PhotoSelectViewController.m; sourceTree = "<group>"; };
		677E24BC2678EE6A007CBDDE /* AlbumSelectViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AlbumSelectViewController.h; sourceTree = "<group>"; };
		6E97780DC03340FFA05C2C14 /* DeletePhotoCollectionViewCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DeletePhotoCollectionViewCell.m; sourceTree = "<group>"; };
		845DC734E03342118CF94867 /* DeleteListViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DeleteListViewController.m; sourceTree = "<group>"; };
		8497666512A249F9AA8C80B7 /* PhotoSettingsViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PhotoSettingsViewController.m; sourceTree = "<group>"; };
		A2BA1BED83BC44918EE8A37A /* DeletePhotoCollectionViewCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DeletePhotoCollectionViewCell.h; sourceTree = "<group>"; };
		ABFDEE59F604496C9A28C892 /* WaterfallFlowLayout.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WaterfallFlowLayout.h; sourceTree = "<group>"; };
		EF049224284249D8BD446CAC /* PhotoSettingsViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PhotoSettingsViewController.h; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		09DF420F2E7BB1750006D3D5 /* PBXFileSystemSynchronizedBuildFileExceptionSet */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = 09DF41FA2E7BB1740006D3D5 /* MyWidgetExtensionExtension */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		09DF42012E7BB1740006D3D5 /* MyWidgetExtension */ = {isa = PBXFileSystemSynchronizedRootGroup; exceptions = (09DF420F2E7BB1750006D3D5 /* PBXFileSystemSynchronizedBuildFileExceptionSet */, ); explicitFileTypes = {}; explicitFolders = (); path = MyWidgetExtension; sourceTree = "<group>"; };
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		09DF41F82E7BB1740006D3D5 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				09DF42002E7BB1740006D3D5 /* SwiftUI.framework in Frameworks */,
				09DF41FE2E7BB1740006D3D5 /* WidgetKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		677E24742678EE2F007CBDDE /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				09A8C92A2E89279F00EC3891 /* JDStatusBarNotification in Frameworks */,
				09A8C9202E89270A00EC3891 /* SwiftyStoreKit in Frameworks */,
				09A8C9232E89272B00EC3891 /* SwiftyStoreKit in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		0977B98A2E8682BB00DE3421 /* Widgetitem4 */ = {
			isa = PBXGroup;
			children = (
				0977B9872E8682B300DE3421 /* WidgetViewController.h */,
				0977B9882E8682B300DE3421 /* WidgetViewController.m */,
				0977B98C2E8682C300DE3421 /* WidgetAlbumSelectViewController.h */,
				0977B98D2E8682C300DE3421 /* WidgetAlbumSelectViewController.m */,
			);
			path = Widgetitem4;
			sourceTree = "<group>";
		};
		097E20D42E825DBD00C090DC /* Manager */ = {
			isa = PBXGroup;
			children = (
				670D9CCE268B696D0025C7B7 /* PhotoTools.h */,
				670D9CCF268B696D0025C7B7 /* PhotoTools.m */,
				097E20812E7D29CD00C090DC /* NotificationManager.h */,
				097E20822E7D29CD00C090DC /* NotificationManager.m */,
				677E24B52678EE69007CBDDE /* Preferences.h */,
				677E24B82678EE6A007CBDDE /* Preferences.m */,
			);
			path = Manager;
			sourceTree = "<group>";
		};
		097E20D52E825DE300C090DC /* deleteListitem3 */ = {
			isa = PBXGroup;
			children = (
				43794212CF9A4A2A9251A3DC /* DeleteListViewController.h */,
				845DC734E03342118CF94867 /* DeleteListViewController.m */,
				A2BA1BED83BC44918EE8A37A /* DeletePhotoCollectionViewCell.h */,
				6E97780DC03340FFA05C2C14 /* DeletePhotoCollectionViewCell.m */,
				ABFDEE59F604496C9A28C892 /* WaterfallFlowLayout.h */,
				10CDD74C48DC4534AAEFF980 /* WaterfallFlowLayout.m */,
			);
			path = deleteListitem3;
			sourceTree = "<group>";
		};
		097E20D62E825E3800C090DC /* PhotoSelectitem1 */ = {
			isa = PBXGroup;
			children = (
				677E24A02678EE53007CBDDE /* PhotoCollectionViewCell.h */,
				677E24982678EE53007CBDDE /* PhotoCollectionViewCell.m */,
				677E24B62678EE69007CBDDE /* PhotoSelectViewController.h */,
				677E24BB2678EE6A007CBDDE /* PhotoSelectViewController.m */,
			);
			path = PhotoSelectitem1;
			sourceTree = "<group>";
		};
		097E20D72E825E6200C090DC /* Albumitem2 */ = {
			isa = PBXGroup;
			children = (
				097E20912E8179A800C090DC /* AlbumTableViewCell.h */,
				097E20922E8179A800C090DC /* AlbumTableViewCell.m */,
				677E24BC2678EE6A007CBDDE /* AlbumSelectViewController.h */,
				677E24B72678EE69007CBDDE /* AlbumSelectViewController.m */,
			);
			path = Albumitem2;
			sourceTree = "<group>";
		};
		097E20D82E825EA000C090DC /* PhotoEdit */ = {
			isa = PBXGroup;
			children = (
				677E24B92678EE6A007CBDDE /* ScrollViewController.h */,
				677E24BA2678EE6A007CBDDE /* ScrollViewController.m */,
				EF049224284249D8BD446CAC /* PhotoSettingsViewController.h */,
				8497666512A249F9AA8C80B7 /* PhotoSettingsViewController.m */,
			);
			path = PhotoEdit;
			sourceTree = "<group>";
		};
		097E20DC2E825EF900C090DC /* Settingitem5 */ = {
			isa = PBXGroup;
			children = (
				09A8C92B2E892C1800EC3891 /* MembershipPurchaseViewController.h */,
				09A8C92C2E892C1800EC3891 /* MembershipPurchaseViewController.m */,
				097E20D92E825EF000C090DC /* SettingViewController.h */,
				097E20DA2E825EF000C090DC /* SettingViewController.m */,
			);
			path = Settingitem5;
			sourceTree = "<group>";
		};
		09DF41FC2E7BB1740006D3D5 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				09DF41FD2E7BB1740006D3D5 /* WidgetKit.framework */,
				09DF41FF2E7BB1740006D3D5 /* SwiftUI.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		67488B98267A43490009F194 /* ViewControllers */ = {
			isa = PBXGroup;
			children = (
				097E20D62E825E3800C090DC /* PhotoSelectitem1 */,
				097E20D72E825E6200C090DC /* Albumitem2 */,
				097E20D52E825DE300C090DC /* deleteListitem3 */,
				0977B98A2E8682BB00DE3421 /* Widgetitem4 */,
				097E20DC2E825EF900C090DC /* Settingitem5 */,
				097E20D82E825EA000C090DC /* PhotoEdit */,
			);
			path = ViewControllers;
			sourceTree = "<group>";
		};
		677E246E2678EE2F007CBDDE = {
			isa = PBXGroup;
			children = (
				09DF42142E7BB8490006D3D5 /* MyWidgetExtensionExtension.entitlements */,
				677E24792678EE2F007CBDDE /* photoclear */,
				09DF42012E7BB1740006D3D5 /* MyWidgetExtension */,
				09DF41FC2E7BB1740006D3D5 /* Frameworks */,
				677E24782678EE2F007CBDDE /* Products */,
			);
			sourceTree = "<group>";
		};
		677E24782678EE2F007CBDDE /* Products */ = {
			isa = PBXGroup;
			children = (
				677E24772678EE2F007CBDDE /* photoclear.app */,
				09DF41FB2E7BB1740006D3D5 /* MyWidgetExtensionExtension.appex */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		677E24792678EE2F007CBDDE /* photoclear */ = {
			isa = PBXGroup;
			children = (
				09DF42132E7BB7BA0006D3D5 /* photoclear.entitlements */,
				677E24942678EE53007CBDDE /* base */,
				677E247A2678EE2F007CBDDE /* AppDelegate.h */,
				677E247B2678EE2F007CBDDE /* AppDelegate.m */,
				677E247D2678EE2F007CBDDE /* SceneDelegate.h */,
				677E247E2678EE2F007CBDDE /* SceneDelegate.m */,
				677E24802678EE2F007CBDDE /* BaseViewController.h */,
				677E24812678EE2F007CBDDE /* BaseViewController.m */,
				09DF42152E7BBB880006D3D5 /* WidgetKitHelper.swift */,
				09A8C9242E89276600EC3891 /* VipGetTool.swift */,
				09A8C9262E89277D00EC3891 /* AlertManager.swift */,
				097E20D42E825DBD00C090DC /* Manager */,
				67488B98267A43490009F194 /* ViewControllers */,
				677E24832678EE2F007CBDDE /* Main.storyboard */,
				677E24862678EE36007CBDDE /* Assets.xcassets */,
				097E20842E7D3D9300C090DC /* photoclear_icon.icon */,
				677E24882678EE36007CBDDE /* LaunchScreen.storyboard */,
				677E248B2678EE36007CBDDE /* Info.plist */,
				677E248C2678EE36007CBDDE /* main.m */,
				09DF42172E7BBB890006D3D5 /* photoclear-Bridging-Header.h */,
				09A8C91C2E88E70E00EC3891 /* PrefixHeader.pch */,
			);
			path = photoclear;
			sourceTree = "<group>";
		};
		677E24942678EE53007CBDDE /* base */ = {
			isa = PBXGroup;
			children = (
				677E24952678EE53007CBDDE /* BaseRadiusView.m */,
				677E24962678EE53007CBDDE /* UIButton+CenterImageAndTitle.m */,
				677E24972678EE53007CBDDE /* UIView+Extension.h */,
				677E249A2678EE53007CBDDE /* TimeHelper.h */,
				677E249B2678EE53007CBDDE /* BaseCycleLabel.h */,
				677E249C2678EE53007CBDDE /* BaseRadiusButton.h */,
				677E249D2678EE53007CBDDE /* UIButton+CenterImageAndTitle.h */,
				677E249E2678EE53007CBDDE /* BaseRadiusView.h */,
				677E24992678EE53007CBDDE /* FQ_CollectionViewCell.h */,
				677E249F2678EE53007CBDDE /* FQ_CollectionViewCell.m */,
				670D9CD2268CC4BF0025C7B7 /* UICollectionViewLineFlowLayout.h */,
				670D9CD3268CC4BF0025C7B7 /* UICollectionViewLineFlowLayout.m */,
				677E24A12678EE53007CBDDE /* PAPreferences */,
				677E24A62678EE53007CBDDE /* UIView+Extension.m */,
				677E24A72678EE53007CBDDE /* BaseRadiusButton.m */,
				677E24A82678EE53007CBDDE /* BaseCycleLabel.m */,
				677E24A92678EE53007CBDDE /* TimeHelper.m */,
				09A8C8C62E87A89800EC3891 /* UIView+Shadow.h */,
				09A8C8C72E87A89800EC3891 /* UIView+Shadow.m */,
				09A8C9192E88E63B00EC3891 /* PHAsset+Identifier.h */,
				09A8C91A2E88E63B00EC3891 /* PHAsset+Identifier.m */,
			);
			path = base;
			sourceTree = "<group>";
		};
		677E24A12678EE53007CBDDE /* PAPreferences */ = {
			isa = PBXGroup;
			children = (
				677E24A22678EE53007CBDDE /* PAPreferences.m */,
				677E24A32678EE53007CBDDE /* PAPropertyDescriptor.h */,
				677E24A42678EE53007CBDDE /* PAPreferences.h */,
				677E24A52678EE53007CBDDE /* PAPropertyDescriptor.m */,
			);
			path = PAPreferences;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		09DF41FA2E7BB1740006D3D5 /* MyWidgetExtensionExtension */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 09DF42102E7BB1750006D3D5 /* Build configuration list for PBXNativeTarget "MyWidgetExtensionExtension" */;
			buildPhases = (
				09DF41F72E7BB1740006D3D5 /* Sources */,
				09DF41F82E7BB1740006D3D5 /* Frameworks */,
				09DF41F92E7BB1740006D3D5 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				09DF42012E7BB1740006D3D5 /* MyWidgetExtension */,
			);
			name = MyWidgetExtensionExtension;
			packageProductDependencies = (
			);
			productName = MyWidgetExtensionExtension;
			productReference = 09DF41FB2E7BB1740006D3D5 /* MyWidgetExtensionExtension.appex */;
			productType = "com.apple.product-type.app-extension";
		};
		677E24762678EE2F007CBDDE /* photoclear */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 677E24902678EE36007CBDDE /* Build configuration list for PBXNativeTarget "photoclear" */;
			buildPhases = (
				677E24732678EE2F007CBDDE /* Sources */,
				677E24742678EE2F007CBDDE /* Frameworks */,
				677E24752678EE2F007CBDDE /* Resources */,
				09DF420C2E7BB1750006D3D5 /* Embed Foundation Extensions */,
			);
			buildRules = (
			);
			dependencies = (
				09DF420A2E7BB1750006D3D5 /* PBXTargetDependency */,
			);
			name = photoclear;
			productName = photoclear;
			productReference = 677E24772678EE2F007CBDDE /* photoclear.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		677E246F2678EE2F007CBDDE /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 2600;
				LastUpgradeCheck = 1240;
				TargetAttributes = {
					09DF41FA2E7BB1740006D3D5 = {
						CreatedOnToolsVersion = 26.0;
					};
					677E24762678EE2F007CBDDE = {
						CreatedOnToolsVersion = 12.4;
						LastSwiftMigration = 2600;
					};
				};
			};
			buildConfigurationList = 677E24722678EE2F007CBDDE /* Build configuration list for PBXProject "photoclear" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 677E246E2678EE2F007CBDDE;
			packageReferences = (
				09A8C9212E89272B00EC3891 /* XCLocalSwiftPackageReference "SwiftyStoreKit" */,
				09A8C9282E89279F00EC3891 /* XCLocalSwiftPackageReference "JDStatusBarNotification" */,
			);
			productRefGroup = 677E24782678EE2F007CBDDE /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				677E24762678EE2F007CBDDE /* photoclear */,
				09DF41FA2E7BB1740006D3D5 /* MyWidgetExtensionExtension */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		09DF41F92E7BB1740006D3D5 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		677E24752678EE2F007CBDDE /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				677E248A2678EE36007CBDDE /* LaunchScreen.storyboard in Resources */,
				677E24872678EE36007CBDDE /* Assets.xcassets in Resources */,
				677E24852678EE2F007CBDDE /* Main.storyboard in Resources */,
				097E20852E7D3D9300C090DC /* photoclear_icon.icon in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		09DF41F72E7BB1740006D3D5 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		677E24732678EE2F007CBDDE /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				677E24BD2678EE6A007CBDDE /* AlbumSelectViewController.m in Sources */,
				677E24AE2678EE53007CBDDE /* PAPreferences.m in Sources */,
				09A8C8C82E87A89800EC3891 /* UIView+Shadow.m in Sources */,
				09A8C92D2E892C1800EC3891 /* MembershipPurchaseViewController.m in Sources */,
				0977B9892E8682B300DE3421 /* WidgetViewController.m in Sources */,
				0977B98B2E8682C300DE3421 /* WidgetAlbumSelectViewController.m in Sources */,
				677E24B22678EE53007CBDDE /* BaseCycleLabel.m in Sources */,
				097E20DB2E825EF000C090DC /* SettingViewController.m in Sources */,
				677E24AC2678EE53007CBDDE /* PhotoCollectionViewCell.m in Sources */,
				677E24B02678EE53007CBDDE /* UIView+Extension.m in Sources */,
				097E20832E7D29CD00C090DC /* NotificationManager.m in Sources */,
				677E24C02678EE6A007CBDDE /* PhotoSelectViewController.m in Sources */,
				677E24B32678EE53007CBDDE /* TimeHelper.m in Sources */,
				677E24AF2678EE53007CBDDE /* PAPropertyDescriptor.m in Sources */,
				677E24B12678EE53007CBDDE /* BaseRadiusButton.m in Sources */,
				677E24822678EE2F007CBDDE /* BaseViewController.m in Sources */,
				09A8C9252E89276600EC3891 /* VipGetTool.swift in Sources */,
				677E24AB2678EE53007CBDDE /* UIButton+CenterImageAndTitle.m in Sources */,
				677E24BE2678EE6A007CBDDE /* Preferences.m in Sources */,
				09A8C9272E89277D00EC3891 /* AlertManager.swift in Sources */,
				677E24AA2678EE53007CBDDE /* BaseRadiusView.m in Sources */,
				09DF42162E7BBB880006D3D5 /* WidgetKitHelper.swift in Sources */,
				670D9CD0268B696D0025C7B7 /* PhotoTools.m in Sources */,
				677E247C2678EE2F007CBDDE /* AppDelegate.m in Sources */,
				677E248D2678EE36007CBDDE /* main.m in Sources */,
				097E20932E8179A800C090DC /* AlbumTableViewCell.m in Sources */,
				677E247F2678EE2F007CBDDE /* SceneDelegate.m in Sources */,
				670D9CD4268CC4BF0025C7B7 /* UICollectionViewLineFlowLayout.m in Sources */,
				677E24AD2678EE53007CBDDE /* FQ_CollectionViewCell.m in Sources */,
				677E24BF2678EE6A007CBDDE /* ScrollViewController.m in Sources */,
				C506702A4F4E48BE820AB7BB /* DeleteListViewController.m in Sources */,
				965DF60FDFB848F088ECAA15 /* DeletePhotoCollectionViewCell.m in Sources */,
				09A8C91B2E88E63B00EC3891 /* PHAsset+Identifier.m in Sources */,
				375C890983B54BDEA95EF945 /* WaterfallFlowLayout.m in Sources */,
				4CEEBF6A135849979CBF79BE /* PhotoSettingsViewController.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		09DF420A2E7BB1750006D3D5 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 09DF41FA2E7BB1740006D3D5 /* MyWidgetExtensionExtension */;
			targetProxy = 09DF42092E7BB1750006D3D5 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		677E24832678EE2F007CBDDE /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				677E24842678EE2F007CBDDE /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		677E24882678EE36007CBDDE /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				677E24892678EE36007CBDDE /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		09DF420D2E7BB1750006D3D5 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_WIDGET_BACKGROUND_COLOR_NAME = WidgetBackground;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CODE_SIGN_ENTITLEMENTS = MyWidgetExtensionExtension.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = DT8RK544YH;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = MyWidgetExtension/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = MyWidgetExtension;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.lfb.manager.photoclear.photoclear.MyWidgetExtension;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				STRING_CATALOG_GENERATE_SYMBOLS = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_APPROACHABLE_CONCURRENCY = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_UPCOMING_FEATURE_MEMBER_IMPORT_VISIBILITY = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		09DF420E2E7BB1750006D3D5 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_WIDGET_BACKGROUND_COLOR_NAME = WidgetBackground;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CODE_SIGN_ENTITLEMENTS = MyWidgetExtensionExtension.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = DT8RK544YH;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = MyWidgetExtension/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = MyWidgetExtension;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.lfb.manager.photoclear.photoclear.MyWidgetExtension;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				STRING_CATALOG_GENERATE_SYMBOLS = YES;
				SWIFT_APPROACHABLE_CONCURRENCY = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_UPCOMING_FEATURE_MEMBER_IMPORT_VISIBILITY = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		677E248E2678EE36007CBDDE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 14.4;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		677E248F2678EE36007CBDDE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 14.4;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		677E24912678EE36007CBDDE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = photoclear_icon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = photoclear/photoclear.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 2;
				DEVELOPMENT_TEAM = DT8RK544YH;
				GCC_PREFIX_HEADER = "$(SRCROOT)/photoclear/PrefixHeader.pch";
				INFOPLIST_FILE = photoclear/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.lfb.manager.photoclear.photoclear;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_OBJC_BRIDGING_HEADER = "photoclear/photoclear-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		677E24922678EE36007CBDDE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = photoclear_icon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = photoclear/photoclear.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 2;
				DEVELOPMENT_TEAM = DT8RK544YH;
				GCC_PREFIX_HEADER = "$(SRCROOT)/photoclear/PrefixHeader.pch";
				INFOPLIST_FILE = photoclear/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.lfb.manager.photoclear.photoclear;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_OBJC_BRIDGING_HEADER = "photoclear/photoclear-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		09DF42102E7BB1750006D3D5 /* Build configuration list for PBXNativeTarget "MyWidgetExtensionExtension" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				09DF420D2E7BB1750006D3D5 /* Debug */,
				09DF420E2E7BB1750006D3D5 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		677E24722678EE2F007CBDDE /* Build configuration list for PBXProject "photoclear" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				677E248E2678EE36007CBDDE /* Debug */,
				677E248F2678EE36007CBDDE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		677E24902678EE36007CBDDE /* Build configuration list for PBXNativeTarget "photoclear" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				677E24912678EE36007CBDDE /* Debug */,
				677E24922678EE36007CBDDE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCLocalSwiftPackageReference section */
		09A8C9212E89272B00EC3891 /* XCLocalSwiftPackageReference "SwiftyStoreKit" */ = {
			isa = XCLocalSwiftPackageReference;
			relativePath = SwiftyStoreKit;
		};
		09A8C9282E89279F00EC3891 /* XCLocalSwiftPackageReference "JDStatusBarNotification" */ = {
			isa = XCLocalSwiftPackageReference;
			relativePath = JDStatusBarNotification;
		};
/* End XCLocalSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		09A8C91F2E89270A00EC3891 /* SwiftyStoreKit */ = {
			isa = XCSwiftPackageProductDependency;
			productName = SwiftyStoreKit;
		};
		09A8C9222E89272B00EC3891 /* SwiftyStoreKit */ = {
			isa = XCSwiftPackageProductDependency;
			productName = SwiftyStoreKit;
		};
		09A8C9292E89279F00EC3891 /* JDStatusBarNotification */ = {
			isa = XCSwiftPackageProductDependency;
			productName = JDStatusBarNotification;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 677E246F2678EE2F007CBDDE /* Project object */;
}
