---
name: Bug report
about: Create a report to help us improve
title: ''
labels: 'type: bug'
assignees: ''

---

## Bug Report
A clear and concise description of what the bug is.

**To Reproduce**
Steps to reproduce the behavior:
1. Step one...

**Expected behavior**
A clear and concise description of what you expected to happen.

## Platform Information
 - OS: [e.g. iOS 13.4, watchOS 6.2.1, tvOS 9.2.3, macOS 10.14.3, Catalyst 13.0]
 - Purchase Type: [e.g. consumable, non-consumable, auto-renewable subscription, non-renewing subscription, discount]
 - Environment: [e.g. sandbox, app review, production]
 - SwiftyStoreKit version: [e.g. 0.16]


## Additional context
Add any other context about the problem here.

**Potentially Related Issues**
 - Issue #___

**Screenshots**
If applicable, add screenshots to help explain your problem.
