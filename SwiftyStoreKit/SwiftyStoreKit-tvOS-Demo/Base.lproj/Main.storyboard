<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder.AppleTV.Storyboard" version="3.0" toolsVersion="13771" targetRuntime="AppleTV" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES" initialViewController="BYZ-38-t0r">
    <device id="appleTV" orientation="landscape">
        <adaptation id="light"/>
    </device>
    <dependencies>
        <deployment identifier="tvOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="13772"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--View Controller-->
        <scene sceneID="tne-QT-ifu">
            <objects>
                <viewController id="BYZ-38-t0r" customClass="ViewController" customModule="SwiftyStoreKit_tvOSDemo" customModuleProvider="target" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="y3c-jy-aDJ"/>
                        <viewControllerLayoutGuide type="bottom" id="wfy-db-euE"/>
                    </layoutGuides>
                    <view key="view" contentMode="scaleToFill" id="8bC-Xf-vdC">
                        <rect key="frame" x="0.0" y="0.0" width="1920" height="1080"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <button opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="0V2-Mg-12F">
                                <rect key="frame" x="876" y="106" width="170" height="86"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <inset key="contentEdgeInsets" minX="40" minY="20" maxX="40" maxY="20"/>
                                <state key="normal" title="info 1"/>
                                <connections>
                                    <action selector="nonConsumableGetInfo" destination="BYZ-38-t0r" eventType="primaryActionTriggered" id="KQL-dt-eTv"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Fad-e7-Q2a">
                                <rect key="frame" x="873" y="227" width="176" height="86"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <inset key="contentEdgeInsets" minX="40" minY="20" maxX="40" maxY="20"/>
                                <state key="normal" title="info 2"/>
                                <connections>
                                    <action selector="autoRenewableGetInfo" destination="BYZ-38-t0r" eventType="primaryActionTriggered" id="Xf3-53-eEI"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="lOy-18-mr9">
                                <rect key="frame" x="828" y="337" width="266" height="86"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <inset key="contentEdgeInsets" minX="40" minY="20" maxX="40" maxY="20"/>
                                <state key="normal" title="purchase 1"/>
                                <connections>
                                    <action selector="nonConsumablePurchase" destination="BYZ-38-t0r" eventType="primaryActionTriggered" id="8j3-oq-8F4"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="VG3-26-V8F">
                                <rect key="frame" x="825" y="447" width="271" height="86"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <inset key="contentEdgeInsets" minX="40" minY="20" maxX="40" maxY="20"/>
                                <state key="normal" title="purchase 2"/>
                                <connections>
                                    <action selector="autoRenewablePurchase" destination="BYZ-38-t0r" eventType="primaryActionTriggered" id="qSE-zE-v41"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="984-qT-rjk">
                                <rect key="frame" x="860" y="557" width="202" height="86"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <inset key="contentEdgeInsets" minX="40" minY="20" maxX="40" maxY="20"/>
                                <state key="normal" title="restore"/>
                                <connections>
                                    <action selector="restorePurchases" destination="BYZ-38-t0r" eventType="primaryActionTriggered" id="Ce8-Mj-esn"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="TE5-kT-3nP">
                                <rect key="frame" x="864" y="667" width="193" height="86"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <inset key="contentEdgeInsets" minX="40" minY="20" maxX="40" maxY="20"/>
                                <state key="normal" title="verify1"/>
                                <connections>
                                    <action selector="nonConsumableVerifyPurchase" destination="BYZ-38-t0r" eventType="primaryActionTriggered" id="VkV-9u-PqD"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="iYp-Z0-czh">
                                <rect key="frame" x="862" y="777" width="198" height="86"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <inset key="contentEdgeInsets" minX="40" minY="20" maxX="40" maxY="20"/>
                                <state key="normal" title="verify2"/>
                                <connections>
                                    <action selector="autoRenewableVerifyPurchase" destination="BYZ-38-t0r" eventType="primaryActionTriggered" id="kqM-Mg-BKc"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="hdi-nV-Cky">
                                <rect key="frame" x="860" y="887" width="200" height="86"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <inset key="contentEdgeInsets" minX="40" minY="20" maxX="40" maxY="20"/>
                                <state key="normal" title="receipt"/>
                                <connections>
                                    <action selector="verifyReceipt" destination="BYZ-38-t0r" eventType="primaryActionTriggered" id="N3M-ke-Dgy"/>
                                </connections>
                            </button>
                        </subviews>
                        <color key="backgroundColor" red="0.0" green="0.0" blue="0.84593750000000001" alpha="0.0" colorSpace="custom" customColorSpace="sRGB"/>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="dkx-z0-nzr" sceneMemberID="firstResponder"/>
            </objects>
        </scene>
    </scenes>
</document>
