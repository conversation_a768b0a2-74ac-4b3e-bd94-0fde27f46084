//
//  OS.swift
//  SwiftyStoreKit
//
//  Copyright (c) 2020 <PERSON> (<EMAIL>)
//
//  Permission is hereby granted, free of charge, to any person obtaining a copy
//  of this software and associated documentation files (the "Software"), to deal
//  in the Software without restriction, including without limitation the rights
//  to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
//  copies of the Software, and to permit persons to whom the Software is
//  furnished to do so, subject to the following conditions:
//
//  The above copyright notice and this permission notice shall be included in
//  all copies or substantial portions of the Software.
//
//  THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
//  IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
//  FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
//  AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
//  LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
//  OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
//  THE SOFTWARE.

import StoreKit

// MARK: - Missing SKMutablePayment init with product on macOS
#if os(OSX)
    extension SKMutablePayment {
        convenience init(product: SKProduct) {
            self.init()
            self.productIdentifier = product.productIdentifier
        }
    }
#endif

// MARK: - Missing SKError on watchOS
#if os(watchOS) && swift(<5.3)
public struct SKError: Error {
    
    public typealias Code = SKErrorCode
    
    let _nsError: NSError

    init(_nsError: NSError) {
        self._nsError = _nsError
    }
    
    public var code: Code {
        return Code(rawValue: _nsError.code) ?? .unknown
    }
    
    static var unknown: Code = .unknown
    static var paymentInvalid: Code = .paymentInvalid
}
#endif
