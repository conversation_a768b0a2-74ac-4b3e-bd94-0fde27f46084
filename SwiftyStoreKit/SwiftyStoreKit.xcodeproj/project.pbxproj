// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		2F2B8B3024A64CC100CEF088 /* SKProductDiscount+LocalizedPrice.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B2124A64CC000CEF088 /* SKProductDiscount+LocalizedPrice.swift */; };
		2F2B8B3124A64CC100CEF088 /* SKProductDiscount+LocalizedPrice.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B2124A64CC000CEF088 /* SKProductDiscount+LocalizedPrice.swift */; };
		2F2B8B3224A64CC100CEF088 /* SKProductDiscount+LocalizedPrice.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B2124A64CC000CEF088 /* SKProductDiscount+LocalizedPrice.swift */; };
		2F2B8B3324A64CC100CEF088 /* SKProductDiscount+LocalizedPrice.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B2124A64CC000CEF088 /* SKProductDiscount+LocalizedPrice.swift */; };
		2F2B8B3424A64CC100CEF088 /* AppleReceiptValidator.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B2224A64CC000CEF088 /* AppleReceiptValidator.swift */; };
		2F2B8B3524A64CC100CEF088 /* AppleReceiptValidator.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B2224A64CC000CEF088 /* AppleReceiptValidator.swift */; };
		2F2B8B3624A64CC100CEF088 /* AppleReceiptValidator.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B2224A64CC000CEF088 /* AppleReceiptValidator.swift */; };
		2F2B8B3724A64CC100CEF088 /* AppleReceiptValidator.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B2224A64CC000CEF088 /* AppleReceiptValidator.swift */; };
		2F2B8B3824A64CC100CEF088 /* InAppProductQueryRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B2324A64CC000CEF088 /* InAppProductQueryRequest.swift */; };
		2F2B8B3924A64CC100CEF088 /* InAppProductQueryRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B2324A64CC000CEF088 /* InAppProductQueryRequest.swift */; };
		2F2B8B3A24A64CC100CEF088 /* InAppProductQueryRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B2324A64CC000CEF088 /* InAppProductQueryRequest.swift */; };
		2F2B8B3B24A64CC100CEF088 /* InAppProductQueryRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B2324A64CC000CEF088 /* InAppProductQueryRequest.swift */; };
		2F2B8B3C24A64CC100CEF088 /* InAppReceipt.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B2424A64CC000CEF088 /* InAppReceipt.swift */; };
		2F2B8B3D24A64CC100CEF088 /* InAppReceipt.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B2424A64CC000CEF088 /* InAppReceipt.swift */; };
		2F2B8B3E24A64CC100CEF088 /* InAppReceipt.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B2424A64CC000CEF088 /* InAppReceipt.swift */; };
		2F2B8B3F24A64CC100CEF088 /* InAppReceipt.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B2424A64CC000CEF088 /* InAppReceipt.swift */; };
		2F2B8B4024A64CC100CEF088 /* SwiftyStoreKit+Types.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B2524A64CC000CEF088 /* SwiftyStoreKit+Types.swift */; };
		2F2B8B4124A64CC100CEF088 /* SwiftyStoreKit+Types.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B2524A64CC000CEF088 /* SwiftyStoreKit+Types.swift */; };
		2F2B8B4224A64CC100CEF088 /* SwiftyStoreKit+Types.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B2524A64CC000CEF088 /* SwiftyStoreKit+Types.swift */; };
		2F2B8B4324A64CC100CEF088 /* SwiftyStoreKit+Types.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B2524A64CC000CEF088 /* SwiftyStoreKit+Types.swift */; };
		2F2B8B4424A64CC100CEF088 /* CompleteTransactionsController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B2624A64CC000CEF088 /* CompleteTransactionsController.swift */; };
		2F2B8B4524A64CC100CEF088 /* CompleteTransactionsController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B2624A64CC000CEF088 /* CompleteTransactionsController.swift */; };
		2F2B8B4624A64CC100CEF088 /* CompleteTransactionsController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B2624A64CC000CEF088 /* CompleteTransactionsController.swift */; };
		2F2B8B4724A64CC100CEF088 /* CompleteTransactionsController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B2624A64CC000CEF088 /* CompleteTransactionsController.swift */; };
		2F2B8B4824A64CC100CEF088 /* PaymentQueueController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B2724A64CC000CEF088 /* PaymentQueueController.swift */; };
		2F2B8B4924A64CC100CEF088 /* PaymentQueueController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B2724A64CC000CEF088 /* PaymentQueueController.swift */; };
		2F2B8B4A24A64CC100CEF088 /* PaymentQueueController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B2724A64CC000CEF088 /* PaymentQueueController.swift */; };
		2F2B8B4B24A64CC100CEF088 /* PaymentQueueController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B2724A64CC000CEF088 /* PaymentQueueController.swift */; };
		2F2B8B4C24A64CC100CEF088 /* InAppReceiptRefreshRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B2824A64CC000CEF088 /* InAppReceiptRefreshRequest.swift */; };
		2F2B8B4D24A64CC100CEF088 /* InAppReceiptRefreshRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B2824A64CC000CEF088 /* InAppReceiptRefreshRequest.swift */; };
		2F2B8B4E24A64CC100CEF088 /* InAppReceiptRefreshRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B2824A64CC000CEF088 /* InAppReceiptRefreshRequest.swift */; };
		2F2B8B4F24A64CC100CEF088 /* InAppReceiptRefreshRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B2824A64CC000CEF088 /* InAppReceiptRefreshRequest.swift */; };
		2F2B8B5024A64CC100CEF088 /* InAppReceiptVerificator.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B2924A64CC100CEF088 /* InAppReceiptVerificator.swift */; };
		2F2B8B5124A64CC100CEF088 /* InAppReceiptVerificator.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B2924A64CC100CEF088 /* InAppReceiptVerificator.swift */; };
		2F2B8B5224A64CC100CEF088 /* InAppReceiptVerificator.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B2924A64CC100CEF088 /* InAppReceiptVerificator.swift */; };
		2F2B8B5324A64CC100CEF088 /* InAppReceiptVerificator.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B2924A64CC100CEF088 /* InAppReceiptVerificator.swift */; };
		2F2B8B5424A64CC100CEF088 /* SKProduct+LocalizedPrice.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B2A24A64CC100CEF088 /* SKProduct+LocalizedPrice.swift */; };
		2F2B8B5524A64CC100CEF088 /* SKProduct+LocalizedPrice.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B2A24A64CC100CEF088 /* SKProduct+LocalizedPrice.swift */; };
		2F2B8B5624A64CC100CEF088 /* SKProduct+LocalizedPrice.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B2A24A64CC100CEF088 /* SKProduct+LocalizedPrice.swift */; };
		2F2B8B5724A64CC100CEF088 /* SKProduct+LocalizedPrice.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B2A24A64CC100CEF088 /* SKProduct+LocalizedPrice.swift */; };
		2F2B8B5824A64CC100CEF088 /* ProductsInfoController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B2B24A64CC100CEF088 /* ProductsInfoController.swift */; };
		2F2B8B5924A64CC100CEF088 /* ProductsInfoController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B2B24A64CC100CEF088 /* ProductsInfoController.swift */; };
		2F2B8B5A24A64CC100CEF088 /* ProductsInfoController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B2B24A64CC100CEF088 /* ProductsInfoController.swift */; };
		2F2B8B5B24A64CC100CEF088 /* ProductsInfoController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B2B24A64CC100CEF088 /* ProductsInfoController.swift */; };
		2F2B8B5C24A64CC100CEF088 /* OS.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B2C24A64CC100CEF088 /* OS.swift */; };
		2F2B8B5D24A64CC100CEF088 /* OS.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B2C24A64CC100CEF088 /* OS.swift */; };
		2F2B8B5E24A64CC100CEF088 /* OS.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B2C24A64CC100CEF088 /* OS.swift */; };
		2F2B8B5F24A64CC100CEF088 /* OS.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B2C24A64CC100CEF088 /* OS.swift */; };
		2F2B8B6024A64CC100CEF088 /* PaymentsController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B2D24A64CC100CEF088 /* PaymentsController.swift */; };
		2F2B8B6124A64CC100CEF088 /* PaymentsController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B2D24A64CC100CEF088 /* PaymentsController.swift */; };
		2F2B8B6224A64CC100CEF088 /* PaymentsController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B2D24A64CC100CEF088 /* PaymentsController.swift */; };
		2F2B8B6324A64CC100CEF088 /* PaymentsController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B2D24A64CC100CEF088 /* PaymentsController.swift */; };
		2F2B8B6424A64CC100CEF088 /* SwiftyStoreKit.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B2E24A64CC100CEF088 /* SwiftyStoreKit.swift */; };
		2F2B8B6524A64CC100CEF088 /* SwiftyStoreKit.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B2E24A64CC100CEF088 /* SwiftyStoreKit.swift */; };
		2F2B8B6624A64CC100CEF088 /* SwiftyStoreKit.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B2E24A64CC100CEF088 /* SwiftyStoreKit.swift */; };
		2F2B8B6724A64CC100CEF088 /* SwiftyStoreKit.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B2E24A64CC100CEF088 /* SwiftyStoreKit.swift */; };
		2F2B8B6824A64CC100CEF088 /* RestorePurchasesController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B2F24A64CC100CEF088 /* RestorePurchasesController.swift */; };
		2F2B8B6924A64CC100CEF088 /* RestorePurchasesController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B2F24A64CC100CEF088 /* RestorePurchasesController.swift */; };
		2F2B8B6A24A64CC100CEF088 /* RestorePurchasesController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B2F24A64CC100CEF088 /* RestorePurchasesController.swift */; };
		2F2B8B6B24A64CC100CEF088 /* RestorePurchasesController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B2F24A64CC100CEF088 /* RestorePurchasesController.swift */; };
		2F2B8B7424A64CD700CEF088 /* SwiftyStoreKit-watchOS.h in Headers */ = {isa = PBXBuildFile; fileRef = 2F2B8B6C24A64CD700CEF088 /* SwiftyStoreKit-watchOS.h */; };
		2F2B8B7524A64CD700CEF088 /* SwiftyStoreKit-watchOS.h in Headers */ = {isa = PBXBuildFile; fileRef = 2F2B8B6C24A64CD700CEF088 /* SwiftyStoreKit-watchOS.h */; };
		2F2B8B7624A64CD700CEF088 /* SwiftyStoreKit-watchOS.h in Headers */ = {isa = PBXBuildFile; fileRef = 2F2B8B6C24A64CD700CEF088 /* SwiftyStoreKit-watchOS.h */; };
		2F2B8B7724A64CD700CEF088 /* SwiftyStoreKit-watchOS.h in Headers */ = {isa = PBXBuildFile; fileRef = 2F2B8B6C24A64CD700CEF088 /* SwiftyStoreKit-watchOS.h */; };
		2F2B8B8424A64CD700CEF088 /* SwiftyStoreKit-tvOS.h in Headers */ = {isa = PBXBuildFile; fileRef = 2F2B8B7024A64CD700CEF088 /* SwiftyStoreKit-tvOS.h */; };
		2F2B8B8524A64CD700CEF088 /* SwiftyStoreKit-tvOS.h in Headers */ = {isa = PBXBuildFile; fileRef = 2F2B8B7024A64CD700CEF088 /* SwiftyStoreKit-tvOS.h */; };
		2F2B8B8624A64CD700CEF088 /* SwiftyStoreKit-tvOS.h in Headers */ = {isa = PBXBuildFile; fileRef = 2F2B8B7024A64CD700CEF088 /* SwiftyStoreKit-tvOS.h */; };
		2F2B8B8724A64CD700CEF088 /* SwiftyStoreKit-tvOS.h in Headers */ = {isa = PBXBuildFile; fileRef = 2F2B8B7024A64CD700CEF088 /* SwiftyStoreKit-tvOS.h */; };
		2F2B8B8824A64CD700CEF088 /* SwiftyStoreKit-iOS.h in Headers */ = {isa = PBXBuildFile; fileRef = 2F2B8B7124A64CD700CEF088 /* SwiftyStoreKit-iOS.h */; };
		2F2B8B8924A64CD700CEF088 /* SwiftyStoreKit-iOS.h in Headers */ = {isa = PBXBuildFile; fileRef = 2F2B8B7124A64CD700CEF088 /* SwiftyStoreKit-iOS.h */; };
		2F2B8B8A24A64CD700CEF088 /* SwiftyStoreKit-iOS.h in Headers */ = {isa = PBXBuildFile; fileRef = 2F2B8B7124A64CD700CEF088 /* SwiftyStoreKit-iOS.h */; };
		2F2B8B8B24A64CD700CEF088 /* SwiftyStoreKit-iOS.h in Headers */ = {isa = PBXBuildFile; fileRef = 2F2B8B7124A64CD700CEF088 /* SwiftyStoreKit-iOS.h */; };
		2F2B8B8C24A64CD700CEF088 /* SwiftyStoreKit-macOS.h in Headers */ = {isa = PBXBuildFile; fileRef = 2F2B8B7224A64CD700CEF088 /* SwiftyStoreKit-macOS.h */; };
		2F2B8B8D24A64CD700CEF088 /* SwiftyStoreKit-macOS.h in Headers */ = {isa = PBXBuildFile; fileRef = 2F2B8B7224A64CD700CEF088 /* SwiftyStoreKit-macOS.h */; };
		2F2B8B8E24A64CD700CEF088 /* SwiftyStoreKit-macOS.h in Headers */ = {isa = PBXBuildFile; fileRef = 2F2B8B7224A64CD700CEF088 /* SwiftyStoreKit-macOS.h */; };
		2F2B8B8F24A64CD700CEF088 /* SwiftyStoreKit-macOS.h in Headers */ = {isa = PBXBuildFile; fileRef = 2F2B8B7224A64CD700CEF088 /* SwiftyStoreKit-macOS.h */; };
		2F2B8BA024A64DE600CEF088 /* PaymentQueueControllerTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B9424A64DE600CEF088 /* PaymentQueueControllerTests.swift */; };
		2F2B8BA124A64DE600CEF088 /* PaymentTransactionObserverFake.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B9524A64DE600CEF088 /* PaymentTransactionObserverFake.swift */; };
		2F2B8BA224A64DE600CEF088 /* ProductsInfoControllerTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B9624A64DE600CEF088 /* ProductsInfoControllerTests.swift */; };
		2F2B8BA324A64DE600CEF088 /* TestPaymentTransaction.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B9724A64DE600CEF088 /* TestPaymentTransaction.swift */; };
		2F2B8BA424A64DE600CEF088 /* TestProduct.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B9824A64DE600CEF088 /* TestProduct.swift */; };
		2F2B8BA524A64DE600CEF088 /* RestorePurchasesControllerTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B9924A64DE600CEF088 /* RestorePurchasesControllerTests.swift */; };
		2F2B8BA624A64DE600CEF088 /* CompleteTransactionsControllerTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B9A24A64DE600CEF088 /* CompleteTransactionsControllerTests.swift */; };
		2F2B8BA724A64DE600CEF088 /* InAppReceiptVerificatorTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B9B24A64DE600CEF088 /* InAppReceiptVerificatorTests.swift */; };
		2F2B8BA824A64DE600CEF088 /* InAppReceiptTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B9C24A64DE600CEF088 /* InAppReceiptTests.swift */; };
		2F2B8BA924A64DE600CEF088 /* PaymentsControllerTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B9D24A64DE600CEF088 /* PaymentsControllerTests.swift */; };
		2F2B8BAB24A64DE600CEF088 /* PaymentQueueSpy.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F2B8B9F24A64DE600CEF088 /* PaymentQueueSpy.swift */; };
		654287F61E79F5A000F61800 /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 654287F41E79F5A000F61800 /* Main.storyboard */; };
		654287F81E79F5A000F61800 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 654287F71E79F5A000F61800 /* Assets.xcassets */; };
		654287FD1E79F75000F61800 /* SwiftyStoreKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 54C0D52C1CF7404500F90BCE /* SwiftyStoreKit.framework */; };
		654287FE1E79F75000F61800 /* SwiftyStoreKit.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 54C0D52C1CF7404500F90BCE /* SwiftyStoreKit.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		654288021E7B34E500F61800 /* ViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 65F7DF701DCD4DF000835D30 /* ViewController.swift */; };
		654288061E7B3A8800F61800 /* NetworkActivityIndicatorManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 65F7DF6F1DCD4DF000835D30 /* NetworkActivityIndicatorManager.swift */; };
		654288071E7B3E1500F61800 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 65F7DF681DCD4DF000835D30 /* AppDelegate.swift */; };
		658A08431E2EC5120074A98F /* SwiftyStoreKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6502F62D1B985C40004E342D /* SwiftyStoreKit.framework */; };
		65F7DF711DCD4DF000835D30 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 65F7DF681DCD4DF000835D30 /* AppDelegate.swift */; };
		65F7DF721DCD4DF000835D30 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 65F7DF691DCD4DF000835D30 /* Assets.xcassets */; };
		65F7DF731DCD4DF000835D30 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 65F7DF6A1DCD4DF000835D30 /* LaunchScreen.storyboard */; };
		65F7DF741DCD4DF000835D30 /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 65F7DF6C1DCD4DF000835D30 /* Main.storyboard */; };
		65F7DF761DCD4DF000835D30 /* NetworkActivityIndicatorManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 65F7DF6F1DCD4DF000835D30 /* NetworkActivityIndicatorManager.swift */; };
		65F7DF771DCD4DF000835D30 /* ViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 65F7DF701DCD4DF000835D30 /* ViewController.swift */; };
		65F7DF841DCD4FC500835D30 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 65F7DF7E1DCD4FC500835D30 /* AppDelegate.swift */; };
		65F7DF851DCD4FC500835D30 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 65F7DF7F1DCD4FC500835D30 /* Assets.xcassets */; };
		65F7DF861DCD4FC500835D30 /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 65F7DF801DCD4FC500835D30 /* Main.storyboard */; };
		65F7DF881DCD4FC500835D30 /* ViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 65F7DF831DCD4FC500835D30 /* ViewController.swift */; };
		65F7DF8E1DCD524300835D30 /* SwiftyStoreKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6502F62D1B985C40004E342D /* SwiftyStoreKit.framework */; };
		65F7DF8F1DCD524300835D30 /* SwiftyStoreKit.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 6502F62D1B985C40004E342D /* SwiftyStoreKit.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		C4FD3A101C2954CD0035CFF3 /* SwiftyStoreKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = C4D74BBB1C24CEC90071AD3E /* SwiftyStoreKit.framework */; };
		C4FD3A111C2954CD0035CFF3 /* SwiftyStoreKit.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = C4D74BBB1C24CEC90071AD3E /* SwiftyStoreKit.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		654287FF1E79F75000F61800 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 6502F5F61B985833004E342D /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 54C0D52B1CF7404500F90BCE;
			remoteInfo = SwiftyStoreKit_tvOS;
		};
		658A08441E2EC5120074A98F /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 6502F5F61B985833004E342D /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 6502F62C1B985C40004E342D;
			remoteInfo = SwiftyStoreKit_iOS;
		};
		658A084D1E2EC83F0074A98F /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 6502F5F61B985833004E342D /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 6502F5FD1B985833004E342D;
			remoteInfo = SwiftyStoreKit_iOSDemo;
		};
		65F7DF901DCD524300835D30 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 6502F5F61B985833004E342D /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 6502F62C1B985C40004E342D;
			remoteInfo = SwiftyStoreKit_iOS;
		};
		C4FD3A121C2954CD0035CFF3 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 6502F5F61B985833004E342D /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = C4D74BBA1C24CEC90071AD3E;
			remoteInfo = SwiftyStoreKitOSX;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		654288011E79F75100F61800 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				654287FE1E79F75000F61800 /* SwiftyStoreKit.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		65F7DF921DCD524300835D30 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				65F7DF8F1DCD524300835D30 /* SwiftyStoreKit.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		C4FD3A141C2954CD0035CFF3 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				C4FD3A111C2954CD0035CFF3 /* SwiftyStoreKit.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		2F2B8B2124A64CC000CEF088 /* SKProductDiscount+LocalizedPrice.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; name = "SKProductDiscount+LocalizedPrice.swift"; path = "Sources/SwiftyStoreKit/SKProductDiscount+LocalizedPrice.swift"; sourceTree = SOURCE_ROOT; };
		2F2B8B2224A64CC000CEF088 /* AppleReceiptValidator.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; name = AppleReceiptValidator.swift; path = Sources/SwiftyStoreKit/AppleReceiptValidator.swift; sourceTree = SOURCE_ROOT; };
		2F2B8B2324A64CC000CEF088 /* InAppProductQueryRequest.swift */ = {isa = PBXFileReference; fileEncoding = 4; indentWidth = 4; lastKnownFileType = sourcecode.swift; name = InAppProductQueryRequest.swift; path = Sources/SwiftyStoreKit/InAppProductQueryRequest.swift; sourceTree = SOURCE_ROOT; };
		2F2B8B2424A64CC000CEF088 /* InAppReceipt.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; name = InAppReceipt.swift; path = Sources/SwiftyStoreKit/InAppReceipt.swift; sourceTree = SOURCE_ROOT; };
		2F2B8B2524A64CC000CEF088 /* SwiftyStoreKit+Types.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; name = "SwiftyStoreKit+Types.swift"; path = "Sources/SwiftyStoreKit/SwiftyStoreKit+Types.swift"; sourceTree = SOURCE_ROOT; };
		2F2B8B2624A64CC000CEF088 /* CompleteTransactionsController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; name = CompleteTransactionsController.swift; path = Sources/SwiftyStoreKit/CompleteTransactionsController.swift; sourceTree = SOURCE_ROOT; };
		2F2B8B2724A64CC000CEF088 /* PaymentQueueController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; name = PaymentQueueController.swift; path = Sources/SwiftyStoreKit/PaymentQueueController.swift; sourceTree = SOURCE_ROOT; };
		2F2B8B2824A64CC000CEF088 /* InAppReceiptRefreshRequest.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; name = InAppReceiptRefreshRequest.swift; path = Sources/SwiftyStoreKit/InAppReceiptRefreshRequest.swift; sourceTree = SOURCE_ROOT; };
		2F2B8B2924A64CC100CEF088 /* InAppReceiptVerificator.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; name = InAppReceiptVerificator.swift; path = Sources/SwiftyStoreKit/InAppReceiptVerificator.swift; sourceTree = SOURCE_ROOT; };
		2F2B8B2A24A64CC100CEF088 /* SKProduct+LocalizedPrice.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; name = "SKProduct+LocalizedPrice.swift"; path = "Sources/SwiftyStoreKit/SKProduct+LocalizedPrice.swift"; sourceTree = SOURCE_ROOT; };
		2F2B8B2B24A64CC100CEF088 /* ProductsInfoController.swift */ = {isa = PBXFileReference; fileEncoding = 4; indentWidth = 4; lastKnownFileType = sourcecode.swift; name = ProductsInfoController.swift; path = Sources/SwiftyStoreKit/ProductsInfoController.swift; sourceTree = SOURCE_ROOT; };
		2F2B8B2C24A64CC100CEF088 /* OS.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; name = OS.swift; path = Sources/SwiftyStoreKit/OS.swift; sourceTree = SOURCE_ROOT; };
		2F2B8B2D24A64CC100CEF088 /* PaymentsController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; name = PaymentsController.swift; path = Sources/SwiftyStoreKit/PaymentsController.swift; sourceTree = SOURCE_ROOT; };
		2F2B8B2E24A64CC100CEF088 /* SwiftyStoreKit.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; name = SwiftyStoreKit.swift; path = Sources/SwiftyStoreKit/SwiftyStoreKit.swift; sourceTree = SOURCE_ROOT; };
		2F2B8B2F24A64CC100CEF088 /* RestorePurchasesController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; name = RestorePurchasesController.swift; path = Sources/SwiftyStoreKit/RestorePurchasesController.swift; sourceTree = SOURCE_ROOT; };
		2F2B8B6C24A64CD700CEF088 /* SwiftyStoreKit-watchOS.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = "SwiftyStoreKit-watchOS.h"; path = "Sources/SwiftyStoreKit/Platforms/SwiftyStoreKit-watchOS.h"; sourceTree = SOURCE_ROOT; };
		2F2B8B6D24A64CD700CEF088 /* Info-macOS.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist; name = "Info-macOS.plist"; path = "Sources/SwiftyStoreKit/Platforms/Info-macOS.plist"; sourceTree = SOURCE_ROOT; };
		2F2B8B6E24A64CD700CEF088 /* Info-watchOS.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist; name = "Info-watchOS.plist"; path = "Sources/SwiftyStoreKit/Platforms/Info-watchOS.plist"; sourceTree = SOURCE_ROOT; };
		2F2B8B6F24A64CD700CEF088 /* Info-iOS.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist; name = "Info-iOS.plist"; path = "Sources/SwiftyStoreKit/Platforms/Info-iOS.plist"; sourceTree = SOURCE_ROOT; };
		2F2B8B7024A64CD700CEF088 /* SwiftyStoreKit-tvOS.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = "SwiftyStoreKit-tvOS.h"; path = "Sources/SwiftyStoreKit/Platforms/SwiftyStoreKit-tvOS.h"; sourceTree = SOURCE_ROOT; };
		2F2B8B7124A64CD700CEF088 /* SwiftyStoreKit-iOS.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = "SwiftyStoreKit-iOS.h"; path = "Sources/SwiftyStoreKit/Platforms/SwiftyStoreKit-iOS.h"; sourceTree = SOURCE_ROOT; };
		2F2B8B7224A64CD700CEF088 /* SwiftyStoreKit-macOS.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = "SwiftyStoreKit-macOS.h"; path = "Sources/SwiftyStoreKit/Platforms/SwiftyStoreKit-macOS.h"; sourceTree = SOURCE_ROOT; };
		2F2B8B7324A64CD700CEF088 /* Info-tvOS.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist; name = "Info-tvOS.plist"; path = "Sources/SwiftyStoreKit/Platforms/Info-tvOS.plist"; sourceTree = SOURCE_ROOT; };
		2F2B8B9424A64DE600CEF088 /* PaymentQueueControllerTests.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; name = PaymentQueueControllerTests.swift; path = Tests/SwiftyStoreKitTests/PaymentQueueControllerTests.swift; sourceTree = SOURCE_ROOT; };
		2F2B8B9524A64DE600CEF088 /* PaymentTransactionObserverFake.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; name = PaymentTransactionObserverFake.swift; path = Tests/SwiftyStoreKitTests/PaymentTransactionObserverFake.swift; sourceTree = SOURCE_ROOT; };
		2F2B8B9624A64DE600CEF088 /* ProductsInfoControllerTests.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; name = ProductsInfoControllerTests.swift; path = Tests/SwiftyStoreKitTests/ProductsInfoControllerTests.swift; sourceTree = SOURCE_ROOT; };
		2F2B8B9724A64DE600CEF088 /* TestPaymentTransaction.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; name = TestPaymentTransaction.swift; path = Tests/SwiftyStoreKitTests/TestPaymentTransaction.swift; sourceTree = SOURCE_ROOT; };
		2F2B8B9824A64DE600CEF088 /* TestProduct.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; name = TestProduct.swift; path = Tests/SwiftyStoreKitTests/TestProduct.swift; sourceTree = SOURCE_ROOT; };
		2F2B8B9924A64DE600CEF088 /* RestorePurchasesControllerTests.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; name = RestorePurchasesControllerTests.swift; path = Tests/SwiftyStoreKitTests/RestorePurchasesControllerTests.swift; sourceTree = SOURCE_ROOT; };
		2F2B8B9A24A64DE600CEF088 /* CompleteTransactionsControllerTests.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; name = CompleteTransactionsControllerTests.swift; path = Tests/SwiftyStoreKitTests/CompleteTransactionsControllerTests.swift; sourceTree = SOURCE_ROOT; };
		2F2B8B9B24A64DE600CEF088 /* InAppReceiptVerificatorTests.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; name = InAppReceiptVerificatorTests.swift; path = Tests/SwiftyStoreKitTests/InAppReceiptVerificatorTests.swift; sourceTree = SOURCE_ROOT; };
		2F2B8B9C24A64DE600CEF088 /* InAppReceiptTests.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; name = InAppReceiptTests.swift; path = Tests/SwiftyStoreKitTests/InAppReceiptTests.swift; sourceTree = SOURCE_ROOT; };
		2F2B8B9D24A64DE600CEF088 /* PaymentsControllerTests.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; name = PaymentsControllerTests.swift; path = Tests/SwiftyStoreKitTests/PaymentsControllerTests.swift; sourceTree = SOURCE_ROOT; };
		2F2B8B9E24A64DE600CEF088 /* Info-Tests.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; name = "Info-Tests.plist"; path = "Tests/SwiftyStoreKitTests/Info-Tests.plist"; sourceTree = SOURCE_ROOT; };
		2F2B8B9F24A64DE600CEF088 /* PaymentQueueSpy.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; name = PaymentQueueSpy.swift; path = Tests/SwiftyStoreKitTests/PaymentQueueSpy.swift; sourceTree = SOURCE_ROOT; };
		54C0D52C1CF7404500F90BCE /* SwiftyStoreKit.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = SwiftyStoreKit.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		6502F5FE1B985833004E342D /* SwiftyStoreKit_iOSDemo.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = SwiftyStoreKit_iOSDemo.app; sourceTree = BUILT_PRODUCTS_DIR; };
		6502F62D1B985C40004E342D /* SwiftyStoreKit.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = SwiftyStoreKit.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		654287EE1E79F5A000F61800 /* SwiftyStoreKit_tvOSDemo.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = SwiftyStoreKit_tvOSDemo.app; sourceTree = BUILT_PRODUCTS_DIR; };
		654287F51E79F5A000F61800 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		654287F71E79F5A000F61800 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		654287F91E79F5A000F61800 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		658A083E1E2EC5120074A98F /* SwiftyStoreKitTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = SwiftyStoreKitTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		65F7DF681DCD4DF000835D30 /* AppDelegate.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		65F7DF691DCD4DF000835D30 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		65F7DF6B1DCD4DF000835D30 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		65F7DF6D1DCD4DF000835D30 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		65F7DF6E1DCD4DF000835D30 /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		65F7DF6F1DCD4DF000835D30 /* NetworkActivityIndicatorManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NetworkActivityIndicatorManager.swift; sourceTree = "<group>"; };
		65F7DF701DCD4DF000835D30 /* ViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ViewController.swift; sourceTree = "<group>"; };
		65F7DF7E1DCD4FC500835D30 /* AppDelegate.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		65F7DF7F1DCD4FC500835D30 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		65F7DF811DCD4FC500835D30 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		65F7DF821DCD4FC500835D30 /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		65F7DF831DCD4FC500835D30 /* ViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ViewController.swift; sourceTree = "<group>"; };
		A61BF4DD2481F4970017D9BC /* SwiftyStoreKit.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = SwiftyStoreKit.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		C4D74BBB1C24CEC90071AD3E /* SwiftyStoreKit.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = SwiftyStoreKit.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		C4FD3A011C2954C10035CFF3 /* SwiftyStoreKit_macOSDemo.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = SwiftyStoreKit_macOSDemo.app; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		54C0D5281CF7404500F90BCE /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6502F5FB1B985833004E342D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				65F7DF8E1DCD524300835D30 /* SwiftyStoreKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6502F6291B985C40004E342D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		654287EB1E79F5A000F61800 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				654287FD1E79F75000F61800 /* SwiftyStoreKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		658A083B1E2EC5120074A98F /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				658A08431E2EC5120074A98F /* SwiftyStoreKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A61BF4D52481F4970017D9BC /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C4D74BB71C24CEC90071AD3E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C4FD39FE1C2954C10035CFF3 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				C4FD3A101C2954CD0035CFF3 /* SwiftyStoreKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		6502F5F51B985833004E342D = {
			isa = PBXGroup;
			children = (
				6502F6001B985833004E342D /* SwiftyStoreKit */,
				65F7DF671DCD4DF000835D30 /* SwiftyStoreKit-iOS-Demo */,
				65F7DF7D1DCD4FC500835D30 /* SwiftyStoreKit-macOS-Demo */,
				654287EF1E79F5A000F61800 /* SwiftyStoreKit-tvOS-Demo */,
				658A083F1E2EC5120074A98F /* SwiftyStoreKitTests */,
				6502F5FF1B985833004E342D /* Products */,
			);
			sourceTree = "<group>";
		};
		6502F5FF1B985833004E342D /* Products */ = {
			isa = PBXGroup;
			children = (
				6502F5FE1B985833004E342D /* SwiftyStoreKit_iOSDemo.app */,
				6502F62D1B985C40004E342D /* SwiftyStoreKit.framework */,
				C4D74BBB1C24CEC90071AD3E /* SwiftyStoreKit.framework */,
				C4FD3A011C2954C10035CFF3 /* SwiftyStoreKit_macOSDemo.app */,
				54C0D52C1CF7404500F90BCE /* SwiftyStoreKit.framework */,
				658A083E1E2EC5120074A98F /* SwiftyStoreKitTests.xctest */,
				654287EE1E79F5A000F61800 /* SwiftyStoreKit_tvOSDemo.app */,
				A61BF4DD2481F4970017D9BC /* SwiftyStoreKit.framework */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		6502F6001B985833004E342D /* SwiftyStoreKit */ = {
			isa = PBXGroup;
			children = (
				2F2B8B2224A64CC000CEF088 /* AppleReceiptValidator.swift */,
				2F2B8B2624A64CC000CEF088 /* CompleteTransactionsController.swift */,
				2F2B8B2324A64CC000CEF088 /* InAppProductQueryRequest.swift */,
				2F2B8B2424A64CC000CEF088 /* InAppReceipt.swift */,
				2F2B8B2824A64CC000CEF088 /* InAppReceiptRefreshRequest.swift */,
				2F2B8B2924A64CC100CEF088 /* InAppReceiptVerificator.swift */,
				2F2B8B2C24A64CC100CEF088 /* OS.swift */,
				2F2B8B2724A64CC000CEF088 /* PaymentQueueController.swift */,
				2F2B8B2D24A64CC100CEF088 /* PaymentsController.swift */,
				2F2B8B2B24A64CC100CEF088 /* ProductsInfoController.swift */,
				2F2B8B2F24A64CC100CEF088 /* RestorePurchasesController.swift */,
				2F2B8B2A24A64CC100CEF088 /* SKProduct+LocalizedPrice.swift */,
				2F2B8B2124A64CC000CEF088 /* SKProductDiscount+LocalizedPrice.swift */,
				2F2B8B2E24A64CC100CEF088 /* SwiftyStoreKit.swift */,
				2F2B8B2524A64CC000CEF088 /* SwiftyStoreKit+Types.swift */,
				65F7DF931DCD536100835D30 /* Platforms */,
			);
			path = SwiftyStoreKit;
			sourceTree = "<group>";
		};
		654287EF1E79F5A000F61800 /* SwiftyStoreKit-tvOS-Demo */ = {
			isa = PBXGroup;
			children = (
				654287F41E79F5A000F61800 /* Main.storyboard */,
				654287F71E79F5A000F61800 /* Assets.xcassets */,
				654287F91E79F5A000F61800 /* Info.plist */,
			);
			path = "SwiftyStoreKit-tvOS-Demo";
			sourceTree = "<group>";
		};
		658A083F1E2EC5120074A98F /* SwiftyStoreKitTests */ = {
			isa = PBXGroup;
			children = (
				2F2B8B9A24A64DE600CEF088 /* CompleteTransactionsControllerTests.swift */,
				2F2B8B9C24A64DE600CEF088 /* InAppReceiptTests.swift */,
				2F2B8B9B24A64DE600CEF088 /* InAppReceiptVerificatorTests.swift */,
				2F2B8B9E24A64DE600CEF088 /* Info-Tests.plist */,
				2F2B8B9424A64DE600CEF088 /* PaymentQueueControllerTests.swift */,
				2F2B8B9F24A64DE600CEF088 /* PaymentQueueSpy.swift */,
				2F2B8B9D24A64DE600CEF088 /* PaymentsControllerTests.swift */,
				2F2B8B9524A64DE600CEF088 /* PaymentTransactionObserverFake.swift */,
				2F2B8B9624A64DE600CEF088 /* ProductsInfoControllerTests.swift */,
				2F2B8B9924A64DE600CEF088 /* RestorePurchasesControllerTests.swift */,
				2F2B8B9724A64DE600CEF088 /* TestPaymentTransaction.swift */,
				2F2B8B9824A64DE600CEF088 /* TestProduct.swift */,
			);
			path = SwiftyStoreKitTests;
			sourceTree = "<group>";
		};
		65F7DF671DCD4DF000835D30 /* SwiftyStoreKit-iOS-Demo */ = {
			isa = PBXGroup;
			children = (
				65F7DF681DCD4DF000835D30 /* AppDelegate.swift */,
				65F7DF691DCD4DF000835D30 /* Assets.xcassets */,
				65F7DF6A1DCD4DF000835D30 /* LaunchScreen.storyboard */,
				65F7DF6C1DCD4DF000835D30 /* Main.storyboard */,
				65F7DF6E1DCD4DF000835D30 /* Info.plist */,
				65F7DF6F1DCD4DF000835D30 /* NetworkActivityIndicatorManager.swift */,
				65F7DF701DCD4DF000835D30 /* ViewController.swift */,
			);
			path = "SwiftyStoreKit-iOS-Demo";
			sourceTree = "<group>";
		};
		65F7DF7D1DCD4FC500835D30 /* SwiftyStoreKit-macOS-Demo */ = {
			isa = PBXGroup;
			children = (
				65F7DF7E1DCD4FC500835D30 /* AppDelegate.swift */,
				65F7DF7F1DCD4FC500835D30 /* Assets.xcassets */,
				65F7DF801DCD4FC500835D30 /* Main.storyboard */,
				65F7DF821DCD4FC500835D30 /* Info.plist */,
				65F7DF831DCD4FC500835D30 /* ViewController.swift */,
			);
			path = "SwiftyStoreKit-macOS-Demo";
			sourceTree = "<group>";
		};
		65F7DF931DCD536100835D30 /* Platforms */ = {
			isa = PBXGroup;
			children = (
				2F2B8B6F24A64CD700CEF088 /* Info-iOS.plist */,
				2F2B8B6D24A64CD700CEF088 /* Info-macOS.plist */,
				2F2B8B7324A64CD700CEF088 /* Info-tvOS.plist */,
				2F2B8B6E24A64CD700CEF088 /* Info-watchOS.plist */,
				2F2B8B7124A64CD700CEF088 /* SwiftyStoreKit-iOS.h */,
				2F2B8B7224A64CD700CEF088 /* SwiftyStoreKit-macOS.h */,
				2F2B8B7024A64CD700CEF088 /* SwiftyStoreKit-tvOS.h */,
				2F2B8B6C24A64CD700CEF088 /* SwiftyStoreKit-watchOS.h */,
			);
			path = Platforms;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		54C0D5291CF7404500F90BCE /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2F2B8B8A24A64CD700CEF088 /* SwiftyStoreKit-iOS.h in Headers */,
				2F2B8B7624A64CD700CEF088 /* SwiftyStoreKit-watchOS.h in Headers */,
				2F2B8B8624A64CD700CEF088 /* SwiftyStoreKit-tvOS.h in Headers */,
				2F2B8B8E24A64CD700CEF088 /* SwiftyStoreKit-macOS.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6502F62A1B985C40004E342D /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2F2B8B8824A64CD700CEF088 /* SwiftyStoreKit-iOS.h in Headers */,
				2F2B8B7424A64CD700CEF088 /* SwiftyStoreKit-watchOS.h in Headers */,
				2F2B8B8424A64CD700CEF088 /* SwiftyStoreKit-tvOS.h in Headers */,
				2F2B8B8C24A64CD700CEF088 /* SwiftyStoreKit-macOS.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A61BF4D62481F4970017D9BC /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2F2B8B8B24A64CD700CEF088 /* SwiftyStoreKit-iOS.h in Headers */,
				2F2B8B7724A64CD700CEF088 /* SwiftyStoreKit-watchOS.h in Headers */,
				2F2B8B8724A64CD700CEF088 /* SwiftyStoreKit-tvOS.h in Headers */,
				2F2B8B8F24A64CD700CEF088 /* SwiftyStoreKit-macOS.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C4D74BB81C24CEC90071AD3E /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2F2B8B8924A64CD700CEF088 /* SwiftyStoreKit-iOS.h in Headers */,
				2F2B8B7524A64CD700CEF088 /* SwiftyStoreKit-watchOS.h in Headers */,
				2F2B8B8524A64CD700CEF088 /* SwiftyStoreKit-tvOS.h in Headers */,
				2F2B8B8D24A64CD700CEF088 /* SwiftyStoreKit-macOS.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		54C0D52B1CF7404500F90BCE /* SwiftyStoreKit_tvOS */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 54C0D5331CF7404500F90BCE /* Build configuration list for PBXNativeTarget "SwiftyStoreKit_tvOS" */;
			buildPhases = (
				54C0D5271CF7404500F90BCE /* Sources */,
				54C0D5281CF7404500F90BCE /* Frameworks */,
				54C0D5291CF7404500F90BCE /* Headers */,
				54C0D52A1CF7404500F90BCE /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = SwiftyStoreKit_tvOS;
			productName = SwiftyStoreKitTV;
			productReference = 54C0D52C1CF7404500F90BCE /* SwiftyStoreKit.framework */;
			productType = "com.apple.product-type.framework";
		};
		6502F5FD1B985833004E342D /* SwiftyStoreKit_iOSDemo */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 6502F6101B985833004E342D /* Build configuration list for PBXNativeTarget "SwiftyStoreKit_iOSDemo" */;
			buildPhases = (
				6502F5FA1B985833004E342D /* Sources */,
				6502F5FB1B985833004E342D /* Frameworks */,
				6502F5FC1B985833004E342D /* Resources */,
				65F7DF921DCD524300835D30 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				65F7DF911DCD524300835D30 /* PBXTargetDependency */,
			);
			name = SwiftyStoreKit_iOSDemo;
			productName = SwiftyStoreKit;
			productReference = 6502F5FE1B985833004E342D /* SwiftyStoreKit_iOSDemo.app */;
			productType = "com.apple.product-type.application";
		};
		6502F62C1B985C40004E342D /* SwiftyStoreKit_iOS */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 6502F6361B985C40004E342D /* Build configuration list for PBXNativeTarget "SwiftyStoreKit_iOS" */;
			buildPhases = (
				6502F6281B985C40004E342D /* Sources */,
				6502F6291B985C40004E342D /* Frameworks */,
				6502F62A1B985C40004E342D /* Headers */,
				6502F62B1B985C40004E342D /* Resources */,
				C4B298351E5C25E5007C87C2 /* swiftlint */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = SwiftyStoreKit_iOS;
			productName = SwiftyStoreKit;
			productReference = 6502F62D1B985C40004E342D /* SwiftyStoreKit.framework */;
			productType = "com.apple.product-type.framework";
		};
		654287ED1E79F5A000F61800 /* SwiftyStoreKit_tvOSDemo */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 654287FC1E79F5A000F61800 /* Build configuration list for PBXNativeTarget "SwiftyStoreKit_tvOSDemo" */;
			buildPhases = (
				654287EA1E79F5A000F61800 /* Sources */,
				654287EB1E79F5A000F61800 /* Frameworks */,
				654287EC1E79F5A000F61800 /* Resources */,
				654288011E79F75100F61800 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				654288001E79F75000F61800 /* PBXTargetDependency */,
			);
			name = SwiftyStoreKit_tvOSDemo;
			productName = "SwiftyStoreKit-tvOS-Demo";
			productReference = 654287EE1E79F5A000F61800 /* SwiftyStoreKit_tvOSDemo.app */;
			productType = "com.apple.product-type.application";
		};
		658A083D1E2EC5120074A98F /* SwiftyStoreKitTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 658A08461E2EC5120074A98F /* Build configuration list for PBXNativeTarget "SwiftyStoreKitTests" */;
			buildPhases = (
				658A083A1E2EC5120074A98F /* Sources */,
				658A083B1E2EC5120074A98F /* Frameworks */,
				658A083C1E2EC5120074A98F /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				658A08451E2EC5120074A98F /* PBXTargetDependency */,
				658A084E1E2EC83F0074A98F /* PBXTargetDependency */,
			);
			name = SwiftyStoreKitTests;
			productName = SwiftyStoreKitTests;
			productReference = 658A083E1E2EC5120074A98F /* SwiftyStoreKitTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		A61BF4C42481F4970017D9BC /* SwiftyStoreKit_watchOS */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A61BF4DA2481F4970017D9BC /* Build configuration list for PBXNativeTarget "SwiftyStoreKit_watchOS" */;
			buildPhases = (
				A61BF4C52481F4970017D9BC /* Sources */,
				A61BF4D62481F4970017D9BC /* Headers */,
				A61BF4D52481F4970017D9BC /* Frameworks */,
				A61BF4D82481F4970017D9BC /* Resources */,
				A61BF4D92481F4970017D9BC /* swiftlint */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = SwiftyStoreKit_watchOS;
			productName = SwiftyStoreKit;
			productReference = A61BF4DD2481F4970017D9BC /* SwiftyStoreKit.framework */;
			productType = "com.apple.product-type.framework";
		};
		C4D74BBA1C24CEC90071AD3E /* SwiftyStoreKit_macOS */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = C4D74BC21C24CECA0071AD3E /* Build configuration list for PBXNativeTarget "SwiftyStoreKit_macOS" */;
			buildPhases = (
				C4D74BB61C24CEC90071AD3E /* Sources */,
				C4D74BB71C24CEC90071AD3E /* Frameworks */,
				C4D74BB81C24CEC90071AD3E /* Headers */,
				C4D74BB91C24CEC90071AD3E /* Resources */,
				C4B298341E5C25DD007C87C2 /* swiftlint */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = SwiftyStoreKit_macOS;
			productName = SwiftyStoreKitOSX;
			productReference = C4D74BBB1C24CEC90071AD3E /* SwiftyStoreKit.framework */;
			productType = "com.apple.product-type.framework";
		};
		C4FD3A001C2954C10035CFF3 /* SwiftyStoreKit_macOSDemo */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = C4FD3A0D1C2954C10035CFF3 /* Build configuration list for PBXNativeTarget "SwiftyStoreKit_macOSDemo" */;
			buildPhases = (
				C4FD39FD1C2954C10035CFF3 /* Sources */,
				C4FD39FE1C2954C10035CFF3 /* Frameworks */,
				C4FD39FF1C2954C10035CFF3 /* Resources */,
				C4FD3A141C2954CD0035CFF3 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				C4FD3A131C2954CD0035CFF3 /* PBXTargetDependency */,
			);
			name = SwiftyStoreKit_macOSDemo;
			productName = SwiftyStoreOSXDemo;
			productReference = C4FD3A011C2954C10035CFF3 /* SwiftyStoreKit_macOSDemo.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		6502F5F61B985833004E342D /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 0820;
				LastUpgradeCheck = 1160;
				ORGANIZATIONNAME = musevisions;
				TargetAttributes = {
					54C0D52B1CF7404500F90BCE = {
						CreatedOnToolsVersion = 7.3.1;
						DevelopmentTeam = M54ZVB688G;
						LastSwiftMigration = 1150;
					};
					6502F5FD1B985833004E342D = {
						CreatedOnToolsVersion = 7.0;
						LastSwiftMigration = 0800;
						ProvisioningStyle = Automatic;
					};
					6502F62C1B985C40004E342D = {
						CreatedOnToolsVersion = 7.0;
						LastSwiftMigration = 1150;
					};
					654287ED1E79F5A000F61800 = {
						CreatedOnToolsVersion = 8.2.1;
						ProvisioningStyle = Automatic;
					};
					658A083D1E2EC5120074A98F = {
						CreatedOnToolsVersion = 8.2.1;
						LastSwiftMigration = 1150;
						ProvisioningStyle = Automatic;
						TestTargetID = 6502F5FD1B985833004E342D;
					};
					A61BF4C42481F4970017D9BC = {
						LastSwiftMigration = 1150;
					};
					C4D74BBA1C24CEC90071AD3E = {
						CreatedOnToolsVersion = 7.2;
						LastSwiftMigration = 1150;
					};
					C4FD3A001C2954C10035CFF3 = {
						CreatedOnToolsVersion = 7.2;
						LastSwiftMigration = 0800;
					};
				};
			};
			buildConfigurationList = 6502F5F91B985833004E342D /* Build configuration list for PBXProject "SwiftyStoreKit" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 6502F5F51B985833004E342D;
			productRefGroup = 6502F5FF1B985833004E342D /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				6502F62C1B985C40004E342D /* SwiftyStoreKit_iOS */,
				C4D74BBA1C24CEC90071AD3E /* SwiftyStoreKit_macOS */,
				54C0D52B1CF7404500F90BCE /* SwiftyStoreKit_tvOS */,
				A61BF4C42481F4970017D9BC /* SwiftyStoreKit_watchOS */,
				6502F5FD1B985833004E342D /* SwiftyStoreKit_iOSDemo */,
				C4FD3A001C2954C10035CFF3 /* SwiftyStoreKit_macOSDemo */,
				654287ED1E79F5A000F61800 /* SwiftyStoreKit_tvOSDemo */,
				658A083D1E2EC5120074A98F /* SwiftyStoreKitTests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		54C0D52A1CF7404500F90BCE /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6502F5FC1B985833004E342D /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				65F7DF721DCD4DF000835D30 /* Assets.xcassets in Resources */,
				65F7DF741DCD4DF000835D30 /* Main.storyboard in Resources */,
				65F7DF731DCD4DF000835D30 /* LaunchScreen.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6502F62B1B985C40004E342D /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		654287EC1E79F5A000F61800 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				654287F81E79F5A000F61800 /* Assets.xcassets in Resources */,
				654287F61E79F5A000F61800 /* Main.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		658A083C1E2EC5120074A98F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A61BF4D82481F4970017D9BC /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C4D74BB91C24CEC90071AD3E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C4FD39FF1C2954C10035CFF3 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				65F7DF851DCD4FC500835D30 /* Assets.xcassets in Resources */,
				65F7DF861DCD4FC500835D30 /* Main.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		A61BF4D92481F4970017D9BC /* swiftlint */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			name = swiftlint;
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "if which swiftlint >/dev/null; then\nswiftlint\nelse\necho \"SwiftLint does not exist, download from https://github.com/realm/SwiftLint\"\nfi\n";
		};
		C4B298341E5C25DD007C87C2 /* swiftlint */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			name = swiftlint;
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "if which swiftlint >/dev/null; then\nswiftlint\nelse\necho \"SwiftLint does not exist, download from https://github.com/realm/SwiftLint\"\nfi";
		};
		C4B298351E5C25E5007C87C2 /* swiftlint */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			name = swiftlint;
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "if which swiftlint >/dev/null; then\nswiftlint\nelse\necho \"SwiftLint does not exist, download from https://github.com/realm/SwiftLint\"\nfi";
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		54C0D5271CF7404500F90BCE /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2F2B8B3A24A64CC100CEF088 /* InAppProductQueryRequest.swift in Sources */,
				2F2B8B6224A64CC100CEF088 /* PaymentsController.swift in Sources */,
				2F2B8B5624A64CC100CEF088 /* SKProduct+LocalizedPrice.swift in Sources */,
				2F2B8B3E24A64CC100CEF088 /* InAppReceipt.swift in Sources */,
				2F2B8B3624A64CC100CEF088 /* AppleReceiptValidator.swift in Sources */,
				2F2B8B6A24A64CC100CEF088 /* RestorePurchasesController.swift in Sources */,
				2F2B8B4624A64CC100CEF088 /* CompleteTransactionsController.swift in Sources */,
				2F2B8B5E24A64CC100CEF088 /* OS.swift in Sources */,
				2F2B8B5224A64CC100CEF088 /* InAppReceiptVerificator.swift in Sources */,
				2F2B8B6624A64CC100CEF088 /* SwiftyStoreKit.swift in Sources */,
				2F2B8B4A24A64CC100CEF088 /* PaymentQueueController.swift in Sources */,
				2F2B8B4224A64CC100CEF088 /* SwiftyStoreKit+Types.swift in Sources */,
				2F2B8B4E24A64CC100CEF088 /* InAppReceiptRefreshRequest.swift in Sources */,
				2F2B8B3224A64CC100CEF088 /* SKProductDiscount+LocalizedPrice.swift in Sources */,
				2F2B8B5A24A64CC100CEF088 /* ProductsInfoController.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6502F5FA1B985833004E342D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				65F7DF771DCD4DF000835D30 /* ViewController.swift in Sources */,
				65F7DF711DCD4DF000835D30 /* AppDelegate.swift in Sources */,
				65F7DF761DCD4DF000835D30 /* NetworkActivityIndicatorManager.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6502F6281B985C40004E342D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2F2B8B3824A64CC100CEF088 /* InAppProductQueryRequest.swift in Sources */,
				2F2B8B6024A64CC100CEF088 /* PaymentsController.swift in Sources */,
				2F2B8B5424A64CC100CEF088 /* SKProduct+LocalizedPrice.swift in Sources */,
				2F2B8B3C24A64CC100CEF088 /* InAppReceipt.swift in Sources */,
				2F2B8B3424A64CC100CEF088 /* AppleReceiptValidator.swift in Sources */,
				2F2B8B6824A64CC100CEF088 /* RestorePurchasesController.swift in Sources */,
				2F2B8B4424A64CC100CEF088 /* CompleteTransactionsController.swift in Sources */,
				2F2B8B5C24A64CC100CEF088 /* OS.swift in Sources */,
				2F2B8B5024A64CC100CEF088 /* InAppReceiptVerificator.swift in Sources */,
				2F2B8B6424A64CC100CEF088 /* SwiftyStoreKit.swift in Sources */,
				2F2B8B4824A64CC100CEF088 /* PaymentQueueController.swift in Sources */,
				2F2B8B4024A64CC100CEF088 /* SwiftyStoreKit+Types.swift in Sources */,
				2F2B8B4C24A64CC100CEF088 /* InAppReceiptRefreshRequest.swift in Sources */,
				2F2B8B3024A64CC100CEF088 /* SKProductDiscount+LocalizedPrice.swift in Sources */,
				2F2B8B5824A64CC100CEF088 /* ProductsInfoController.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		654287EA1E79F5A000F61800 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				654288071E7B3E1500F61800 /* AppDelegate.swift in Sources */,
				654288021E7B34E500F61800 /* ViewController.swift in Sources */,
				654288061E7B3A8800F61800 /* NetworkActivityIndicatorManager.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		658A083A1E2EC5120074A98F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2F2B8BA924A64DE600CEF088 /* PaymentsControllerTests.swift in Sources */,
				2F2B8BA224A64DE600CEF088 /* ProductsInfoControllerTests.swift in Sources */,
				2F2B8BA524A64DE600CEF088 /* RestorePurchasesControllerTests.swift in Sources */,
				2F2B8BA724A64DE600CEF088 /* InAppReceiptVerificatorTests.swift in Sources */,
				2F2B8BA124A64DE600CEF088 /* PaymentTransactionObserverFake.swift in Sources */,
				2F2B8BAB24A64DE600CEF088 /* PaymentQueueSpy.swift in Sources */,
				2F2B8BA624A64DE600CEF088 /* CompleteTransactionsControllerTests.swift in Sources */,
				2F2B8BA024A64DE600CEF088 /* PaymentQueueControllerTests.swift in Sources */,
				2F2B8BA324A64DE600CEF088 /* TestPaymentTransaction.swift in Sources */,
				2F2B8BA424A64DE600CEF088 /* TestProduct.swift in Sources */,
				2F2B8BA824A64DE600CEF088 /* InAppReceiptTests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A61BF4C52481F4970017D9BC /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2F2B8B3B24A64CC100CEF088 /* InAppProductQueryRequest.swift in Sources */,
				2F2B8B6324A64CC100CEF088 /* PaymentsController.swift in Sources */,
				2F2B8B5724A64CC100CEF088 /* SKProduct+LocalizedPrice.swift in Sources */,
				2F2B8B3F24A64CC100CEF088 /* InAppReceipt.swift in Sources */,
				2F2B8B3724A64CC100CEF088 /* AppleReceiptValidator.swift in Sources */,
				2F2B8B6B24A64CC100CEF088 /* RestorePurchasesController.swift in Sources */,
				2F2B8B4724A64CC100CEF088 /* CompleteTransactionsController.swift in Sources */,
				2F2B8B5F24A64CC100CEF088 /* OS.swift in Sources */,
				2F2B8B5324A64CC100CEF088 /* InAppReceiptVerificator.swift in Sources */,
				2F2B8B6724A64CC100CEF088 /* SwiftyStoreKit.swift in Sources */,
				2F2B8B4B24A64CC100CEF088 /* PaymentQueueController.swift in Sources */,
				2F2B8B4324A64CC100CEF088 /* SwiftyStoreKit+Types.swift in Sources */,
				2F2B8B4F24A64CC100CEF088 /* InAppReceiptRefreshRequest.swift in Sources */,
				2F2B8B3324A64CC100CEF088 /* SKProductDiscount+LocalizedPrice.swift in Sources */,
				2F2B8B5B24A64CC100CEF088 /* ProductsInfoController.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C4D74BB61C24CEC90071AD3E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2F2B8B3924A64CC100CEF088 /* InAppProductQueryRequest.swift in Sources */,
				2F2B8B6124A64CC100CEF088 /* PaymentsController.swift in Sources */,
				2F2B8B5524A64CC100CEF088 /* SKProduct+LocalizedPrice.swift in Sources */,
				2F2B8B3D24A64CC100CEF088 /* InAppReceipt.swift in Sources */,
				2F2B8B3524A64CC100CEF088 /* AppleReceiptValidator.swift in Sources */,
				2F2B8B6924A64CC100CEF088 /* RestorePurchasesController.swift in Sources */,
				2F2B8B4524A64CC100CEF088 /* CompleteTransactionsController.swift in Sources */,
				2F2B8B5D24A64CC100CEF088 /* OS.swift in Sources */,
				2F2B8B5124A64CC100CEF088 /* InAppReceiptVerificator.swift in Sources */,
				2F2B8B6524A64CC100CEF088 /* SwiftyStoreKit.swift in Sources */,
				2F2B8B4924A64CC100CEF088 /* PaymentQueueController.swift in Sources */,
				2F2B8B4124A64CC100CEF088 /* SwiftyStoreKit+Types.swift in Sources */,
				2F2B8B4D24A64CC100CEF088 /* InAppReceiptRefreshRequest.swift in Sources */,
				2F2B8B3124A64CC100CEF088 /* SKProductDiscount+LocalizedPrice.swift in Sources */,
				2F2B8B5924A64CC100CEF088 /* ProductsInfoController.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C4FD39FD1C2954C10035CFF3 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				65F7DF841DCD4FC500835D30 /* AppDelegate.swift in Sources */,
				65F7DF881DCD4FC500835D30 /* ViewController.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		654288001E79F75000F61800 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 54C0D52B1CF7404500F90BCE /* SwiftyStoreKit_tvOS */;
			targetProxy = 654287FF1E79F75000F61800 /* PBXContainerItemProxy */;
		};
		658A08451E2EC5120074A98F /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 6502F62C1B985C40004E342D /* SwiftyStoreKit_iOS */;
			targetProxy = 658A08441E2EC5120074A98F /* PBXContainerItemProxy */;
		};
		658A084E1E2EC83F0074A98F /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 6502F5FD1B985833004E342D /* SwiftyStoreKit_iOSDemo */;
			targetProxy = 658A084D1E2EC83F0074A98F /* PBXContainerItemProxy */;
		};
		65F7DF911DCD524300835D30 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 6502F62C1B985C40004E342D /* SwiftyStoreKit_iOS */;
			targetProxy = 65F7DF901DCD524300835D30 /* PBXContainerItemProxy */;
		};
		C4FD3A131C2954CD0035CFF3 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = C4D74BBA1C24CEC90071AD3E /* SwiftyStoreKit_macOS */;
			targetProxy = C4FD3A121C2954CD0035CFF3 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		654287F41E79F5A000F61800 /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				654287F51E79F5A000F61800 /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		65F7DF6A1DCD4DF000835D30 /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				65F7DF6B1DCD4DF000835D30 /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
		65F7DF6C1DCD4DF000835D30 /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				65F7DF6D1DCD4DF000835D30 /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		65F7DF801DCD4FC500835D30 /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				65F7DF811DCD4FC500835D30 /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		54C0D5311CF7404500F90BCE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ENABLE_MODULES = YES;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = M54ZVB688G;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				INFOPLIST_FILE = "$(SRCROOT)/Sources/SwiftyStoreKit/Platforms/Info-tvOS.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = com.musevisions.tvOS.SwiftyStoreKit;
				PRODUCT_NAME = SwiftyStoreKit;
				SDKROOT = appletvos;
				SKIP_INSTALL = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 3;
				TVOS_DEPLOYMENT_TARGET = 9.0;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		54C0D5321CF7404500F90BCE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ENABLE_MODULES = YES;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = M54ZVB688G;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				INFOPLIST_FILE = "$(SRCROOT)/Sources/SwiftyStoreKit/Platforms/Info-tvOS.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = com.musevisions.tvOS.SwiftyStoreKit;
				PRODUCT_NAME = SwiftyStoreKit;
				SDKROOT = appletvos;
				SKIP_INSTALL = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Owholemodule";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 3;
				TVOS_DEPLOYMENT_TARGET = 9.0;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		6502F60E1B985833004E342D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				MACOSX_DEPLOYMENT_TARGET = 10.10;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		6502F60F1B985833004E342D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				MACOSX_DEPLOYMENT_TARGET = 10.10;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				SWIFT_VERSION = 5.0;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		6502F6111B985833004E342D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				DEVELOPMENT_TEAM = "";
				INFOPLIST_FILE = "$(SRCROOT)/SwiftyStoreKit-iOS-Demo/Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = com.musevisions.iOS.SwiftyStoreDemo;
				PRODUCT_NAME = "$(TARGET_NAME)";
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		6502F6121B985833004E342D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				DEVELOPMENT_TEAM = "";
				INFOPLIST_FILE = "$(SRCROOT)/SwiftyStoreKit-iOS-Demo/Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = com.musevisions.iOS.SwiftyStoreDemo;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_OPTIMIZATION_LEVEL = "-Owholemodule";
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
		6502F6371B985C40004E342D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				APPLICATION_EXTENSION_API_ONLY = YES;
				CLANG_ENABLE_MODULES = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = "";
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				INFOPLIST_FILE = "$(SRCROOT)/Sources/SwiftyStoreKit/Platforms/Info-iOS.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = com.musevisions.iOS.SwiftyStoreKit;
				PRODUCT_NAME = SwiftyStoreKit;
				SKIP_INSTALL = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		6502F6381B985C40004E342D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				APPLICATION_EXTENSION_API_ONLY = YES;
				CLANG_ENABLE_MODULES = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = "";
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				INFOPLIST_FILE = "$(SRCROOT)/Sources/SwiftyStoreKit/Platforms/Info-iOS.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = com.musevisions.iOS.SwiftyStoreKit;
				PRODUCT_NAME = SwiftyStoreKit;
				SKIP_INSTALL = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Owholemodule";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		654287FA1E79F5A000F61800 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ASSETCATALOG_COMPILER_APPICON_NAME = "App Icon & Top Shelf Image";
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = LaunchImage;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "iPhone Developer";
				DEVELOPMENT_TEAM = "";
				INFOPLIST_FILE = "SwiftyStoreKit-tvOS-Demo/Info.plist";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = com.musevisions.iOS.SwiftyStoreDemo;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = appletvos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_VERSION = 4.2;
				TARGETED_DEVICE_FAMILY = 3;
				TVOS_DEPLOYMENT_TARGET = 10.1;
			};
			name = Debug;
		};
		654287FB1E79F5A000F61800 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ASSETCATALOG_COMPILER_APPICON_NAME = "App Icon & Top Shelf Image";
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = LaunchImage;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "iPhone Developer";
				DEVELOPMENT_TEAM = "";
				INFOPLIST_FILE = "SwiftyStoreKit-tvOS-Demo/Info.plist";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = com.musevisions.iOS.SwiftyStoreDemo;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = appletvos;
				SWIFT_OPTIMIZATION_LEVEL = "-Owholemodule";
				SWIFT_VERSION = 4.2;
				TARGETED_DEVICE_FAMILY = 3;
				TVOS_DEPLOYMENT_TARGET = 10.1;
			};
			name = Release;
		};
		658A08471E2EC5120074A98F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ENABLE_MODULES = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				INFOPLIST_FILE = "Tests/SwiftyStoreKitTests/Info-Tests.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 10.2;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = com.musevisions.iOS.SwiftyStoreKitTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_SWIFT3_OBJC_INFERENCE = Default;
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/SwiftyStoreKit_iOSDemo.app/SwiftyStoreKit_iOSDemo";
			};
			name = Debug;
		};
		658A08481E2EC5120074A98F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ENABLE_MODULES = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				INFOPLIST_FILE = "Tests/SwiftyStoreKitTests/Info-Tests.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 10.2;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = com.musevisions.iOS.SwiftyStoreKitTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_OPTIMIZATION_LEVEL = "-Owholemodule";
				SWIFT_SWIFT3_OBJC_INFERENCE = Default;
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/SwiftyStoreKit_iOSDemo.app/SwiftyStoreKit_iOSDemo";
			};
			name = Release;
		};
		A61BF4DB2481F4970017D9BC /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				APPLICATION_EXTENSION_API_ONLY = YES;
				CLANG_ENABLE_MODULES = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = "";
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				INFOPLIST_FILE = "$(SRCROOT)/Sources/SwiftyStoreKit/Platforms/Info-watchOS.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = com.musevisions.iOS.SwiftyStoreKit;
				PRODUCT_NAME = SwiftyStoreKit;
				SDKROOT = watchos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "watchsimulator watchos";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		A61BF4DC2481F4970017D9BC /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				APPLICATION_EXTENSION_API_ONLY = YES;
				CLANG_ENABLE_MODULES = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = "";
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				INFOPLIST_FILE = "$(SRCROOT)/Sources/SwiftyStoreKit/Platforms/Info-watchOS.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = com.musevisions.iOS.SwiftyStoreKit;
				PRODUCT_NAME = SwiftyStoreKit;
				SDKROOT = watchos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "watchsimulator watchos";
				SWIFT_OPTIMIZATION_LEVEL = "-Owholemodule";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		C4D74BC01C24CECA0071AD3E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_IDENTITY = "-";
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				FRAMEWORK_VERSION = A;
				INFOPLIST_FILE = "$(SRCROOT)/Sources/SwiftyStoreKit/Platforms/Info-macOS.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/../Frameworks @loader_path/Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 10.10;
				PRODUCT_BUNDLE_IDENTIFIER = com.musevisions.macOS.SwiftyStoreKit;
				PRODUCT_NAME = SwiftyStoreKit;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		C4D74BC11C24CECA0071AD3E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_IDENTITY = "-";
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				FRAMEWORK_VERSION = A;
				INFOPLIST_FILE = "$(SRCROOT)/Sources/SwiftyStoreKit/Platforms/Info-macOS.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/../Frameworks @loader_path/Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 10.10;
				PRODUCT_BUNDLE_IDENTIFIER = com.musevisions.macOS.SwiftyStoreKit;
				PRODUCT_NAME = SwiftyStoreKit;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Owholemodule";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		C4FD3A0E1C2954C10035CFF3 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_IDENTITY = "-";
				COMBINE_HIDPI_IMAGES = YES;
				INFOPLIST_FILE = "$(SRCROOT)/SwiftyStoreKit-macOS-Demo/Info.plist";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 10.10;
				PRODUCT_BUNDLE_IDENTIFIER = com.musevisions.OSX.SwiftyStoreDemo;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = macosx;
			};
			name = Debug;
		};
		C4FD3A0F1C2954C10035CFF3 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_IDENTITY = "-";
				COMBINE_HIDPI_IMAGES = YES;
				INFOPLIST_FILE = "$(SRCROOT)/SwiftyStoreKit-macOS-Demo/Info.plist";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 10.10;
				PRODUCT_BUNDLE_IDENTIFIER = com.musevisions.OSX.SwiftyStoreDemo;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = macosx;
				SWIFT_OPTIMIZATION_LEVEL = "-Owholemodule";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		54C0D5331CF7404500F90BCE /* Build configuration list for PBXNativeTarget "SwiftyStoreKit_tvOS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				54C0D5311CF7404500F90BCE /* Debug */,
				54C0D5321CF7404500F90BCE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		6502F5F91B985833004E342D /* Build configuration list for PBXProject "SwiftyStoreKit" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				6502F60E1B985833004E342D /* Debug */,
				6502F60F1B985833004E342D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		6502F6101B985833004E342D /* Build configuration list for PBXNativeTarget "SwiftyStoreKit_iOSDemo" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				6502F6111B985833004E342D /* Debug */,
				6502F6121B985833004E342D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		6502F6361B985C40004E342D /* Build configuration list for PBXNativeTarget "SwiftyStoreKit_iOS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				6502F6371B985C40004E342D /* Debug */,
				6502F6381B985C40004E342D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		654287FC1E79F5A000F61800 /* Build configuration list for PBXNativeTarget "SwiftyStoreKit_tvOSDemo" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				654287FA1E79F5A000F61800 /* Debug */,
				654287FB1E79F5A000F61800 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		658A08461E2EC5120074A98F /* Build configuration list for PBXNativeTarget "SwiftyStoreKitTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				658A08471E2EC5120074A98F /* Debug */,
				658A08481E2EC5120074A98F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A61BF4DA2481F4970017D9BC /* Build configuration list for PBXNativeTarget "SwiftyStoreKit_watchOS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A61BF4DB2481F4970017D9BC /* Debug */,
				A61BF4DC2481F4970017D9BC /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		C4D74BC21C24CECA0071AD3E /* Build configuration list for PBXNativeTarget "SwiftyStoreKit_macOS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C4D74BC01C24CECA0071AD3E /* Debug */,
				C4D74BC11C24CECA0071AD3E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		C4FD3A0D1C2954C10035CFF3 /* Build configuration list for PBXNativeTarget "SwiftyStoreKit_macOSDemo" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C4FD3A0E1C2954C10035CFF3 /* Debug */,
				C4FD3A0F1C2954C10035CFF3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 6502F5F61B985833004E342D /* Project object */;
}
