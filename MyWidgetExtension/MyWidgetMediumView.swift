//
//  MyWidgetMediumView.swift
//  MyWidgetExtensionExtension
//
//  Created by lifubing on 2025/9/21.
//

import WidgetKit
import SwiftUI
import AppIntents

struct MyWidgetMediumView: View {
    var entry: PhotoEntry
    
    var body: some View {
        
        GeometryReader { geometry in
            
            let sizeMax = 1.3
            let offsetSize = entry.isResized ? 0.01 : 0.14
            let sizeForHeight = entry.isResized ? 1 : 1.1
            
            ZStack {
                // 背景图片或渐变，完全填满Widget
                if let photo = entry.photo, let photoIdentifier = entry.photoIdentifier, !photoIdentifier.isEmpty {
                    
                    if entry.isResized,
                       let photo = entry.photo {
                            Image(uiImage: photo)
                                .aspectRatio(contentMode: .fill) // 让背景图片填满视图
                                .frame(width: geometry.size.width, height: geometry.size.height)
                                .blur(radius: 7) // 调整模糊程度
                    }
                    
                    let resizedPhotoWidth = geometry.size.width * 0.8
                    let resizedPhotoHeight = resizedPhotoWidth * photo.size.height / photo.size.width

                    // 计算调整后的高度，确保图片不会超出容器高度，同时保持宽高比
                    let finalPhotoHeight = min(resizedPhotoHeight, geometry.size.height)
                    let finalPhotoWidth = finalPhotoHeight * photo.size.width / photo.size.height

                    // 如果计算后的宽度超出容器宽度，就调整宽度到最大宽度，并重新计算高度
                    let finalResizedPhotoWidth = min(finalPhotoWidth, geometry.size.width)
                    let finalResizedPhotoHeight = finalResizedPhotoWidth * photo.size.height / photo.size.width
                    
                    if entry.isResized {
                        HStack(spacing: 0) {
                            Spacer()
                            
                            // 左侧：完整照片显示区域
                            Link(destination: URL(string: "photoclear://openPhoto?id=\(photoIdentifier)")!) {
                                Image(uiImage: photo)
                                    .resizable()
                                    .aspectRatio(contentMode: .fit)
                                    .frame(width: finalResizedPhotoWidth, height: finalResizedPhotoHeight)
                                    .cornerRadius(10)
                                    .clipped()
//                                    .padding(1)
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 10)
                                            .stroke(Color.white, lineWidth: 2)
                                    )
                            }
                            
                            Spacer()
                            Spacer()
                        }
                    } else {
                        // 正常模式：平铺显示
                        Link(destination: URL(string: "photoclear://openPhoto?id=\(photoIdentifier)")!) {
                            Image(uiImage: photo)
                                .resizable()
                                .aspectRatio(contentMode: .fill)
                                .frame(width: geometry.size.width * sizeMax, height: geometry.size.height * sizeMax)
                                .offset(x: -geometry.size.width * offsetSize, y: -geometry.size.height * offsetSize)
                        }
                    }
                    
                } else {
                    // 默认背景
                    Link(destination: URL(string: "photoclear://openPhoto")!) {
                        Text("当前相簿所有照片都已操作过，请点击打开APP更新缓存或者更换其他相簿")
                            .font(.system(size: 14))
                            .multilineTextAlignment(.center)
                            .frame(maxWidth: .infinity, maxHeight: .infinity)
                    }
                }
                
                if !entry.isResized {
                    // 时间显示区域 - 放在左下角
                    VStack {
                        Spacer()
                        HStack {
                            if let photoMetadata = entry.photoMetadata,
                               let creationDate = photoMetadata.creationDate {
                                Text(formatPhotoDate(creationDate))
                                    .font(.system(size: 12, weight: .medium))
                                    .foregroundColor(.white)
                                    .padding(.horizontal, 8)
                                    .padding(.vertical, 4)
                                    .background(Color.black.opacity(0.2))
                                    .cornerRadius(6)
                                    .shadow(radius: 2)
                            }
                            Spacer()
                        }
                    }
                    .frame(width: geometry.size.width, height: geometry.size.height * 1)
                    .offset(x: -geometry.size.width * offsetSize - 8, y: -geometry.size.height * offsetSize)
                }
                
                // 按钮区域 - 放在右上角
                let frameSize = 30.0
                let btnFontSize = 13.0
                
                VStack(spacing:22) {
                    HStack {
                        Spacer()
                        if let photoIdentifier = entry.photoIdentifier {
                            if entry.isResized {
                                Button(intent: UnResizePhotoIntent(photoIdentifier:photoIdentifier)) {
                                    Image(systemName: "arrow.up.left.and.arrow.down.right")
                                        .font(.system(size: btnFontSize))
                                        .foregroundColor(.white)
                                        .frame(width: frameSize, height: frameSize)
                                        .background(Color.black.opacity(0.2))
                                        .clipShape(Rectangle())
                                        .cornerRadius(4)
                                }
                                .buttonStyle(PlainButtonStyle())
                                
                            } else {
                                Button(intent: ResizePhotoIntent(photoIdentifier:photoIdentifier)) {
                                    Image(systemName: "arrow.up.right.and.arrow.down.left")
                                        .font(.system(size: btnFontSize))
                                        .foregroundColor(.white)
                                        .frame(width: frameSize, height: frameSize) // 设置正方形尺寸
                                        .background(Color.black.opacity(0.2)) // 背景色
                                        .clipShape(Rectangle()) // 方形背景
                                        .cornerRadius(4) // 可选：给按钮加上圆角
                                }
                                .buttonStyle(PlainButtonStyle())
                            }
                        }
                    }
                    
                    HStack {
                        
                        Spacer()
                        if let photoIdentifier = entry.photoIdentifier {
                            
                            Button(intent: ArchivePhotoIntent(photoIdentifier:photoIdentifier)) {
                                Image(systemName: "archivebox")
                                    .font(.system(size: btnFontSize))
                                    .foregroundColor(.white)
                                    .frame(width: frameSize, height: frameSize) // 设置正方形尺寸
                                    .background(Color.black.opacity(0.2)) // 背景色
                                    .clipShape(Rectangle()) // 方形背景
                                    .cornerRadius(6) // 可选：给按钮加上圆角
                            }
                            .buttonStyle(PlainButtonStyle())
                        }
                    }
                    
                    
                    
                    HStack {
                        Spacer()
                        
                        HStack(spacing: 12) {
                            if let photoIdentifier = entry.photoIdentifier {
                                  
                                // 标记删除按钮
                                Button(intent: MarkForDeletionIntent(photoIdentifier: photoIdentifier)) {
                                    Image(systemName: "trash")
                                        .font(.system(size: btnFontSize))
                                        .foregroundColor(.white)
                                        .frame(width: frameSize, height: frameSize) // 设置正方形尺寸
                                        .background(Color.black.opacity(0.2)) // 背景色
                                        .clipShape(Rectangle()) // 方形背景
                                        .cornerRadius(6) // 可选：给按钮加上圆角
                                }
                                .buttonStyle(PlainButtonStyle())
                                
                                // 切换到下一张照片按钮
                                Button(intent: NextPhotoIntent(photoIdentifier:photoIdentifier)) {
                                    Image(systemName: "chevron.right")
                                        .font(.system(size: btnFontSize))
                                        .foregroundColor(.white)
                                        .frame(width: frameSize, height: frameSize) // 设置正方形尺寸
                                        .background(Color.black.opacity(0.2)) // 背景色
                                        .clipShape(Rectangle()) // 方形背景
                                        .cornerRadius(6) // 可选：给按钮加上圆角
                                }
                                .buttonStyle(PlainButtonStyle())
                                   
                            }
                        }
                    }
                }
                .frame(width: geometry.size.width, height: geometry.size.height * sizeForHeight)
                .offset(x: -geometry.size.width * offsetSize, y: -geometry.size.height * offsetSize)
            }
        }
    }
}
