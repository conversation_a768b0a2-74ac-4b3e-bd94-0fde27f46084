//
//  SharedPhotoManager.swift
//  MyWidgetExtension
//
//  Created by <PERSON><PERSON> on 2025/9/18.
//

import Foundation
import UIKit

struct PhotoMetadata {
    let localIdentifier: String
    let creationDate: Date?
    let modificationDate: Date?
}

struct WidgetShowPhotoModel {
    let photoIdentifier: String?
    let photoMetadata: PhotoMetadata?
    let photo:UIImage?
    let isResized: Bool // 新增：标记照片是否处于缩放状态
}

class SharedPhotoManager {
    static let shared = SharedPhotoManager()
    
    private let groupIdentifier = "group.com.lfb.manager.photoclear.shared"
    
    private init() {}
    
    var sharedContainerURL: URL? {
        return FileManager.default.containerURL(forSecurityApplicationGroupIdentifier: groupIdentifier)
    }
    
    var photosDirectoryURL: URL? {
        guard let containerURL = sharedContainerURL else { return nil }
        return containerURL.appendingPathComponent("WidgetPhotos")
    }
    
    func getRandomPhotoModel() -> WidgetShowPhotoModel? {
        guard let photosDir = photosDirectoryURL else { return nil }
        
        do {
            let photoFiles = try FileManager.default.contentsOfDirectory(at: photosDir,
                                                                       includingPropertiesForKeys: nil,
                                                                       options: [])
            
            let jpgFiles = photoFiles.filter { $0.pathExtension.lowercased() == "jpg" }
            
            guard !jpgFiles.isEmpty else { return nil }
            
            // 获取已操作过的照片列表
            let archivedPhotos = getArchivedPhotos()
            
            // 过滤掉已操作过的照片
            let availableFiles = jpgFiles.filter { file in
                let fileName = file.lastPathComponent
                if let identifier = extractLocalIdentifierFromFileName(fileName) {
                    return !archivedPhotos.contains(identifier)
                }
                return true
            }
            
            var targetPhotoURL: URL
            
            if !availableFiles.isEmpty {
                // 从未操作过的照片中随机选择
                let randomIndex = Int.random(in: 0..<availableFiles.count)
                targetPhotoURL = availableFiles[randomIndex]
            } else {
                // 如果所有照片都操作过了，从全部照片中随机选择
//                let randomIndex = Int.random(in: 0..<jpgFiles.count)
//                targetPhotoURL = jpgFiles[randomIndex]
                return nil
            }
            
            let fileName = targetPhotoURL.lastPathComponent
            let currentPhotoIdentifier = extractLocalIdentifierFromFileName(fileName)
            
            // 加载照片元数据
            let currentPhotoMetadata = loadPhotoMetadata(for: fileName)
            
//            NSLog("targetPhotoURL = \(targetPhotoURL)")
            guard let imageData = try? Data(contentsOf: targetPhotoURL),
                  let image = UIImage(data: imageData) else { return nil }
            
            // 检查照片是否处于缩放状态
            let isResized = self.getResizePhotoEnable()

            return WidgetShowPhotoModel(photoIdentifier: currentPhotoIdentifier, photoMetadata: currentPhotoMetadata, photo: image, isResized: isResized)
        } catch {
            print("获取随机照片失败: \(error)")
            return nil
        }
    }
    
    func getPhotoModel(_ photoIdentifier: String) -> WidgetShowPhotoModel? {
        guard let photosDir = photosDirectoryURL else { return nil }
        
        do {
            let photoFiles = try FileManager.default.contentsOfDirectory(at: photosDir,
                                                                       includingPropertiesForKeys: nil,
                                                                       options: [])
            
            let jpgFiles = photoFiles.filter { $0.pathExtension.lowercased() == "jpg" }
            
            guard !jpgFiles.isEmpty else { return nil }
            
            var targetPhotoURL: URL?
            var fileName: String?
            var currentPhotoIdentifier: String?
            
            for file in jpgFiles {
                let theFileName = file.lastPathComponent
                let thePhotoIdentifier = extractLocalIdentifierFromFileName(theFileName)
                if photoIdentifier == thePhotoIdentifier {
                    fileName = theFileName
                    currentPhotoIdentifier = photoIdentifier
                    targetPhotoURL = file
                }
            }
            
//            if !availableFiles.isEmpty {
//                // 从未操作过的照片中随机选择
//                let randomIndex = Int.random(in: 0..<availableFiles.count)
//                targetPhotoURL = availableFiles[randomIndex]
//            } else {
//                // 如果所有照片都操作过了，从全部照片中随机选择
//                let randomIndex = Int.random(in: 0..<jpgFiles.count)
//                targetPhotoURL = jpgFiles[randomIndex]
////                return nil
//            }
            
//            let fileName = targetPhotoURL.lastPathComponent
//            let currentPhotoIdentifier = extractLocalIdentifierFromFileName(fileName)
            
            // 加载照片元数据
            
            
            let currentPhotoMetadata = loadPhotoMetadata(for: fileName ?? "")
            
//            NSLog("targetPhotoURL = \(targetPhotoURL)")
            guard let imageData = try? Data(contentsOf: targetPhotoURL ?? URL(fileURLWithPath: "")),
                  let image = UIImage(data: imageData) else { return nil }
            
            // 检查照片是否处于缩放状态
            let isResized = self.getResizePhotoEnable()

            return WidgetShowPhotoModel(photoIdentifier: currentPhotoIdentifier, photoMetadata: currentPhotoMetadata, photo: image, isResized: isResized)
        } catch {
            print("获取随机照片失败: \(error)")
            return nil
        }
    }
    
    
    func getCachedPhotosCount() -> Int {
        guard let photosDir = photosDirectoryURL else { return 0 }
        
        do {
            let photoFiles = try FileManager.default.contentsOfDirectory(at: photosDir, 
                                                                       includingPropertiesForKeys: nil, 
                                                                       options: [])
            return photoFiles.filter { $0.pathExtension.lowercased() == "jpg" }.count
        } catch {
            return 0
        }
    }
    
    func markPhotoForDeletion(_ photoIdentifier: String) {
        // 使用UserDefaults在App Group中共享数据
        let userDefaults = UserDefaults(suiteName: groupIdentifier)
        
        // 获取当前的deleteArray
        var deleteArray = userDefaults?.array(forKey: "deleteArray") as? [String] ?? []
        
        // 如果照片标识符不在数组中，则添加
        if !deleteArray.contains(photoIdentifier) {
            deleteArray.append(photoIdentifier)
            userDefaults?.set(deleteArray, forKey: "deleteArray")
            userDefaults?.synchronize()
        }
        
        // 同时记录到操作过的照片列表中
        recordOperatedPhoto(photoIdentifier)
    }
    
    func recordOperatedPhoto(_ photoIdentifier: String) {
        // 记录用户操作过的照片ID，用于过滤
        let userDefaults = UserDefaults(suiteName: groupIdentifier)
        
        var operatedPhotos = userDefaults?.array(forKey: "operatedPhotos") as? [String] ?? []
        
        if !operatedPhotos.contains(photoIdentifier) {
            operatedPhotos.append(photoIdentifier)
            userDefaults?.set(operatedPhotos, forKey: "operatedPhotos")
            userDefaults?.synchronize()
        }
    }

    func resizePhotoEnable(_ resizeEnable: Bool) {
        let userDefaults = UserDefaults(suiteName: groupIdentifier)

        // 获取当前缩放状态的照片列表
        userDefaults?.set(resizeEnable, forKey: "resizePhotoEnable")

        userDefaults?.synchronize()
    }
    
    func getResizePhotoEnable() -> Bool {
        let userDefaults = UserDefaults(suiteName: groupIdentifier)

        let result = userDefaults?.bool(forKey: "resizePhotoEnable")
        return result ?? false
    }
    
    func setCurrentActionPhoto(_ photoIdentifier: String) {
        let userDefaults = UserDefaults(suiteName: groupIdentifier)

        // 获取当前缩放状态的照片列表
        userDefaults?.set(photoIdentifier, forKey: "currentActionPhoto")

        userDefaults?.synchronize()
    }
    
    
    func getCurrentActionPhoto() -> String {
        let userDefaults = UserDefaults(suiteName: groupIdentifier)
        let resutl = userDefaults?.string(forKey: "currentActionPhoto") as? String ?? ""
        return resutl
    }
    
    
    func updateResizePhotoTime() {
        let userDefaults = UserDefaults(suiteName: groupIdentifier)

        // 获取当前缩放状态的照片列表
        userDefaults?.set(Date(), forKey: "resizedPhotoTime")
        userDefaults?.synchronize()
    }
    
    func clearResizePhotoTime() {
        let userDefaults = UserDefaults(suiteName: groupIdentifier)

        // 获取当前缩放状态的照片列表
        userDefaults?.set(nil, forKey: "resizedPhotoTime")
        userDefaults?.synchronize()
    }
    
    func getResizePhotoTime() -> Date? {
        let userDefaults = UserDefaults(suiteName: groupIdentifier)
        let resutl = userDefaults?.object(forKey: "resizedPhotoTime") as? Date
        
        return resutl
    }
    
    func markPhotoForArchive(_ photoIdentifier: String) {
        // 使用UserDefaults在App Group中共享数据
        let userDefaults = UserDefaults(suiteName: groupIdentifier)
        
        // 获取当前的deleteArray
        var archiveArray = userDefaults?.array(forKey: "archiveArray") as? [String] ?? []
        
        // 如果照片标识符不在数组中，则添加
        if !archiveArray.contains(photoIdentifier) {
            archiveArray.append(photoIdentifier)
            userDefaults?.set(archiveArray, forKey: "archiveArray")
            userDefaults?.synchronize()
        }
        
        // 同时记录到操作过的照片列表中
        recordOperatedPhoto(photoIdentifier)
    }
    
    
    func getArchivedPhotos() -> [String] {
        let userDefaults = UserDefaults(suiteName: groupIdentifier)
        return userDefaults?.array(forKey: "archiveArray") as? [String] ?? []
    }
    
    
    private func extractLocalIdentifierFromFileName(_ fileName: String) -> String? {
        // 文件名格式: photo_6218C9ED_52CD_432D_A2E7_1C0D15C2EF0A_L0_001_jpg.jpg
        // 移除 "photo_" 前缀和 ".jpg" 后缀
        guard fileName.hasPrefix("photo_") && fileName.hasSuffix(".jpg") else {
            return nil
        }
        
        let withoutPrefix = String(fileName.dropFirst(6)) // 移除 "photo_"
        let withoutSuffix = String(withoutPrefix.dropLast(4)) // 移除 ".jpg"
        
        // 将下划线替换回原始字符，重构UUID格式
        // 原始格式: 6218C9ED-52CD-432D-A2E7-1C0D15C2EF0A/L0/001
        let components = withoutSuffix.components(separatedBy: "_")
        
        // UUID部分应该是前5个组件 (8-4-4-4-12的格式被分割成8个部分)
        if components.count >= 5 {
            let uuidPart = "\(components[0])-\(components[1])-\(components[2])-\(components[3])-\(components[4])"
            return uuidPart
        }
        
        return nil
    }
    
    private func loadPhotoMetadata(for photoFileName: String) -> PhotoMetadata? {
        guard let photosDir = photosDirectoryURL else { return nil }
        
        // 构建元数据文件名
        let metadataFileName = photoFileName.replacingOccurrences(of: ".jpg", with: "_metadata.json")
        let metadataURL = photosDir.appendingPathComponent(metadataFileName)
        
        do {
            let jsonData = try Data(contentsOf: metadataURL)
            let jsonObject = try JSONSerialization.jsonObject(with: jsonData, options: [])
            
            guard let metadata = jsonObject as? [String: Any],
                  let localIdentifier = metadata["localIdentifier"] as? String else {
                return nil
            }
            
            var creationDate: Date?
            var modificationDate: Date?
            
            if let creationTimestamp = metadata["creationDate"] as? TimeInterval {
                creationDate = Date(timeIntervalSince1970: creationTimestamp)
            }
            
            if let modificationTimestamp = metadata["modificationDate"] as? TimeInterval {
                modificationDate = Date(timeIntervalSince1970: modificationTimestamp)
            }
            
            return PhotoMetadata(
                localIdentifier: localIdentifier,
                creationDate: creationDate,
                modificationDate: modificationDate
            )
        } catch {
            print("加载照片元数据失败: \(error)")
            return nil
        }
    }
}
