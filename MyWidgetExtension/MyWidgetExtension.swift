//
//  MyWidgetExtension.swift
//  MyWidgetExtension
//
//  Created by lifubing on 2025/9/18.
//

import WidgetKit
import SwiftUI
import AppIntents

struct Provider: TimelineProvider {
    typealias Entry = PhotoEntry
    
    public func placeholder(in context: Context) -> PhotoEntry {
        PhotoEntry(date: Date(), photo: nil, photosCount: 0, photoIdentifier: nil, photoMetadata: nil, isResized: false)
    }

    func getSnapshot(in context: Context, completion: @escaping (PhotoEntry) -> ()) {
        let showPhotoModel = SharedPhotoManager.shared.getRandomPhotoModel()
        let count = SharedPhotoManager.shared.getCachedPhotosCount()
        let entry = PhotoEntry(date: Date(), photo: showPhotoModel?.photo, photosCount: count, photoIdentifier: showPhotoModel?.photoIdentifier, photoMetadata: showPhotoModel?.photoMetadata, isResized: showPhotoModel?.isResized ?? false)
        completion(entry)
    }

    func getTimeline(in context: Context, completion: @escaping (Timeline<PhotoEntry>) -> ()) {
        var entries: [PhotoEntry] = []

        // 生成接下来2小时的时间线，每5分钟更新一次照片
        let currentDate = Date()

        let updateInterval: TimeInterval = 60 * 5 // 多少秒刷新一次
        let totalDuration: TimeInterval = 60 * 60 * 1 // 1小时
        let numberOfEntries = Int(totalDuration / updateInterval)
        
        let count = SharedPhotoManager.shared.getCachedPhotosCount()
        
        for i in 0 ..< numberOfEntries {
            
            let entryDate = currentDate.addingTimeInterval(TimeInterval(i) * updateInterval)
            if i == 0 {
                if let resizePhotoTime = SharedPhotoManager.shared.getResizePhotoTime() {
                    let subTime = Date().timeIntervalSince(resizePhotoTime)
                    if subTime < 10 {
                        NSLog("subTime: \(subTime)")
                        let currentActionPhoto = SharedPhotoManager.shared.getCurrentActionPhoto()
                        if !currentActionPhoto.isEmpty  {
                            let showPhotoModel = SharedPhotoManager.shared.getPhotoModel(currentActionPhoto)
                            if showPhotoModel != nil {
                                let entry = PhotoEntry(date: entryDate, photo: showPhotoModel?.photo, photosCount: count, photoIdentifier: showPhotoModel?.photoIdentifier, photoMetadata: showPhotoModel?.photoMetadata, isResized: showPhotoModel?.isResized ?? false)
                                entries.append(entry)
                                continue
                            } else {
                                
                            }
                        }
                    }
                }
            }
            
            
            
            let showPhotoModel = SharedPhotoManager.shared.getRandomPhotoModel()
            
            NSLog("photoIdentifier = \(String(describing: showPhotoModel?.photoIdentifier))")
            
            let entry = PhotoEntry(date: entryDate, photo: showPhotoModel?.photo, photosCount: count, photoIdentifier: showPhotoModel?.photoIdentifier, photoMetadata: showPhotoModel?.photoMetadata, isResized: showPhotoModel?.isResized ?? false)
            
            entries.append(entry)
        }

//         设置更新策略：在最后一个条目后重新加载
        let timeline = Timeline(entries: entries, policy: .atEnd)
        
        // 确保 Widget 在某个固定的时间后刷新
//        let nextUpdateDate = currentDate.addingTimeInterval(totalDuration)
//        let timeline = Timeline(entries: entries, policy: .after(nextUpdateDate))
        
        completion(timeline)
    }
}

struct PhotoEntry: TimelineEntry {
    let date: Date
    let photo: UIImage?
    let photosCount: Int
    let photoIdentifier: String?
    let photoMetadata: PhotoMetadata?
    let isResized: Bool // 新增：标记照片是否处于缩放状态
}

struct MyWidgetExtensionEntryView: View {
    var entry: PhotoEntry
    @Environment(\.widgetFamily) var family

    var body: some View {
        switch family {
        case .systemSmall:
            MyWidgetSmallView(entry: entry)
        case .systemMedium:
            MyWidgetMediumView(entry: entry)
        case .systemLarge, .systemExtraLarge:
            MyWidgetLargeView(entry: entry)
        default:
            MyWidgetSmallView(entry: entry)
        }
    }
}


struct MyWidgetExtension: Widget {
    let kind: String = "MyWidgetExtension"

    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: Provider()) { entry in
            MyWidgetExtensionEntryView(entry: entry)
                .containerBackground(.fill, for: .widget)
        }
        .configurationDisplayName("相册照片")
        .description("随机展示系统相册中的照片")
        .supportedFamilies([.systemSmall, .systemMedium, .systemLarge, .systemExtraLarge])
    }
}

// MARK: - App Intent for marking photos for deletion
struct MarkForDeletionIntent: AppIntent {
    static var title: LocalizedStringResource = "标记删除照片"
    static var description = IntentDescription("将照片标记为待删除")
    
    @Parameter(title: "照片标识符")
    var photoIdentifier: String
    
    init() {}
    
    init(photoIdentifier: String) {
        self.photoIdentifier = photoIdentifier
    }
    
    func perform() async throws -> some IntentResult {
        // 标记照片为待删除
        SharedPhotoManager.shared.markPhotoForDeletion(photoIdentifier)
        // 刷新widget
//        WidgetCenter.shared.reloadAllTimelines()
        
        return .result()
    }
}

// MARK: - App Intent for switching to next photo
struct NextPhotoIntent: AppIntent {
    static var title: LocalizedStringResource = "切换到下一张照片"
    static var description = IntentDescription("切换到下一张照片")
    
    @Parameter(title: "照片标识符")
    var photoIdentifier: String
    
    init() {}
    
    init(photoIdentifier: String) {
        self.photoIdentifier = photoIdentifier
    }
    
    
    func perform() async throws -> some IntentResult {
        // 切换到下一张照片
        SharedPhotoManager.shared.recordOperatedPhoto(photoIdentifier)
        SharedPhotoManager.shared.clearResizePhotoTime()
        
        return .result()
    }
}

struct ArchivePhotoIntent: AppIntent {
    static var title: LocalizedStringResource = "整理该照片"
    static var description = IntentDescription("整理该照片")
    
    @Parameter(title: "照片标识符")
    var photoIdentifier: String
    
    init() {}
    
    init(photoIdentifier: String) {
        self.photoIdentifier = photoIdentifier
    }
    
    
    func perform() async throws -> some IntentResult {
        SharedPhotoManager.shared.markPhotoForArchive(photoIdentifier)
        SharedPhotoManager.shared.clearResizePhotoTime()
        return .result()
    }
}


struct ResizePhotoIntent: AppIntent {
    static var title: LocalizedStringResource = "缩放该照片"
    static var description = IntentDescription("缩放该照片")
    
    @Parameter(title: "照片标识符")
    var photoIdentifier: String
    
    init() {}
    
    init(photoIdentifier: String) {
        self.photoIdentifier = photoIdentifier
    }
    
    
    func perform() async throws -> some IntentResult {
        // 缩放照片
        SharedPhotoManager.shared.setCurrentActionPhoto(photoIdentifier)
        SharedPhotoManager.shared.updateResizePhotoTime()
        SharedPhotoManager.shared.resizePhotoEnable(true)
        return .result()
    }
}

struct UnResizePhotoIntent: AppIntent {
    static var title: LocalizedStringResource = "缩放该照片"
    static var description = IntentDescription("缩放该照片")
    
    @Parameter(title: "照片标识符")
    var photoIdentifier: String
    
    init() {}
    
    init(photoIdentifier: String) {
        self.photoIdentifier = photoIdentifier
    }
    
    
    func perform() async throws -> some IntentResult {
        // 缩放照片
        SharedPhotoManager.shared.setCurrentActionPhoto(photoIdentifier)
        SharedPhotoManager.shared.updateResizePhotoTime()
        SharedPhotoManager.shared.resizePhotoEnable(false)
        return .result()
    }
}

// MARK: - Helper Functions
func formatPhotoDate(_ date: Date) -> String {
    let formatter = DateFormatter()
    let calendar = Calendar.current
    
    if calendar.isDateInToday(date) {
        formatter.dateFormat = "HH:mm"
        return "今天 \(formatter.string(from: date))"
    } else if calendar.isDateInYesterday(date) {
        formatter.dateFormat = "HH:mm"
        return "昨天 \(formatter.string(from: date))"
    } else {
        formatter.dateFormat = "yyyy年M月d日"
        return formatter.string(from: date)
    }
}


