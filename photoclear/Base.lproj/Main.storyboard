<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="24127" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES" initialViewController="Atc-YT-OV7">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="24063"/>
        <capability name="Image references" minToolsVersion="12.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="collection view cell content view" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--Scroll View Controller-->
        <scene sceneID="tne-QT-ifu">
            <objects>
                <viewController storyboardIdentifier="ScrollViewController" id="BYZ-38-t0r" customClass="ScrollViewController" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="jbw-W5-Rb7">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <collectionView hidden="YES" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" dataMode="prototypes" translatesAutoresizingMaskIntoConstraints="NO" id="Ihe-hh-fGF">
                                <rect key="frame" x="-10" y="0.0" width="444" height="896"/>
                                <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <collectionViewFlowLayout key="collectionViewLayout" automaticEstimatedItemSize="YES" minimumLineSpacing="10" minimumInteritemSpacing="10" id="vzN-Tu-Y0A">
                                    <size key="itemSize" width="128" height="128"/>
                                    <size key="headerReferenceSize" width="0.0" height="0.0"/>
                                    <size key="footerReferenceSize" width="0.0" height="0.0"/>
                                    <inset key="sectionInset" minX="0.0" minY="0.0" maxX="0.0" maxY="0.0"/>
                                </collectionViewFlowLayout>
                                <cells/>
                            </collectionView>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="oAa-3n-hh4" customClass="BaseRadiusButton">
                                <rect key="frame" x="326" y="109.5" width="66" height="27"/>
                                <color key="backgroundColor" systemColor="systemRedColor"/>
                                <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                <color key="tintColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                <connections>
                                    <action selector="recycleBtnClicked:" destination="BYZ-38-t0r" eventType="touchUpInside" id="AFZ-iD-rS8"/>
                                </connections>
                            </button>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="a03-dB-Wiw" customClass="BaseCycleLabel">
                                <rect key="frame" x="207" y="828" width="0.0" height="0.0"/>
                                <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                <color key="textColor" red="0.92846510447613395" green="0.93765782828282829" blue="0.93765782828282829" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                <nil key="highlightedColor"/>
                                <size key="shadowOffset" width="0.0" height="0.0"/>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadii">
                                        <real key="value" value="0.0"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="删除(0)" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="5ph-TE-nFF">
                                <rect key="frame" x="338" y="115.5" width="42" height="15"/>
                                <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="3K1-Cy-pTC">
                                <rect key="frame" x="20" y="101" width="44" height="44"/>
                                <constraints>
                                    <constraint firstAttribute="width" secondItem="3K1-Cy-pTC" secondAttribute="height" multiplier="1:1" id="8Ar-fy-Ws2"/>
                                    <constraint firstAttribute="height" constant="44" id="dNC-ZE-1xk"/>
                                </constraints>
                                <color key="tintColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <state key="normal" image="icon_back"/>
                                <connections>
                                    <action selector="closeBtnClicked:" destination="BYZ-38-t0r" eventType="touchUpInside" id="VEj-8T-7bk"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="1fd-nQ-cnc">
                                <rect key="frame" x="72" y="101" width="44" height="44"/>
                                <constraints>
                                    <constraint firstAttribute="width" secondItem="1fd-nQ-cnc" secondAttribute="height" multiplier="1:1" id="19o-ur-71f"/>
                                    <constraint firstAttribute="width" secondItem="1fd-nQ-cnc" secondAttribute="height" multiplier="1:1" id="WH3-Gz-x36"/>
                                    <constraint firstAttribute="height" constant="44" id="YON-ll-4Cl"/>
                                </constraints>
                                <color key="tintColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <state key="normal" image="setting"/>
                                <connections>
                                    <action selector="setBtnClicked:" destination="BYZ-38-t0r" eventType="touchUpInside" id="XHg-1k-m6p"/>
                                </connections>
                            </button>
                            <visualEffectView opaque="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="toolbar-blur-view">
                                <rect key="frame" x="37" y="744" width="340" height="64"/>
                                <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" insetsLayoutMarginsFromSafeArea="NO" id="toolbar-content-view">
                                    <rect key="frame" x="0.0" y="0.0" width="340" height="64"/>
                                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                    <subviews>
                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="toolbar-view">
                                            <rect key="frame" x="20" y="0.0" width="300" height="64"/>
                                            <subviews>
                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="archive-btn">
                                                    <rect key="frame" x="81" y="8" width="65" height="48"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                                    <color key="tintColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    <state key="normal" title="归档">
                                                        <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    </state>
                                                    <connections>
                                                        <action selector="archiveBtnClicked:" destination="BYZ-38-t0r" eventType="touchUpInside" id="archive-btn-action"/>
                                                    </connections>
                                                </button>
                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="qXp-2T-vay">
                                                    <rect key="frame" x="154" y="8" width="65" height="48"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                                    <color key="tintColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    <state key="normal" title="删除">
                                                        <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    </state>
                                                    <connections>
                                                        <action selector="deleteBtnClicked:" destination="BYZ-38-t0r" eventType="touchUpInside" id="JtP-bF-tS9"/>
                                                    </connections>
                                                </button>
                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="next-btn">
                                                    <rect key="frame" x="227" y="8" width="65" height="48"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                                    <color key="tintColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    <state key="normal" title="下一个">
                                                        <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    </state>
                                                    <connections>
                                                        <action selector="nextBtnClicked:" destination="BYZ-38-t0r" eventType="touchUpInside" id="next-btn-action"/>
                                                    </connections>
                                                </button>
                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="lXX-Jg-hp5">
                                                    <rect key="frame" x="8" y="8" width="65" height="48"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                    <state key="normal" title="上一个"/>
                                                    <connections>
                                                        <action selector="preBtnClicked:" destination="BYZ-38-t0r" eventType="touchUpInside" id="0Fj-fb-7Gq"/>
                                                    </connections>
                                                </button>
                                            </subviews>
                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <constraints>
                                                <constraint firstItem="archive-btn" firstAttribute="height" secondItem="lXX-Jg-hp5" secondAttribute="height" id="0k6-yr-oLs"/>
                                                <constraint firstItem="next-btn" firstAttribute="leading" secondItem="qXp-2T-vay" secondAttribute="trailing" constant="8" id="6mp-9P-jc8"/>
                                                <constraint firstItem="qXp-2T-vay" firstAttribute="width" secondItem="lXX-Jg-hp5" secondAttribute="width" id="6tV-p3-OiI"/>
                                                <constraint firstItem="next-btn" firstAttribute="top" secondItem="toolbar-view" secondAttribute="top" constant="8" id="8am-Sr-oJr"/>
                                                <constraint firstItem="lXX-Jg-hp5" firstAttribute="leading" secondItem="toolbar-view" secondAttribute="leading" constant="8" id="GEL-Ai-eh7"/>
                                                <constraint firstAttribute="trailing" secondItem="next-btn" secondAttribute="trailing" constant="8" id="HPE-Xq-vPq"/>
                                                <constraint firstItem="next-btn" firstAttribute="centerY" secondItem="lXX-Jg-hp5" secondAttribute="centerY" id="K6L-Jr-40u"/>
                                                <constraint firstAttribute="bottom" secondItem="lXX-Jg-hp5" secondAttribute="bottom" constant="8" id="L8b-A1-qKZ"/>
                                                <constraint firstItem="lXX-Jg-hp5" firstAttribute="top" secondItem="toolbar-view" secondAttribute="top" constant="8" id="Lvg-Tn-fpn"/>
                                                <constraint firstItem="qXp-2T-vay" firstAttribute="centerY" secondItem="lXX-Jg-hp5" secondAttribute="centerY" id="WKj-o8-TZT"/>
                                                <constraint firstItem="archive-btn" firstAttribute="leading" secondItem="lXX-Jg-hp5" secondAttribute="trailing" constant="8" id="YRo-9m-H75"/>
                                                <constraint firstItem="next-btn" firstAttribute="width" secondItem="lXX-Jg-hp5" secondAttribute="width" id="bKL-DL-XVS"/>
                                                <constraint firstItem="qXp-2T-vay" firstAttribute="height" secondItem="lXX-Jg-hp5" secondAttribute="height" id="cLm-h1-i9O"/>
                                                <constraint firstItem="archive-btn" firstAttribute="width" secondItem="lXX-Jg-hp5" secondAttribute="width" id="h0S-wu-s4d"/>
                                                <constraint firstItem="archive-btn" firstAttribute="centerY" secondItem="lXX-Jg-hp5" secondAttribute="centerY" id="mdd-ye-VKe"/>
                                                <constraint firstAttribute="bottom" secondItem="next-btn" secondAttribute="bottom" constant="8" id="nz5-oL-ch5"/>
                                                <constraint firstAttribute="bottom" secondItem="archive-btn" secondAttribute="bottom" constant="8" id="q1C-Td-kKB"/>
                                                <constraint firstItem="qXp-2T-vay" firstAttribute="leading" secondItem="archive-btn" secondAttribute="trailing" constant="8" id="tVz-go-Yn1"/>
                                                <constraint firstItem="next-btn" firstAttribute="height" secondItem="lXX-Jg-hp5" secondAttribute="height" id="tkC-Lu-ciq"/>
                                                <constraint firstAttribute="height" constant="64" id="toolbar-height"/>
                                                <constraint firstAttribute="width" constant="300" id="toolbar-width"/>
                                                <constraint firstItem="archive-btn" firstAttribute="top" secondItem="toolbar-view" secondAttribute="top" constant="8" id="vIZ-aU-iiy"/>
                                            </constraints>
                                        </view>
                                    </subviews>
                                    <constraints>
                                        <constraint firstItem="toolbar-view" firstAttribute="centerX" secondItem="toolbar-content-view" secondAttribute="centerX" id="toolbar-centerX"/>
                                        <constraint firstItem="toolbar-view" firstAttribute="centerY" secondItem="toolbar-content-view" secondAttribute="centerY" id="toolbar-centerY"/>
                                    </constraints>
                                </view>
                                <constraints>
                                    <constraint firstAttribute="height" constant="64" id="blur-view-height"/>
                                    <constraint firstAttribute="width" constant="340" id="blur-view-width"/>
                                </constraints>
                                <blurEffect style="regular"/>
                            </visualEffectView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="M8y-UP-WhB"/>
                        <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="M8y-UP-WhB" firstAttribute="bottom" secondItem="a03-dB-Wiw" secondAttribute="bottom" id="6cD-Xx-ICP"/>
                            <constraint firstItem="3K1-Cy-pTC" firstAttribute="centerY" secondItem="5ph-TE-nFF" secondAttribute="centerY" id="7wB-dY-iqf"/>
                            <constraint firstAttribute="trailing" secondItem="Ihe-hh-fGF" secondAttribute="trailing" constant="-20" id="8v6-T2-cLE"/>
                            <constraint firstAttribute="bottom" secondItem="Ihe-hh-fGF" secondAttribute="bottom" id="9j3-pS-ape"/>
                            <constraint firstItem="1fd-nQ-cnc" firstAttribute="centerY" secondItem="3K1-Cy-pTC" secondAttribute="centerY" id="Mr4-PE-2DQ"/>
                            <constraint firstItem="Ihe-hh-fGF" firstAttribute="leading" secondItem="jbw-W5-Rb7" secondAttribute="leading" constant="-10" id="O8b-kY-F32"/>
                            <constraint firstItem="3K1-Cy-pTC" firstAttribute="leading" secondItem="M8y-UP-WhB" secondAttribute="leading" constant="20" id="WJg-Pw-jBU"/>
                            <constraint firstItem="5ph-TE-nFF" firstAttribute="centerX" secondItem="oAa-3n-hh4" secondAttribute="centerX" id="bH4-0U-h3L"/>
                            <constraint firstItem="5ph-TE-nFF" firstAttribute="centerY" secondItem="oAa-3n-hh4" secondAttribute="centerY" id="dSU-mN-O3q"/>
                            <constraint firstItem="1fd-nQ-cnc" firstAttribute="leading" secondItem="3K1-Cy-pTC" secondAttribute="trailing" constant="8" id="gCZ-pR-nbU"/>
                            <constraint firstItem="Ihe-hh-fGF" firstAttribute="top" secondItem="jbw-W5-Rb7" secondAttribute="top" id="izI-tX-ehD"/>
                            <constraint firstItem="M8y-UP-WhB" firstAttribute="trailing" secondItem="oAa-3n-hh4" secondAttribute="trailing" constant="22" id="kUR-PL-LOs"/>
                            <constraint firstItem="3K1-Cy-pTC" firstAttribute="top" secondItem="M8y-UP-WhB" secondAttribute="top" constant="5" id="lb7-pW-Kjh"/>
                            <constraint firstItem="toolbar-blur-view" firstAttribute="centerX" secondItem="jbw-W5-Rb7" secondAttribute="centerX" id="r4T-5g-cdj"/>
                            <constraint firstItem="M8y-UP-WhB" firstAttribute="bottom" secondItem="toolbar-blur-view" secondAttribute="bottom" constant="20" id="toolbar-bottom-constraint"/>
                            <constraint firstItem="5ph-TE-nFF" firstAttribute="trailing" secondItem="oAa-3n-hh4" secondAttribute="trailing" constant="-12" id="vGv-6c-Z41"/>
                            <constraint firstItem="a03-dB-Wiw" firstAttribute="centerX" secondItem="M8y-UP-WhB" secondAttribute="centerX" id="zWZ-ex-9eA"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="archiveBtn" destination="archive-btn" id="archive-btn-outlet"/>
                        <outlet property="backBtn" destination="3K1-Cy-pTC" id="GCY-6G-cvz"/>
                        <outlet property="deleteBtn" destination="qXp-2T-vay" id="vHT-70-EVm"/>
                        <outlet property="infoLabel" destination="a03-dB-Wiw" id="to7-Cf-DNz"/>
                        <outlet property="nextBtn" destination="next-btn" id="next-btn-outlet"/>
                        <outlet property="preBtn" destination="lXX-Jg-hp5" id="qGU-6o-Mqa"/>
                        <outlet property="recycleBtn" destination="oAa-3n-hh4" id="MbC-y5-9dt"/>
                        <outlet property="recycleInfoLabel" destination="5ph-TE-nFF" id="PWX-jf-MrO"/>
                        <outlet property="settingBtn" destination="1fd-nQ-cnc" id="yw0-p5-Rmp"/>
                        <outlet property="toolbarBlurView" destination="toolbar-blur-view" id="toolbar-blur-view-outlet"/>
                        <outlet property="toolbarView" destination="toolbar-view" id="toolbar-view-outlet"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="dkx-z0-nzr" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="285.50724637681162" y="620.75892857142856"/>
        </scene>
        <!--Navigation Controller-->
        <scene sceneID="zzF-I8-hcx">
            <objects>
                <navigationController storyboardIdentifier="albumselectnavigation" navigationBarHidden="YES" id="Atc-YT-OV7" sceneMemberID="viewController">
                    <navigationBar key="navigationBar" contentMode="scaleToFill" id="LJ4-48-oGE">
                        <autoresizingMask key="autoresizingMask"/>
                    </navigationBar>
                    <connections>
                        <segue destination="tP0-ee-Kxf" kind="relationship" relationship="rootViewController" id="28C-Rc-oug"/>
                    </connections>
                </navigationController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="YWq-2W-Rel" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1410" y="-575"/>
        </scene>
        <!--相簿-->
        <scene sceneID="Ibf-Z9-iyI">
            <objects>
                <viewController storyboardIdentifier="AlbumSelectViewController" title="相簿" id="ktx-je-a7A" customClass="AlbumSelectViewController" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="2nq-fc-8e2">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="default" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="-1" estimatedSectionHeaderHeight="-1" sectionFooterHeight="-1" estimatedSectionFooterHeight="-1" translatesAutoresizingMaskIntoConstraints="NO" id="dKg-aE-Bru">
                                <rect key="frame" x="0.0" y="138" width="414" height="758"/>
                                <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </tableView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="选择相簿" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="wmw-fI-i2m">
                                <rect key="frame" x="177" y="104" width="60" height="24"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="24" id="yPC-MH-zi0"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                <color key="textColor" red="1" green="1" blue="1" alpha="0.90323882450000004" colorSpace="custom" customColorSpace="sRGB"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="6gJ-Yj-aef"/>
                        <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="wmw-fI-i2m" firstAttribute="centerX" secondItem="2nq-fc-8e2" secondAttribute="centerX" id="IPu-Hg-Qv2"/>
                            <constraint firstItem="wmw-fI-i2m" firstAttribute="top" secondItem="6gJ-Yj-aef" secondAttribute="top" constant="8" id="MxJ-7d-Ti5"/>
                            <constraint firstItem="6gJ-Yj-aef" firstAttribute="trailing" secondItem="dKg-aE-Bru" secondAttribute="trailing" id="Pde-Ou-MWZ"/>
                            <constraint firstItem="dKg-aE-Bru" firstAttribute="leading" secondItem="6gJ-Yj-aef" secondAttribute="leading" id="cf7-rM-7TT"/>
                            <constraint firstItem="dKg-aE-Bru" firstAttribute="top" secondItem="wmw-fI-i2m" secondAttribute="bottom" constant="10" id="dLz-pb-KuS"/>
                            <constraint firstAttribute="bottom" secondItem="dKg-aE-Bru" secondAttribute="bottom" id="yrb-o4-PPF"/>
                        </constraints>
                    </view>
                    <tabBarItem key="tabBarItem" title="相簿" image="icon_album" selectedImage="icon_album" id="ith-3N-hlS">
                        <color key="badgeColor" red="0.1215686277" green="0.1294117719" blue="0.1411764771" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </tabBarItem>
                    <navigationItem key="navigationItem" title="选择相簿" id="6lU-hR-Cgi"/>
                    <connections>
                        <outlet property="tableView" destination="dKg-aE-Bru" id="Prm-5g-Tqp"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="XVe-7H-G5c" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="2711.594202898551" y="762.72321428571422"/>
        </scene>
        <!--设置-->
        <scene sceneID="vgv-K7-giK">
            <objects>
                <viewController id="fnM-cw-Sad" customClass="SettingViewController" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="4nd-bM-xu1">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <viewLayoutGuide key="safeArea" id="Rco-P3-ECh"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                    </view>
                    <tabBarItem key="tabBarItem" title="设置" image="icon_config" selectedImage="icon_config" id="e8G-gf-tXM"/>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Aij-a3-NpJ" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="4280" y="-346"/>
        </scene>
        <!--Custom Tab Bar View Controller-->
        <scene sceneID="cOk-Xl-MTP">
            <objects>
                <tabBarController id="tP0-ee-Kxf" customClass="CustomTabBarViewController" sceneMemberID="viewController">
                    <navigationItem key="navigationItem" id="m8n-6C-cPt"/>
                    <tabBar key="tabBar" contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" barStyle="black" id="8b0-Cq-kVt">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="49"/>
                        <autoresizingMask key="autoresizingMask"/>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <color key="barTintColor" red="0.1215686277" green="0.1294117719" blue="0.1411764771" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <tabBarAppearance key="standardAppearance" stackedItemPositioning="automatic"/>
                        <tabBarAppearance key="scrollEdgeAppearance" stackedItemPositioning="automatic"/>
                    </tabBar>
                    <connections>
                        <outlet property="tabBar" destination="8b0-Cq-kVt" id="rGA-dP-pAc"/>
                        <segue destination="tbq-vw-VQS" kind="relationship" relationship="viewControllers" id="Shd-lV-RmG"/>
                        <segue destination="ktx-je-a7A" kind="relationship" relationship="viewControllers" id="g1W-v1-t68"/>
                        <segue destination="K0b-n7-ReN" kind="relationship" relationship="viewControllers" id="nLz-7a-QxM"/>
                        <segue destination="mZT-9F-css" kind="relationship" relationship="viewControllers" id="JrD-Q8-JQz"/>
                        <segue destination="fnM-cw-Sad" kind="relationship" relationship="viewControllers" id="A9q-cE-ZCG"/>
                    </connections>
                </tabBarController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Cp0-0e-d6v" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="2658" y="-345"/>
        </scene>
        <!--照片-->
        <scene sceneID="3El-V4-mPh">
            <objects>
                <viewController storyboardIdentifier="PhotoSelectViewController" title="照片" id="tbq-vw-VQS" customClass="PhotoSelectViewController" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="EGr-EQ-PIa">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <collectionView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" dataMode="prototypes" translatesAutoresizingMaskIntoConstraints="NO" id="NYS-cR-FHT">
                                <rect key="frame" x="4" y="138" width="406" height="758"/>
                                <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <collectionViewFlowLayout key="collectionViewLayout" automaticEstimatedItemSize="YES" minimumLineSpacing="2" minimumInteritemSpacing="2" id="lk0-V5-QKk">
                                    <size key="itemSize" width="128" height="128"/>
                                    <size key="headerReferenceSize" width="0.0" height="0.0"/>
                                    <size key="footerReferenceSize" width="0.0" height="0.0"/>
                                    <inset key="sectionInset" minX="0.0" minY="0.0" maxX="0.0" maxY="0.0"/>
                                </collectionViewFlowLayout>
                                <cells>
                                    <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" id="DH5-WA-qMP">
                                        <rect key="frame" x="0.0" y="0.0" width="128" height="128"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                        <collectionViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" insetsLayoutMarginsFromSafeArea="NO" id="Rtb-g4-WfU">
                                            <rect key="frame" x="0.0" y="0.0" width="128" height="128"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                        </collectionViewCellContentView>
                                    </collectionViewCell>
                                    <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" insetsLayoutMarginsFromSafeArea="NO" id="ia8-ap-dK9">
                                        <rect key="frame" x="139" y="0.0" width="128" height="128"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <collectionViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" insetsLayoutMarginsFromSafeArea="NO" id="Lc8-z4-aNr">
                                            <rect key="frame" x="0.0" y="0.0" width="128" height="128"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                        </collectionViewCellContentView>
                                    </collectionViewCell>
                                    <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" insetsLayoutMarginsFromSafeArea="NO" id="hcZ-aD-p4U">
                                        <rect key="frame" x="278" y="0.0" width="128" height="128"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <collectionViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" insetsLayoutMarginsFromSafeArea="NO" id="usM-Vl-1Sj">
                                            <rect key="frame" x="0.0" y="0.0" width="128" height="128"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                        </collectionViewCellContentView>
                                    </collectionViewCell>
                                    <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" insetsLayoutMarginsFromSafeArea="NO" id="exz-pq-tCM">
                                        <rect key="frame" x="0.0" y="130" width="128" height="128"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <collectionViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" insetsLayoutMarginsFromSafeArea="NO" id="0oo-WG-utF">
                                            <rect key="frame" x="0.0" y="0.0" width="128" height="128"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                        </collectionViewCellContentView>
                                    </collectionViewCell>
                                    <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" insetsLayoutMarginsFromSafeArea="NO" id="Jf2-sN-ANp">
                                        <rect key="frame" x="139" y="130" width="128" height="128"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <collectionViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" insetsLayoutMarginsFromSafeArea="NO" id="j1A-vP-RJL">
                                            <rect key="frame" x="0.0" y="0.0" width="128" height="128"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                        </collectionViewCellContentView>
                                    </collectionViewCell>
                                    <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" insetsLayoutMarginsFromSafeArea="NO" id="uzJ-aw-tCF">
                                        <rect key="frame" x="278" y="130" width="128" height="128"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <collectionViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" insetsLayoutMarginsFromSafeArea="NO" id="KfH-BF-BeZ">
                                            <rect key="frame" x="0.0" y="0.0" width="128" height="128"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                        </collectionViewCellContentView>
                                    </collectionViewCell>
                                </cells>
                            </collectionView>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="pSK-UI-vNT">
                                <rect key="frame" x="169" y="104" width="84" height="24"/>
                                <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                <state key="normal" title="最近项目">
                                    <imageReference key="image" image="change"/>
                                </state>
                                <connections>
                                    <action selector="selectPhotoAlbum:" destination="BYZ-38-t0r" eventType="touchUpInside" id="32B-7E-U95"/>
                                    <action selector="titleBtnClicked:" destination="tbq-vw-VQS" eventType="touchUpInside" id="YeM-le-ZQc"/>
                                </connections>
                            </button>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="sP3-5x-j6A"/>
                        <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="pSK-UI-vNT" firstAttribute="centerX" secondItem="EGr-EQ-PIa" secondAttribute="centerX" constant="4" id="9UL-HU-BxX"/>
                            <constraint firstItem="NYS-cR-FHT" firstAttribute="leading" secondItem="sP3-5x-j6A" secondAttribute="leading" constant="4" id="D0q-zp-50S"/>
                            <constraint firstAttribute="bottom" secondItem="NYS-cR-FHT" secondAttribute="bottom" id="I8O-gl-mlc"/>
                            <constraint firstItem="NYS-cR-FHT" firstAttribute="trailing" secondItem="sP3-5x-j6A" secondAttribute="trailing" constant="-4" id="PWr-MW-Lfc"/>
                            <constraint firstItem="pSK-UI-vNT" firstAttribute="top" secondItem="sP3-5x-j6A" secondAttribute="top" constant="8" id="TFl-UB-OKE"/>
                            <constraint firstItem="NYS-cR-FHT" firstAttribute="top" secondItem="pSK-UI-vNT" secondAttribute="bottom" constant="10" id="Yho-PZ-cIl"/>
                        </constraints>
                    </view>
                    <tabBarItem key="tabBarItem" title="照片" image="icon_list" id="H1E-Dq-Ico"/>
                    <navigationItem key="navigationItem" title="照片" id="Fjz-DR-gNq"/>
                    <connections>
                        <outlet property="collectionView" destination="NYS-cR-FHT" id="8oT-aC-sJS"/>
                        <outlet property="titleBtn" destination="pSK-UI-vNT" id="2lG-mx-owj"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="PYD-NN-zsw" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1779.7101449275365" y="763.39285714285711"/>
        </scene>
        <!--待删除-->
        <scene sceneID="bIb-Ta-EwZ">
            <objects>
                <viewController id="K0b-n7-ReN" customClass="DeleteListViewController" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="laR-uT-bQt">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <viewLayoutGuide key="safeArea" id="POi-23-cJZ"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                    </view>
                    <tabBarItem key="tabBarItem" title="待删除" image="pdelete" selectedImage="pdelete" id="dQC-vY-yU6"/>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="E5l-Xu-ERZ" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="3581" y="763"/>
        </scene>
        <!--Widget-->
        <scene sceneID="Fpf-56-Z4S">
            <objects>
                <viewController id="mZT-9F-css" customClass="WidgetViewController" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="XCx-6K-Iqu">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <viewLayoutGuide key="safeArea" id="5tA-Uw-c0R"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                    </view>
                    <tabBarItem key="tabBarItem" title="Widget" image="icon_widget" id="o2B-a3-DgN"/>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="iLg-av-mV6" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="4355" y="766"/>
        </scene>
    </scenes>
    <resources>
        <image name="change" width="24" height="24"/>
        <image name="icon_album" width="20" height="21"/>
        <image name="icon_back" width="10" height="18"/>
        <image name="icon_config" width="21" height="20"/>
        <image name="icon_list" width="20" height="20"/>
        <image name="icon_widget" width="22" height="22"/>
        <image name="pdelete" width="24" height="24"/>
        <image name="setting" width="24" height="24"/>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
        <systemColor name="systemRedColor">
            <color red="1" green="0.23137254900000001" blue="0.18823529410000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
    </resources>
</document>
