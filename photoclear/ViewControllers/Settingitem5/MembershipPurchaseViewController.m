//
//  MembershipPurchaseViewController.m
//  photoclear
//
//  Created by AI Assistant on 2025-09-28.
//

#import "MembershipPurchaseViewController.h"
#import "photoclear-Swift.h"

@implementation MembershipPurchaseViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = [UIColor whiteColor];
    self.title = @"升级会员";

    // 设置导航栏
    self.navigationItem.leftBarButtonItem = [[UIBarButtonItem alloc] initWithTitle:@"取消"
                                                                             style:UIBarButtonItemStylePlain
                                                                            target:self
                                                                            action:@selector(dismissViewController)];

    [self setupUI];
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(purchaseResult) name:@"VipChangeNotification" object:nil];
}

- (void)dismissViewController {
    [self dismissViewControllerAnimated:YES completion:nil];
}

- (void)setupUI {
    [self setupScrollView];
    [self setupHeaderSection];
    [self setupPrivilegesSection];
    [self setupMembershipOptions];
    [self setupPurchaseSection];
}

- (void)setupScrollView {
    self.scrollView = [[UIScrollView alloc] init];
    self.scrollView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:self.scrollView];

    self.contentView = [[UIView alloc] init];
    self.contentView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.scrollView addSubview:self.contentView];

    [NSLayoutConstraint activateConstraints:@[
        [self.scrollView.topAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.topAnchor],
        [self.scrollView.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor],
        [self.scrollView.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor],
        [self.scrollView.bottomAnchor constraintEqualToAnchor:self.view.bottomAnchor],

        [self.contentView.topAnchor constraintEqualToAnchor:self.scrollView.topAnchor],
        [self.contentView.leadingAnchor constraintEqualToAnchor:self.scrollView.leadingAnchor],
        [self.contentView.trailingAnchor constraintEqualToAnchor:self.scrollView.trailingAnchor],
        [self.contentView.bottomAnchor constraintEqualToAnchor:self.scrollView.bottomAnchor],
        [self.contentView.widthAnchor constraintEqualToAnchor:self.scrollView.widthAnchor]
    ]];
}

- (void)setupHeaderSection {
    // Header container
    UIView *headerContainer = [[UIView alloc] init];
    headerContainer.translatesAutoresizingMaskIntoConstraints = NO;
    headerContainer.backgroundColor = [UIColor colorWithRed:0.95 green:0.95 blue:0.95 alpha:1.0];
    headerContainer.layer.cornerRadius = 12;
    [self.contentView addSubview:headerContainer];

    // Header image
    self.headerImageView = [[UIImageView alloc] init];
    self.headerImageView.translatesAutoresizingMaskIntoConstraints = NO;
    self.headerImageView.image = [UIImage systemImageNamed:@"crown.fill"];
    self.headerImageView.tintColor = [UIColor systemOrangeColor];
    self.headerImageView.contentMode = UIViewContentModeScaleAspectFit;
    [headerContainer addSubview:self.headerImageView];

    // Title label
    self.titleLabel = [[UILabel alloc] init];
    self.titleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    self.titleLabel.text = @"升级到VIP会员";
    self.titleLabel.font = [UIFont boldSystemFontOfSize:24];
    self.titleLabel.textColor = [UIColor blackColor];
    [headerContainer addSubview:self.titleLabel];

    // Subtitle label
    self.subtitleLabel = [[UILabel alloc] init];
    self.subtitleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    self.subtitleLabel.text = @"解锁无限删除照片功能";
    self.subtitleLabel.font = [UIFont systemFontOfSize:16];
    self.subtitleLabel.textColor = [UIColor grayColor];
    [headerContainer addSubview:self.subtitleLabel];

    // Set constraints
    [NSLayoutConstraint activateConstraints:@[
        [headerContainer.topAnchor constraintEqualToAnchor:self.contentView.topAnchor constant:20],
        [headerContainer.leadingAnchor constraintEqualToAnchor:self.contentView.leadingAnchor constant:20],
        [headerContainer.trailingAnchor constraintEqualToAnchor:self.contentView.trailingAnchor constant:-20],
        [headerContainer.heightAnchor constraintEqualToConstant:100],

        [self.headerImageView.leadingAnchor constraintEqualToAnchor:headerContainer.leadingAnchor constant:20],
        [self.headerImageView.centerYAnchor constraintEqualToAnchor:headerContainer.centerYAnchor],
        [self.headerImageView.widthAnchor constraintEqualToConstant:60],
        [self.headerImageView.heightAnchor constraintEqualToConstant:60],

        [self.titleLabel.leadingAnchor constraintEqualToAnchor:self.headerImageView.trailingAnchor constant:20],
        [self.titleLabel.trailingAnchor constraintEqualToAnchor:headerContainer.trailingAnchor constant:-20],
        [self.titleLabel.centerYAnchor constraintEqualToAnchor:headerContainer.centerYAnchor constant:-15],

        [self.subtitleLabel.leadingAnchor constraintEqualToAnchor:self.titleLabel.leadingAnchor],
        [self.subtitleLabel.trailingAnchor constraintEqualToAnchor:self.titleLabel.trailingAnchor],
        [self.subtitleLabel.topAnchor constraintEqualToAnchor:self.titleLabel.bottomAnchor constant:8]
    ]];
}

- (void)setupPrivilegesSection {
    self.privilegesContainer = [[UIView alloc] init];
    self.privilegesContainer.translatesAutoresizingMaskIntoConstraints = NO;
    self.privilegesContainer.backgroundColor = [UIColor whiteColor];
    self.privilegesContainer.layer.cornerRadius = 12;
    self.privilegesContainer.layer.borderWidth = 1;
    self.privilegesContainer.layer.borderColor = [UIColor colorWithRed:0.9 green:0.9 blue:0.9 alpha:1.0].CGColor;
    [self.contentView addSubview:self.privilegesContainer];

    // Privileges data
    NSArray *privileges = @[
        @{@"icon": @"infinity", @"title": @"无限删除照片"},
        @{@"icon": @"speedometer", @"title": @"无任何广告"},
        @{@"icon": @"shield.checkered", @"title": @"优先技术支持"},
        @{@"icon": @"star.fill", @"title": @"专属VIP标识"}
    ];

    NSMutableArray *privilegeViews = [NSMutableArray array];
    UIView *previousView = nil;

    for (NSDictionary *privilege in privileges) {
        UIView *privilegeView = [self createPrivilegeViewWithIcon:privilege[@"icon"] title:privilege[@"title"]];
        [self.privilegesContainer addSubview:privilegeView];
        [privilegeViews addObject:privilegeView];

        [NSLayoutConstraint activateConstraints:@[
            [privilegeView.leadingAnchor constraintEqualToAnchor:self.privilegesContainer.leadingAnchor constant:20],
            [privilegeView.trailingAnchor constraintEqualToAnchor:self.privilegesContainer.trailingAnchor constant:-20],
            [privilegeView.heightAnchor constraintEqualToConstant:32]
        ]];

        if (previousView) {
            [privilegeView.topAnchor constraintEqualToAnchor:previousView.bottomAnchor constant:10].active = YES;
        } else {
            [privilegeView.topAnchor constraintEqualToAnchor:self.privilegesContainer.topAnchor constant:20].active = YES;
        }

        previousView = privilegeView;
    }

    self.privilegeViews = [privilegeViews copy];

    // Set privileges container constraints
    UIView *headerContainer = self.contentView.subviews.firstObject;
    [NSLayoutConstraint activateConstraints:@[
        [self.privilegesContainer.topAnchor constraintEqualToAnchor:headerContainer.bottomAnchor constant:20],
        [self.privilegesContainer.leadingAnchor constraintEqualToAnchor:self.contentView.leadingAnchor constant:20],
        [self.privilegesContainer.trailingAnchor constraintEqualToAnchor:self.contentView.trailingAnchor constant:-20],
        [self.privilegesContainer.bottomAnchor constraintEqualToAnchor:previousView.bottomAnchor constant:20]
    ]];
}

- (void)setupMembershipOptions {
    self.membershipOptionsContainer = [[UIView alloc] init];
    self.membershipOptionsContainer.translatesAutoresizingMaskIntoConstraints = NO;
    [self.contentView addSubview:self.membershipOptionsContainer];

    // Create membership options
    self.yearlyOptionView = [self createMembershipOptionWithTitle:@"年度会员"
                                                         subtitle:@"12个月无限使用"
                                                            price:@"¥68"
                                                    originalPrice:@"¥168"
                                                         discount:@"限时优惠"
                                                         isYearly:YES];

    self.lifetimeOptionView = [self createMembershipOptionWithTitle:@"终身会员"
                                                           subtitle:@"一次购买，终身使用"
                                                              price:@"¥198"
                                                      originalPrice:@"¥398"
                                                           discount:@"最受欢迎"
                                                           isYearly:NO];

    [self.membershipOptionsContainer addSubview:self.yearlyOptionView];
    [self.membershipOptionsContainer addSubview:self.lifetimeOptionView];

    // Default selection
    [self selectMembershipOption:self.lifetimeOptionView];

    // Set constraints
    [NSLayoutConstraint activateConstraints:@[
        [self.membershipOptionsContainer.topAnchor constraintEqualToAnchor:self.privilegesContainer.bottomAnchor constant:20],
        [self.membershipOptionsContainer.leadingAnchor constraintEqualToAnchor:self.contentView.leadingAnchor constant:20],
        [self.membershipOptionsContainer.trailingAnchor constraintEqualToAnchor:self.contentView.trailingAnchor constant:-20],

        [self.yearlyOptionView.topAnchor constraintEqualToAnchor:self.membershipOptionsContainer.topAnchor],
        [self.yearlyOptionView.leadingAnchor constraintEqualToAnchor:self.membershipOptionsContainer.leadingAnchor],
        [self.yearlyOptionView.trailingAnchor constraintEqualToAnchor:self.membershipOptionsContainer.trailingAnchor],
        [self.yearlyOptionView.heightAnchor constraintEqualToConstant:80],

        [self.lifetimeOptionView.topAnchor constraintEqualToAnchor:self.yearlyOptionView.bottomAnchor constant:15],
        [self.lifetimeOptionView.leadingAnchor constraintEqualToAnchor:self.membershipOptionsContainer.leadingAnchor],
        [self.lifetimeOptionView.trailingAnchor constraintEqualToAnchor:self.membershipOptionsContainer.trailingAnchor],
        [self.lifetimeOptionView.heightAnchor constraintEqualToConstant:80],
        [self.lifetimeOptionView.bottomAnchor constraintEqualToAnchor:self.membershipOptionsContainer.bottomAnchor]
    ]];
}

- (void)setupPurchaseSection {
    // Purchase button
    self.purchaseButton = [UIButton buttonWithType:UIButtonTypeSystem];
    self.purchaseButton.translatesAutoresizingMaskIntoConstraints = NO;
    [self.purchaseButton setTitle:@"立即购买" forState:UIControlStateNormal];
    [self.purchaseButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    self.purchaseButton.titleLabel.font = [UIFont boldSystemFontOfSize:18];
    self.purchaseButton.backgroundColor = [UIColor systemBlueColor];
    self.purchaseButton.layer.cornerRadius = 25;
    [self.purchaseButton addTarget:self action:@selector(purchaseButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    [self.contentView addSubview:self.purchaseButton];

    // Restore button
    self.restoreButton = [UIButton buttonWithType:UIButtonTypeSystem];
    self.restoreButton.translatesAutoresizingMaskIntoConstraints = NO;
    [self.restoreButton setTitle:@"恢复购买" forState:UIControlStateNormal];
    [self.restoreButton setTitleColor:[UIColor systemBlueColor] forState:UIControlStateNormal];
    self.restoreButton.titleLabel.font = [UIFont systemFontOfSize:16];
    [self.restoreButton addTarget:self action:@selector(restoreButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    [self.contentView addSubview:self.restoreButton];

    // Disclaimer
    self.disclaimerLabel = [[UILabel alloc] init];
    self.disclaimerLabel.translatesAutoresizingMaskIntoConstraints = NO;
    self.disclaimerLabel.text = @"购买即表示您同意我们的服务条款和隐私政策。订阅将自动续费，您可以随时在设置中取消。";
    self.disclaimerLabel.font = [UIFont systemFontOfSize:12];
    self.disclaimerLabel.textColor = [UIColor grayColor];
    self.disclaimerLabel.numberOfLines = 0;
    self.disclaimerLabel.textAlignment = NSTextAlignmentCenter;
    [self.contentView addSubview:self.disclaimerLabel];

    // Set constraints
    [NSLayoutConstraint activateConstraints:@[
        [self.purchaseButton.topAnchor constraintEqualToAnchor:self.membershipOptionsContainer.bottomAnchor constant:30],
        [self.purchaseButton.leadingAnchor constraintEqualToAnchor:self.contentView.leadingAnchor constant:20],
        [self.purchaseButton.trailingAnchor constraintEqualToAnchor:self.contentView.trailingAnchor constant:-20],
        [self.purchaseButton.heightAnchor constraintEqualToConstant:50],

        [self.restoreButton.topAnchor constraintEqualToAnchor:self.purchaseButton.bottomAnchor constant:15],
        [self.restoreButton.centerXAnchor constraintEqualToAnchor:self.contentView.centerXAnchor],
        [self.restoreButton.heightAnchor constraintEqualToConstant:40],

        [self.disclaimerLabel.topAnchor constraintEqualToAnchor:self.restoreButton.bottomAnchor constant:20],
        [self.disclaimerLabel.leadingAnchor constraintEqualToAnchor:self.contentView.leadingAnchor constant:30],
        [self.disclaimerLabel.trailingAnchor constraintEqualToAnchor:self.contentView.trailingAnchor constant:-30],
        [self.disclaimerLabel.bottomAnchor constraintEqualToAnchor:self.contentView.bottomAnchor constant:-30]
    ]];
}

- (UIView *)createPrivilegeViewWithIcon:(NSString *)iconName title:(NSString *)title {
    UIView *containerView = [[UIView alloc] init];
    containerView.translatesAutoresizingMaskIntoConstraints = NO;

    UIImageView *iconView = [[UIImageView alloc] init];
    iconView.translatesAutoresizingMaskIntoConstraints = NO;
    iconView.image = [UIImage systemImageNamed:iconName];
    iconView.tintColor = [UIColor systemGreenColor];
    iconView.contentMode = UIViewContentModeScaleAspectFit;
    [containerView addSubview:iconView];

    UILabel *titleLabel = [[UILabel alloc] init];
    titleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    titleLabel.text = title;
    titleLabel.font = [UIFont systemFontOfSize:16];
    titleLabel.textColor = [UIColor blackColor];
    [containerView addSubview:titleLabel];

    [NSLayoutConstraint activateConstraints:@[
        [iconView.leadingAnchor constraintEqualToAnchor:containerView.leadingAnchor],
        [iconView.centerYAnchor constraintEqualToAnchor:containerView.centerYAnchor],
        [iconView.widthAnchor constraintEqualToConstant:24],
        [iconView.heightAnchor constraintEqualToConstant:24],

        [titleLabel.leadingAnchor constraintEqualToAnchor:iconView.trailingAnchor constant:15],
        [titleLabel.trailingAnchor constraintEqualToAnchor:containerView.trailingAnchor],
        [titleLabel.centerYAnchor constraintEqualToAnchor:containerView.centerYAnchor]
    ]];

    return containerView;
}

- (UIView *)createMembershipOptionWithTitle:(NSString *)title
                                   subtitle:(NSString *)subtitle
                                      price:(NSString *)price
                               originalPrice:(NSString *)originalPrice
                                    discount:(NSString *)discount
                                    isYearly:(BOOL)isYearly {
    UIView *containerView = [[UIView alloc] init];
    containerView.translatesAutoresizingMaskIntoConstraints = NO;
    containerView.backgroundColor = [UIColor whiteColor];
    containerView.layer.cornerRadius = 12;
    containerView.layer.borderWidth = 2;
    containerView.layer.borderColor = [UIColor colorWithRed:0.9 green:0.9 blue:0.9 alpha:1.0].CGColor;

    // Add tap gesture
    UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(membershipOptionTapped:)];
    [containerView addGestureRecognizer:tapGesture];

    // Discount badge (if applicable)
    if (discount) {
        UILabel *discountLabel = [[UILabel alloc] init];
        discountLabel.translatesAutoresizingMaskIntoConstraints = NO;
        discountLabel.text = discount;
        discountLabel.font = [UIFont boldSystemFontOfSize:12];
        discountLabel.textColor = [UIColor whiteColor];
        discountLabel.backgroundColor = [UIColor systemOrangeColor];
        discountLabel.textAlignment = NSTextAlignmentCenter;
        discountLabel.layer.cornerRadius = 8;
        discountLabel.clipsToBounds = YES;
        [containerView addSubview:discountLabel];

        [NSLayoutConstraint activateConstraints:@[
            [discountLabel.topAnchor constraintEqualToAnchor:containerView.topAnchor constant:8],
            [discountLabel.trailingAnchor constraintEqualToAnchor:containerView.trailingAnchor constant:-8],
            [discountLabel.widthAnchor constraintEqualToConstant:80],
            [discountLabel.heightAnchor constraintEqualToConstant:20]
        ]];
    }

    // Title
    UILabel *titleLabel = [[UILabel alloc] init];
    titleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    titleLabel.text = title;
    titleLabel.font = [UIFont boldSystemFontOfSize:18];
    titleLabel.textColor = [UIColor blackColor];
    [containerView addSubview:titleLabel];

    // Subtitle
    UILabel *subtitleLabel = [[UILabel alloc] init];
    subtitleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    subtitleLabel.text = subtitle;
    subtitleLabel.font = [UIFont systemFontOfSize:14];
    subtitleLabel.textColor = [UIColor grayColor];
    [containerView addSubview:subtitleLabel];

    // Price
    UILabel *priceLabel = [[UILabel alloc] init];
    priceLabel.translatesAutoresizingMaskIntoConstraints = NO;
    priceLabel.text = price;
    priceLabel.font = [UIFont boldSystemFontOfSize:20];
    priceLabel.textColor = [UIColor systemBlueColor];
    [containerView addSubview:priceLabel];

    // Original price (if applicable)
//    if (originalPrice) {
        UILabel *originalPriceLabel = [[UILabel alloc] init];
        originalPriceLabel.translatesAutoresizingMaskIntoConstraints = NO;
        originalPriceLabel.text = originalPrice;
        originalPriceLabel.font = [UIFont systemFontOfSize:14];
        originalPriceLabel.textColor = [UIColor grayColor];

        // Add strikethrough
        NSAttributedString *attributedText = [[NSAttributedString alloc] initWithString:originalPrice
                                                                             attributes:@{NSStrikethroughStyleAttributeName: @(NSUnderlineStyleSingle)}];
        originalPriceLabel.attributedText = attributedText;
        [containerView addSubview:originalPriceLabel];

        [NSLayoutConstraint activateConstraints:@[
//            [originalPriceLabel.leadingAnchor constraintEqualToAnchor:priceLabel.trailingAnchor constant:8],
            [originalPriceLabel.trailingAnchor constraintEqualToAnchor:containerView.trailingAnchor constant:-8],
            
            [originalPriceLabel.centerYAnchor constraintEqualToAnchor:priceLabel.centerYAnchor]
        ]];
//    }

    // Set constraints
    [NSLayoutConstraint activateConstraints:@[
        [titleLabel.leadingAnchor constraintEqualToAnchor:containerView.leadingAnchor constant:16],
        [titleLabel.topAnchor constraintEqualToAnchor:containerView.topAnchor constant:16],

        [subtitleLabel.leadingAnchor constraintEqualToAnchor:titleLabel.leadingAnchor],
        [subtitleLabel.topAnchor constraintEqualToAnchor:titleLabel.bottomAnchor constant:4],

        [priceLabel.trailingAnchor constraintEqualToAnchor:originalPriceLabel.leadingAnchor constant:-8],
        [priceLabel.centerYAnchor constraintEqualToAnchor:containerView.centerYAnchor]
    ]];

    return containerView;
}

- (void)membershipOptionTapped:(UITapGestureRecognizer *)gesture {
    [self selectMembershipOption:gesture.view];
}

- (void)selectMembershipOption:(UIView *)optionView {
    // Reset all options
    self.yearlyOptionView.layer.borderColor = [UIColor colorWithRed:0.9 green:0.9 blue:0.9 alpha:1.0].CGColor;
    self.lifetimeOptionView.layer.borderColor = [UIColor colorWithRed:0.9 green:0.9 blue:0.9 alpha:1.0].CGColor;

    // Highlight selected option
    optionView.layer.borderColor = [UIColor systemBlueColor].CGColor;
    self.selectedOptionView = optionView;

    // Update purchase button text
    if (optionView == self.yearlyOptionView) {
        [self.purchaseButton setTitle:@"购买年度会员 - ¥68" forState:UIControlStateNormal];
    } else {
        [self.purchaseButton setTitle:@"购买终身会员 - ¥198" forState:UIControlStateNormal];
    }
}

- (void)purchaseButtonTapped {
    NSLog(@"Purchase button tapped");

    // Show loading state
    [self.purchaseButton setTitle:@"处理中..." forState:UIControlStateNormal];
    self.purchaseButton.enabled = NO;

    // TODO: Integrate with VipGetToolManager
    // [[VipGetToolManager shared] purchaseProduct];
    [[VipGetToolManager shared] purchaseProduct];
}

- (void)purchaseResult {
    if (Preferences.sharedInstance.didVipGet) {
        [self.purchaseButton setTitle:@"立即购买" forState:UIControlStateNormal];
        self.purchaseButton.enabled = YES;

        // Show success message
        UIAlertController *alert = [UIAlertController alertControllerWithTitle:@"购买成功"
                                                                       message:@"恭喜您成为VIP会员！"
                                                                preferredStyle:UIAlertControllerStyleAlert];
        UIAlertAction *okAction = [UIAlertAction actionWithTitle:@"确定"
                                                           style:UIAlertActionStyleDefault
                                                         handler:^(UIAlertAction * _Nonnull action) {
            [self dismissViewController];
        }];
        [alert addAction:okAction];
        [self presentViewController:alert animated:YES completion:nil];
    }
}

- (void)restoreButtonTapped {
    NSLog(@"Restore button tapped");

    // TODO: Integrate with VipGetToolManager
    // [[VipGetToolManager shared] restorePurchases];

    UIAlertController *alert = [UIAlertController alertControllerWithTitle:@"恢复购买"
                                                                   message:@"正在检查您的购买记录..."
                                                            preferredStyle:UIAlertControllerStyleAlert];
    UIAlertAction *okAction = [UIAlertAction actionWithTitle:@"确定"
                                                       style:UIAlertActionStyleDefault
                                                     handler:nil];
    [alert addAction:okAction];
    [self presentViewController:alert animated:YES completion:nil];
}

@end
