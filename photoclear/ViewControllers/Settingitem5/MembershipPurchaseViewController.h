//
//  MembershipPurchaseViewController.h
//  photoclear
//
//  Created by AI Assistant on 2025-09-28.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface MembershipPurchaseViewController : UIViewController

// 主要UI组件
@property (nonatomic, strong) UIScrollView *scrollView;
@property (nonatomic, strong) UIView *contentView;
@property (nonatomic, strong) UIImageView *headerImageView;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *subtitleLabel;

// 特权列表
@property (nonatomic, strong) UIView *privilegesContainer;
@property (nonatomic, strong) NSArray<UIView *> *privilegeViews;

// 会员选项
@property (nonatomic, strong) UIView *membershipOptionsContainer;
@property (nonatomic, strong) UIView *yearlyOptionView;
@property (nonatomic, strong) UIView *lifetimeOptionView;
@property (nonatomic, strong) UIView *selectedOptionView;

// 购买按钮
@property (nonatomic, strong) UIButton *purchaseButton;
@property (nonatomic, strong) UIButton *restoreButton;

// 底部说明
@property (nonatomic, strong) UILabel *disclaimerLabel;

// 方法
- (void)setupUI;
- (void)setupHeaderSection;
- (void)setupPrivilegesSection;
- (void)setupMembershipOptions;
- (void)setupPurchaseSection;
- (UIView *)createPrivilegeViewWithIcon:(NSString *)iconName title:(NSString *)title;
- (UIView *)createMembershipOptionWithTitle:(NSString *)title
                                   subtitle:(NSString *)subtitle
                                      price:(NSString *)price
                               originalPrice:(NSString * _Nullable)originalPrice
                                    discount:(NSString * _Nullable)discount
                                    isYearly:(BOOL)isYearly;
- (void)selectMembershipOption:(UIView *)optionView;
- (void)purchaseButtonTapped;
- (void)restoreButtonTapped;

@end

NS_ASSUME_NONNULL_END
