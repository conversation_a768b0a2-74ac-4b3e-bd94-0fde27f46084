//
//  SettingViewController.h
//  photoclear
//
//  Created by lifubing on 2025/9/23.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface SettingViewController : UIViewController

@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UIView *mainStatsCard;
@property (nonatomic, strong) UIView *statsRowView;
@property (nonatomic, strong) UIView *totalPhotosCard;
@property (nonatomic, strong) UIView *deletedPhotosCard;
@property (nonatomic, strong) UIView *processedPhotosCard;

@property (nonatomic, strong) UILabel *totalPhotosLabel;
@property (nonatomic, strong) UILabel *totalPhotosCountLabel;
@property (nonatomic, strong) UILabel *deletedPhotosLabel;
@property (nonatomic, strong) UILabel *deletedPhotosCountLabel;
@property (nonatomic, strong) UILabel *processedPhotosLabel;
@property (nonatomic, strong) UILabel *processedPhotosCountLabel;

@property (nonatomic, strong) UIView *progressContainer;
@property (nonatomic, strong) UILabel *progressLabel;
@property (nonatomic, strong) UILabel *progressPercentLabel;
@property (nonatomic, strong) UILabel *totalCountLabel;
@property (nonatomic, strong) UIView *progressBarBackground;
@property (nonatomic, strong) UIView *progressBarFill;
@property (nonatomic, strong) NSLayoutConstraint *progressBarWidthConstraint;

// Widget功能卡片
@property (nonatomic, strong) UILabel *widgetSectionTitleLabel;
@property (nonatomic, strong) UIView *widgetFunctionCard;
@property (nonatomic, strong) UILabel *widgetDescriptionLabel;
@property (nonatomic, strong) UIImageView *widgetPreviewImageView;

// 会员提示卡片
@property (nonatomic, strong) UIView *membershipCard;
@property (nonatomic, strong) UIImageView *crownIconView;
@property (nonatomic, strong) UILabel *membershipTitleLabel;
@property (nonatomic, strong) UILabel *membershipDescriptionLabel;
@property (nonatomic, strong) UIButton *purchaseButton;

- (void)setupStatsView;
- (void)setupMembershipCard;
- (void)updateStatsData;
- (UIView *)createStatsCardWithTitle:(NSString *)title count:(NSString *)count;
- (void)purchaseButtonTapped:(UIButton *)sender;

@end

NS_ASSUME_NONNULL_END
