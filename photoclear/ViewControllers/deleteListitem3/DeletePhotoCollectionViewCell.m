//
//  DeletePhotoCollectionViewCell.m
//  photoclear
//
//  Created by <PERSON><PERSON> on 2025/9/18.
//

#import "DeletePhotoCollectionViewCell.h"

@interface DeletePhotoCollectionViewCell ()

@property (nonatomic, strong) UIImageView *imageView;
@property (nonatomic, strong) UIButton *removeButton;
@property (nonatomic, strong) UIView *overlayView;
@property (nonatomic, strong) PHAsset *currentAsset;

@end

@implementation DeletePhotoCollectionViewCell

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    // 图片视图
    self.imageView = [[UIImageView alloc] init];
    self.imageView.contentMode = UIViewContentModeScaleAspectFill;
    self.imageView.clipsToBounds = YES;
    self.imageView.backgroundColor = [UIColor darkGrayColor];
    [self.contentView addSubview:self.imageView];
    
    // 半透明遮罩
    self.overlayView = [[UIView alloc] init];
    self.overlayView.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.3];
    [self.contentView addSubview:self.overlayView];
    
    // 移除按钮
    self.removeButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [self.removeButton setTitle:@"移除" forState:UIControlStateNormal];
    [self.removeButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    self.removeButton.titleLabel.font = [UIFont systemFontOfSize:14];
    [self.removeButton sizeToFit];
    [self.removeButton addTarget:self action:@selector(removeButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    [self.contentView addSubview:self.removeButton];
    
    // 设置圆角
    self.contentView.layer.cornerRadius = 8;
    self.contentView.layer.masksToBounds = YES;
}

- (void)layoutSubviews {
    [super layoutSubviews];
    
    self.imageView.frame = self.contentView.bounds;
    self.overlayView.frame = self.contentView.bounds;
    
    // 移除按钮位于右上角
    CGFloat buttonSize = 44;
    self.removeButton.frame = CGRectMake(self.contentView.bounds.size.width - buttonSize - 2,
                                        0,
                                        buttonSize,
                                        buttonSize);
}

- (void)configureWithAsset:(PHAsset *)asset {
    self.currentAsset = asset;
    
    // 清除之前的图片
    self.imageView.image = nil;
    
    // 请求缩略图
    PHImageRequestOptions *options = [[PHImageRequestOptions alloc] init];
    options.synchronous = NO;
    options.deliveryMode = PHImageRequestOptionsDeliveryModeHighQualityFormat;
    options.resizeMode = PHImageRequestOptionsResizeModeExact;
    
    CGSize targetSize = CGSizeMake(self.bounds.size.width * 2, self.bounds.size.height * 2); // 2x for retina
    
    [[PHImageManager defaultManager] requestImageForAsset:asset
                                               targetSize:targetSize
                                              contentMode:PHImageContentModeAspectFill
                                                  options:options
                                            resultHandler:^(UIImage * _Nullable result, NSDictionary * _Nullable info) {
        dispatch_async(dispatch_get_main_queue(), ^{
            // 确保这个结果是为当前asset的
            if (self.currentAsset == asset) {
                self.imageView.image = result;
            }
        });
    }];
}

- (void)removeButtonTapped {
    if (self.delegate && [self.delegate respondsToSelector:@selector(deletePhotoCell:didTapRemoveButtonAtIndex:)]) {
        [self.delegate deletePhotoCell:self didTapRemoveButtonAtIndex:self.cellIndex];
    }
}

- (void)prepareForReuse {
    [super prepareForReuse];
    self.imageView.image = nil;
    self.currentAsset = nil;
    self.cellIndex = 0;
}

@end
