//
//  WidgetViewController.m
//  photoclear
//
//  Created by lifubing on 2025/9/26.
//

#import "WidgetViewController.h"
#import "Preferences.h"
#import "WidgetAlbumSelectViewController.h"
#import <Photos/Photos.h>
#import "PhotoTools.h"
#import "photoclear-Swift.h"

@interface WidgetViewController () <WidgetAlbumSelectViewControllerDelegate>

@property (nonatomic, strong) UIScrollView *scrollView;
@property (nonatomic, strong) UIView *contentView;
@property (nonatomic, weak) UILabel *albumStatusLabel; // 保存状态标签的引用

@end

@implementation WidgetViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = [UIColor blackColor];
    [self setupScrollView];
    [self setupContent];
}

- (void)setupScrollView {
    // 创建滚动视图
    self.scrollView = [[UIScrollView alloc] init];
    self.scrollView.translatesAutoresizingMaskIntoConstraints = NO;
    self.scrollView.backgroundColor = [UIColor blackColor];
    self.scrollView.showsVerticalScrollIndicator = YES;
    self.scrollView.showsHorizontalScrollIndicator = NO;
    [self.view addSubview:self.scrollView];

    // 创建内容视图
    self.contentView = [[UIView alloc] init];
    self.contentView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.scrollView addSubview:self.contentView];

    // 设置约束
    [NSLayoutConstraint activateConstraints:@[
        [self.scrollView.topAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.topAnchor],
        [self.scrollView.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor],
        [self.scrollView.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor],
        [self.scrollView.bottomAnchor constraintEqualToAnchor:self.view.bottomAnchor],

        [self.contentView.topAnchor constraintEqualToAnchor:self.scrollView.topAnchor],
        [self.contentView.leadingAnchor constraintEqualToAnchor:self.scrollView.leadingAnchor],
        [self.contentView.trailingAnchor constraintEqualToAnchor:self.scrollView.trailingAnchor],
        [self.contentView.bottomAnchor constraintEqualToAnchor:self.scrollView.bottomAnchor],
        [self.contentView.widthAnchor constraintEqualToAnchor:self.scrollView.widthAnchor]
    ]];
}

- (void)setupContent {
    CGFloat currentY = 12;
    CGFloat sectionSpacing = 25;

    // Widget 功能介绍
    currentY = [self addSectionWithTitle:@"📱 Widget 功能介绍"
                                 content:@"APP可以通过Widget可以在桌面上随机展示系统相册中的照片，支持多种交互操作，帮助您利用碎片化时间，轻松在桌面上快速整理和删除照片"
                                     atY:currentY];
    currentY += sectionSpacing;
    // 相册选择配置
    currentY = [self addAlbumSelectionAtY:currentY];
    currentY += sectionSpacing;
    
    // Widget 尺寸展示
    currentY = [self addWidgetSizeDemoAtY:currentY];
    currentY += sectionSpacing;

    // 功能按钮说明
    currentY = [self addButtonFunctionsAtY:currentY];
    currentY += sectionSpacing;

    // 添加Widget指南
    currentY = [self addWidgetInstallGuideAtY:currentY];
    currentY += sectionSpacing;

    // 使用技巧
//    currentY = [self addUsageTipsAtY:currentY];
//    currentY += 50;

    // 设置内容视图高度
    [self.contentView.heightAnchor constraintEqualToConstant:currentY].active = YES;
}

#pragma mark - UI Creation Methods

- (UILabel *)createTitleLabel:(NSString *)text {
    UILabel *label = [[UILabel alloc] init];
    label.translatesAutoresizingMaskIntoConstraints = NO;
    label.text = text;
    label.font = [UIFont boldSystemFontOfSize:28];
    label.textColor = [UIColor whiteColor];
    label.textAlignment = NSTextAlignmentCenter;
    return label;
}

- (UILabel *)createSectionTitleLabel:(NSString *)text {
    UILabel *label = [[UILabel alloc] init];
    label.translatesAutoresizingMaskIntoConstraints = NO;
    label.text = text;
    label.font = [UIFont boldSystemFontOfSize:18];
    label.textColor = [UIColor whiteColor];
    label.textAlignment = NSTextAlignmentLeft;
    return label;
}

- (UILabel *)createContentLabel:(NSString *)text {
    UILabel *label = [[UILabel alloc] init];
    label.translatesAutoresizingMaskIntoConstraints = NO;
    label.text = text;
    label.font = [UIFont systemFontOfSize:14];
    label.textColor = [UIColor lightGrayColor];
    label.textAlignment = NSTextAlignmentLeft;
    label.numberOfLines = 0;
    return label;
}

- (UIView *)createCardView {
    UIView *cardView = [[UIView alloc] init];
    cardView.translatesAutoresizingMaskIntoConstraints = NO;
    cardView.backgroundColor = [UIColor colorWithWhite:0.1 alpha:1.0];
    cardView.layer.cornerRadius = 12;
    cardView.layer.borderWidth = 1;
    cardView.layer.borderColor = [UIColor colorWithWhite:0.2 alpha:1.0].CGColor;
    return cardView;
}

- (void)setupConstraintsForView:(UIView *)view atY:(CGFloat)y {
    [NSLayoutConstraint activateConstraints:@[
        [view.topAnchor constraintEqualToAnchor:self.contentView.topAnchor constant:y],
        [view.leadingAnchor constraintEqualToAnchor:self.contentView.leadingAnchor constant:20],
        [view.trailingAnchor constraintEqualToAnchor:self.contentView.trailingAnchor constant:-20]
    ]];
}

- (CGFloat)addSectionWithTitle:(NSString *)title content:(NSString *)content atY:(CGFloat)y {
    // 创建卡片视图
    UIView *cardView = [self createCardView];
    [self.contentView addSubview:cardView];

    // 标题标签
    UILabel *titleLabel = [self createSectionTitleLabel:title];
    [cardView addSubview:titleLabel];

    // 内容标签
    UILabel *contentLabel = [self createContentLabel:content];
    [cardView addSubview:contentLabel];

    // 设置约束
    [NSLayoutConstraint activateConstraints:@[
        [cardView.topAnchor constraintEqualToAnchor:self.contentView.topAnchor constant:y],
        [cardView.leadingAnchor constraintEqualToAnchor:self.contentView.leadingAnchor constant:20],
        [cardView.trailingAnchor constraintEqualToAnchor:self.contentView.trailingAnchor constant:-20],

        [titleLabel.topAnchor constraintEqualToAnchor:cardView.topAnchor constant:16],
        [titleLabel.leadingAnchor constraintEqualToAnchor:cardView.leadingAnchor constant:16],
        [titleLabel.trailingAnchor constraintEqualToAnchor:cardView.trailingAnchor constant:-16],

        [contentLabel.topAnchor constraintEqualToAnchor:titleLabel.bottomAnchor constant:12],
        [contentLabel.leadingAnchor constraintEqualToAnchor:cardView.leadingAnchor constant:16],
        [contentLabel.trailingAnchor constraintEqualToAnchor:cardView.trailingAnchor constant:-16],
        [contentLabel.bottomAnchor constraintEqualToAnchor:cardView.bottomAnchor constant:-16]
    ]];

    // 计算卡片高度并返回下一个Y位置
    CGFloat titleHeight = [title boundingRectWithSize:CGSizeMake(self.view.frame.size.width - 72, CGFLOAT_MAX)
                                              options:NSStringDrawingUsesLineFragmentOrigin
                                           attributes:@{NSFontAttributeName: [UIFont boldSystemFontOfSize:18]}
                                              context:nil].size.height;

    CGFloat contentHeight = [content boundingRectWithSize:CGSizeMake(self.view.frame.size.width - 72, CGFLOAT_MAX)
                                                  options:NSStringDrawingUsesLineFragmentOrigin
                                               attributes:@{NSFontAttributeName: [UIFont systemFontOfSize:14]}
                                                  context:nil].size.height;

    return y + titleHeight + contentHeight + 40; // 16 + 12 + 16 + 16 padding
}

- (CGFloat)addWidgetSizeDemoAtY:(CGFloat)y {
    // 创建卡片视图
    UIView *cardView = [self createCardView];
    [self.contentView addSubview:cardView];

    // 标题
    UILabel *titleLabel = [self createSectionTitleLabel:@"📏 Widget 尺寸展示"];
    [cardView addSubview:titleLabel];

    // 小尺寸Widget演示
    UIView *smallWidgetDemo = [self createWidgetDemo:@"小尺寸 Widget"
                                        imageName:@"widget_preview1"
                                                size:CGSizeMake(120, 120)
                                         description:@"显示单张照片，包含缩放、归档、删除、切换按钮"];
    [cardView addSubview:smallWidgetDemo];

    // 中尺寸Widget演示
    UIView *mediumWidgetDemo = [self createWidgetDemo:@"中尺寸 Widget"
                                            imageName:@"widget_preview2"
                                                 size:CGSizeMake(250, 120)
                                          description:@"更大的照片显示区域，按钮布局更清晰"];
    [cardView addSubview:mediumWidgetDemo];

    // 大尺寸Widget演示
    UIView *largeWidgetDemo = [self createWidgetDemo:@"大尺寸 Widget"
                                           imageName:@"widget_preview3"
                                                size:CGSizeMake(250, 250)
                                         description:@"最佳的照片展示效果，所有功能一目了然"];
    [cardView addSubview:largeWidgetDemo];

    // 设置约束
    [NSLayoutConstraint activateConstraints:@[
        [cardView.topAnchor constraintEqualToAnchor:self.contentView.topAnchor constant:y],
        [cardView.leadingAnchor constraintEqualToAnchor:self.contentView.leadingAnchor constant:20],
        [cardView.trailingAnchor constraintEqualToAnchor:self.contentView.trailingAnchor constant:-20],

        [titleLabel.topAnchor constraintEqualToAnchor:cardView.topAnchor constant:12],
        [titleLabel.leadingAnchor constraintEqualToAnchor:cardView.leadingAnchor constant:12],
        [titleLabel.trailingAnchor constraintEqualToAnchor:cardView.trailingAnchor constant:-12],

        [smallWidgetDemo.topAnchor constraintEqualToAnchor:titleLabel.bottomAnchor constant:16],
        [smallWidgetDemo.leadingAnchor constraintEqualToAnchor:cardView.leadingAnchor constant:12],
        [smallWidgetDemo.trailingAnchor constraintEqualToAnchor:cardView.trailingAnchor constant:-12],

        [mediumWidgetDemo.topAnchor constraintEqualToAnchor:smallWidgetDemo.bottomAnchor constant:16],
        [mediumWidgetDemo.leadingAnchor constraintEqualToAnchor:cardView.leadingAnchor constant:12],
        [mediumWidgetDemo.trailingAnchor constraintEqualToAnchor:cardView.trailingAnchor constant:-12],

        [largeWidgetDemo.topAnchor constraintEqualToAnchor:mediumWidgetDemo.bottomAnchor constant:16],
        [largeWidgetDemo.leadingAnchor constraintEqualToAnchor:cardView.leadingAnchor constant:12],
        [largeWidgetDemo.trailingAnchor constraintEqualToAnchor:cardView.trailingAnchor constant:-12],
        [largeWidgetDemo.bottomAnchor constraintEqualToAnchor:cardView.bottomAnchor constant:-12]
    ]];

    return y + 400; // 估算高度
}

- (UIView *)createWidgetDemo:(NSString *)title imageName:(NSString *)imageName size:(CGSize)size description:(NSString *)description {
    UIView *containerView = [[UIView alloc] init];
    containerView.translatesAutoresizingMaskIntoConstraints = NO;

    // Widget预览容器
    UIView *widgetContainer = [[UIView alloc] init];
    widgetContainer.translatesAutoresizingMaskIntoConstraints = NO;
    widgetContainer.backgroundColor = [UIColor colorWithWhite:0.15 alpha:1.0];
    widgetContainer.layer.cornerRadius = 8;
    widgetContainer.layer.borderWidth = 1;
    widgetContainer.layer.borderColor = [UIColor colorWithWhite:0.3 alpha:1.0].CGColor;
    [containerView addSubview:widgetContainer];

    // Widget预览图片
    UIImageView *previewImageView = [[UIImageView alloc] init];
    previewImageView.translatesAutoresizingMaskIntoConstraints = NO;
    previewImageView.image = [UIImage imageNamed:imageName];
    previewImageView.contentMode = UIViewContentModeScaleAspectFill;
//    previewImageView.clipsToBounds = YES;
//    previewImageView.layer.cornerRadius = 6;
    [widgetContainer addSubview:previewImageView];

    // 标题标签
    UILabel *titleLabel = [[UILabel alloc] init];
    titleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    titleLabel.text = title;
    titleLabel.font = [UIFont boldSystemFontOfSize:16];
    titleLabel.textColor = [UIColor whiteColor];
    [containerView addSubview:titleLabel];

    // 描述标签
    UILabel *descLabel = [[UILabel alloc] init];
    descLabel.translatesAutoresizingMaskIntoConstraints = NO;
    descLabel.text = description;
    descLabel.font = [UIFont systemFontOfSize:14];
    descLabel.textColor = [UIColor lightGrayColor];
    descLabel.numberOfLines = 0;
    [containerView addSubview:descLabel];

    // 设置约束
    [NSLayoutConstraint activateConstraints:@[
        [widgetContainer.topAnchor constraintEqualToAnchor:containerView.topAnchor],
        [widgetContainer.leadingAnchor constraintEqualToAnchor:containerView.leadingAnchor],
        [widgetContainer.widthAnchor constraintEqualToConstant:size.width * 0.6],
        [widgetContainer.heightAnchor constraintEqualToConstant:size.height * 0.6],

        [previewImageView.topAnchor constraintEqualToAnchor:widgetContainer.topAnchor constant:4],
        [previewImageView.leadingAnchor constraintEqualToAnchor:widgetContainer.leadingAnchor constant:4],
        [previewImageView.trailingAnchor constraintEqualToAnchor:widgetContainer.trailingAnchor constant:-4],
        [previewImageView.bottomAnchor constraintEqualToAnchor:widgetContainer.bottomAnchor constant:-4],

        [titleLabel.topAnchor constraintEqualToAnchor:containerView.topAnchor],
        [titleLabel.leadingAnchor constraintEqualToAnchor:widgetContainer.trailingAnchor constant:16],
        [titleLabel.trailingAnchor constraintEqualToAnchor:containerView.trailingAnchor],

        [descLabel.topAnchor constraintEqualToAnchor:titleLabel.bottomAnchor constant:8],
        [descLabel.leadingAnchor constraintEqualToAnchor:widgetContainer.trailingAnchor constant:16],
        [descLabel.trailingAnchor constraintEqualToAnchor:containerView.trailingAnchor],
        [descLabel.bottomAnchor constraintLessThanOrEqualToAnchor:containerView.bottomAnchor],

        [containerView.heightAnchor constraintEqualToConstant:MAX(size.height * 0.6, 80)]
    ]];

    return containerView;
}

- (CGFloat)addButtonFunctionsAtY:(CGFloat)y {
    // 创建卡片视图
    UIView *cardView = [self createCardView];
    [self.contentView addSubview:cardView];

    // 标题
    UILabel *titleLabel = [self createSectionTitleLabel:@"🎛️ 功能按钮说明"];
    [cardView addSubview:titleLabel];

    // 缩放按钮说明
    UIView *zoomButtonView = [self createButtonExplanation:@"🔍 缩放按钮"
                                                      icon:@"arrow.up.right.and.arrow.down.left"
                                               description:@"点击可在平铺模式和缩放模式之间切换。缩放模式下照片完整显示，平铺模式下照片填满整个Widget。"];
    [cardView addSubview:zoomButtonView];

    // 归档按钮说明
    UIView *archiveButtonView = [self createButtonExplanation:@"📦 归档按钮"
                                                         icon:@"archivebox"
                                                  description:@"点击可将当前照片标记为已整理，帮助您管理照片的处理状态。"];
    [cardView addSubview:archiveButtonView];

    // 删除按钮说明
    UIView *deleteButtonView = [self createButtonExplanation:@"🗑️ 删除按钮"
                                                        icon:@"trash"
                                                 description:@"点击可将照片标记为待删除，在主应用中可以批量处理这些照片。"];
    [cardView addSubview:deleteButtonView];

    // 切换按钮说明
    UIView *nextButtonView = [self createButtonExplanation:@"➡️ 切换按钮"
                                                      icon:@"chevron.right"
                                               description:@"点击可立即切换到下一张照片，无需等待自动更新。"];
    [cardView addSubview:nextButtonView];

    // 设置约束
    [NSLayoutConstraint activateConstraints:@[
        [cardView.topAnchor constraintEqualToAnchor:self.contentView.topAnchor constant:y],
        [cardView.leadingAnchor constraintEqualToAnchor:self.contentView.leadingAnchor constant:20],
        [cardView.trailingAnchor constraintEqualToAnchor:self.contentView.trailingAnchor constant:-20],

        [titleLabel.topAnchor constraintEqualToAnchor:cardView.topAnchor constant:16],
        [titleLabel.leadingAnchor constraintEqualToAnchor:cardView.leadingAnchor constant:16],
        [titleLabel.trailingAnchor constraintEqualToAnchor:cardView.trailingAnchor constant:-16],

        [zoomButtonView.topAnchor constraintEqualToAnchor:titleLabel.bottomAnchor constant:16],
        [zoomButtonView.leadingAnchor constraintEqualToAnchor:cardView.leadingAnchor constant:16],
        [zoomButtonView.trailingAnchor constraintEqualToAnchor:cardView.trailingAnchor constant:-16],

        [archiveButtonView.topAnchor constraintEqualToAnchor:zoomButtonView.bottomAnchor constant:12],
        [archiveButtonView.leadingAnchor constraintEqualToAnchor:cardView.leadingAnchor constant:16],
        [archiveButtonView.trailingAnchor constraintEqualToAnchor:cardView.trailingAnchor constant:-16],

        [deleteButtonView.topAnchor constraintEqualToAnchor:archiveButtonView.bottomAnchor constant:12],
        [deleteButtonView.leadingAnchor constraintEqualToAnchor:cardView.leadingAnchor constant:16],
        [deleteButtonView.trailingAnchor constraintEqualToAnchor:cardView.trailingAnchor constant:-16],

        [nextButtonView.topAnchor constraintEqualToAnchor:deleteButtonView.bottomAnchor constant:12],
        [nextButtonView.leadingAnchor constraintEqualToAnchor:cardView.leadingAnchor constant:16],
        [nextButtonView.trailingAnchor constraintEqualToAnchor:cardView.trailingAnchor constant:-16],
        [nextButtonView.bottomAnchor constraintEqualToAnchor:cardView.bottomAnchor constant:-16]
    ]];

    return y + 440; // 估算高度
}

- (CGFloat)addAlbumSelectionAtY:(CGFloat)y {
    // 创建卡片视图
    UIView *cardView = [self createCardView];
    [self.contentView addSubview:cardView];

    // 标题
    UILabel *titleLabel = [self createSectionTitleLabel:@"📁 相簿选择配置"];
    [cardView addSubview:titleLabel];

    // 当前选择状态标签
    UILabel *statusLabel = [[UILabel alloc] init];
    statusLabel.translatesAutoresizingMaskIntoConstraints = NO;
    statusLabel.font = [UIFont systemFontOfSize:14];
    statusLabel.textColor = [UIColor lightGrayColor];
    statusLabel.numberOfLines = 0;
    [cardView addSubview:statusLabel];

    // 选择相册按钮
    UIButton *selectButton = [[UIButton alloc] init];
    selectButton.translatesAutoresizingMaskIntoConstraints = NO;
    [selectButton setTitle:@"选择相册" forState:UIControlStateNormal];
    [selectButton setTitleColor:[UIColor systemBlueColor] forState:UIControlStateNormal];
    selectButton.titleLabel.font = [UIFont systemFontOfSize:16];
    selectButton.backgroundColor = [UIColor colorWithWhite:0.15 alpha:1.0];
    selectButton.layer.cornerRadius = 8;
    selectButton.layer.borderWidth = 1;
    selectButton.layer.borderColor = [UIColor systemBlueColor].CGColor;
    [selectButton addTarget:self action:@selector(selectAlbumsButtonTapped:) forControlEvents:UIControlEventTouchUpInside];
    [cardView addSubview:selectButton];

    // 保存状态标签引用
    self.albumStatusLabel = statusLabel;

    // 更新状态显示
    [self updateAlbumSelectionStatus:statusLabel];

    // 设置约束
    [NSLayoutConstraint activateConstraints:@[
        [cardView.topAnchor constraintEqualToAnchor:self.contentView.topAnchor constant:y],
        [cardView.leadingAnchor constraintEqualToAnchor:self.contentView.leadingAnchor constant:20],
        [cardView.trailingAnchor constraintEqualToAnchor:self.contentView.trailingAnchor constant:-20],

        [titleLabel.topAnchor constraintEqualToAnchor:cardView.topAnchor constant:16],
        [titleLabel.leadingAnchor constraintEqualToAnchor:cardView.leadingAnchor constant:16],
        [titleLabel.trailingAnchor constraintEqualToAnchor:cardView.trailingAnchor constant:-16],

        [statusLabel.topAnchor constraintEqualToAnchor:titleLabel.bottomAnchor constant:12],
        [statusLabel.leadingAnchor constraintEqualToAnchor:cardView.leadingAnchor constant:16],
        [statusLabel.trailingAnchor constraintEqualToAnchor:cardView.trailingAnchor constant:-16],

        [selectButton.topAnchor constraintEqualToAnchor:statusLabel.bottomAnchor constant:16],
        [selectButton.leadingAnchor constraintEqualToAnchor:cardView.leadingAnchor constant:16],
        [selectButton.trailingAnchor constraintEqualToAnchor:cardView.trailingAnchor constant:-16],
        [selectButton.heightAnchor constraintEqualToConstant:44],
        [selectButton.bottomAnchor constraintEqualToAnchor:cardView.bottomAnchor constant:-16]
    ]];

    return y + 190; // 估算高度
}

- (void)updateAlbumSelectionStatus:(UILabel *)statusLabel {
    NSArray *selectedAlbums = [Preferences sharedInstance].selectedAlbumIdentifiers;

    if (!selectedAlbums || selectedAlbums.count == 0) {
        statusLabel.text = @"当前设置：从所有相册中选择照片\n\nWidget将从系统相册中的所有照片中随机选择进行缓存和显示。";
    } else {
        // 获取相册名称
        dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
            NSMutableArray *albumNames = [NSMutableArray array];

            PHFetchResult *collections = [PHAssetCollection fetchAssetCollectionsWithLocalIdentifiers:selectedAlbums options:nil];
            for (PHAssetCollection *collection in collections) {
                [albumNames addObject:collection.localizedTitle ?: @"未知相簿"];
            }

            dispatch_async(dispatch_get_main_queue(), ^{
                NSString *albumsText = [albumNames componentsJoinedByString:@"、"];
                statusLabel.text = [NSString stringWithFormat:@"当前选中的相簿：%@\n\nWidget将仅从这些相簿中的照片中随机选择进行缓存和显示。", albumsText];
            });
        });
    }
}

- (void)selectAlbumsButtonTapped:(UIButton *)sender {
    WidgetAlbumSelectViewController *albumSelectVC = [[WidgetAlbumSelectViewController alloc] init];
    albumSelectVC.delegate = self;

    UINavigationController *navController = [[UINavigationController alloc] initWithRootViewController:albumSelectVC];
    navController.modalPresentationStyle = UIModalPresentationFormSheet;

    [self presentViewController:navController animated:YES completion:nil];
}

#pragma mark - WidgetAlbumSelectViewControllerDelegate

- (void)widgetAlbumSelectViewController:(UIViewController *)controller didSelectAlbumIdentifiers:(NSArray<NSString *> *)albumIdentifiers {
    // 保存选择的相册
    [Preferences sharedInstance].selectedAlbumIdentifiers = albumIdentifiers;

    // 更新状态显示
    if (self.albumStatusLabel) {
        [self updateAlbumSelectionStatus:self.albumStatusLabel];
    }

    NSLog(@"已选择 %ld 个相册", albumIdentifiers.count);
    
    [PhotoTools asyncCacheRandomPhotosForWidgetCompletion:^(BOOL success) {
        if (success) {
            // 通知Widget更新
            [WidgetKitHelper reloadAllWidgets];
            NSLog(@"Photo cache updated successfully");
        } else {
            NSLog(@"Photo cache update failed");
        }
    }];
}

- (UIView *)createButtonExplanation:(NSString *)title icon:(NSString *)iconName description:(NSString *)description {
    UIView *containerView = [[UIView alloc] init];
    containerView.translatesAutoresizingMaskIntoConstraints = NO;
    containerView.backgroundColor = [UIColor colorWithWhite:0.05 alpha:1.0];
    containerView.layer.cornerRadius = 8;

    // 图标
    UIImageView *iconView = [[UIImageView alloc] init];
    iconView.translatesAutoresizingMaskIntoConstraints = NO;
    iconView.image = [UIImage systemImageNamed:iconName];
    iconView.tintColor = [UIColor systemBlueColor];
    iconView.contentMode = UIViewContentModeScaleAspectFit;
    [containerView addSubview:iconView];

    // 标题
    UILabel *titleLabel = [[UILabel alloc] init];
    titleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    titleLabel.text = title;
    titleLabel.font = [UIFont boldSystemFontOfSize:16];
    titleLabel.textColor = [UIColor whiteColor];
    [containerView addSubview:titleLabel];

    // 描述
    UILabel *descLabel = [[UILabel alloc] init];
    descLabel.translatesAutoresizingMaskIntoConstraints = NO;
    descLabel.text = description;
    descLabel.font = [UIFont systemFontOfSize:14];
    descLabel.textColor = [UIColor lightGrayColor];
    descLabel.numberOfLines = 0;
    [containerView addSubview:descLabel];

    // 设置约束
    [NSLayoutConstraint activateConstraints:@[
        [iconView.leadingAnchor constraintEqualToAnchor:containerView.leadingAnchor constant:12],
        [iconView.centerYAnchor constraintEqualToAnchor:containerView.centerYAnchor],
        [iconView.widthAnchor constraintEqualToConstant:24],
        [iconView.heightAnchor constraintEqualToConstant:24],

        [titleLabel.topAnchor constraintEqualToAnchor:containerView.topAnchor constant:12],
        [titleLabel.leadingAnchor constraintEqualToAnchor:iconView.trailingAnchor constant:12],
        [titleLabel.trailingAnchor constraintEqualToAnchor:containerView.trailingAnchor constant:-12],

        [descLabel.topAnchor constraintEqualToAnchor:titleLabel.bottomAnchor constant:4],
        [descLabel.leadingAnchor constraintEqualToAnchor:iconView.trailingAnchor constant:12],
        [descLabel.trailingAnchor constraintEqualToAnchor:containerView.trailingAnchor constant:-12],
        [descLabel.bottomAnchor constraintEqualToAnchor:containerView.bottomAnchor constant:-12],

        [containerView.heightAnchor constraintGreaterThanOrEqualToConstant:60]
    ]];

    return containerView;
}

- (CGFloat)addWidgetInstallGuideAtY:(CGFloat)y {
    // 创建卡片视图
    UIView *cardView = [self createCardView];
    [self.contentView addSubview:cardView];

    // 标题
    UILabel *titleLabel = [self createSectionTitleLabel:@"📲 如何添加 Widget"];
    [cardView addSubview:titleLabel];

    // 步骤1
    UIView *step1View = [self createInstallStep:@"1"
                                          title:@"长按桌面空白处"
                                    description:@"在iPhone桌面上长按空白区域，直到应用图标开始抖动"];
    [cardView addSubview:step1View];

    // 步骤2
    UIView *step2View = [self createInstallStep:@"2"
                                          title:@"点击左上角的 +"
                                    description:@"点击屏幕左上角的加号按钮，进入Widget添加界面"];
    [cardView addSubview:step2View];

    // 步骤3
    UIView *step3View = [self createInstallStep:@"3"
                                          title:@"搜索 \"相册整理\""
                                    description:@"在搜索框中输入\"相册整理\"或滑动找到本应用"];
    [cardView addSubview:step3View];

    // 步骤4
    UIView *step4View = [self createInstallStep:@"4"
                                          title:@"选择Widget尺寸"
                                    description:@"选择小、中或大尺寸的Widget，然后点击\"添加Widget\""];
    [cardView addSubview:step4View];

    // 步骤5
    UIView *step5View = [self createInstallStep:@"5"
                                          title:@"完成设置"
                                    description:@"Widget添加到桌面后，点击\"完成\"即可开始使用"];
    [cardView addSubview:step5View];

    // 设置约束
    [NSLayoutConstraint activateConstraints:@[
        [cardView.topAnchor constraintEqualToAnchor:self.contentView.topAnchor constant:y],
        [cardView.leadingAnchor constraintEqualToAnchor:self.contentView.leadingAnchor constant:20],
        [cardView.trailingAnchor constraintEqualToAnchor:self.contentView.trailingAnchor constant:-20],

        [titleLabel.topAnchor constraintEqualToAnchor:cardView.topAnchor constant:16],
        [titleLabel.leadingAnchor constraintEqualToAnchor:cardView.leadingAnchor constant:16],
        [titleLabel.trailingAnchor constraintEqualToAnchor:cardView.trailingAnchor constant:-16],

        [step1View.topAnchor constraintEqualToAnchor:titleLabel.bottomAnchor constant:16],
        [step1View.leadingAnchor constraintEqualToAnchor:cardView.leadingAnchor constant:16],
        [step1View.trailingAnchor constraintEqualToAnchor:cardView.trailingAnchor constant:-16],

        [step2View.topAnchor constraintEqualToAnchor:step1View.bottomAnchor constant:12],
        [step2View.leadingAnchor constraintEqualToAnchor:cardView.leadingAnchor constant:16],
        [step2View.trailingAnchor constraintEqualToAnchor:cardView.trailingAnchor constant:-16],

        [step3View.topAnchor constraintEqualToAnchor:step2View.bottomAnchor constant:12],
        [step3View.leadingAnchor constraintEqualToAnchor:cardView.leadingAnchor constant:16],
        [step3View.trailingAnchor constraintEqualToAnchor:cardView.trailingAnchor constant:-16],

        [step4View.topAnchor constraintEqualToAnchor:step3View.bottomAnchor constant:12],
        [step4View.leadingAnchor constraintEqualToAnchor:cardView.leadingAnchor constant:16],
        [step4View.trailingAnchor constraintEqualToAnchor:cardView.trailingAnchor constant:-16],

        [step5View.topAnchor constraintEqualToAnchor:step4View.bottomAnchor constant:12],
        [step5View.leadingAnchor constraintEqualToAnchor:cardView.leadingAnchor constant:16],
        [step5View.trailingAnchor constraintEqualToAnchor:cardView.trailingAnchor constant:-16],
        [step5View.bottomAnchor constraintEqualToAnchor:cardView.bottomAnchor constant:-16]
    ]];

    return y + 400; // 估算高度
}

- (UIView *)createInstallStep:(NSString *)stepNumber title:(NSString *)title description:(NSString *)description {
    UIView *containerView = [[UIView alloc] init];
    containerView.translatesAutoresizingMaskIntoConstraints = NO;

    // 步骤编号圆圈
    UIView *numberCircle = [[UIView alloc] init];
    numberCircle.translatesAutoresizingMaskIntoConstraints = NO;
    numberCircle.backgroundColor = [UIColor systemBlueColor];
    numberCircle.layer.cornerRadius = 15;
    [containerView addSubview:numberCircle];

    // 步骤编号标签
    UILabel *numberLabel = [[UILabel alloc] init];
    numberLabel.translatesAutoresizingMaskIntoConstraints = NO;
    numberLabel.text = stepNumber;
    numberLabel.font = [UIFont boldSystemFontOfSize:14];
    numberLabel.textColor = [UIColor whiteColor];
    numberLabel.textAlignment = NSTextAlignmentCenter;
    [numberCircle addSubview:numberLabel];

    // 标题
    UILabel *titleLabel = [[UILabel alloc] init];
    titleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    titleLabel.text = title;
    titleLabel.font = [UIFont boldSystemFontOfSize:16];
    titleLabel.textColor = [UIColor whiteColor];
    [containerView addSubview:titleLabel];

    // 描述
    UILabel *descLabel = [[UILabel alloc] init];
    descLabel.translatesAutoresizingMaskIntoConstraints = NO;
    descLabel.text = description;
    descLabel.font = [UIFont systemFontOfSize:14];
    descLabel.textColor = [UIColor lightGrayColor];
    descLabel.numberOfLines = 0;
    [containerView addSubview:descLabel];

    // 设置约束
    [NSLayoutConstraint activateConstraints:@[
        [numberCircle.leadingAnchor constraintEqualToAnchor:containerView.leadingAnchor],
        [numberCircle.topAnchor constraintEqualToAnchor:containerView.topAnchor],
        [numberCircle.widthAnchor constraintEqualToConstant:30],
        [numberCircle.heightAnchor constraintEqualToConstant:30],

        [numberLabel.centerXAnchor constraintEqualToAnchor:numberCircle.centerXAnchor],
        [numberLabel.centerYAnchor constraintEqualToAnchor:numberCircle.centerYAnchor],

        [titleLabel.topAnchor constraintEqualToAnchor:containerView.topAnchor],
        [titleLabel.leadingAnchor constraintEqualToAnchor:numberCircle.trailingAnchor constant:12],
        [titleLabel.trailingAnchor constraintEqualToAnchor:containerView.trailingAnchor],

        [descLabel.topAnchor constraintEqualToAnchor:titleLabel.bottomAnchor constant:4],
        [descLabel.leadingAnchor constraintEqualToAnchor:numberCircle.trailingAnchor constant:12],
        [descLabel.trailingAnchor constraintEqualToAnchor:containerView.trailingAnchor],
        [descLabel.bottomAnchor constraintEqualToAnchor:containerView.bottomAnchor],

        [containerView.heightAnchor constraintGreaterThanOrEqualToConstant:50]
    ]];

    return containerView;
}

//- (CGFloat)addUsageTipsAtY:(CGFloat)y {
//    // 创建卡片视图
//    UIView *cardView = [self createCardView];
//    [self.contentView addSubview:cardView];
//
//    // 标题
//    UILabel *titleLabel = [self createSectionTitleLabel:@"💡 使用技巧"];
//    [cardView addSubview:titleLabel];
//
//    // 技巧1
//    UIView *tip1View = [self createTipItem:@"⏰ 自动更新"
//                                description:@"Widget每小时自动更新一次，展示不同的照片"];
//    [cardView addSubview:tip1View];
//
//    // 技巧2
//    UIView *tip2View = [self createTipItem:@"🎯 点击照片"
//                                description:@"点击Widget中的照片可以直接跳转到主应用查看大图"];
//    [cardView addSubview:tip2View];
//
//    // 技巧3
//    UIView *tip3View = [self createTipItem:@"🔄 手动刷新"
//                                description:@"在主应用中点击\"更新Widget照片\"可以手动刷新照片缓存"];
//    [cardView addSubview:tip3View];
//
//    // 技巧4
//    UIView *tip4View = [self createTipItem:@"📱 权限设置"
//                                description:@"确保已授权相册访问权限，否则Widget无法显示照片"];
//    [cardView addSubview:tip4View];
//
//    // 技巧5
//    UIView *tip5View = [self createTipItem:@"🎨 最佳体验"
//                                description:@"建议使用中尺寸或大尺寸Widget以获得最佳的视觉效果"];
//    [cardView addSubview:tip5View];
//
//    // 设置约束
//    [NSLayoutConstraint activateConstraints:@[
//        [cardView.topAnchor constraintEqualToAnchor:self.contentView.topAnchor constant:y],
//        [cardView.leadingAnchor constraintEqualToAnchor:self.contentView.leadingAnchor constant:20],
//        [cardView.trailingAnchor constraintEqualToAnchor:self.contentView.trailingAnchor constant:-20],
//
//        [titleLabel.topAnchor constraintEqualToAnchor:cardView.topAnchor constant:16],
//        [titleLabel.leadingAnchor constraintEqualToAnchor:cardView.leadingAnchor constant:16],
//        [titleLabel.trailingAnchor constraintEqualToAnchor:cardView.trailingAnchor constant:-16],
//
//        [tip1View.topAnchor constraintEqualToAnchor:titleLabel.bottomAnchor constant:16],
//        [tip1View.leadingAnchor constraintEqualToAnchor:cardView.leadingAnchor constant:16],
//        [tip1View.trailingAnchor constraintEqualToAnchor:cardView.trailingAnchor constant:-16],
//
//        [tip2View.topAnchor constraintEqualToAnchor:tip1View.bottomAnchor constant:8],
//        [tip2View.leadingAnchor constraintEqualToAnchor:cardView.leadingAnchor constant:16],
//        [tip2View.trailingAnchor constraintEqualToAnchor:cardView.trailingAnchor constant:-16],
//
//        [tip3View.topAnchor constraintEqualToAnchor:tip2View.bottomAnchor constant:8],
//        [tip3View.leadingAnchor constraintEqualToAnchor:cardView.leadingAnchor constant:16],
//        [tip3View.trailingAnchor constraintEqualToAnchor:cardView.trailingAnchor constant:-16],
//
//        [tip4View.topAnchor constraintEqualToAnchor:tip3View.bottomAnchor constant:8],
//        [tip4View.leadingAnchor constraintEqualToAnchor:cardView.leadingAnchor constant:16],
//        [tip4View.trailingAnchor constraintEqualToAnchor:cardView.trailingAnchor constant:-16],
//
//        [tip5View.topAnchor constraintEqualToAnchor:tip4View.bottomAnchor constant:8],
//        [tip5View.leadingAnchor constraintEqualToAnchor:cardView.leadingAnchor constant:16],
//        [tip5View.trailingAnchor constraintEqualToAnchor:cardView.trailingAnchor constant:-16],
//        [tip5View.bottomAnchor constraintEqualToAnchor:cardView.bottomAnchor constant:-16]
//    ]];
//
//    return y + 350; // 估算高度
//}

- (UIView *)createTipItem:(NSString *)title description:(NSString *)description {
    UIView *containerView = [[UIView alloc] init];
    containerView.translatesAutoresizingMaskIntoConstraints = NO;

    // 标题
    UILabel *titleLabel = [[UILabel alloc] init];
    titleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    titleLabel.text = title;
    titleLabel.font = [UIFont boldSystemFontOfSize:15];
    titleLabel.textColor = [UIColor whiteColor];
    [containerView addSubview:titleLabel];

    // 描述
    UILabel *descLabel = [[UILabel alloc] init];
    descLabel.translatesAutoresizingMaskIntoConstraints = NO;
    descLabel.text = description;
    descLabel.font = [UIFont systemFontOfSize:14];
    descLabel.textColor = [UIColor lightGrayColor];
    descLabel.numberOfLines = 0;
    [containerView addSubview:descLabel];

    // 设置约束
    [NSLayoutConstraint activateConstraints:@[
        [titleLabel.topAnchor constraintEqualToAnchor:containerView.topAnchor],
        [titleLabel.leadingAnchor constraintEqualToAnchor:containerView.leadingAnchor],
        [titleLabel.trailingAnchor constraintEqualToAnchor:containerView.trailingAnchor],

        [descLabel.topAnchor constraintEqualToAnchor:titleLabel.bottomAnchor constant:4],
        [descLabel.leadingAnchor constraintEqualToAnchor:containerView.leadingAnchor constant:20],
        [descLabel.trailingAnchor constraintEqualToAnchor:containerView.trailingAnchor],
        [descLabel.bottomAnchor constraintEqualToAnchor:containerView.bottomAnchor],

        [containerView.heightAnchor constraintGreaterThanOrEqualToConstant:40]
    ]];

    return containerView;
}

@end
