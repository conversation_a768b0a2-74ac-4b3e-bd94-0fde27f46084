//
//  WidgetAlbumSelectViewController.m
//  photoclear
//
//  Created by lifubing on 2025/9/27.
//

#import "WidgetAlbumSelectViewController.h"
#import "../../Manager/Preferences.h"

@interface WidgetAlbumSelectViewController () <UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) PHFetchResult<PHAssetCollection *> *smartAlbumsArray;
@property (nonatomic, strong) PHFetchResult<PHAsset *> *photoAssetArray;
@property (nonatomic, strong) PHImageRequestOptions *requestOptions;
@property (nonatomic, strong) NSMutableSet<NSString *> *selectedAlbumIdentifiers;
@property (nonatomic, strong) UIBarButtonItem *doneButton;
@property (nonatomic, strong) UIBarButtonItem *selectAllButton;

@end

@implementation WidgetAlbumSelectViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.title = @"选择相册";
    self.view.backgroundColor = [UIColor blackColor];
    
    // 初始化选中的相册集合
    NSArray *currentSelected = [Preferences sharedInstance].selectedAlbumIdentifiers;
    self.selectedAlbumIdentifiers = [NSMutableSet setWithArray:currentSelected ?: @[]];
    
    [self setupNavigationBar];
    [self setupTableView];
    [self loadAlbums];
}

- (void)setupNavigationBar {
    // 取消按钮
    UIBarButtonItem *cancelButton = [[UIBarButtonItem alloc] initWithBarButtonSystemItem:UIBarButtonSystemItemCancel target:self action:@selector(cancelButtonTapped)];
    self.navigationItem.leftBarButtonItem = cancelButton;
    
    // 完成按钮
    self.doneButton = [[UIBarButtonItem alloc] initWithBarButtonSystemItem:UIBarButtonSystemItemDone target:self action:@selector(doneButtonTapped)];
    
    // 全选/取消全选按钮
    self.selectAllButton = [[UIBarButtonItem alloc] initWithTitle:@"全选" style:UIBarButtonItemStylePlain target:self action:@selector(selectAllButtonTapped)];
    
    self.navigationItem.rightBarButtonItems = @[self.doneButton, self.selectAllButton];
    
    [self updateSelectAllButtonTitle];
}

- (void)setupTableView {
    self.tableView = [[UITableView alloc] initWithFrame:self.view.bounds style:UITableViewStylePlain];
    self.tableView.delegate = self;
    self.tableView.dataSource = self;
    self.tableView.backgroundColor = [UIColor blackColor];
    self.tableView.separatorColor = [UIColor colorWithWhite:0.2 alpha:1.0];
    [self.tableView registerClass:[UITableViewCell class] forCellReuseIdentifier:@"AlbumCell"];
    [self.view addSubview:self.tableView];
    
    self.tableView.translatesAutoresizingMaskIntoConstraints = NO;
    [NSLayoutConstraint activateConstraints:@[
        [self.tableView.topAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.topAnchor],
        [self.tableView.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor],
        [self.tableView.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor],
        [self.tableView.bottomAnchor constraintEqualToAnchor:self.view.bottomAnchor]
    ]];
}

- (void)loadAlbums {
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        // 获取所有相册
        PHFetchResult *smartAlbums = [PHAssetCollection fetchAssetCollectionsWithType:PHAssetCollectionTypeAlbum subtype:PHAssetCollectionSubtypeAny options:nil];
        self.smartAlbumsArray = smartAlbums;
        
        // 获取所有照片
        PHFetchOptions *fetchOptions = [[PHFetchOptions alloc] init];
        fetchOptions.sortDescriptors = @[[NSSortDescriptor sortDescriptorWithKey:@"creationDate" ascending:NO]];
        self.photoAssetArray = [PHAsset fetchAssetsWithMediaType:PHAssetMediaTypeImage options:fetchOptions];
        
        // 设置请求选项
        self.requestOptions = [[PHImageRequestOptions alloc] init];
        self.requestOptions.resizeMode = PHImageRequestOptionsResizeModeExact;
        self.requestOptions.deliveryMode = PHImageRequestOptionsDeliveryModeHighQualityFormat;
        self.requestOptions.synchronous = YES;
        
        dispatch_async(dispatch_get_main_queue(), ^{
            [self.tableView reloadData];
        });
    });
}

#pragma mark - UITableViewDataSource

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.smartAlbumsArray.count + 1; // +1 for "所有照片"
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:@"AlbumCell" forIndexPath:indexPath];
    
    cell.backgroundColor = [UIColor blackColor];
    cell.textLabel.textColor = [UIColor whiteColor];
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    
    if (indexPath.row == 0) {
        // "所有照片" 选项
        cell.textLabel.text = [NSString stringWithFormat:@"所有照片 (%ld)", self.photoAssetArray.count];
        cell.accessoryType = (self.selectedAlbumIdentifiers.count == 0) ? UITableViewCellAccessoryCheckmark : UITableViewCellAccessoryNone;
    } else {
        PHAssetCollection *collection = [self.smartAlbumsArray objectAtIndex:indexPath.row - 1];
        
        PHFetchOptions *fetchOptions = [[PHFetchOptions alloc] init];
        fetchOptions.sortDescriptors = @[[NSSortDescriptor sortDescriptorWithKey:@"creationDate" ascending:NO]];
        PHFetchResult *fetchResult = [PHAsset fetchAssetsInAssetCollection:collection options:fetchOptions];
        
        if (fetchResult.count > 0) {
            cell.textLabel.text = [NSString stringWithFormat:@"%@ (%ld)", collection.localizedTitle, fetchResult.count];
            cell.accessoryType = [self.selectedAlbumIdentifiers containsObject:collection.localIdentifier] ? UITableViewCellAccessoryCheckmark : UITableViewCellAccessoryNone;
        } else {
            cell.textLabel.text = [NSString stringWithFormat:@"%@ (0)", collection.localizedTitle];
            cell.textLabel.textColor = [UIColor grayColor];
            cell.accessoryType = UITableViewCellAccessoryNone;
        }
    }
    
    return cell;
}

#pragma mark - UITableViewDelegate

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    
    if (indexPath.row == 0) {
        // 选择"所有照片"
        [self.selectedAlbumIdentifiers removeAllObjects];
    } else {
        PHAssetCollection *collection = [self.smartAlbumsArray objectAtIndex:indexPath.row - 1];
        
        // 检查相册是否有照片
        PHFetchOptions *fetchOptions = [[PHFetchOptions alloc] init];
        PHFetchResult *fetchResult = [PHAsset fetchAssetsInAssetCollection:collection options:fetchOptions];
        
        if (fetchResult.count == 0) {
            return; // 空相册不能选择
        }
        
        if ([self.selectedAlbumIdentifiers containsObject:collection.localIdentifier]) {
            [self.selectedAlbumIdentifiers removeObject:collection.localIdentifier];
        } else {
            [self.selectedAlbumIdentifiers addObject:collection.localIdentifier];
        }
    }
    
    [self.tableView reloadData];
    [self updateSelectAllButtonTitle];
}

#pragma mark - Actions

- (void)cancelButtonTapped {
    [self dismissViewControllerAnimated:YES completion:nil];
}

- (void)doneButtonTapped {
    NSArray *selectedArray = [self.selectedAlbumIdentifiers allObjects];
    
    if (self.delegate) {
        [self.delegate widgetAlbumSelectViewController:self didSelectAlbumIdentifiers:selectedArray];
    }
    
    [self dismissViewControllerAnimated:YES completion:nil];
}

- (void)selectAllButtonTapped {
    if (self.selectedAlbumIdentifiers.count == 0) {
        // 当前是"所有照片"，切换到全选所有相册
        for (NSInteger i = 0; i < self.smartAlbumsArray.count; i++) {
            PHAssetCollection *collection = [self.smartAlbumsArray objectAtIndex:i];
            
            // 只选择有照片的相册
            PHFetchOptions *fetchOptions = [[PHFetchOptions alloc] init];
            PHFetchResult *fetchResult = [PHAsset fetchAssetsInAssetCollection:collection options:fetchOptions];
            
            if (fetchResult.count > 0) {
                [self.selectedAlbumIdentifiers addObject:collection.localIdentifier];
            }
        }
    } else {
        // 当前有选中的相册，切换到"所有照片"
        [self.selectedAlbumIdentifiers removeAllObjects];
    }
    
    [self.tableView reloadData];
    [self updateSelectAllButtonTitle];
}

- (void)updateSelectAllButtonTitle {
    if (self.selectedAlbumIdentifiers.count == 0) {
        self.selectAllButton.title = @"全选";
    } else {
        self.selectAllButton.title = @"重置";
    }
}

@end
