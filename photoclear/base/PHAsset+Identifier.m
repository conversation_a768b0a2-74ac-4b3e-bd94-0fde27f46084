//
//  PHAsset+Identifier.m
//  photoclear
//
//  Created by lifubing on 2025/9/28.
//

#import "PHAsset+Identifier.h"

@implementation PHAsset (Identifier)

- (NSString *)identifier {
    NSString *localIdentifier = self.localIdentifier;
    NSArray *components = [localIdentifier componentsSeparatedByString:@"/"];
    NSString *identifier = components.firstObject;
    
    return identifier;
}

@end
