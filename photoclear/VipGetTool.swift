//
//  VipGetTool.swift
//  wordWidget
//
//  Created by lifubing on 2024/8/22.
//

import Foundation
import SwiftyStoreKit

// 定义通知的名称
extension Notification.Name {
    static let VipChangeNotification = Notification.Name("VipChangeNotification")
}

class VipGetToolManager:NSObject {
    
    // 创建单例实例
    static let shared = VipGetToolManager()
    
    let productID = "com.lfb.wordWidget.app.yearplus"

    // 1、支付
    func purchaseProduct() {
        SwiftyStoreKit.purchaseProduct(productID, atomically: true) { result in
            switch result {
                
            case .success(let purchase):
                AlertManager.shared.showLoading("购买成功，正在校验..")
                print("购买成功: \(purchase.productId)")
                self.verifyReceipt(purchase.transaction, true)
                
            case .error(let error):
                var alertTips = ""
                switch error.code {
                case .unknown:
                    alertTips = "未知错误，请联系作者支持"
                case .clientInvalid:
                    alertTips = "不允许进行支付"
                case .paymentCancelled:
                    alertTips = "支付已取消"
                case .paymentInvalid:
                    alertTips = "购买标识无效"
                case .paymentNotAllowed:
                    alertTips = "设备不允许进行支付"
                case .storeProductNotAvailable:
                    alertTips = "产品在当前商店中不可用"
                case .cloudServicePermissionDenied:
                    alertTips = "访问云服务信息被拒绝"
                case .cloudServiceNetworkConnectionFailed:
                    alertTips = "无法连接到网络"
                case .cloudServiceRevoked:
                    alertTips = "用户已撤销使用云服务的权限"
                default:
                    alertTips = "错误详情: \(error.localizedDescription)"
                }
                AlertManager.shared.showTips(alertTips)
            case .deferred(purchase: _):
                AlertManager.shared.showTips("未知错误，请联系作者支持")
            }
        }
    }
    
    // 2、验证
    func verifyReceipt(_ transaction: PaymentTransaction? = nil, _ needshowTips:Bool = false) {
        // 验证收据并检查订阅状态

        SwiftyStoreKit.verifyReceipt(using: AppleReceiptValidator(service: .production, sharedSecret: "8fb4ebf15fbd48bd9222d144e88c3a38"), forceRefresh: false) { result in
            switch result {
            case .success(let receipt):
                self.checkSubscriptionStatus(receipt: receipt,transaction:transaction, needshowTips)
            case .error(let error):
                print("收据验证失败: \(error.localizedDescription)")
                
//                UserDefaultsManager.shared.save(value: "0", for: .vipGet)
                Preferences.sharedInstance().didVipGet = false
                if needshowTips {
                    AlertManager.shared.showTips("验证失败，请换个网络重试，或者联系作者。")
                }
            }
        }
    }
    
    // 3、恢复购买
    func restorePurchases() {
        SwiftyStoreKit.restorePurchases(atomically: true) { results in
            if results.restoreFailedPurchases.count > 0 {
                print("恢复失败: \(results.restoreFailedPurchases)")
                AlertManager.shared.showTips("恢复购买失败")
            } else if results.restoredPurchases.count > 0 {
                for purchase in results.restoredPurchases {
                    AlertManager.shared.showLoading("恢复购买成功，验证中")
                    // 如果需要，手动完成交易
                    self.verifyReceipt(purchase.transaction, true)
                }
            } else {
                AlertManager.shared.showTips("还未购买此订阅。")
            }
        }
    }
    
    fileprivate func checkSubscriptionStatus(receipt: ReceiptInfo, transaction: PaymentTransaction? , _ needshowTips:Bool = false) {
        // 验证订阅状态
        let result = SwiftyStoreKit.verifySubscription(ofType: .autoRenewable, productId: productID, inReceipt: receipt, validUntil: Date())
        
        switch result {
         case .purchased(let expiryDate, _):
            if let trans = transaction {
                SwiftyStoreKit.finishTransaction(trans)
            }
            if needshowTips {
                showAlert("购买成功，有效至 \(expiryDate)")
            }
//            UserDefaultsManager.shared.save(value: "1", for: .vipGet)
            Preferences.sharedInstance().didVipGet = true
            NotificationCenter.default.post(name: .VipChangeNotification, object: nil)
         case .expired(let expiryDate, _):
            if needshowTips {
                showAlert("订阅已过期，过期时间: \(expiryDate)")
            }
//            UserDefaultsManager.shared.save(value: "0", for: .vipGet)
            
         case .notPurchased:
            
            if needshowTips {
                showAlert("未购买此订阅")
            }
//            UserDefaultsManager.shared.save(value: "0", for: .vipGet)
            Preferences.sharedInstance().didVipGet = false
         }
    }
    
    func showAlert(_ str:String) {
        NSObject.cancelPreviousPerformRequests(withTarget: self)
        self.perform(#selector(doShowAlert), with: str, afterDelay: 1)
    }
    
    @objc func doShowAlert(_ str:String) {
        AlertManager.shared.showTips(str)
    }
}
