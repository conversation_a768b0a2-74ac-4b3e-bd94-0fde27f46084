//
//  PhotoTools.h
//  photoclear
//
//  Created by lifubing on 2021/6/29.
//

#import <Foundation/Foundation.h>
#import <Photos/Photos.h>
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface PhotoTools : NSObject

+ (PHFetchResult<PHAsset *> *)allPhotos;
+ (PHFetchResult<PHAsset *> *)allPhotosFromSelectedAlbums; // 从选中的相册获取照片

+ (void)asyncCacheRandomPhotosForWidgetCompletion:(void(^)(BOOL success))completion; // 异步缓存

+ (void)normalCacheSmallRandomPhotosForWidgetCompletion:(void(^)(BOOL success))completion;

+ (NSURL *)sharedContainerURL;
+ (NSArray<NSString *> *)getCachedPhotoNames;

+ (NSArray<NSString *> *)getOperatedPhotos;
+ (void)updateOperatedPhotos:(NSArray<NSString *>*)operatedPhotos;
+ (void)recordOperatedPhotos:(NSString *)operatedPhoto;

+ (void)clearOperatedPhotos;

// 照片缓存次数管理
+ (NSDictionary<NSString *, NSNumber *> *)getPhotoCacheCount;
+ (void)incrementCacheCountForPhoto:(NSString *)identifier;
+ (void)clearPhotoCacheCount;

@end

NS_ASSUME_NONNULL_END
