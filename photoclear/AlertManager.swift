//
//  AlertManager.swift
//  wordWidget
//
//  Created by lifubing on 2024/8/21.
//

import Foundation
import UIKit
import SwiftUI
import JDStatusBarNotification


class AlertManager {
    
    static let shared = AlertManager()
    
    private init() {}
    
    func showAutoDismissAlert(_ message: String, delay: TimeInterval = 3.0) {
        guard let topViewController = getTopViewController() else {
            print("未能找到顶层视图控制器")
            return
        }
        
        let alert = UIAlertController(title: nil, message: message, preferredStyle: .alert)
        
        // 显示 Alert
        topViewController.present(alert, animated: true, completion: nil)
        
        // 3 秒后自动消失
        DispatchQueue.main.asyncAfter(deadline: .now() + delay) {
            alert.dismiss(animated: true, completion: nil)
        }
    }
    
    private func getTopViewController(base: UIViewController? = UIApplication.shared.windows.first?.rootViewController) -> UIViewController? {
        if let nav = base as? UINavigationController {
            return getTopViewController(base: nav.visibleViewController)
        }
        
        if let tab = base as? UITabBarController, let selected = tab.selectedViewController {
            return getTopViewController(base: selected)
        }
        
        if let presented = base?.presentedViewController {
            return getTopViewController(base: presented)
        }
        
        return base
    }
    
    func showTips(_ str:String) {
        NotificationPresenter.shared.present(str, duration: 1)
    }
    
    func showLoading(_ str:String) {
        NotificationPresenter.shared.present(str)
        NotificationPresenter.shared.displayActivityIndicator(true)
    }
    
    func hideLoading() {
        NotificationPresenter.shared.displayActivityIndicator(false)
    }
}

